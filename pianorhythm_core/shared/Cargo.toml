[package]
name = "pianorhythm_shared"
version = "0.1.0"
authors = ["Oak <<EMAIL>>"]
edition = "2021"
rust-version.workspace = true

[dependencies]
pianorhythm_proto = { path = "../proto" }
rustc-hash = { workspace = true }
seahash = { workspace = true }
protobuf = { workspace = true }
serde = { workspace = true }
uuid = { workspace = true }
log = { workspace = true }
strum = "0.26.3"
strum_macros = "0.26.4"
garde = { version = "0.22.0", features = ["derive", "regex"] }

[target.'cfg(target_arch = "x86_64")'.dependencies]
cached = { workspace = true }
instant = { version = "0.1.12" }

[target.'cfg(all(target_arch = "wasm32", target_os = "unknown"))'.dependencies]
cached = { workspace = true, features = ["wasm"] }
instant = { version = "0.1.12", features = ["wasm-bindgen", "inaccurate"] }
js-sys = "0.3.77"
