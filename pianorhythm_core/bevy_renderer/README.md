# PianoRhythmMania

## What is it?

PianoRhythmMania is a rhythm game component of the PianoRhythm project, built with the Bevy game engine. It offers a vertical scrolling note gameplay experience similar to games like KeyboardMania, Dance Dance Revolution, and StepMania.

## Features

- Vertical scrolling note gameplay
- Timing-based judgment system (Perfect, Great, Good, Miss)
- Support for both single notes and hold notes
- Dynamic BPM changes and beat tracking
- Custom chart format using TOML
- Piano keyboard visualization
- Score and combo tracking

## Chart Format

PianoRhythmMania uses TOML files for chart data. Example:

```toml
[song]
title = "Example Song"
artist = "Example Artist"

[meta]
author = "Chart Creator"
version = "1.0"
bpm = 120
difficulty = 3

# Notes follow with timing and key information
```

## Integration

This module is part of the larger PianoRhythm project, providing an interactive rhythm game experience alongside the piano learning and visualization tools.
