use crossbeam_channel::Sender;
use protobuf::{Message, ProtobufEnum};

use js_sys::{Int32<PERSON>rray, Reflect, Shared<PERSON><PERSON><PERSON><PERSON>uffer, Uint8Array};
use pianorhythm_shared::SHARED_BUFFERS::{
    SharedSynthEventBuffer, HEADER_SIZE, MAX_EVENTS, MAX_EVENT_SIZE, READ_INDEX, WRITE_INDEX
};
use wasm_bindgen::prelude::*;

use crate::core::events::ECSSynthEventsAction;
use crate::core::process_synth_event;
use crate::types::PianoRhythmSynthEvent;

fn read_events_from_shared_buffer(event_buffer: &SharedSynthEventBuffer) -> Option<(PianoRhythmSynthEvent, Vec<u8>)> {
    let read_idx = event_buffer.header.get_index(READ_INDEX as u32) as usize;
    let write_idx = event_buffer.header.get_index(WRITE_INDEX as u32) as usize;

    if read_idx == write_idx {
        return None; // No new events
    }

    let event_offset = read_idx * MAX_EVENT_SIZE;
    //let event_size = event_buffer.data_buffer.get_index(event_offset as u32) as usize;
    let event_size = (event_buffer.data_buffer.get_index(event_offset as u32) as u32)
        | ((event_buffer.data_buffer.get_index((event_offset + 1) as u32) as u32) << 8)
        | ((event_buffer.data_buffer.get_index((event_offset + 2) as u32) as u32) << 16)
        | ((event_buffer.data_buffer.get_index((event_offset + 3) as u32) as u32) << 24);
    let event_size = event_size as usize;

    if event_size == 0 || event_size > MAX_EVENT_SIZE - 4 {
        return None;
    }

    // Read event data
    let mut event_data = vec![0u8; event_size];
    for i in 0..event_size {
        event_data[i] = event_buffer.data_buffer.get_index((event_offset + 4 + i) as u32);
    }

    // Try to deserialize
    if let Ok(synth_event) = serde_json::from_slice::<PianoRhythmSynthEvent>(&event_data) {
        // Update read index
        let next_read = (read_idx + 1) % MAX_EVENTS;
        event_buffer.header.set_index(READ_INDEX as u32, next_read as i32);

        // Extract raw_bytes if available
        let raw_bytes = synth_event.raw_bytes.clone();
        Some((synth_event, raw_bytes))
    } else {
        log::error!("Failed to deserialize synth event: {:?}", &event_data);
        None
    }
}

pub fn execute(synth_events_sender: Sender<ECSSynthEventsAction>) {
    log::info!("Now listening for synth events on renderer...");

    let channel = web_sys::BroadcastChannel::new("pianorhythm.synth.events").unwrap();
    let cb = Closure::<dyn FnMut(web_sys::MessageEvent)>::new(move |message: web_sys::MessageEvent| {
        let data = message.data();
        let raw_bytes = Reflect::get(&data, &"raw_bytes".into())
            .unwrap()
            .unchecked_into::<Uint8Array>();
        let raw_bytes_vec = raw_bytes.to_vec(); // Convert Uint8Array to Vec<u8>
        let raw_bytes_slice = raw_bytes_vec.as_slice(); // Get a slice from the Vec<u8>

        if let Ok(synth_event) = serde_wasm_bindgen::from_value::<PianoRhythmSynthEvent>(data) {
            if let Some(output) = process_synth_event::process(&synth_event, raw_bytes_slice) {
                //log::info!("synth event output: {:?}", &output);
                _ = synth_events_sender.send(output);
            } else {
                log::error!("failed to process synth event: {:?}", synth_event);
            }
        } else {
            log::error!("failed to deserialize synth event");
        }
    });

    channel.set_onmessage(Some(cb.as_ref().unchecked_ref()));
    cb.forget();
}

pub fn execute_with_shared_buffer(synth_events_sender: Sender<ECSSynthEventsAction>, shared_buffer: SharedArrayBuffer) {
    log::info!("Now listening for synth events via SharedArrayBuffer2...");

    let event_buffer = SharedSynthEventBuffer {
        shared_buffer: shared_buffer.clone(),
        header: Int32Array::new_with_byte_offset(&shared_buffer, 0),
        data_buffer: Uint8Array::new_with_byte_offset_and_length(
            &shared_buffer,
            (HEADER_SIZE * 4) as u32,
            (MAX_EVENT_SIZE * MAX_EVENTS) as u32,
        ),
    };

    // Poll for events using requestAnimationFrame or setInterval
    let closure = Closure::wrap(Box::new(move || {
        while let Some((synth_event, raw_bytes)) = read_events_from_shared_buffer(&event_buffer) {
            if let Some(output) = process_synth_event::process(&synth_event, &raw_bytes) {
                _ = synth_events_sender.send(output);
            } else {
                log::error!("failed to process synth event");
            }
        }
    }) as Box<dyn FnMut()>);

    // Schedule periodic polling (check if in main thread or worker)
    const poll_interval_ms: i32 = 16; // ~60fps polling

    if crate::utils::is_main_thread_scope() {
        let window = web_sys::window().unwrap();
        window
            .set_interval_with_callback_and_timeout_and_arguments_0(closure.as_ref().unchecked_ref(), poll_interval_ms)
            .unwrap();
    } else if crate::utils::is_worker_global_scope() {
        let worker_global_scope = crate::utils::get_worker_global_scope();
        worker_global_scope
            .set_interval_with_callback_and_timeout_and_arguments_0(closure.as_ref().unchecked_ref(), poll_interval_ms)
            .unwrap();
    }

    closure.forget();
}
