use crossbeam_channel::Sender;
use js_sys::{Reflect, Uint8Array};
use protobuf::Message;
use protobuf::ProtobufEnum;
use wasm_bindgen::prelude::*;
use pianorhythm_proto::pianorhythm_actions::AppStateActions;

use pianorhythm_proto::pianorhythm_effects::AppStateEffects;
use pianorhythm_proto::pianorhythm_events::AppStateEvents;

use crate::core::events::{ECSWorldAction, ECSWorldActions};

pub fn execute(event_sender: Sender<ECSWorldActions>) {
    log::debug!("Now listening for core events...");
    let channel = web_sys::BroadcastChannel::new("pianorhythm.core-to-renderer.events").unwrap();
    let cb = Closure::<dyn FnMut(web_sys::MessageEvent)>::new(move |message: web_sys::MessageEvent| {
        let data = message.data();

        let raw_data = Reflect::get(&data, &"data".into()).unwrap().unchecked_into::<Uint8Array>().to_vec();
        let event_type = Reflect::get(&data, &"event".into()).unwrap().as_string().unwrap_or_default();

        match event_type.as_str() {
            "app_effects" => {
                if let Ok(effect) = AppStateEffects::parse_from_bytes(&raw_data) {
                    _ = event_sender.send(ECSWorldActions::new(ECSWorldAction::AppEffect(effect)));
                }
            }
            "app_events" if raw_data.len() > 0 => {
                if let Some(event) = AppStateEvents::from_i32(raw_data[0] as i32) {
                    _ = event_sender.send(ECSWorldActions::new(ECSWorldAction::AppEvent(event)));
                }
            }
            "app_actions" => {
                if let Ok(action) = AppStateActions::parse_from_bytes(&raw_data) {
                    _ = event_sender.send(ECSWorldActions::new(ECSWorldAction::AppAction(action)));
                }
            }
            _ => {}
        }
    });

    channel.set_onmessage(Some(cb.as_ref().unchecked_ref()));
    cb.forget();
}