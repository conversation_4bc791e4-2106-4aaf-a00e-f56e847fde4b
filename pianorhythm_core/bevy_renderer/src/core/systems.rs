use bevy::prelude::*;

use crate::resources;
use crate::resources::ActiveStageSettings;

#[cfg(feature = "desktop")]
pub(super) fn cursor_position_desktop_set(
    mut event_reader: EventReader<CursorMoved>,
    mut desktop_cursor_position: ResMut<crate::resources::CursorPositionDesktop>,
) {
    for cursor_event in event_reader.read() {
        desktop_cursor_position.0 = Some(cursor_event.position.clone());
    }
}

pub(super) fn on_active_stage_setting_change(
    mut commands: Commands,
    active_stage_settings: Res<ActiveStageSettings>,
    app_settings: Res<resources::ClientAppSettings>,
    view_targets: Query<Entity, With<crate::components::MainCamera>>,
) {
    if app_settings.0.GRAPHICS_ENABLE_FOG {
        for view in view_targets.iter() {
            if let Some(stage_fog) = active_stage_settings.fog_color {
                commands
                    .entity(view)
                    .insert(DistanceFog {
                        color: stage_fog.clone(),
                        falloff: active_stage_settings.fog_falloff.clone(),
                        ..default()
                    });
            } else {
                commands.entity(view).remove::<DistanceFog>();
            }
        }
    }
}
