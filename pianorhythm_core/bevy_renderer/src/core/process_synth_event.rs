use bevy::prelude::*;
use midly::live::LiveEvent;
use midly::MidiMessage;
use midly::num::u7;
use protobuf::ProtobufEnum;

use pianorhythm_proto::midi_renditions::MidiNoteSource;
use pianorhythm_shared::{GENERAL_MIDI, MIDI_CONTROL_BYTES};
use pianorhythm_shared::midi::DRUM_CHANNEL;

use crate::core::events;
use crate::core::events::{ECSSynthEventsAction, UserNoteData};
use crate::types::PianoRhythmSynthEvent;

pub fn process(synth_event: &PianoRhythmSynthEvent, raw_bytes: &[u8]) -> Option<ECSSynthEventsAction> {
    if let Ok(event) = LiveEvent::parse(&raw_bytes.to_vec()) {
        let source = synth_event.source.unwrap_or_default() as i32;
        let note_source = MidiNoteSource::from_i32(source).unwrap_or_default();
        let socket_id = synth_event.socket_id;

        match event {
            LiveEvent::Midi { channel, message } => {
                let get_gm_inst = |channel: u8, synth_event: Option<PianoRhythmSynthEvent>| {
                    // Force drum channel instruments to drum GM
                    if channel == DRUM_CHANNEL {
                        return GENERAL_MIDI::GMSoundSet::SynthDrum;
                    }

                    synth_event
                        .and_then(|evt| evt.current_program)
                        .and_then(GENERAL_MIDI::get_gm_set_from_program)
                        .unwrap_or(GENERAL_MIDI::GMSoundSet::AcousticGrandPiano)
                };

                let user_note_data = UserNoteData {
                    socket_id,
                    source: note_source,
                    channel: channel.into(),
                    gm_instrument: Some(get_gm_inst(channel.into(), Some(synth_event.clone()))),
                    raw_synth_event: Some(synth_event.clone()),
                    ..default()
                };

                let note_off = |key: u7, vel: u7| {
                    let note_data = ECSSynthEventsAction::NoteOff(UserNoteData {
                        socket_id,
                        note: key.into(),
                        vel: vel.into(),
                        ..user_note_data.clone()
                    });

                    return Some(note_data);
                };

                match message {
                    MidiMessage::NoteOn { key, vel } if vel == 0 => { return note_off(key, vel); }
                    MidiMessage::NoteOff { key, vel } => { return note_off(key, vel); }
                    MidiMessage::NoteOn { key, vel } => {
                        return Some(ECSSynthEventsAction::NoteOn(UserNoteData {
                            socket_id,
                            note: key.into(),
                            vel: vel.into(),
                            ..user_note_data.clone()
                        }));
                    }
                    MidiMessage::Controller { controller, value } => {
                        match controller.as_int() {
                            MIDI_CONTROL_BYTES::ALL_CONTROLLERS_OFF |
                            MIDI_CONTROL_BYTES::ALL_SOUND_OFF |
                            MIDI_CONTROL_BYTES::ALL_NOTES_OFF => {
                                return Some(events::ECSSynthEventsAction::AllNoteOff(channel.into()));
                            }
                            MIDI_CONTROL_BYTES::DAMPER_PEDAL => {
                                return Some(ECSSynthEventsAction::SustainPedal(UserNoteData {
                                    socket_id,
                                    vel: value.into(),
                                    ..user_note_data.clone()
                                }));
                            }
                            MIDI_CONTROL_BYTES::SOFT_PEDAL => {
                                return Some(ECSSynthEventsAction::SoftPedal(UserNoteData {
                                    socket_id,
                                    vel: value.into(),
                                    ..user_note_data.clone()
                                }));
                            }
                            _ => {}
                        }
                    }
                    _ => {}
                }
            }
            _ => {}
        }
    }

    None
}