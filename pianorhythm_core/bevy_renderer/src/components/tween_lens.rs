use bevy::prelude::{Vec3, *};
use bevy_tweening::{Lens, Targetable};
use smooth_bevy_cameras::LookTransform;

#[derive(Debug, Co<PERSON>, Clone, PartialEq)]
pub struct StandardMaterialBaseColorLens {
    pub start: Color,
    pub end: Color,
}

impl Lens<StandardMaterial> for StandardMaterialBaseColorLens {
    fn lerp(&mut self, target: &mut dyn Targetable<StandardMaterial>, ratio: f32) {
        target.base_color = self.start.mix(&self.end, ratio);
    }
}

#[derive(Debug, Copy, Clone, PartialEq)]
pub struct StandardMaterialEmissiveColorLens {
    pub start: Color,
    pub end: Color,
}

impl Lens<StandardMaterial> for StandardMaterialEmissiveColorLens {
    fn lerp(&mut self, target: &mut dyn Targetable<StandardMaterial>, ratio: f32) {
        target.emissive = self.start.mix(&self.end, ratio).to_linear();
    }
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct StandardMaterialBaseAndEmissiveColorLens {
    pub base_start: Color,
    pub base_end: Color,
    pub emissive_start: Color,
    pub emissive_end: Color,
}

impl Lens<StandardMaterial> for StandardMaterialBaseAndEmissiveColorLens {
    fn lerp(&mut self, target: &mut dyn Targetable<StandardMaterial>, ratio: f32) {
        target.base_color = self.base_start.mix(&self.base_end, ratio);
        target.emissive = self.emissive_start.mix(&self.emissive_end, ratio).into();
    }
}

#[derive(Debug, Copy, Clone, PartialEq)]
pub struct LookTransformEyeLens {
    pub start: Vec3,
    pub end: Vec3,
}

impl Lens<LookTransform> for LookTransformEyeLens {
    fn lerp(&mut self, target: &mut dyn Targetable<LookTransform>, ratio: f32) {
        let value = self.start + (self.end - self.start) * ratio;
        target.eye = value;
    }
}

#[derive(Debug, Copy, Clone, PartialEq)]
pub struct LookTransformTargetLens {
    pub start: Vec3,
    pub end: Vec3,
}

impl Lens<LookTransform> for LookTransformTargetLens {
    fn lerp(&mut self, target: &mut dyn Targetable<LookTransform>, ratio: f32) {
        let value = self.start + (self.end - self.start) * ratio;
        target.target = value;
    }
}
