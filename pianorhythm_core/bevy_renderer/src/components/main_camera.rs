use bevy::core_pipeline::tonemapping::Tonemapping;
use bevy::input::mouse::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Uni<PERSON>, MouseWheel};
use bevy::prelude::*;
use bevy_sequential_actions::ActionsBundle;
use smooth_bevy_cameras::controllers::orbit::{ControlEvent, OrbitCameraBundle, OrbitCameraController};
use smooth_bevy_cameras::LookTransform;

use crate::components::MainCamera;
use crate::resources;
use crate::resources::{CameraBounds, CameraLock};

pub fn setup_camera(mut commands: Commands, app_settings: Res<resources::ClientAppSettings>) {
    let mut entity_commands = commands.spawn((
        Camera3d::default(),
        Camera {
            order: 0,
            hdr: app_settings.0.GRAPHICS_ENABLE_HDR,
            ..default()
        },
        Tonemapping::AgX,
        crate::components::MainCamera,
    ));

    #[cfg(feature = "desktop")]
    {
        entity_commands
            .insert(bevy::pbr::ClusterConfig::Single)
            //.insert(GpuCulling)
            .insert(bevy::render::view::NoCpuCulling);
    }

    entity_commands.insert(ActionsBundle::new()).insert(OrbitCameraBundle::new(
        OrbitCameraController {
            mouse_rotate_sensitivity: Vec2::splat(if cfg!(feature = "desktop") { 1.0 } else { 0.5 }),
            mouse_translate_sensitivity: Vec2::splat(if cfg!(feature = "desktop") { 1.0 } else { 0.5 }),
            mouse_wheel_zoom_sensitivity: if cfg!(feature = "desktop") { 0.15 } else { 0.1 },
            ..default()
        },
        Vec3::new(-2.5, 4.5, 9.0),
        Vec3::ZERO,
        Vec3::Y,
    ));
}

pub fn orbit_camera_input_map(
    // #[cfg(debug_assertions)] mut contexts: bevy_inspector_egui::bevy_egui::EguiContexts,
    mut events: EventWriter<ControlEvent>,
    mut mouse_wheel_reader: EventReader<MouseWheel>,
    mut mouse_motion_events: EventReader<MouseMotion>,
    camera_lock: Res<CameraLock>,
    mouse_buttons: Res<ButtonInput<MouseButton>>,
    controllers: Query<&OrbitCameraController>,
) {
    // #[cfg(debug_assertions)]
    // {
    //     let ctx = contexts.ctx_mut();
    //     if ctx.wants_pointer_input() {
    //         return;
    //     }
    // }

    // Prevent movement if camera is locked
    if camera_lock.0 {
        return;
    }

    // Can only control one camera at a time.
    let controller = if let Some(controller) = controllers.iter().find(|c| c.enabled) {
        controller
    } else {
        return;
    };

    let OrbitCameraController {
        mouse_rotate_sensitivity,
        mouse_translate_sensitivity,
        mouse_wheel_zoom_sensitivity,
        pixels_per_line,
        ..
    } = *controller;

    let mut cursor_delta = Vec2::ZERO;
    for event in mouse_motion_events.read() {
        cursor_delta += event.delta;
    }

    if mouse_buttons.pressed(MouseButton::Left) {
        events.write(ControlEvent::Orbit(mouse_rotate_sensitivity * cursor_delta));
    }

    if mouse_buttons.pressed(MouseButton::Right) {
        events.write(ControlEvent::TranslateTarget(mouse_translate_sensitivity * cursor_delta));
    }

    let mut scalar = 1.0;
    for event in mouse_wheel_reader.read() {
        // scale the event magnitude per pixel or per line
        let scroll_amount = match event.unit {
            MouseScrollUnit::Line => event.y,
            MouseScrollUnit::Pixel => event.y / pixels_per_line,
        };
        scalar *= 1.0 - scroll_amount * mouse_wheel_zoom_sensitivity;
    }

    events.write(ControlEvent::Zoom(scalar));
}

pub fn limit_camera_movement(mut query: Query<&mut LookTransform, With<MainCamera>>, bounds: Res<CameraBounds>) {
    for mut transform in &mut query {
        let mut translation = transform.eye;

        translation.x = translation.x.clamp(bounds.min_x, bounds.max_x);
        translation.y = translation.y.clamp(bounds.min_y, bounds.max_y);
        translation.z = translation.z.clamp(bounds.min_z, bounds.max_z);

        transform.eye = translation;

        let mut trans_target = transform.target;

        trans_target.x = trans_target.x.clamp(bounds.min_x, bounds.max_x);
        trans_target.y = trans_target.y.clamp(bounds.min_y, bounds.max_y);
        trans_target.z = trans_target.z.clamp(bounds.min_z, bounds.max_z);

        transform.target = trans_target;
    }
}
