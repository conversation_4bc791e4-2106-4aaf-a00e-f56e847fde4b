use std::time::Duration;

use bevy::prelude::*;
use bevy_sequential_actions::*;
use bevy_tweening::{Animator, Delay, Tracks, Tween};
use bevy_tweening::lens::TransformPositionLens;

#[derive(Default)]
pub struct AnimateCameraAction {
    pub start_position: Option<Vec3>,
    pub target_position: Vec3,
    pub start_rotation: Option<Vec3>,
    pub target_rotation: Option<Vec3>,
    pub delay: Option<u64>,
    pub duration: Option<u64>,
}

impl Action for AnimateCameraAction {
    fn is_finished(&self, agent: Entity, world: &World) -> bool {
        let Some(animator) = world.get::<Animator<Transform>>(agent) else { return false; };

        animator.tweenable().times_completed() > 0
    }

    fn on_start(&mut self, agent: Entity, world: &mut World) -> bool {
        // let mut commands = world.commands();
        // let Some(transform) = world.entity(agent).get::<Transform>() else { return false; };
        let mut entity_commands = world.entity_mut(agent);

        let delay_duration = Duration::from_millis(self.delay.unwrap_or(1));
        let animate_duration = std::time::Duration::from_millis(self.duration.unwrap_or(1000));

        let easing_function = EaseFunction::QuadraticIn;

        let position_tween = Delay::new(delay_duration).then(
            Tween::new(
                easing_function,
                animate_duration,
                TransformPositionLens {
                    start: self.start_position.unwrap_or_default(),
                    end: self.target_position,
                },
            ));

        let tracks = vec![
            position_tween
        ];

        // if let Some(rotation_end) = self.target_rotation {
        //     let rotation_start = self.start_rotation.unwrap_or_default();
        //
        //     let rotation_tween = Delay::new(delay_duration).then(
        //         Tween::new(
        //             easing_function,
        //             animate_duration,
        //             TransformRotationLens {
        //                 start: Quat::from_euler(EulerRot::XYZ, rotation_start.x, rotation_start.y, rotation_start.z),
        //                 end: Quat::from_euler(EulerRot::XYZ, rotation_end.x, rotation_end.y, rotation_end.z),
        //             },
        //         ));
        //
        //     tracks.push(rotation_tween);
        // }

        entity_commands.insert(Animator::new(Tracks::new(tracks)));

        false
    }

    fn on_stop(&mut self, _agent: Option<Entity>, _world: &mut World, _reason: StopReason) {}
}