use bevy::prelude::*;
use tween::{Tween, Tweener};

use pianorhythm_proto::pianorhythm_app_renditions::AppPianoKeyType;
use pianorhythm_proto::user_renditions::UserDto;

use crate::core::events::UserNoteData;

pub mod main_camera;
pub mod fps_counter;
pub mod actions;
pub mod tween_lens;

pub type SendSyncTween<Value, Time> = Tweener<Value, Time, Box<dyn Tween<Value> + Send + Sync>>;

#[derive(Default, Debug, Clone, Component)]
pub struct MeshID(pub String);

impl AsRef<str> for MeshID {
    fn as_ref(&self) -> &str { &self.0 }
}

#[derive(Default, Debug, Clone, Component)]
pub struct EntityName(pub String);

#[derive(Default, Debug, Component)]
pub struct MainCamera;

#[derive(Default, Debug, Component)]
pub struct Main2DCamera;

#[derive(<PERSON><PERSON><PERSON>, Debug, Component)]
pub struct MainScene;

#[derive(De<PERSON>ult, Debug, <PERSON><PERSON>, Component)]
pub struct PianoKey;

#[derive(Default, Debug, <PERSON>lone, Component)]
pub struct StagePart;

#[derive(Default, Debug, Clone, Component)]
pub struct StageFloor;

#[derive(Default, Debug, Clone, Copy, Component, Reflect)]
#[reflect(Component)]
pub struct MidiID(pub u8);

#[derive(Default, Debug, Component)]
pub struct PedalID(pub String);

#[derive(Default, Debug, Component)]
pub struct SustainPedal;

#[derive(Default, Debug, Component)]
pub struct DampenPedal;

#[derive(Default, Debug, Component)]
pub struct PianoKeyType(pub AppPianoKeyType);

#[derive(Default, Debug, Component, Reflect, Clone, Copy)]
pub struct AnimationState {
    pub is_down: bool,
}

#[derive(Default, Debug, Component, Reflect, Clone)]
pub struct OnActiveNote(pub UserNoteData);

/// A timer to track how long a mesh animation
/// has been in the 'down' position.
#[derive(Component, Debug, Clone)]
#[component(storage = "SparseSet")]
pub struct AnimationDown {
    pub start_time: Timer,
}

#[derive(Component, Debug, Reflect)]
#[component(storage = "SparseSet")]
pub struct SplitModeChannel(pub u8);

#[derive(Default, Component)]
#[component(storage = "SparseSet")]
pub struct MeshDisabled;

#[derive(Default, Component)]
pub struct MeshVisible;

#[derive(Component, Debug)]
#[component(storage = "SparseSet")]
pub struct AllSoundOffTriggered;

#[derive(Component, Debug, Default)]
pub struct Client;

#[derive(Component, Reflect)]
pub struct SocketID(pub u32);

#[derive(Component, Debug)]
pub struct UserData(pub UserDto);

#[derive(Component, Debug, Reflect, PartialEq)]
#[reflect(Component, Debug)]
pub enum InstrumentType {
    PIANO,
    PIANO_BENCH,
    DRUMS,
    GUITARS
}

impl Default for InstrumentType {
    fn default() -> Self {
        InstrumentType::PIANO
    }
}

#[derive(Component, Debug, Default, Reflect)]
pub struct MainInstrument(pub InstrumentType);

#[derive(Component, Debug, Default, Reflect)]
pub struct LowPolyModel;

#[derive(Component, Debug, Default, Reflect)]
pub struct DrumsScene;

#[derive(Component, Debug, Default, Reflect)]
pub struct DrumsMeshType;

#[derive(Component, Debug, Default, Reflect)]
pub struct PianoMeshType;

#[derive(Component, Debug, Default, Reflect)]
pub struct PianoBenchMeshType;

#[derive(Component, Debug, Default, Reflect)]
pub struct PianoMeshKey;

#[derive(Component, Debug, Default, Reflect)]
pub struct PianoMeshPedal;

#[derive(Component, Debug, Default, Reflect)]
pub struct SplitModeMaterialColor(pub Color);

#[derive(Default, Debug, Clone, Copy, Component, Reflect)]
#[reflect(Component, Debug)]
pub struct BaseMeshColor(pub Color);

#[derive(Default, Debug, Clone, Copy, Component, Reflect)]
#[reflect(Component, Debug)]
pub struct DefaultMeshColor(pub Color);

pub mod consts {
    pub const KEY_ANIMATION_DURATION_MAX: f64 = 0.04;
    pub const KEY_ANIMATION_DURATION_MIN: f64 = 0.08;
    pub const PEDAL_ANIMATION_DURATION: f64 = 0.12;
    pub const DISABLED_KEY_ALPHA: f32 = 0.2;
    pub const MAX_KEYDOWN_ANIMATION: u64 = 10;
    pub const MAX_KEYDOWN_ANIMATION_FOR_MOUSE: u64 = 3;
}