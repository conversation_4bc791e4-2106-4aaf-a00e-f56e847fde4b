use bevy::ecs::system::SystemState;
use bevy::window::WindowCloseRequested;
#[cfg(target_arch = "wasm32")]
use canvas_view::*;
#[cfg_attr(debug_assertions, allow(dead_code, unused_imports))]
use std::ops::{Deref, DerefMut};
use std::sync::atomic::Ordering;
#[cfg(target_arch = "wasm32")]
pub use utils::web_ffi::*;
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::prelude::*;

#[cfg(feature = "desktop")]
pub mod bevy_desktop {
    pub use crate::plugins::games::mania::events::{ManiaAudioEvent, ManiaEvent, MANIA_EVENTS_CHANNEL};
    pub use bevy::app::App;
    pub use bevy::prelude::*;
    pub use bevy::render::*;
    pub use bevy::window::*;
    pub use bevy::*;
    pub use bevy_async_ecs::AsyncWorld;
    use std::cell::RefMut;

    pub fn create_async_world(mut app: RefMut<'_, App>) -> AsyncWorld {
        bevy_async_ecs::AsyncWorld::from_world(&mut app.world_mut())
    }
}

pub mod actions;
pub mod app;
pub mod components;
pub mod core;
pub mod plugins;
pub mod resources;
pub mod types;
pub mod utils;

use bevy::app::App;
use bevy::prelude::*;

#[cfg(feature = "standalone")]
#[cfg(not(target_arch = "wasm32"))]
pub mod wry_webview;

#[cfg(target_arch = "wasm32")]
pub mod canvas_view;

pub struct WorkerApp {
    pub app: App,
    pub window: Entity,
    pub scale_factor: f32,
    pub plugins_ready: bool,
}

impl Deref for WorkerApp {
    type Target = App;

    fn deref(&self) -> &Self::Target {
        &self.app
    }
}

impl DerefMut for WorkerApp {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.app
    }
}

impl WorkerApp {
    pub fn new(app: App) -> Self {
        Self {
            app,
            window: Entity::PLACEHOLDER,
            scale_factor: 1.0,
            plugins_ready: false,
        }
    }

    pub fn to_physical_size(&self, x: f32, y: f32) -> Vec2 {
        Vec2::new(x * self.scale_factor, y * self.scale_factor)
    }
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen(start)]
fn main_wasm() {
    use log::Level;
    console_log::init_with_level(Level::Info).expect("error initializing log");

    #[cfg(debug_assertions)]
    console_error_panic_hook::set_once();

    #[cfg(not(feature = "run_bevy_in_web_worker"))]
    log::info!("Running in main thread!");

    #[cfg(feature = "run_bevy_in_web_worker")]
    log::info!("Running in web worker!");

    #[cfg(not(feature = "run_bevy_in_web_worker"))]
    app::root_app(None).run();
}

pub(crate) fn close_bevy_window(mut app: Box<App>) {
    // Already exited
    if plugins::SHOULD_EXIT.load(Ordering::SeqCst) {
        return;
    }

    let mut windows_state: SystemState<Query<(Entity, &mut Window)>> = SystemState::from_world(app.world_mut());

    let windows = windows_state.get_mut(app.world_mut());

    if let Some((entity, _)) = windows.iter().last() {
        app.world_mut().send_event(WindowCloseRequested { window: entity });
    }

    windows_state.apply(app.world_mut());

    app.update();

    plugins::SHOULD_EXIT.store(true, Ordering::SeqCst);
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn get_wasm_module() -> JsValue {
    wasm_bindgen::module()
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn get_wasm_memory() -> JsValue {
    wasm_bindgen::memory()
}
