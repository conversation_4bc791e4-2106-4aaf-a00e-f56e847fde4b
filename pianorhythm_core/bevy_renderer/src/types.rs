use bevy::prelude::*;
use crossbeam_channel::{Receiver, Sender, unbounded};
use raw_window_handle::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HasWindowHandle};
#[cfg(target_arch = "wasm32")]
use serde::{Deserialize, Serialize};

use pianorhythm_proto::pianorhythm_actions::AppStateActions;
use pianorhythm_proto::pianorhythm_effects::AppStateEffects;

pub trait DataDispatcher {
    /// Dispatches non-render app state actions back to front end
    fn dispatch_app_state_action(&self, _action: &AppStateActions) -> () {}

    /// Dispatches non-render app state effects back to front end
    fn dispatch_app_state_effect(&self, _app_state_effect: &AppStateEffects) -> ();

    /// Dispatches outbound render data back to front end
    fn dispatch_render_data(&self, _app_state_effect: &AppStateEffects) -> ();
}

pub type DataDispatcherType = Box<dyn DataDispatcher + Send + Sync>;

#[cfg_attr(target_arch = "wasm32", derive(Serialize, Deserialize))]
#[derive(<PERSON><PERSON>, Debu<PERSON>, De<PERSON>ult)]
pub struct PianoRhythmSynthEvent {
    pub message_type: Option<u8>,
    #[cfg_attr(target_arch = "wasm32", serde(default))]
    pub channel: u8,
    #[cfg_attr(target_arch = "wasm32", serde(default))]
    pub raw_bytes: Vec<u8>,
    pub current_program: Option<u8>,
    pub current_bank: Option<i32>,
    pub current_volume: Option<u8>,
    pub current_pitch: Option<u32>,
    pub current_expression: Option<u8>,
    pub current_pan: Option<u8>,
    pub source: Option<u8>,
    pub device_id: Option<u32>,
    pub socket_id: Option<u32>,
    #[cfg_attr(target_arch = "wasm32", serde(default))]
    pub is_client: bool,
}

#[derive(Debug, Clone, Copy, Default, Eq, PartialEq, Hash, States, Reflect)]
pub enum StageState {
    #[default]
    Idle,
    Loading,
    Loaded,
}

#[derive(Resource)]
pub struct AppStateDispatcher(pub DataDispatcherType);

#[derive(Resource)]
pub struct AppEffectsToTauri(pub Channels<AppStateEffects>);

#[derive(Resource)]
pub struct AppEffectsFromTauri(pub Channels<AppStateEffects>);

#[derive(Resource)]
pub struct AppActionFromTauri(pub Channels<AppStateActions>);

pub struct Channels<T>(pub (Sender<T>, Receiver<T>));

impl<T> Channels<T> {
    pub fn new() -> Self {
        Self(unbounded())
    }

    pub fn send(&self, event: T) {
        _ = self.0.0.send(event);
    }

    pub fn recv(&self) -> Result<T, ()> {
        self.0.1.try_recv().map_err(|_| ())
    }

    pub fn clone_sender(&self) -> Sender<T> {
        self.0.0.clone()
    }

    pub fn clone_rcv(&self) -> Receiver<T> {
        self.0.1.clone()
    }

    pub fn clone(&self) -> Self {
        Self(self.0.clone())
    }
}

impl AppEffectsToTauri {
    pub fn new() -> Self {
        Self(Channels::new())
    }
}

impl AppEffectsFromTauri {
    pub fn new() -> Self {
        Self(Channels::new())
    }
}

impl AppActionFromTauri {
    pub fn new() -> Self {
        Self(Channels::new())
    }
}

#[derive(Clone, Debug)]
pub struct SendSyncWrapper<T>(pub T)
where
    T: HasWindowHandle + HasDisplayHandle + Send + Sync + 'static;

unsafe impl<T> Send for SendSyncWrapper<T> where T: HasWindowHandle + HasDisplayHandle + Send + Sync + 'static {}

unsafe impl<T> Sync for SendSyncWrapper<T> where T: HasWindowHandle + HasDisplayHandle + Send + Sync + 'static {}