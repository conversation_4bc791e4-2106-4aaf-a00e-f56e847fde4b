use std::time::Duration;

use bevy::prelude::*;
use bevy_sequential_actions::*;
use bevy_tweening::{Animator, Delay, Tracks, Tween};
use smooth_bevy_cameras::LookTransform;

use crate::components::tween_lens::{LookTransformEyeLens, LookTransformTargetLens};

#[derive(Resource)]
pub struct CameraAnimating;

#[derive(Default)]
pub struct AnimateCameraLookTransformAction {
    pub target_start: Option<Vec3>,
    pub target_end: Vec3,
    pub eye_start: Option<Vec3>,
    pub eye_end: Option<Vec3>,
    pub delay: Option<u64>,
    pub duration: Option<u64>,
}

impl Action for AnimateCameraLookTransformAction {
    fn is_finished(&self, agent: Entity, world: &World) -> bool {
        let Some(animator) = world.get::<Animator<LookTransform>>(agent) else { return false; };
        animator.tweenable().times_completed() > 0
    }

    fn on_start(&mut self, agent: Entity, world: &mut World) -> bool {
        world.commands().insert_resource(CameraAnimating);

        let mut entity_commands = world.entity_mut(agent);
        let look_transform = entity_commands.get::<LookTransform>().cloned().unwrap_or_default();

        let delay_duration = Duration::from_millis(self.delay.unwrap_or(1));
        let animate_duration = std::time::Duration::from_millis(self.duration.unwrap_or(1000));

        let easing_function = EaseFunction::QuadraticIn;

        let target_tween = Delay::new(delay_duration).then(
            Tween::new(
                easing_function,
                animate_duration,
                LookTransformTargetLens {
                    start: self.target_start.unwrap_or(look_transform.target),
                    end: self.target_end,
                },
            ));

        let mut tracks = vec![
            target_tween
        ];

        if let Some(eye_end) = self.eye_end {
            let eye_start = self.eye_start.unwrap_or(look_transform.eye);

            let eye_tween = Delay::new(delay_duration).then(
                Tween::new(
                    easing_function,
                    animate_duration,
                    LookTransformEyeLens {
                        start: eye_start,
                        end: eye_end,
                    },
                ));

            tracks.push(eye_tween);
        }

        entity_commands.insert(Animator::new(Tracks::new(tracks)));

        false
    }

    fn on_stop(&mut self, _agent: Option<Entity>, _world: &mut World, _reason: StopReason) {}

    fn on_remove(&mut self, _agent: Option<Entity>, world: &mut World) {
        world.commands().remove_resource::<CameraAnimating>();
    }
}