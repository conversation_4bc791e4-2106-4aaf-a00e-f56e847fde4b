use bevy::asset::UnapprovedPathMode;
use bevy::input::common_conditions::input_toggle_active;
use bevy::log::tracing_subscriber::{Layer, Registry};
use bevy::log::Level;
use bevy::prelude::*;
use bevy::render::render_resource::WgpuFeatures;
use bevy::render::settings::WgpuSettings;
use bevy::window::WindowEvent;
use bevy::winit::WinitSettings;
use bevy_egui::EguiPlugin;
use bevy_inspector_egui::bevy_egui::EguiContext;
use bevy_inspector_egui::prelude::*;
use bevy_inspector_egui::{bevy_inspector, egui};
use std::any::TypeId;

use crate::plugins::external::bevy_mod_billboard;
#[cfg(feature = "desktop")]
use bevy_kira_audio::prelude::*;
#[cfg(feature = "desktop")]
use kira::backend::mock;
#[cfg(feature = "desktop")]
use kira::{
    sound::static_sound::{StaticSoundData, StaticSoundSettings}, AudioManager, AudioManagerSettings, DefaultBackend
};

pub fn root_app(#[cfg(target_arch = "wasm32")] synth_events_shared_buffer: Option<js_sys::SharedArrayBuffer>) -> App {
    let mut app = App::new();

    let mut default_plugins = DefaultPlugins
        .set(ImagePlugin::default_nearest())
        .set(AssetPlugin {
            #[cfg(target_arch = "wasm32")]
            meta_check: bevy::asset::AssetMetaCheck::Never,
            unapproved_path_mode: UnapprovedPathMode::Allow,
            ..default()
        })
        .set(bevy::log::LogPlugin {
            level: Level::DEBUG,
            filter: "wgpu=error,naga=error,wgpu_core=error,bevy_render=debug,bevy_ecs=warn,bevy_hanabi=info"
                .to_string(),
            custom_layer: |_| None,
        })
        .set(WindowPlugin {
            primary_window: Some(Window {
                title: "PianoRhythm".to_string(),
                #[cfg(target_arch = "wasm32")]
                present_mode: bevy::window::PresentMode::AutoNoVsync,
                #[cfg(target_arch = "wasm32")]
                canvas: Some(String::from("#piano-canvas-renderer")),
                // #[cfg(feature = "desktop")]
                // decorations: false,
                // #[cfg(feature = "desktop")]
                // skip_taskbar: true,
                ..default()
            }),
            ..default()
        });

    #[cfg(feature = "desktop")]
    {
        // default_plugins = default_plugins.disable::<bevy::winit::WinitPlugin>();
        // Note: If the log plugin is not disabled, then it causes the desktop
        // app to crash when via Tauri
        app.add_plugins(bevy_async_ecs::AsyncEcsPlugin);
        default_plugins = default_plugins.disable::<bevy::log::LogPlugin>();

        app.add_plugins(bevy_panorbit_camera::PanOrbitCameraPlugin);
    }

    #[cfg(any(feature = "webgpu", feature = "desktop"))]
    {
        log::info!("Using Webgpu render plugin.");
        let mut wgpu_settings = WgpuSettings::default();
        wgpu_settings.features.set(WgpuFeatures::VERTEX_WRITABLE_STORAGE, true);

        default_plugins = default_plugins.set(bevy::render::RenderPlugin {
            render_creation: wgpu_settings.into(),
            synchronous_pipeline_compilation: false,
            ..default()
        });
    }

    #[cfg(feature = "run_bevy_in_web_worker")]
    {
        #[cfg(debug_assertions)]
        info!("Disabling Winit plugin.");
        default_plugins = default_plugins.disable::<bevy::winit::WinitPlugin>();
    }

    #[cfg(not(debug_assertions))]
    {
        default_plugins = default_plugins
            .disable::<bevy::diagnostic::DiagnosticsPlugin>()
            // .disable::<bevy::prelude::GilrsPlugin>()
            .disable::<bevy::log::LogPlugin>();
    }

    // -- Plugins
    app.insert_resource(ClearColor(Color::NONE))
        // .add_plugins(AudioPlugin)
        .add_plugins((default_plugins, MeshPickingPlugin))
        .add_plugins(bevy_sequential_actions::SequentialActionsPlugin)
        .add_plugins(bevy_tweening::TweeningPlugin)
        .add_plugins(bevy_mod_billboard::plugin::BillboardPlugin);

    let core_plugin = crate::core::CorePlugin::default();
    #[cfg(target_arch = "wasm32")]
    core_plugin.setup_web_events(synth_events_shared_buffer);
    app.add_plugins(core_plugin);

    #[cfg(debug_assertions)]
    {
        // app.add_plugins(DefaultPickingPlugins.build());
        // TODO: Look into
        // .insert_resource(bevy_mod_picking::prelude::DebugPickingMode::Disabled);
    }

    // #[cfg(not(debug_assertions))]
    // {
    //     app.add_plugins(DefaultPickingPlugins.build().disable::<bevy_mod_picking::debug::DebugPickingPlugin>())
    //         .insert_resource(bevy_mod_picking::debug::DebugPickingMode::Disabled);
    // }

    #[cfg(debug_assertions)] // debug/dev builds only
    {
        // app.add_plugins(EguiPlugin)
        //     .add_plugins(bevy_inspector_egui::DefaultInspectorConfigPlugin)
        //     .add_systems(Update, inspector_ui);
        // app.add_plugins(bevy_inspector_egui::quick::WorldInspectorPlugin::default().run_if(input_toggle_active(true, KeyCode::Escape)));
    }

    app.insert_resource(WinitSettings {
        focused_mode: bevy::winit::UpdateMode::Continuous,
        unfocused_mode: bevy::winit::UpdateMode::Continuous,
    });

    app
}

fn setup(
    mut commands: Commands,
    asset_server: Res<AssetServer>,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    commands.spawn((
        Camera3d::default(),
        Transform::from_xyz(3.0, 1.0, 3.0).looking_at(Vec3::new(0.0, -0.5, 0.0), Vec3::Y),
        EnvironmentMapLight {
            diffuse_map: asset_server.load("environment_maps/pisa_diffuse_rgb9e5_zstd.ktx2"),
            specular_map: asset_server.load("environment_maps/pisa_specular_rgb9e5_zstd.ktx2"),
            intensity: 2_000.0,
            ..default()
        },
    ));

    let cube = meshes.add(Cuboid::new(0.5, 0.5, 0.5));

    const GOLDEN_ANGLE: f32 = 137.507_77;

    let mut hsla = Hsla::hsl(0.0, 1.0, 0.5);
    for x in -1..2 {
        for z in -1..2 {
            commands.spawn((
                Mesh3d(cube.clone()),
                MeshMaterial3d(materials.add(Color::from(hsla))),
                Transform::from_translation(Vec3::new(x as f32, 0.0, z as f32)),
            ));
            hsla = hsla.rotate_hue(GOLDEN_ANGLE);
        }
    }
}

fn animate_materials(
    material_handles: Query<&MeshMaterial3d<StandardMaterial>>,
    time: Res<Time>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    for material_handle in material_handles.iter() {
        if let Some(material) = materials.get_mut(material_handle) {
            if let Color::Hsla(ref mut hsla) = material.base_color {
                *hsla = hsla.rotate_hue(time.delta_secs() * 100.0);
            }
        }
    }
}

fn inspector_ui(world: &mut World) {
    let mut egui_context = world.query_filtered::<&mut EguiContext, With<bevy::window::PrimaryWindow>>();
    // .query_filtered::<Entity, With<bevy::window::PrimaryWindow>>();
    // .single(world)
    // .clone();

    let has_window = egui_context.single(world).is_ok();
    log::info!("Inspector window: {:?}", has_window);

    // egui::Window::new("UI").show(egui_context.get_mut(), |ui| {
    //     egui::ScrollArea::both().show(ui, |ui| {
    //         // equivalent to `WorldInspectorPlugin`
    //         bevy_inspector::ui_for_world(world, ui);
    //         // works with any `Reflect` value, including `Handle`s
    //         let mut any_reflect_value: i32 = 5;
    //         bevy_inspector::ui_for_value(&mut any_reflect_value, ui, world);
    //         egui::CollapsingHeader::new("Materials").show(ui, |ui| {
    //             bevy_inspector::ui_for_assets::<StandardMaterial>(world, ui);
    //         });
    //         ui.heading("Entities");
    //         // bevy_inspector::ui_for_entities(world, ui);
    //     });
    // });
}
