use bevy::prelude::*;

#[derive(Component)]
struct DebugUIText;

#[derive(Component)]
struct TranslationText;

#[derive(Component)]
struct RotationText;

#[derive(Default)]
pub struct DebugUIPlugin;

//TODO
// impl Plugin for DebugUIPlugin {
//     fn build(&self, app: &mut App) {
//         app
//             .add_systems(Startup, setup)
//             .add_systems(
//                 PostUpdate,
//                 (update_translation_text, update_rotation_text)
//                     .distributive_run_if(in_state(AppState::InGame)),
//             );
//
//         info!("Debug UI plugin built.");
//     }
// }
//
// pub fn setup(
//     mut commands: Commands,
// ) {
//     let root = commands.spawn(NodeBundle {
//         z_index: ZIndex::Global(i32::MAX),
//         background_color: BackgroundColor(Color::BLACK.with_alpha(0.)),
//         style: Style {
//             position_type: PositionType::Absolute,
//             flex_direction: FlexDirection::Column,
//             top: Val::Px(25.),
//             right: Val::Px(10.),
//             padding: UiRect::all(Val::Px(4.0)),
//             bottom: Val::Auto,
//             left: Val::Auto,
//             ..default()
//         },
//         ..default()
//     }).id();
//
//     let translation_text = commands.spawn((
//         DebugUIText,
//         TranslationText,
//         TextBundle {
//             text: Text::from_sections([
//                 TextSection {
//                     value: "Camera Translation".to_string(),
//                     style: TextStyle {
//                         font_size: 12.0,
//                         color: Color::WHITE,
//                         ..default()
//                     },
//                 }
//             ]),
//             ..default()
//         }
//     )).id();
//
//     let rotation_text = commands.spawn((
//         DebugUIText,
//         RotationText,
//         TextBundle {
//             text: Text::from_sections([
//                 TextSection {
//                     value: "Camera Rotation".to_string(),
//                     style: TextStyle {
//                         font_size: 12.0,
//                         color: Color::WHITE,
//                         ..default()
//                     },
//                 }
//             ]),
//             ..default()
//         }
//     )).id();
//
//     commands.entity(root).with_children(&[
//         translation_text,
//         rotation_text
//     ]);
// }
//
// fn update_translation_text(
//     camera_query: Query<&LookTransform, With<MainCamera>>,
//     mut translation_query: Query<&mut Text, With<TranslationText>>,
// ) {
//     let Ok(transform) = camera_query.single() else { return; };
//
//     for mut text in &mut translation_query {
//         text.sections[0].value = format!("{:?}", transform.target);
//     }
// }
//
// fn update_rotation_text(
//     camera_query: Query<&LookTransform, With<MainCamera>>,
//     mut rotation_query: Query<&mut Text, With<RotationText>>,
// ) {
//     let Ok(transform) = camera_query.single() else { return; };
//
//     for mut text in &mut rotation_query {
//         // let rot = transform.rotation.to_euler(EulerRot::XYZ);
//         // let mapped = (
//         //     rot.0.to_degrees(),
//         //     rot.1.to_degrees(),
//         //     rot.2.to_degrees(),
//         // );
//
//         // text.sections[0].value = format!("{:?}", mapped);
//         text.sections[0].value = format!("{:?}", transform.eye);
//     }
// }