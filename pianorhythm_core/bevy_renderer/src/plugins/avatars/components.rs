use std::time::Duration;

use crate::plugins::avatars::resources::AvatarTargetPosition;
use bevy::prelude::*;
use bevy_descendant_collector::*;
use bevy_inspector_egui::prelude::ReflectInspectorOptions;
use bevy_inspector_egui::InspectorOptions;

#[derive(Component)]
pub struct AvatarRelatedPlugin;

#[derive(Component)]
pub struct AvatarModel;

#[derive(Component)]
pub struct AvatarModelHandle(pub Handle<Gltf>);

#[derive(Component)]
pub struct AvatarDisplayModelForCustomization;

#[derive(Component)]
pub struct AvatarDisplayModelActiveModelKey(pub String);

#[derive(Component)]
pub struct AvatarModelHiddenDuringCustomizationScreen;

#[derive(Component)]
pub struct AvatarArmature;

#[derive(Component)]
pub struct ClientAvatarArmature;

#[derive(Debug, <PERSON><PERSON>, Copy, Component, Reflect)]
#[reflect(Component)]
pub struct AvatarReference(pub Entity);

#[derive(Debug, Clone, Copy, Component, Reflect)]
#[reflect(Component)]
pub struct AvatarSocketID(pub u32);

#[derive(Debug, Clone, Copy, Component, Reflect)]
#[reflect(Component)]
pub struct AvatarArmatureReference(pub Entity);

#[derive(Component)]
pub struct ClientAvatarModel;

#[derive(Component)]
pub struct AvatarModelMesh;

#[derive(Component)]
pub struct AvatarNameTag;

#[derive(Component, Reflect)]
#[reflect(Component)]
pub struct AvatarSittingOnSeat(pub u8);

#[derive(Component)]
pub struct ClientAvatarTargetPositionMesh;

#[derive(Default, Debug, Clone, Copy, Component, Reflect, PartialEq, Eq)]
#[reflect(Component)]
pub enum AvatarAnimationID {
    IDLE,
    WALK,
    RUN,
    SIT,
    #[default]
    NO_ANIMATION,
}

#[derive(Debug, Clone, Copy, Component, Reflect)]
#[reflect(Component)]
pub struct AvatarTargetAnimation {
    pub animation: AvatarAnimationID,
    pub repeat: bool,
    pub transition_duration: Option<Duration>,
    pub speed: Option<f32>,
}

#[derive(Debug, Clone, Copy, Component, Reflect, Resource)]
#[reflect(Component)]
/// The seat index that the avatar is currently sitting on.
pub(super) struct AvatarPianoSeatIndex(pub u8);

#[derive(Debug, Clone, Copy, Component, Reflect, Resource)]
#[reflect(Component)]
/// The seat index that the avatar is currently targeting.
pub(super) struct AvatarPianoSeatTargetIndex(pub u8);

#[derive(Debug, Clone, Copy, Component, Reflect)]
#[reflect(Component)]
/// The avatar that is currently occupying this seat.
pub(super) struct AvatarPianoSeatOccupiedBy(pub Entity);

#[derive(Debug, Clone, Copy, Component, Reflect)]
#[reflect(Component)]
pub(super) struct AvatarPianoSeatTooltipReference(pub Entity);

#[derive(Debug, Clone, Copy, Component, Reflect)]
#[reflect(Component)]
pub(super) struct AvatarPianoSeatTooltip;

impl AvatarTargetAnimation {
    pub fn new(animation: AvatarAnimationID) -> Self {
        Self { animation, ..default() }
    }

    pub fn with_repeat(mut self, value: bool) -> Self {
        self.repeat = value;
        self
    }

    pub fn idle() -> Self {
        Self {
            animation: AvatarAnimationID::IDLE,
            ..default()
        }
    }

    pub fn walk() -> Self {
        Self {
            animation: AvatarAnimationID::WALK,
            ..default()
        }
    }

    pub fn run() -> Self {
        Self {
            animation: AvatarAnimationID::RUN,
            ..default()
        }
    }

    pub fn to_node_index(&self) -> usize {
        match self.animation {
            AvatarAnimationID::IDLE => 0,
            //AvatarAnimationID::WALK => 15,
            //AvatarAnimationID::RUN => 8,
            //AvatarAnimationID::SIT => 11,
            _ => 0,
        }
    }
}

impl Default for AvatarTargetAnimation {
    fn default() -> Self {
        Self {
            animation: AvatarAnimationID::default(),
            repeat: true,
            transition_duration: Some(Duration::from_millis(1000)),
            speed: Default::default(),
        }
    }
}

/// Local state for tracking the avatar's position.
#[derive(Debug, Clone, Copy, Component, Reflect, Default)]
#[reflect(Component)]
pub(super) struct AvatarTrackingState {
    pub first_run: bool,
    pub initial_position: Option<Vec3>,
    pub previous_translation: Vec3,
    pub lerp_v: f32,
    pub last_position: Vec3,
}

impl AvatarTrackingState {
    pub fn update(&mut self, translation: Vec3) {
        self.previous_translation = translation;
    }

    pub fn set_target_reached(&mut self, commands: &mut Commands, entity: Entity) {
        commands.entity(entity.clone()).insert(AvatarTargetAnimation::idle());

        self.first_run = false;
        self.initial_position = None;
        self.previous_translation = Vec3::default();
    }
}

#[derive(Component, EntityCollectorTarget, Reflect, InspectorOptions)]
#[reflect(Component, InspectorOptions)]
#[name_path("CharacterArmature")]
pub struct BaseCharacterArmature {
    #[name_path("Bone")]
    pub base: Entity,
    #[name_path("Bone", "Body")]
    pub body: Entity,
    #[name_path("Bone", "Body", "Hips", "Abdomen", "Torso", "Neck", "Head")]
    pub head: Entity,

    #[name_path("Bone", "Body", "Hips", "Abdomen", "Torso", "Shoulder.L")]
    pub left_shoulder: Entity,
    #[name_path("Bone", "Body", "Hips", "Abdomen", "Torso", "Shoulder.L", "UpperArm.L")]
    pub left_upper_arm: Entity,
    #[name_path("Bone", "Body", "Hips", "Abdomen", "Torso", "Shoulder.L", "UpperArm.L", "LowerArm.L")]
    pub left_lower_arm: Entity,
    #[name_path(
        "Bone",
        "Body",
        "Hips",
        "Abdomen",
        "Torso",
        "Shoulder.L",
        "UpperArm.L",
        "LowerArm.L",
        "Fist.L"
    )]
    pub left_hand: Entity,

    #[name_path("Bone", "Body", "Hips", "Abdomen", "Torso", "Shoulder.R")]
    pub right_shoulder: Entity,
    #[name_path("Bone", "Body", "Hips", "Abdomen", "Torso", "Shoulder.R", "UpperArm.R")]
    pub right_upper_arm: Entity,
    #[name_path("Bone", "Body", "Hips", "Abdomen", "Torso", "Shoulder.R", "UpperArm.R", "LowerArm.R")]
    pub right_lower_arm: Entity,
    #[name_path(
        "Bone",
        "Body",
        "Hips",
        "Abdomen",
        "Torso",
        "Shoulder.R",
        "UpperArm.R",
        "LowerArm.R",
        "Fist.R"
    )]
    pub right_hand: Entity,
}

/// An armature component that maps to the bone hierarchy shown in the image.
///
/// This struct provides direct access to the `Entity` of each bone,
/// allowing for precise programmatic control over the character's rig.
/// It includes detailed paths for the torso, head, arms, legs, and individual fingers.
#[derive(Component, EntityCollectorTarget, Reflect, InspectorOptions)]
#[reflect(Component, InspectorOptions)]
#[name_path("Boy.Armature")]
pub struct BaseMaleArmature2 {
    #[name_path("hips")]
    pub hips: Entity,

    // --- Spine and Head ---
    #[name_path("hips", "spine")]
    pub spine: Entity,
    #[name_path("hips", "spine", "chest")]
    pub chest: Entity,
    #[name_path("hips", "spine", "chest", "upper.chest")]
    pub upper_chest: Entity,
    #[name_path("hips", "spine", "chest", "upper.chest", "neck")]
    pub neck: Entity,
    #[name_path("hips", "spine", "chest", "upper.chest", "neck", "head")]
    pub head: Entity,

    // --- Left Arm ---
    #[name_path("hips", "spine", "chest", "upper.chest", "shoulder.L")]
    pub left_shoulder: Entity,
    #[name_path("hips", "spine", "chest", "upper.chest", "shoulder.L", "upper.Arm.L")]
    pub left_upper_arm: Entity,
    #[name_path("hips", "spine", "chest", "upper.chest", "shoulder.L", "upper.Arm.L", "forearm.L")]
    pub left_lower_arm: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L"
    )]
    pub left_hand: Entity,

    // --- Left Hand Fingers ---
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "thumb.01.L"
    )]
    pub left_thumb_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "thumb.01.L",
        "thumb.02.L"
    )]
    pub left_thumb_2: Entity,

    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_index.01.L"
    )]
    pub left_index_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_index.01.L",
        "f_index.02.L"
    )]
    pub left_index_2: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_index.01.L",
        "f_index.02.L",
        "f_index.03.L"
    )]
    pub left_index_3: Entity,

    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_middle.01.L"
    )]
    pub left_middle_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_middle.01.L",
        "f_middle.02.L"
    )]
    pub left_middle_2: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_middle.01.L",
        "f_middle.02.L",
        "f_middle.03.L"
    )]
    pub left_middle_3: Entity,

    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_ring.01.L"
    )]
    pub left_ring_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_ring.01.L",
        "f_ring.02.L"
    )]
    pub left_ring_2: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_ring.01.L",
        "f_ring.02.L",
        "f_ring.03.L"
    )]
    pub left_ring_3: Entity,

    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_pinky.01.L"
    )]
    pub left_pinky_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_pinky.01.L",
        "f_pinky.02.L"
    )]
    pub left_pinky_2: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.L",
        "upper.Arm.L",
        "forearm.L",
        "hand.L",
        "f_pinky.01.L",
        "f_pinky.02.L",
        "f_pinky.03.L"
    )]
    pub left_pinky_3: Entity,

    // --- Right Arm ---
    #[name_path("hips", "spine", "chest", "upper.chest", "shoulder.R")]
    pub right_shoulder: Entity,
    #[name_path("hips", "spine", "chest", "upper.chest", "shoulder.R", "upper.Arm.R")]
    pub right_upper_arm: Entity,
    #[name_path("hips", "spine", "chest", "upper.chest", "shoulder.R", "upper.Arm.R", "forearm.R")]
    pub right_lower_arm: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R"
    )]
    pub right_hand: Entity,

    // --- Right Hand Fingers ---
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "thumb.01.R"
    )]
    pub right_thumb_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "thumb.01.R",
        "thumb.02.R"
    )]
    pub right_thumb_2: Entity,

    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_index.01.R"
    )]
    pub right_index_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_index.01.R",
        "f_index.02.R"
    )]
    pub right_index_2: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_index.01.R",
        "f_index.02.R",
        "f_index.03.R"
    )]
    pub right_index_3: Entity,

    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_middle.01.R"
    )]
    pub right_middle_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_middle.01.R",
        "f_middle.02.R"
    )]
    pub right_middle_2: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_middle.01.R",
        "f_middle.02.R",
        "f_middle.03.R"
    )]
    pub right_middle_3: Entity,

    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_ring.01.R"
    )]
    pub right_ring_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_ring.01.R",
        "f_ring.02.R"
    )]
    pub right_ring_2: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_ring.01.R",
        "f_ring.02.R",
        "f_ring.03.R"
    )]
    pub right_ring_3: Entity,

    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_pinky.01.R"
    )]
    pub right_pinky_1: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_pinky.01.R",
        "f_pinky.02.R"
    )]
    pub right_pinky_2: Entity,
    #[name_path(
        "hips",
        "spine",
        "chest",
        "upper.chest",
        "shoulder.R",
        "upper.Arm.R",
        "forearm.R",
        "hand.R",
        "f_pinky.01.R",
        "f_pinky.02.R",
        "f_pinky.03.R"
    )]
    pub right_pinky_3: Entity,

    // --- Left Leg ---
    #[name_path("hips", "thigh.L")]
    pub left_thigh: Entity,
    #[name_path("hips", "thigh.L", "shin.L")]
    pub left_shin: Entity,
    #[name_path("hips", "thigh.L", "shin.L", "foot.L")]
    pub left_foot: Entity,

    // --- Right Leg ---
    #[name_path("hips", "thigh.R")]
    pub right_thigh: Entity,
    #[name_path("hips", "thigh.R", "shin.R")]
    pub right_shin: Entity,
    #[name_path("hips", "thigh.R", "shin.R", "foot.R")]
    pub right_foot: Entity,
}

/// Armature mapping for the "Chibi-Boy" rig.
///
/// Conventions:
/// - Field names follow the BaseMaleArmature style (hips, spine, left_shoulder, left_index_1, etc.).
/// - Toe fields are suffixed with `_toe_` to avoid name collisions (e.g., `left_index_toe_1`).
/// - Torso/arms include `CC_Base_Waist` between `CC_Base_Hip` and `CC_Base_Spine01`.
/// - This rig has no `Spine03`; `upper_chest` maps to `Spine02`.
#[derive(Component, EntityCollectorTarget, Reflect, InspectorOptions)]
#[reflect(Component, InspectorOptions)]
#[name_path("Chibi-Boy")]
pub struct BaseMaleArmature {
    // --- Root / Pelvis ---
    #[name_path("RL_BoneRoot", "CC_Base_Hip")]
    pub hips: Entity,
    #[name_path("RL_BoneRoot", "CC_Base_Hip", "CC_Base_Pelvis")]
    pub pelvis: Entity,

    // =====================
    // Torso & Head
    // =====================
    #[name_path("RL_BoneRoot", "CC_Base_Hip", "CC_Base_Waist", "CC_Base_Spine01")]
    pub spine: Entity,
    #[name_path("RL_BoneRoot", "CC_Base_Hip", "CC_Base_Waist", "CC_Base_Spine01", "CC_Base_Spine02")]
    pub chest: Entity,
    #[name_path("RL_BoneRoot", "CC_Base_Hip", "CC_Base_Waist", "CC_Base_Spine01", "CC_Base_Spine02")]
    pub upper_chest: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist",
        "CC_Base_Spine01","CC_Base_Spine02","CC_Base_NeckTwist01"
    )]
    pub neck: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist",
        "CC_Base_Spine01","CC_Base_Spine02","CC_Base_NeckTwist01","CC_Base_NeckTwist02","CC_Base_Head"
    )]
    pub head: Entity,

    // Optional: ribs/breast helpers present in this rig dump
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_RibsTwist"
    )]
    pub left_ribs_twist: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_RibsTwist","CC_Base_L_Breast"
    )]
    pub left_breast: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_RibsTwist"
    )]
    pub right_ribs_twist: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_RibsTwist","CC_Base_R_Breast"
    )]
    pub right_breast: Entity,

    // =====================
    // Left Arm
    // =====================
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02","CC_Base_L_Clavicle"
    )]
    pub left_shoulder: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm"
    )]
    pub left_upper_arm: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_UpperarmTwist01"
    )]
    pub left_upper_arm_twist_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_UpperarmTwist01","CC_Base_L_UpperarmTwist02"
    )]
    pub left_upper_arm_twist_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm"
    )]
    pub left_lower_arm: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_ForearmTwist01"
    )]
    pub left_lower_arm_twist_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_ForearmTwist01","CC_Base_L_ForearmTwist02"
    )]
    pub left_lower_arm_twist_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_ElbowShareBone"
    )]
    pub left_elbow_share_bone: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand"
    )]
    pub left_hand: Entity,

    // --- Left Fingers (hand) ---
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Thumb1"
    )]
    pub left_thumb_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Thumb1","CC_Base_L_Thumb2"
    )]
    pub left_thumb_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Thumb1","CC_Base_L_Thumb2","CC_Base_L_Thumb3"
    )]
    pub left_thumb_3: Entity,

    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Index1"
    )]
    pub left_index_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Index1","CC_Base_L_Index2"
    )]
    pub left_index_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Index1","CC_Base_L_Index2","CC_Base_L_Index3"
    )]
    pub left_index_3: Entity,

    // Middle uses "Mid"
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Mid1"
    )]
    pub left_middle_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Mid1","CC_Base_L_Mid2"
    )]
    pub left_middle_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Mid1","CC_Base_L_Mid2","CC_Base_L_Mid3"
    )]
    pub left_middle_3: Entity,

    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Ring1"
    )]
    pub left_ring_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Ring1","CC_Base_L_Ring2"
    )]
    pub left_ring_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Ring1","CC_Base_L_Ring2","CC_Base_L_Ring3"
    )]
    pub left_ring_3: Entity,

    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Pinky1"
    )]
    pub left_pinky_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Pinky1","CC_Base_L_Pinky2"
    )]
    pub left_pinky_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_L_Clavicle","CC_Base_L_Upperarm","CC_Base_L_Forearm","CC_Base_L_Hand","CC_Base_L_Pinky1","CC_Base_L_Pinky2","CC_Base_L_Pinky3"
    )]
    pub left_pinky_3: Entity,

    // =====================
    // Right Arm
    // =====================
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02","CC_Base_R_Clavicle"
    )]
    pub right_shoulder: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm"
    )]
    pub right_upper_arm: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_UpperarmTwist01"
    )]
    pub right_upper_arm_twist_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_UpperarmTwist01","CC_Base_R_UpperarmTwist02"
    )]
    pub right_upper_arm_twist_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm"
    )]
    pub right_lower_arm: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_ForearmTwist01"
    )]
    pub right_lower_arm_twist_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_ForearmTwist01","CC_Base_R_ForearmTwist02"
    )]
    pub right_lower_arm_twist_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_ElbowShareBone"
    )]
    pub right_elbow_share_bone: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand"
    )]
    pub right_hand: Entity,

    // --- Right Fingers (hand) ---
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Thumb1"
    )]
    pub right_thumb_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Thumb1","CC_Base_R_Thumb2"
    )]
    pub right_thumb_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Thumb1","CC_Base_R_Thumb2","CC_Base_R_Thumb3"
    )]
    pub right_thumb_3: Entity,

    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Index1"
    )]
    pub right_index_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Index1","CC_Base_R_Index2"
    )]
    pub right_index_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Index1","CC_Base_R_Index2","CC_Base_R_Index3"
    )]
    pub right_index_3: Entity,

    // Middle uses "Mid"
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Mid1"
    )]
    pub right_middle_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Mid1","CC_Base_R_Mid2"
    )]
    pub right_middle_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Mid1","CC_Base_R_Mid2","CC_Base_R_Mid3"
    )]
    pub right_middle_3: Entity,

    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Ring1"
    )]
    pub right_ring_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Ring1","CC_Base_R_Ring2"
    )]
    pub right_ring_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Ring1","CC_Base_R_Ring2","CC_Base_R_Ring3"
    )]
    pub right_ring_3: Entity,

    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Pinky1"
    )]
    pub right_pinky_1: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Pinky1","CC_Base_R_Pinky2"
    )]
    pub right_pinky_2: Entity,
    #[name_path(
        "RL_BoneRoot","CC_Base_Hip","CC_Base_Waist","CC_Base_Spine01","CC_Base_Spine02",
        "CC_Base_R_Clavicle","CC_Base_R_Upperarm","CC_Base_R_Forearm","CC_Base_R_Hand","CC_Base_R_Pinky1","CC_Base_R_Pinky2","CC_Base_R_Pinky3"
    )]
    pub right_pinky_3: Entity,

    // =====================
    // Left Leg
    // =====================
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh")]
    pub left_thigh: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_ThighTwist01")]
    pub left_thigh_twist_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_ThighTwist01","CC_Base_L_ThighTwist02")]
    pub left_thigh_twist_2: Entity,

    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf")]
    pub left_shin: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_CalfTwist01")]
    pub left_calf_twist_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_CalfTwist01","CC_Base_L_CalfTwist02")]
    pub left_calf_twist_2: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_KneeShareBone")]
    pub left_knee_share_bone: Entity,

    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_Foot")]
    pub left_foot: Entity,

    // Left toes under ToeBase
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_Foot","CC_Base_L_ToeBase")]
    pub left_toe_base: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_Foot","CC_Base_L_ToeBaseShareBone")]
    pub left_toe_base_share_bone: Entity,

    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_Foot","CC_Base_L_ToeBase","CC_Base_L_PinkyToe1")]
    pub left_pinky_toe_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_Foot","CC_Base_L_ToeBase","CC_Base_L_RingToe1")]
    pub left_ring_toe_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_Foot","CC_Base_L_ToeBase","CC_Base_L_MidToe1")]
    pub left_middle_toe_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_Foot","CC_Base_L_ToeBase","CC_Base_L_IndexToe1")]
    pub left_index_toe_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_L_Thigh","CC_Base_L_Calf","CC_Base_L_Foot","CC_Base_L_ToeBase","CC_Base_L_BigToe1")]
    pub left_big_toe_1: Entity,

    // =====================
    // Right Leg
    // =====================
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh")]
    pub right_thigh: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_ThighTwist01")]
    pub right_thigh_twist_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_ThighTwist01","CC_Base_R_ThighTwist02")]
    pub right_thigh_twist_2: Entity,

    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf")]
    pub right_shin: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_CalfTwist01")]
    pub right_calf_twist_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_CalfTwist01","CC_Base_R_CalfTwist02")]
    pub right_calf_twist_2: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_KneeShareBone")]
    pub right_knee_share_bone: Entity,

    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_Foot")]
    pub right_foot: Entity,

    // Right toes under ToeBase
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_Foot","CC_Base_R_ToeBase")]
    pub right_toe_base: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_Foot","CC_Base_R_ToeBaseShareBone")]
    pub right_toe_base_share_bone: Entity,

    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_Foot","CC_Base_R_ToeBase","CC_Base_R_PinkyToe1")]
    pub right_pinky_toe_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_Foot","CC_Base_R_ToeBase","CC_Base_R_RingToe1")]
    pub right_ring_toe_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_Foot","CC_Base_R_ToeBase","CC_Base_R_MidToe1")]
    pub right_middle_toe_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_Foot","CC_Base_R_ToeBase","CC_Base_R_IndexToe1")]
    pub right_index_toe_1: Entity,
    #[name_path("RL_BoneRoot","CC_Base_Hip","CC_Base_Pelvis","CC_Base_R_Thigh","CC_Base_R_Calf","CC_Base_R_Foot","CC_Base_R_ToeBase","CC_Base_R_BigToe1")]
    pub right_big_toe_1: Entity,
}

