use crate::plugins::avatars::systems;
use bevy::ecs::system::SystemId;
use bevy::platform::collections::HashMap;
use bevy::prelude::*;
use pianorhythm_proto::world_renditions::AvatarCustomizationData;

#[derive(De<PERSON>ult, <PERSON>)]
pub struct VisibleInstrumentsBeforeCustomizationScreen(pub Vec<Entity>);

#[derive(Default, Resource)]
pub struct AvatarCustomizationScreenDisplay(pub bool);

#[derive(Resource)]
pub struct AvatarAnimations {
    pub animations: Vec<AnimationNodeIndex>,
    #[allow(dead_code)]
    pub graph: Handle<AnimationGraph>,
}

#[derive(Reflect, Debug, Component, Clone, Default)]
#[reflect(Component)]
pub struct AvatarTargetPosition(pub Vec3);

#[derive(Co<PERSON>, <PERSON>lone, PartialEq, Eq, Hash)]
pub enum AvatarSystemsID {
    DespawnAllAvatars,
    UpdateAvatarCustomization,
    ShowAvatarCustomizationScreen,
    HideAvatarCustomizationScreen,
}

#[derive(Resource)]
pub struct AvatarSystems(pub HashMap<AvatarSystemsID, SystemId>);

impl FromWorld for AvatarSystems {
    fn from_world(world: &mut World) -> Self {
        let mut system = AvatarSystems(HashMap::new());

        system
            .0
            .insert(AvatarSystemsID::DespawnAllAvatars, world.register_system(systems::despawn_all_avatars));

        system.0.insert(
            AvatarSystemsID::ShowAvatarCustomizationScreen,
            world.register_system(systems::handle_show_avatar_customization_screen),
        );

        system.0.insert(
            AvatarSystemsID::HideAvatarCustomizationScreen,
            world.register_system(systems::handle_hide_avatar_customization_screen),
        );

        system
    }
}

#[derive(Resource)]
pub struct AvatarCustomizationSystems(pub HashMap<AvatarSystemsID, SystemId<In<AvatarCustomizationData>>>);

impl FromWorld for AvatarCustomizationSystems {
    fn from_world(world: &mut World) -> Self {
        let mut system = AvatarCustomizationSystems(HashMap::new());

        system.0.insert(
            AvatarSystemsID::UpdateAvatarCustomization,
            world.register_system(systems::handle_avatar_customization),
        );

        system
    }
}
