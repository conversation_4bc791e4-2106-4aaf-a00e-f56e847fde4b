use bevy::platform::collections::HashMap;
use bevy::prelude::*;
use bevy_asset_loader::prelude::*;
use pianorhythm_proto::world_renditions::WorldAssetPresetCharacterType;

#[derive(AssetCollection, Resource)]
pub(super) struct CharacterAnimationAssets {
    //#[asset(
    //    key="model.characters.base.chibi.animation-idle",
    //    collection(mapped, typed)
    //)]
    //base_chibi_animations: HashMap<AssetLabel, Handle<AnimationClip>>,
}

#[derive(AssetCollection, Resource)]
pub(super) struct CharacterAssets {
    #[asset(key = "model.characters.base-character-face")]
    pub base_character_face: Handle<Gltf>,
    #[asset(key = "model.characters.base-character")]
    pub base_character: Handle<Gltf>,

    #[asset(key = "model.characters.base-chibi-boy-male")]
    pub base_chibi_boy_male: Handle<Gltf>,
    #[asset(key = "model.characters.base-chibi-girl-female")]
    pub base_chibi_girl_female: Handle<Gltf>,
}

// Get Character Asset from Avatar world preset key
impl CharacterAssets {
    pub fn get_character_asset(&self, key: &WorldAssetPresetCharacterType) -> Option<Handle<Gltf>> {
        match key {
            //WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_WITCH => Some(self.preset_witch.clone()),
            //WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_WIZARD => Some(self.preset_wizard.clone()),
            _ => None,
        }
    }
}

#[derive(AssetCollection, Resource)]
pub(super) struct TextureAssets {
    #[asset(key = "texture.active-cursor-arrow")]
    pub cursor_arrow_down: Handle<Image>,
}