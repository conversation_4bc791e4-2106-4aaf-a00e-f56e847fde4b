use std::convert::Into;
use std::time::Duration;

use bevy::asset::Assets;
use bevy::color::palettes;
use bevy::color::palettes::basic;
use bevy::pbr::StandardMaterial;
use bevy::prelude::*;
use bevy_sequential_actions::{ActionsProxy, ModifyActions};
use bevy_tweening::lens::{TransformPositionLens, TransformRotationLens, TransformScaleLens};
use bevy_tweening::{Animator, AssetAnimator, RepeatStrategy, Tween};
use pianorhythm_shared::midi::DRUM_CHANNEL;

use crate::components::tween_lens::StandardMaterialBaseColorLens;
use crate::components::MainInstrument;
use crate::core::events::{AvatarEventsBroadcastAction, ECSSynthEventsAction};
use crate::plugins::avatars::avatar_play_piano::actions;
use crate::plugins::avatars::avatar_play_piano::components::{AvatarIKSet, BoneDefaultTransform, IKTargetParent};
use crate::plugins::avatars::components::{
    AvatarArmatureReference, AvatarModel, AvatarPianoSeatIndex, AvatarPianoSeatTargetIndex, AvatarPianoSeatTooltip, AvatarPianoSeatTooltipReference, AvatarReference, AvatarRelatedPlugin, AvatarSittingOnSeat, AvatarSocketID, BaseCharacterArmature, BaseMaleArmature, ClientAvatarModel
};
use crate::plugins::avatars::resources::AvatarTargetPosition;
use crate::plugins::avatars::{components, events};
use crate::plugins::external::bevy_mod_billboard::BillboardText;
use crate::plugins::ik::IkConstraint;

const TOTAL_BENCH_WIDTH: f32 = 40.0;
const TOTAL_SEATS: f32 = 5.0;
const GAP_BETWEEN_SEATS: f32 = 2.;
const SEAT_WIDTH: f32 = (TOTAL_BENCH_WIDTH / TOTAL_SEATS) + GAP_BETWEEN_SEATS;
const BENCH_START_MIN_X: f32 = -20. - (GAP_BETWEEN_SEATS * 2.);
const BENCH_START_MAX_X: f32 = 20.;
const BASE_ALPHA: f32 = 0.1;
const BASE_COLOR: Srgba = basic::GRAY;
const TARGET_COLOR: Srgba = basic::WHITE;

pub(super) fn plugin_setup_piano(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    query: Query<(Entity, &MainInstrument)>,
) {
    for (entity, instrument) in &query {
        if instrument.0 != crate::components::InstrumentType::PIANO_BENCH {
            continue;
        };
        let mut entities: Vec<Entity> = vec![];

        for index in 0..TOTAL_SEATS as i32 {
            let text = format!("Seat: {}", index);
            let tooltip_entity = commands
                .spawn((
                    Visibility::Hidden,
                    AvatarPianoSeatTooltip,
                    Transform::from_translation(Vec3::default().with_z(7.)).with_scale(Vec3::splat(0.001)),
                    BillboardText::default(),
                    TextLayout::new_with_justify(JustifyText::Center),
                ))
                .with_child((
                    TextSpan::new("Click to take a seat!"),
                    TextColor::from(Color::WHITE),
                    TextFont::default().with_font_size(36.0),
                ))
                .id();

            let seat_mesh_entity = commands
                .spawn((
                    Mesh3d(meshes.add(Extrusion::new(Circle::new(5.), 0.01))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: BASE_COLOR.with_alpha(BASE_ALPHA).into(),
                        alpha_mode: AlphaMode::Blend,
                        ..default()
                    })),
                    Transform::from_rotation(Quat::from_rotation_x(-std::f32::consts::FRAC_PI_2)).with_translation(
                        Vec3::default()
                            .with_x(BENCH_START_MIN_X + (SEAT_WIDTH * index as f32) + (SEAT_WIDTH / 2.))
                            .with_y(27.2)
                            .with_z(-5.),
                    ),
                    Name::new(format!("Avatar Piano Mesh Seat: {}", index)),
                    AvatarPianoSeatIndex(index as u8),
                    AvatarPianoSeatTooltipReference(tooltip_entity.clone()),
                    AvatarRelatedPlugin,
                ))
                .id();

            commands
                .entity(seat_mesh_entity)
                .observe(move |event: Trigger<Pointer<Over>>, mut commands: Commands| {
                    commands.entity(tooltip_entity.clone()).insert(Visibility::Visible);
                    commands.entity(event.target).insert(AssetAnimator::new(Tween::new(
                        EaseFunction::Linear,
                        std::time::Duration::from_millis(250),
                        StandardMaterialBaseColorLens {
                            start: BASE_COLOR.with_alpha(BASE_ALPHA).into(),
                            end: TARGET_COLOR.with_alpha(1.0).into(),
                        },
                    )));
                })
                .observe(move |event: Trigger<Pointer<Out>>, mut commands: Commands| {
                    commands.entity(tooltip_entity.clone()).insert(Visibility::Hidden);
                    commands.entity(event.target).insert(AssetAnimator::new(Tween::new(
                        EaseFunction::Linear,
                        std::time::Duration::from_millis(250),
                        StandardMaterialBaseColorLens {
                            start: TARGET_COLOR.with_alpha(1.0).into(),
                            end: BASE_COLOR.with_alpha(BASE_ALPHA).into(),
                        },
                    )));
                })
                .observe(
                    move |event: Trigger<Pointer<Pressed>>,
                          mut ev: EventWriter<events::OnHitAvatarPianoSeat>,
                          client_avatar: Query<Entity, With<ClientAvatarModel>>| {
                        let Ok(avatar_entity) = client_avatar.single() else {
                            return;
                        };

                        ev.write(events::OnHitAvatarPianoSeat {
                            seat_entity: event.target,
                            avatar_entity: avatar_entity.clone(),
                        });
                    },
                );

            // Seat meshes
            entities.push(seat_mesh_entity);
            commands.entity(seat_mesh_entity).add_children(&[tooltip_entity]);
        }

        commands.entity(entity).add_children(&entities);
    }
}

pub(super) fn receive_on_hit_avatar_piano_seat(
    mut commands: Commands,
    mut events: EventReader<events::OnHitAvatarPianoSeat>,
    mut event_writer: EventWriter<AvatarEventsBroadcastAction>,
    q_bench_seats: Query<(&GlobalTransform, &AvatarPianoSeatIndex, Option<&AvatarPianoSeatTooltipReference>)>,
    q_avatars: Query<(Entity, &components::AvatarSittingOnSeat), With<AvatarModel>>,
    q_is_client: Query<&ClientAvatarModel>,
) {
    for event in events.read() {
        let seat_mesh_entity = event.seat_entity;

        // Animate marker mesh
        commands.entity(seat_mesh_entity.clone()).insert(Animator::new(
            Tween::new(
                EaseFunction::BounceInOut,
                Duration::from_millis(200),
                TransformScaleLens {
                    start: Vec3::splat(1.1),
                    end: Vec3::splat(1.),
                },
            )
            .with_repeat_strategy(RepeatStrategy::MirroredRepeat),
        ));

        let Ok((seat_g_transform, seat_index, tooltip_ref)) = q_bench_seats.get(seat_mesh_entity.clone()) else {
            #[cfg(debug_assertions)]
            log::warn!("Failed to get seat mesh entity: {:?}", seat_mesh_entity);
            continue;
        };

        // Hide the tooltip
        if let Some(tooltip) = tooltip_ref {
            commands.entity(tooltip.0.clone()).insert(Visibility::Hidden);
        }

        // Emit event if client
        let is_client = q_is_client.get(event.avatar_entity).is_ok();
        if is_client {
            #[cfg(debug_assertions)]
            log::info!("Client is going to seat: {}", seat_index.0);
            event_writer.write(AvatarEventsBroadcastAction::SetPianoBench(seat_index.0));
        }

        match q_avatars.get(event.avatar_entity) {
            // If the avatar is already sitting but on a different seat, then move instantly
            Ok((entity, seat_ref)) if seat_ref.0 != seat_index.0 => {
                commands.entity(entity).insert(AvatarPianoSeatTargetIndex(seat_index.0));
                commands
                    .actions(entity.clone())
                    .add(actions::AvatarSitOnPianoBenchAction {
                        seat_index: seat_index.0,
                        seat_position: seat_g_transform.translation(),
                        armature_entity: event.avatar_entity.clone(),
                    });
            }
            // Ignore if already sitting in the same seat
            Ok((_, seat_ref)) if seat_ref.0 == seat_index.0 => {}
            // Set the target position for the avatar
            _ => {
                let seat_position = seat_g_transform.translation();
                let target_position = Vec3::new(seat_position.x as f32, 0.0, seat_position.z as f32);
                commands
                    .entity(event.avatar_entity)
                    .remove::<AvatarSittingOnSeat>()
                    .insert(AvatarTargetPosition(target_position))
                    .insert(AvatarPianoSeatTargetIndex(seat_index.0));

                #[cfg(debug_assertions)]
                log::info!("Setting avatar target position: {}", target_position);
            }
        }

        if is_client {
            commands.insert_resource(seat_index.clone());
        }
    }
}

pub(super) fn receive_target_reached_piano_seat(
    mut commands: Commands,
    mut events: EventReader<events::OnAvatarReachedTarget>,
    q_seat: Query<(&GlobalTransform, &components::AvatarPianoSeatIndex), With<Mesh3d>>,
    q_armature: Query<(&AvatarPianoSeatTargetIndex, &AvatarArmatureReference)>,
) {
    for event in events.read() {
        let avatar_entity = event.0.clone();
        let Ok((target_seat_index, armature_entity)) = q_armature.get(avatar_entity) else {
            #[cfg(debug_assertions)]
            log::warn!("Failed to get armature entity for avatar: {:?}", avatar_entity);
            continue;
        };

        for (seat_transform, seat_index) in q_seat.iter() {
            if seat_index.0 != target_seat_index.0 {
                continue;
            }

            #[cfg(debug_assertions)]
            log::info!("Avatar reached seat: {}", seat_index.0);

            commands
                .actions(event.0.clone())
                .add(actions::AvatarSitOnPianoBenchAction {
                    seat_index: target_seat_index.0,
                    seat_position: seat_transform.translation(),
                    armature_entity: armature_entity.0.clone(),
                });
            break;
        }
    }
}

pub(super) fn set_avatar_ik(
    mut commands: Commands,
    avatar: Query<
        (Entity, &AvatarArmatureReference, &BaseMaleArmature),
        (Or<(Added<BaseMaleArmature>, Added<AvatarArmatureReference>)>, Without<AvatarIKSet>),
    >,
    q_name: Query<&Name>,
    q_transform: Query<&Transform>,
    #[cfg(debug_assertions)] mut meshes: ResMut<Assets<Mesh>>,
    #[cfg(debug_assertions)] mut materials: ResMut<Assets<StandardMaterial>>,
) {
    for (avatar_entity, armature_reference, base_armature) in avatar.iter() {
        let set_references =
            |commands: &mut Commands, avatar_entity: Entity, armature_entity: Entity, target_entity: Entity| {
                commands
                    .entity(target_entity.clone())
                    .insert(AvatarReference(avatar_entity))
                    .insert(AvatarArmatureReference(armature_entity))
                    .insert(BoneDefaultTransform(q_transform.get(target_entity.clone()).cloned().unwrap_or_default()));
            };

        let mut spawn_ik_target =
            |commands: &mut Commands, avatar_entity: Entity, target_entity: Entity, use_pole: bool| {
                let name = q_name
                    .get(target_entity.clone())
                    .cloned()
                    .unwrap_or_default()
                    .to_string();

                #[cfg(debug_assertions)]
                log::info!("Spawning IK target for: {}", name);

                let target = commands
                    .spawn((
                        #[cfg(debug_assertions)]
                        Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                        // TODO
                        //#[cfg(debug_assertions)]
                        //PbrBundle {
                        //    mesh: Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                        //    material: MeshMaterial3d(materials.add(StandardMaterial {
                        //        base_color: palettes::css::RED.into(),
                        //        ..default()
                        //    })),
                        //    visibility: Visibility::Hidden,
                        //    ..default()
                        //},
                        #[cfg(not(debug_assertions))]
                        Transform::default(),
                        Visibility::default(),
                        IKTargetParent(target_entity.clone()),
                        Name::new(format!("IK Target: {}", name)),
                    ))
                    .id();

                let mut children: Vec<Entity> = vec![target];

                let mut constraint = IkConstraint {
                    chain_length: 2,
                    iterations: 20,
                    target: target.clone(),
                    pole_target: None,
                    pole_angle: -std::f32::consts::FRAC_PI_2,
                    enabled: false,
                };

                if use_pole {
                    let pole_target = commands
                        .spawn((
                            #[cfg(debug_assertions)]
                            Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                            #[cfg(debug_assertions)]
                            MeshMaterial3d(materials.add(StandardMaterial {
                                base_color: palettes::css::LIME.into(),
                                ..default()
                            })),
                            #[cfg(debug_assertions)]
                            Visibility::Hidden,
                            #[cfg(not(debug_assertions))]
                            Transform::default(),
                            #[cfg(not(debug_assertions))]
                            Visibility::default(),
                            IKTargetParent(target_entity.clone()),
                            Name::new(format!("IK Pole Target: {}", name)),
                        ))
                        .id();

                    constraint.pole_target = Some(pole_target.clone());
                    children.push(pole_target.clone());
                }

                commands.entity(target_entity.clone()).insert(constraint);

                commands.entity(avatar_entity).add_children(&children);
            };

        #[cfg(debug_assertions)]
        log::info!("Setting up IK for avatar: {:?}", avatar_entity);

        //set_references(&mut commands, avatar_entity.clone(), armature_reference.0.clone(), base_armature.base.clone());
        //set_references(&mut commands, avatar_entity.clone(), armature_reference.0.clone(), base_armature.body.clone());
        set_references(&mut commands, avatar_entity.clone(), armature_reference.0.clone(), base_armature.head.clone());
        set_references(
            &mut commands,
            avatar_entity.clone(),
            armature_reference.0.clone(),
            base_armature.left_shoulder.clone(),
        );
        set_references(
            &mut commands,
            avatar_entity.clone(),
            armature_reference.0.clone(),
            base_armature.left_upper_arm.clone(),
        );
        set_references(
            &mut commands,
            avatar_entity.clone(),
            armature_reference.0.clone(),
            base_armature.left_lower_arm.clone(),
        );
        set_references(
            &mut commands,
            avatar_entity.clone(),
            armature_reference.0.clone(),
            base_armature.left_hand.clone(),
        );
        set_references(
            &mut commands,
            avatar_entity.clone(),
            armature_reference.0.clone(),
            base_armature.right_shoulder.clone(),
        );
        set_references(
            &mut commands,
            avatar_entity.clone(),
            armature_reference.0.clone(),
            base_armature.right_upper_arm.clone(),
        );
        set_references(
            &mut commands,
            avatar_entity.clone(),
            armature_reference.0.clone(),
            base_armature.right_lower_arm.clone(),
        );
        set_references(
            &mut commands,
            avatar_entity.clone(),
            armature_reference.0.clone(),
            base_armature.right_hand.clone(),
        );

        spawn_ik_target(&mut commands, avatar_entity.clone(), base_armature.left_hand, true);
        spawn_ik_target(&mut commands, avatar_entity.clone(), base_armature.right_hand, true);
        commands.entity(avatar_entity).insert(AvatarIKSet);
    }
}

const HAND_DEFAULT_TARGET_Z: f32 = 0.9;
const HAND_DEFAULT_TARGET_Y: f32 = 1.1;

const LEFT_HAND_DEFAULT_TARGET_X: f32 = 0.1;
const LEFT_HAND_DEFAULT_TARGET_Y: f32 = 1.1;
const LEFT_HAND_MIN_TARGET_X: f32 = 1.0;
const LEFT_HAND_MAX_TARGET_X: f32 = LEFT_HAND_DEFAULT_TARGET_X * -1.;
const LEFT_HAND_DEFAULT_ROT: [f32; 3] = [0., 0., 0.];

const RIGHT_HAND_DEFAULT_TARGET_X: f32 = LEFT_HAND_DEFAULT_TARGET_X * -1.;
const RIGHT_HAND_DEFAULT_TARGET_Y: f32 = 1.1;
const RIGHT_HAND_MIN_TARGET_X: f32 = RIGHT_HAND_DEFAULT_TARGET_X * -1.;
const RIGHT_HAND_MAX_TARGET_X: f32 = LEFT_HAND_MIN_TARGET_X * -1.;
const RIGHT_HAND_DEFAULT_ROT: [f32; 3] = [0.2, -0.6, 0.4];

const MIDDLE_NOTE: u8 = 60;
const DEFAULT_EASING: EaseFunction = EaseFunction::QuadraticInOut;
const DEFAULT_DOWN_DURATION: Duration = std::time::Duration::from_millis(200);
const DEFAULT_UP_DURATION: Duration = std::time::Duration::from_millis(100);

struct HandConfig {
    default_target: [f32; 3],
    min_target_x: f32,
    max_target_x: f32,
    default_rot: [f32; 3],
    pole_target_translation: Vec3,
    rotation: Vec3,
    pole_angle: f32,
}

impl HandConfig {
    fn get_target_translation(&self) -> Vec3 {
        Vec3::from_array(self.default_target)
    }
}

const LEFT_HAND: HandConfig = HandConfig {
    default_target: [LEFT_HAND_DEFAULT_TARGET_X, HAND_DEFAULT_TARGET_Y, HAND_DEFAULT_TARGET_Z],
    min_target_x: LEFT_HAND_MIN_TARGET_X,
    max_target_x: LEFT_HAND_MAX_TARGET_X,
    default_rot: LEFT_HAND_DEFAULT_ROT,
    pole_target_translation: Vec3::new(2.0, 2.6, -1.9),
    rotation: Vec3::new(0.1, 0.0, -0.2),
    pole_angle: 3.6,
};

const RIGHT_HAND: HandConfig = HandConfig {
    default_target: [
        RIGHT_HAND_DEFAULT_TARGET_X,
        HAND_DEFAULT_TARGET_Y,
        HAND_DEFAULT_TARGET_Z,
    ],
    min_target_x: RIGHT_HAND_MIN_TARGET_X,
    max_target_x: RIGHT_HAND_MAX_TARGET_X,
    default_rot: RIGHT_HAND_DEFAULT_ROT,
    pole_target_translation: Vec3::new(-2.0, 2.6, -1.9),
    rotation: Vec3::new(0.0, -0.4, 0.3),
    pole_angle: -0.2,
};

/// Sets up the avatar's IK constraints and transforms for playing piano.
pub(super) fn get_ready_to_play_piano(
    mut q_ik: Query<&mut IkConstraint>,
    mut q_transform: Query<&mut Transform>,
    avatar: Query<&BaseMaleArmature, Changed<AvatarSittingOnSeat>>,
) {
    for base_armature in avatar.iter() {
        if let Ok(mut transform) = q_transform.get_mut(base_armature.head.clone()) {
            transform.rotation.x = 0.5;
        }

        #[cfg(debug_assertions)]
        log::info!("Setting up hands for avatar to play the piano: {:?} | {:?}", base_armature.left_hand, base_armature.right_hand);

        setup_hand(&mut q_ik, &mut q_transform, &LEFT_HAND, base_armature.left_hand.clone());
        setup_hand(&mut q_ik, &mut q_transform, &RIGHT_HAND, base_armature.right_hand.clone());
    }
}

pub(super) fn playing_piano(
    mut commands: Commands,
    mut q_ik: Query<&mut IkConstraint>,
    mut event_reader: EventReader<ECSSynthEventsAction>,
    q_transform: Query<&Transform>,
    q_global_transform: Query<&GlobalTransform>,
    q_avatar: Query<(&AvatarSocketID, &BaseMaleArmature), With<AvatarSittingOnSeat>>,
) {
    for event in event_reader.read() {
        for (socket_id, base_armature) in q_avatar.iter() {
            match &event {
                ECSSynthEventsAction::NoteOn(data)
                    if data.channel != DRUM_CHANNEL && data.socket_id == Some(socket_id.0) =>
                {
                    let on_left_side = data.note <= MIDDLE_NOTE;
                    let (hand_entity, hand_config) = if on_left_side {
                        (base_armature.left_hand, LEFT_HAND)
                    } else {
                        (base_armature.right_hand, RIGHT_HAND)
                    };

                    let is_black_note = matches!(data.note % 12, 1 | 3 | 6 | 8 | 10);
                    let target_x =
                        (data.note as f32).remap(21.0, 98.0, hand_config.min_target_x, hand_config.max_target_x);

                    let mut target_y = (data.vel as f32).remap(0.0, 127.0, 0.0, 0.1);
                    target_y = hand_config.default_target[1] - target_y;

                    let target_z = if is_black_note {
                        HAND_DEFAULT_TARGET_Z + 0.1
                    } else {
                        HAND_DEFAULT_TARGET_Z
                    };

                    animate_hand(
                        &mut commands,
                        &mut q_ik,
                        &q_transform,
                        &hand_entity,
                        Vec3::new(target_x, target_y, target_z),
                        &hand_config,
                    );

                    if let (Ok(left_hand_transform), Ok(right_hand_transform)) = (
                        q_global_transform.get(base_armature.left_hand.clone()),
                        q_global_transform.get(base_armature.right_hand.clone()),
                    ) {
                        let center_between_hands =
                            (left_hand_transform.translation() + right_hand_transform.translation()) / 2.0;

                        // Animate head to look at the center point
                        if let Ok(head_transform) = q_global_transform.get(base_armature.head.clone()) {
                            let mut direction = (center_between_hands - head_transform.translation()).normalize();

                            direction.x = 0.45;
                            direction.y = direction.y.remap(-0.37, -0.30, -0.2, 0.2);
                            direction.z = 0.;

                            commands
                                .entity(base_armature.head.clone())
                                .insert(Animator::new(Tween::new(
                                    DEFAULT_EASING,
                                    DEFAULT_DOWN_DURATION,
                                    TransformRotationLens {
                                        start: q_transform
                                            .get(base_armature.head.clone())
                                            .cloned()
                                            .unwrap_or_default()
                                            .rotation,
                                        end: Quat::from_euler(EulerRot::XYZ, direction.x, direction.y, direction.z),
                                    },
                                )));
                        }
                    }
                }
                ECSSynthEventsAction::NoteOff(data)
                    if data.channel != DRUM_CHANNEL && data.socket_id == Some(socket_id.0) =>
                {
                    let on_left_side = data.note <= MIDDLE_NOTE;
                    let (hand_entity, hand_config) = if on_left_side {
                        (base_armature.left_hand, LEFT_HAND)
                    } else {
                        (base_armature.right_hand, RIGHT_HAND)
                    };

                    reset_hand(&mut commands, &mut q_ik, &q_transform, &hand_entity, &hand_config);
                }
                _ => {}
            }
        }
    }
}

/// Sets up the IK constraints and transforms for a hand.
fn setup_hand(
    q_ik: &mut Query<&mut IkConstraint>,
    q_transform: &mut Query<&mut Transform>,
    hand_config: &HandConfig,
    hand_entity: Entity,
) {
    if let Ok(mut hand_ik) = q_ik.get_mut(hand_entity) {
        hand_ik.enabled = true;
        hand_ik.pole_angle = hand_config.pole_angle;

        if let Ok(mut transform) = q_transform.get_mut(hand_entity) {
            #[cfg(debug_assertions)]
            log::info!("Setting hand translation: {:?}", Vec3::from_array(hand_config.default_rot));

            transform.translation = Vec3::from_array(hand_config.default_rot);
            let r = hand_config.rotation;
            transform.rotation = Quat::from_euler(EulerRot::XYZ, r.x, r.y, r.z);
        }

        if let Ok(mut transform) = q_transform.get_mut(hand_ik.target) {
            #[cfg(debug_assertions)]
            log::info!("Setting hand target translation: {:?}", hand_config.get_target_translation());

            transform.translation = hand_config.get_target_translation();
        }

        if let Some(mut transform) = hand_ik.pole_target.and_then(|x| q_transform.get_mut(x).ok()) {
            #[cfg(debug_assertions)]
            log::info!("Setting pole target translation: {:?}", hand_config.pole_target_translation);

            transform.translation = hand_config.pole_target_translation;
        }
    } else {
        #[cfg(debug_assertions)]
        log::warn!("Failed to get IK constraint for hand: {:?}", hand_entity);
    }
}

fn animate_hand(
    commands: &mut Commands,
    q_ik: &mut Query<&mut IkConstraint>,
    q_transform: &Query<&Transform>,
    hand_entity: &Entity,
    target: Vec3,
    hand_config: &HandConfig,
) {
    let Ok(hand_ik) = q_ik.get_mut(hand_entity.clone()) else {
        #[cfg(debug_assertions)]
        log::warn!("Failed to get IK constraint for hand: {:?}", hand_entity);
        return;
    };

    let Ok(ik_transform) = q_transform.get(hand_ik.target) else {
        #[cfg(debug_assertions)]
        log::warn!("Failed to get transform for hand IK target: {:?}", hand_ik.target);
        return;
    };

    commands.entity(hand_entity.clone()).insert(Animator::new(Tween::new(
        DEFAULT_EASING,
        DEFAULT_DOWN_DURATION,
        TransformRotationLens {
            start: Quat::from_euler(
                EulerRot::XYZ,
                ik_transform.rotation.x,
                ik_transform.rotation.y,
                ik_transform.rotation.z + 0.1,
            ),
            end: Quat::from_euler(
                EulerRot::XYZ,
                hand_config.default_rot[0],
                hand_config.default_rot[1],
                hand_config.default_rot[2] - 0.1,
            ),
        },
    )));

    commands.entity(hand_ik.target).insert(Animator::new(Tween::new(
        DEFAULT_EASING,
        DEFAULT_DOWN_DURATION,
        TransformPositionLens {
            start: ik_transform.translation,
            end: target,
        },
    )));
}

fn reset_hand(
    commands: &mut Commands,
    q_ik: &mut Query<&mut IkConstraint>,
    q_transform: &Query<&Transform>,
    hand_entity: &Entity,
    hand_config: &HandConfig,
) {
    let Ok(hand_ik) = q_ik.get_mut(hand_entity.clone()) else {
        return;
    };

    if let Ok(transform) = q_transform.get(hand_entity.clone()) {
        commands.entity(*hand_entity).insert(Animator::new(Tween::new(
            DEFAULT_EASING,
            DEFAULT_UP_DURATION,
            TransformRotationLens {
                start: transform.rotation,
                end: Quat::from_euler(
                    EulerRot::XYZ,
                    hand_config.default_rot[0],
                    hand_config.default_rot[1],
                    hand_config.default_rot[2],
                ),
            },
        )));
    }

    if let Ok(transform) = q_transform.get(hand_ik.target) {
        commands.entity(hand_ik.target).insert(Animator::new(Tween::new(
            DEFAULT_EASING,
            DEFAULT_UP_DURATION,
            TransformPositionLens {
                start: transform.translation,
                end: transform.translation.with_y(hand_config.default_target[1]),
            },
        )));
    }
}
