use bevy::prelude::*;
use bevy_sequential_actions::*;

use crate::plugins::avatars::components;
use crate::plugins::avatars::components::AvatarPianoSeatTargetIndex;

/// Action representing an avatar sitting on a piano bench.
///
/// Fields:
/// - `seat_index`: The index of the seat to sit on.
/// - `seat_position`: The position of the seat in world coordinates.
/// - `armature_entity`: The entity representing the avatar's armature.
pub struct AvatarSitOnPianoBenchAction {
    pub seat_index: u8,
    pub seat_position: Vec3,
    pub armature_entity: Entity,
}

impl Action for AvatarSitOnPianoBenchAction {
    fn is_finished(&self, agent: Entity, world: &World) -> bool {
        world.get::<components::AvatarSittingOnSeat>(agent).is_some()
    }

    fn on_start(&mut self, agent: Entity, world: &mut World) -> bool {
        let mut commands = world.commands();

        if let Some(mut transform) = world.get_mut::<Transform>(agent) {
            let mut target_translation = self.seat_position.clone();
            target_translation.y -= 0.14;
            target_translation.z -= 0.2;

            // Set position
            *transform = transform
                .with_translation(target_translation)
                .with_rotation(Quat::from_rotation_y(std::f32::consts::PI));
        } else {
            #[cfg(debug_assertions)]
            log::warn!("Failed to get transform for avatar: {:?}", agent);
            return true;
        }

        // Set seat index
        world
            .entity_mut(agent.clone())
            .remove::<AvatarPianoSeatTargetIndex>()
            .insert(components::AvatarSittingOnSeat(self.seat_index));

        // Set animation
        if let Some(armature_entity) = world.get::<components::AvatarArmatureReference>(agent) {
            let mut armature_entity_commands = world.entity_mut(armature_entity.0.clone());
            armature_entity_commands
                .insert(components::AvatarTargetAnimation::new(components::AvatarAnimationID::SIT).with_repeat(false));
            #[cfg(debug_assertions)]
            log::info!("Avatar sitting on seat: {} | Setting animation to SIT", self.seat_index);
        } else {
            #[cfg(debug_assertions)]
            log::warn!("Failed to get armature entity for avatar: {:?}", agent);
        }

        false
    }

    fn on_stop(&mut self, _agent: Option<Entity>, _world: &mut World, _reason: StopReason) {}
}
