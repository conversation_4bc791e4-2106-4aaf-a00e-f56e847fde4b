use bevy::asset::Handle;
use bevy::prelude::*;

use pianorhythm_shared::midi::DRUM_CHANNEL;

use crate::components::{AnimationDown, AnimationState, DampenPedal, MidiID, OnActiveNote, PianoKey, SustainPedal};
use crate::core::events::ECSSynthEventsAction;
use crate::resources::PrimaryAudioChannel;

pub fn receiving_sustain_pedal_events(
    mut commands: Commands,
    mut event_reader: EventReader<ECSSynthEventsAction>,
    mut query: Query<(Entity, &mut AnimationState), (With<Mesh3d>, With<SustainPedal>)>,
    primary_audio_channel: Res<PrimaryAudioChannel>,
) {
    for my_event in event_reader.read() {
        match &my_event {
            ECSSynthEventsAction::SustainPedal(data)
            if data.channel != DRUM_CHANNEL && data.channel == primary_audio_channel.0 => {
                if let Ok((entity, mut state)) = query.single_mut() {
                    state.is_down = data.vel >= 64;
                    commands.entity(entity).insert(OnActiveNote(data.clone()));
                }
            }
            _ => {}
        }
    }
}

pub fn receiving_soft_pedal_events(
    mut commands: Commands,
    mut event_reader: EventReader<ECSSynthEventsAction>,
    mut query: Query<(Entity, &mut AnimationState), (With<Mesh3d>, With<DampenPedal>)>,
    primary_audio_channel: Res<PrimaryAudioChannel>,
) {
    for my_event in event_reader.read() {
        match &my_event {
            ECSSynthEventsAction::SoftPedal(data)
            if data.channel != DRUM_CHANNEL && data.channel == primary_audio_channel.0 => {
                if let Ok((entity, mut state)) = query.single_mut() {
                    state.is_down = data.vel >= 64;
                    commands.entity(entity).insert(OnActiveNote(data.clone()));
                }
            }
            _ => {}
        }
    }
}

pub fn receiving_events(
    mut commands: Commands,
    mut event_reader: EventReader<ECSSynthEventsAction>,
    mut query: Query<(Entity, &MidiID, &mut AnimationState), (With<Mesh3d>, With<PianoKey>)>,
) {
    for my_event in event_reader.read() {
        match &my_event {
            ECSSynthEventsAction::NoteOn(data) if data.channel != DRUM_CHANNEL => {
                for (entity, _, mut state) in query.iter_mut().filter(|(_, midi, ..)| midi.0 == data.note) {
                    commands.entity(entity)
                        .remove::<AnimationDown>()
                        .insert(OnActiveNote(data.clone()));
                    state.is_down = true;
                }
            }
            ECSSynthEventsAction::NoteOff(data) if data.channel != DRUM_CHANNEL => {
                for (entity, .., mut state) in query.iter_mut().filter(|(_, midi, ..)| midi.0 == data.note) {
                    commands.entity(entity).insert(OnActiveNote(data.clone()));
                    state.is_down = false;
                }
            }
            ECSSynthEventsAction::AllNoteOff(channel) if *channel != DRUM_CHANNEL => {
                for (entity, ..) in query.iter() {
                    commands.entity(entity).insert(crate::components::AllSoundOffTriggered);
                }
            }
            _ => {}
        }
    }
}