use bevy::ecs::system::SystemId;
use bevy::input::common_conditions::input_just_released;
use bevy::platform::collections::HashMap;
use bevy::prelude::*;
use bevy::window::PrimaryWindow;
use bevy_asset_loader::loading_state::{LoadingState, LoadingStateAppExt};
use bevy_asset_loader::prelude::*;

use pianorhythm_proto::midi_renditions::MidiNoteSource;
use pianorhythm_proto::pianorhythm_app_renditions::AppPianoKeyType;

use crate::components::{
    AnimationState, BaseMeshColor, DampenPedal, DefaultMeshColor, InstrumentType, MainInstrument, MidiID, PianoKey, PianoKeyType, PianoMeshType, SustainPedal
};
use crate::core::events::{SynthEventsBroadcastAction, UserNoteData};
use crate::core::AppState;
use crate::plugins::piano::resources::PianoModelAssets;
use crate::resources::{DisplayKeysInputMapping, KeysInputMapping, SynthAudioChannelsActiveState};
use crate::types::StageState;
use crate::utils;
use crate::utils::note_to_midi;

pub mod events;
mod queries;
pub mod resources;
mod systems;

#[derive(Default, Resource)]
struct PianoMeshLoaded;

#[derive(Default, Resource)]
struct PianoMeshSetup;

#[derive(Default, Resource)]
struct PianoBenchMeshSetup;

#[derive(Resource)]
struct PianoModelAsset(Handle<Scene>);

#[derive(Resource)]
struct PianoModelLowPolyAsset(Handle<Scene>);

#[derive(Resource)]
struct PianoBenchAsset(Handle<Scene>);

#[derive(Resource)]
struct PianoBenchLowPolyAsset(Handle<Scene>);

#[derive(Component)]
struct ModelPath(pub String);

#[derive(Debug, Clone, Copy, Default, Eq, PartialEq, Hash, States, Reflect)]
enum PluginState {
    #[default]
    AssetLoading,
    Ready,
}

pub const PIANO_MODEL_PATH: &str = "/models/White_BabyGrand_Aug_2024.glb";
pub const PIANO_LOW_POLY_MODEL_PATH: &str = "/models/White_BabyGrand_Aug_2024_Low_Poly.glb";

const BENCH_MODEL_PATH: &str = "/models/White_BabyGrand_Bench_Aug_2024.glb";
const BENCH_LOW_POLY_MODEL_PATH: &str = "/models/White_BabyGrand_Bench_Aug_2024_Low_Poly.glb";

#[derive(Default)]
pub struct PianoModelPlugin;

#[derive(SystemSet, Debug, Clone, PartialEq, Eq, Hash)]
struct MeshReactiveSystem;

impl Plugin for PianoModelPlugin {
    fn build(&self, app: &mut App) {
        app.init_state::<PluginState>()
            .add_loading_state(
                LoadingState::new(PluginState::AssetLoading)
                    .continue_to_state(PluginState::Ready)
                    .with_dynamic_assets_file::<bevy_asset_loader::prelude::StandardDynamicAssetCollection>(utils::get_model_assets_file_path())
                    .load_collection::<PianoModelAssets>(),
            )
            .insert_resource(resources::LastMouseNoteOn::default());

        app.add_systems(OnEnter(PluginState::Ready), (load_piano_gltf, load_bench_gltf))
            .add_systems(OnEnter(PluginState::Ready), |mut assets_loaded: ResMut<crate::resources::AppAssetsLoaded>| {
                assets_loaded.piano_models = true;
            })
            .add_systems(OnEnter(StageState::Loaded), systems::on_stage_loaded)
            .add_systems(
                OnTransition {
                    exited: StageState::Loaded,
                    entered: StageState::Loaded,
                },
                systems::on_stage_loaded,
            )
            .add_systems(Update, setup_piano_after_load.run_if(resource_exists::<PianoMeshSetup>))
            .add_systems(Update, setup_bench_after_load.run_if(resource_exists::<PianoBenchMeshSetup>))
            .add_systems(
                PostUpdate,
                (systems::remove_animation_tween_on_complete, systems::mesh_on_all_notes_off).distributive_run_if(in_state(AppState::InGame)),
            )
            .add_systems(
                PreUpdate,
                (events::receiving_events, events::receiving_sustain_pedal_events, events::receiving_soft_pedal_events)
                    .distributive_run_if(in_state(AppState::InGame)),
            )
            .add_systems(
                Update,
                (
                    (
                        systems::mesh_animation_down_timer,
                        systems::on_add_mesh_disabled,
                        systems::on_remove_mesh_disabled,
                        systems::on_key_input_mapping_visualize.run_if(resource_changed::<DisplayKeysInputMapping>),
                        systems::on_key_input_mapping_visualize.run_if(resource_changed::<KeysInputMapping>),
                        systems::on_synth_audio_channel_active_change.run_if(resource_changed::<SynthAudioChannelsActiveState>),
                    )
                        .in_set(MeshReactiveSystem),
                    systems::on_base_mesh_color_changed.after(MeshReactiveSystem),
                    systems::on_mouse_up.run_if(input_just_released(MouseButton::Left)),
                )
                    .run_if(in_state(AppState::InGame)),
            )
            .add_systems(
                Update,
                (systems::check_animation_state_is_down, systems::check_animation_state_is_up)
                    .chain()
                    .run_if(in_state(AppState::InGame)),
            );

        app.register_type::<MidiID>();

        app.init_resource::<PianoPluginSystems>().init_resource::<PianoPluginSystems2>();

        log::info!("Built {:?}", self.name());
    }

    fn cleanup(&self, app: &mut App) {
        app.world_mut().remove_resource::<PianoMeshLoaded>();
    }

    fn finish(&self, _app: &mut App) {
        log::info!("{:?} finished.", &self.name());
    }
}

#[derive(Copy, Clone, PartialEq, Eq, Hash)]
pub enum PianoPluginSystemID {
    TriggerKeyDown,
    TriggerKeyUp,
}

#[derive(Resource)]
pub struct PianoPluginSystems(pub HashMap<PianoPluginSystemID, SystemId<In<Entity>, ()>>);

impl FromWorld for PianoPluginSystems {
    fn from_world(world: &mut World) -> Self {
        let mut system = PianoPluginSystems(HashMap::new());

        system
            .0
            .insert(PianoPluginSystemID::TriggerKeyDown, world.register_system(systems::set_animation_down_component));

        system
            .0
            .insert(PianoPluginSystemID::TriggerKeyUp, world.register_system(systems::set_animation_up_component));

        system
    }
}

#[derive(Copy, Clone, PartialEq, Eq, Hash)]
pub enum PianoPluginSystemID2 {
    OnChannelsModeChange,
    OnSynthChannelUpdate,
}

#[derive(Resource)]
pub struct PianoPluginSystems2(pub HashMap<PianoPluginSystemID2, SystemId>);

impl FromWorld for PianoPluginSystems2 {
    fn from_world(world: &mut World) -> Self {
        let mut system = PianoPluginSystems2(HashMap::new());

        system
            .0
            .insert(PianoPluginSystemID2::OnChannelsModeChange, world.register_system(systems::on_active_channels_mode_change));

        system
            .0
            .insert(PianoPluginSystemID2::OnSynthChannelUpdate, world.register_system(systems::on_synth_audio_channel_active_change));

        system
    }
}

fn load_bench_gltf(mut commands: Commands, assets: Res<PianoModelAssets>, assets_gltf: Res<Assets<Gltf>>) {
    let Some(high_poly_gltf) = assets_gltf.get(&assets.high_poly_piano_bench_model) else {
        return;
    };
    let Some(low_poly_gltf) = assets_gltf.get(&assets.low_poly_piano_bench_model) else {
        return;
    };

    let handle = high_poly_gltf.scenes[0].clone();
    let low_poly_handle = low_poly_gltf.scenes[0].clone();

    commands.insert_resource(PianoBenchAsset(handle.clone()));
    commands.insert_resource(PianoBenchLowPolyAsset(low_poly_handle.clone()));
    commands.init_resource::<PianoBenchMeshSetup>();

    commands.spawn((
        Name::new("Piano Bench"),
        ModelPath(BENCH_MODEL_PATH.to_string()),
        MainInstrument(InstrumentType::PIANO_BENCH),
        SceneRoot(handle),
        Transform::from_xyz(0.0, 0.0, 1.2).with_scale(Vec3::new(0.025, 0.025, 0.025)),
    ));

    commands.spawn((
        Name::new("Piano Bench (Low Poly)"),
        ModelPath(BENCH_LOW_POLY_MODEL_PATH.to_string()),
        MainInstrument(InstrumentType::PIANO_BENCH),
        crate::components::LowPolyModel,
        SceneRoot(low_poly_handle),
        Transform::from_xyz(0.0, 0.0, 1.2).with_scale(Vec3::new(0.025, 0.025, 0.025)),
    ));
}

fn load_piano_gltf(mut commands: Commands, assets: Res<PianoModelAssets>, assets_gltf: Res<Assets<Gltf>>) {
    let Some(high_poly_gltf) = assets_gltf.get(&assets.high_poly_piano_model) else {
        return;
    };

    let Some(low_poly_gltf) = assets_gltf.get(&assets.low_poly_piano_model) else {
        return;
    };

    let handle = high_poly_gltf.scenes[0].clone();
    let low_poly_handle = low_poly_gltf.scenes[0].clone();

    commands.init_resource::<PianoMeshSetup>();
    commands.insert_resource(PianoModelAsset(handle.clone()));
    commands.insert_resource(PianoModelLowPolyAsset(low_poly_handle.clone()));

    commands.spawn((
        Name::new("Piano"),
        ModelPath(PIANO_MODEL_PATH.to_string()),
        MainInstrument(InstrumentType::PIANO),
        SceneRoot(handle),
        Transform::from_xyz(0.0, 0.0, 0.0),
    ));

    commands.spawn((
        Name::new("Piano (LowPoly)"),
        ModelPath(PIANO_LOW_POLY_MODEL_PATH.to_string()),
        MainInstrument(InstrumentType::PIANO),
        crate::components::LowPolyModel,
        SceneRoot(low_poly_handle),
        Transform::from_xyz(0.0, 0.0, 0.0),
    ));
}

fn setup_bench_after_load(
    mut commands: Commands, mut setup: Local<bool>, mut materials: ResMut<Assets<StandardMaterial>>, scene_query: Query<(Entity, &ModelPath)>,
    mesh_query: Query<(&Name, &MeshMaterial3d<StandardMaterial>), With<Mesh3d>>, children: Query<&Children>,
) {
    if !*setup {
        for (entity, _) in scene_query
            .iter()
            .filter(|(_, x)| x.0 == BENCH_MODEL_PATH || x.0 == BENCH_LOW_POLY_MODEL_PATH)
        {
            for entity in children.iter_descendants(entity) {
                if let Ok((_, mat_handle)) = mesh_query.get(entity) {
                    if let Ok(mut cmd) = commands.get_entity(entity) {
                        *setup = true;

                        if let Some(material) = materials.get_mut(mat_handle) {
                            material.clearcoat = 1.;
                            material.base_color = Color::LinearRgba(LinearRgba::new(0.007, 0.007, 0.007, 1.)).into();
                            cmd.insert(BaseMeshColor(material.base_color.clone()));
                        }
                    }
                }
            }
        }

        if *setup {
            commands.remove_resource::<PianoBenchMeshSetup>();
        }
    }
}

fn setup_piano_after_load(
    mut commands: Commands, mut setup: Local<bool>, mut materials: ResMut<Assets<StandardMaterial>>, scene_query: Query<(Entity, &ModelPath)>,
    mesh_query: Query<(Entity, &Name, &MeshMaterial3d<StandardMaterial>), With<Mesh3d>>, children: Query<&Children>,
    systems2: Res<PianoPluginSystems2>,
) {
    if !*setup {
        let mut high_poly_processed = false;
        let mut low_poly_processed = false;

        for (entity, model_path) in scene_query
            .iter()
            .filter(|(_, x)| x.0 == PIANO_MODEL_PATH || x.0 == PIANO_LOW_POLY_MODEL_PATH)
        {
            for entity in children.iter_descendants(entity) {
                let Ok((mesh_entity, name, mat_handle)) = mesh_query.get(entity) else {
                    continue;
                };

                let Ok(mut cmd) = commands.get_entity(entity) else {
                    continue;
                };

                let entity_name = name.to_lowercase();
                let mut is_key = false;

                let mut key_midi_id: Option<u8> = None;

                if entity_name.contains("body") {
                    if let Some(material) = materials.get_mut(mat_handle) {
                        material.clearcoat = 1.;
                        material.base_color = Color::LinearRgba(LinearRgba::new(0.007, 0.007, 0.007, 1.)).into();
                        cmd.insert(BaseMeshColor(material.base_color.clone()));
                    }
                }

                if entity_name.contains("whitekey") {
                    is_key = true;
                    cmd.insert(PianoKeyType(AppPianoKeyType::White));

                    let note_name = entity_name.replace("whitekey-", "");
                    note_to_midi(&note_name).inspect(|midi_id| {
                        key_midi_id = Some(midi_id.clone());
                        cmd.insert(MidiID(midi_id.clone()));
                    });
                }

                if entity_name.contains("blackkey") {
                    is_key = true;
                    cmd.insert(PianoKeyType(AppPianoKeyType::Black));

                    let note_name = entity_name.replace("blackkey-", "");
                    note_to_midi(&note_name).inspect(|midi_id| {
                        key_midi_id = Some(midi_id.clone());
                        cmd.insert(MidiID(midi_id.clone()));
                    });
                }

                if entity_name.contains("sustain_pedal") {
                    cmd.insert(SustainPedal);
                }

                if entity_name.contains("dampen_pedal") {
                    cmd.insert(DampenPedal);
                }

                if entity_name.contains("_pedal") {
                    cmd.insert(PianoMeshType::default()).insert(AnimationState::default());

                    if let Some(material) = materials.get(mat_handle) {
                        cmd.insert(BaseMeshColor(material.base_color.clone()));
                    }
                }

                if is_key {
                    if model_path.0 == PIANO_MODEL_PATH {
                        high_poly_processed = true;
                    }

                    if model_path.0 == PIANO_LOW_POLY_MODEL_PATH {
                        low_poly_processed = true;
                    }

                    if let Some(material) = materials.get_mut(mat_handle) {
                        material.clearcoat = 5.;
                        material.perceptual_roughness = 1.;
                        material.metallic = 1.;
                        cmd.insert(BaseMeshColor(material.base_color.clone()));
                        cmd.insert(DefaultMeshColor(material.base_color.clone()));
                    }

                    cmd.insert(PianoKey).insert(PianoMeshType::default()).insert(AnimationState::default());

                    commands
                        .entity(mesh_entity)
                        .observe(move |event: Trigger<Pointer<Pressed>>, mut ev: EventWriter<SynthEventsBroadcastAction>| {
                            if event.button != PointerButton::Primary {
                                return;
                            }

                            ev.write(SynthEventsBroadcastAction::NoteOn(UserNoteData {
                                socket_id: None,
                                channel: 0,
                                note: key_midi_id.unwrap_or_default(),
                                vel: 100,
                                source: MidiNoteSource::MOUSE,
                                ..default()
                            }));
                        })
                        .observe(move |event: Trigger<Pointer<Released>>, mut ev: EventWriter<SynthEventsBroadcastAction>| {
                            if event.button != PointerButton::Primary {
                                return;
                            }

                            ev.write(SynthEventsBroadcastAction::NoteOff(UserNoteData {
                                socket_id: None,
                                channel: 0,
                                note: key_midi_id.unwrap_or_default(),
                                source: MidiNoteSource::MOUSE,
                                ..default()
                            }));
                        });
                }
            }
        }

        if high_poly_processed && low_poly_processed {
            *setup = true;
        }

        if *setup {
            commands.init_resource::<PianoMeshLoaded>();
            commands.remove_resource::<PianoMeshSetup>();
            commands.run_system(systems2.0[&PianoPluginSystemID2::OnSynthChannelUpdate]);
            commands.run_system(systems2.0[&PianoPluginSystemID2::OnChannelsModeChange]);
        }
    }
}

fn on_down(click: Trigger<Pointer<Pressed>>) {
    log::info!("{} is down!", click.target);
}

fn on_up(click: Trigger<Pointer<Released>>) {
    log::info!("{} is up!", click.target);
}


//-----------------------

/// Here we will store the position of the mouse cursor on the 3D ground plane.
#[derive(Resource, Default)]
struct MyGroundCoords {
    // Global (world-space) coordinates
    global: Vec3,
    // Local (relative to the ground plane) coordinates
    local: Vec2,
}

/// Used to help identify our main camera
#[derive(Component)]
struct MyGameCamera;

/// Used to help identify our ground plane
#[derive(Component)]
struct MyGroundPlane;

fn cursor_to_ground_plane(
    mut mycoords: ResMut<MyGroundCoords>,
    // query to get the window (so we can read the current cursor position)
    // (we will only work with the primary window)
    q_window: Query<&Window, With<PrimaryWindow>>,
    // query to get camera transform
    q_camera: Query<(&Camera, &GlobalTransform), With<MyGameCamera>>,
    // query to get ground plane's transform
    q_plane: Query<&GlobalTransform, With<MyGroundPlane>>,
) {
    // get the camera info and transform
    // assuming there is exactly one main camera entity, so Query::single() is OK
    let Ok((camera, camera_transform)) = q_camera.single() else {
        return;
    };

    // Ditto for the ground plane's transform
    let Ok(ground_transform) = q_plane.single() else {
        return;
    };

    // There is only one primary window, so we can similarly get it from the query:
    let Ok(window) = q_window.single() else {
        return;
    };

    // check if the cursor is inside the window and get its position
    let Some(cursor_position) = window.cursor_position() else {
        // if the cursor is not inside the window, we can't do anything
        return;
    };

    // Mathematically, we can represent the ground as an infinite flat plane.
    // To do that, we need a point (to position the plane) and a normal vector
    // (the "up" direction, perpendicular to the ground plane).

    // We can get the correct values from the ground entity's GlobalTransform
    let plane_origin = ground_transform.translation();
    let plane = Plane3d::default(); //new(ground_transform.up(), Vec2::splat(0.5));

    // Ask Bevy to give us a ray pointing from the viewport (screen) into the world
    let Ok(ray) = camera.viewport_to_world(camera_transform, cursor_position) else {
        // if it was impossible to compute for whatever reason; we can't do anything
        return;
    };

    // do a ray-plane intersection test, giving us the distance to the ground
    let Some(distance) = ray.intersect_plane(plane_origin, InfinitePlane3d::default()) else {
        // If the ray does not intersect the ground
        // (the camera is not looking towards the ground), we can't do anything
        return;
    };

    // use the distance to compute the actual point on the ground in world-space
    let global_cursor = ray.get_point(distance);

    mycoords.global = global_cursor;
    log::info!("Global cursor coords: {}/{}/{}",
              global_cursor.x, global_cursor.y, global_cursor.z
    );

    // to compute the local coordinates, we need the inverse of the plane's transform
    let inverse_transform_matrix = ground_transform.compute_matrix().inverse();
    let local_cursor = inverse_transform_matrix.transform_point3(global_cursor);

    // we can discard the Y coordinate, because it should always be zero
    // (our point is supposed to be on the plane)
    mycoords.local = local_cursor.xz();
    log::info!("Local cursor coords: {}/{}", local_cursor.x, local_cursor.z);
}