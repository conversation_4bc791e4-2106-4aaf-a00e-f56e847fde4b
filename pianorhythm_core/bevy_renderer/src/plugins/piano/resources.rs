use bevy::prelude::*;
use bevy_asset_loader::prelude::*;

#[derive(<PERSON><PERSON><PERSON>, Debug, Resource)]
pub struct LastMouseNoteOn(pub Option<u8>);

#[derive(Default, Debug, Component)]
pub struct KeyInputMappingBillboard;

#[derive(AssetCollection, Resource)]
pub struct PianoModelAssets {
    #[asset(key = "model.babygrand-piano-high-poly")]
    pub high_poly_piano_model: Handle<Gltf>,

    #[asset(key = "model.babygrand-piano-low-poly")]
    pub low_poly_piano_model: Handle<Gltf>,

    #[asset(key = "model.babygrand-piano-bench-high-poly")]
    pub high_poly_piano_bench_model: Handle<Gltf>,

    #[asset(key = "model.babygrand-piano-bench-low-poly")]
    pub low_poly_piano_bench_model: Handle<Gltf>,
}