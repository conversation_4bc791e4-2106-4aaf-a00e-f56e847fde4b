use crate::components::{BaseMeshColor, DefaultMeshColor, MeshDisabled, MidiID, OnActiveNote, PianoKey, PianoKeyType, SplitModeChannel};
use bevy::ecs::query::QueryData;
use bevy::prelude::*;

#[derive(QueryData)]
pub struct PianoKeyInputMappingQuery {
    pub entity: Entity,
    pub midi_id: &'static MidiID,
    pub key_type: &'static PianoKeyType,
}

#[derive(QueryData)]
pub struct PianoKeyMaterialQuery {
    pub entity: Entity,
    pub key: &'static PianoKey,
    pub midi_id: &'static MidiID,
    pub material_handle: &'static MeshMaterial3d<StandardMaterial>,
    pub base_mesh_color: &'static BaseMeshColor,
    pub default_mesh_color: &'static DefaultMeshColor,
    pub disabled: Option<&'static MeshDisabled>,
}

#[derive(QueryData)]
pub struct PianoKeyMaterialSplitModeQuery {
    pub key: PianoKeyMaterialQuery,
    pub split_mode_channel: Option<&'static SplitModeChannel>,
}

#[derive(QueryData)]
pub struct PianoKeyTriggerDownQuery {
    pub material_handle: &'static MeshMaterial3d<StandardMaterial>,
    pub active_info: &'static OnActiveNote,
}

#[derive(QueryData)]
pub struct PianoKeyTriggerUpQuery {
    pub material_handle: &'static MeshMaterial3d<StandardMaterial>,
    pub active_info: &'static OnActiveNote,
    pub transform: &'static Transform,
    pub base_mesh_color: &'static BaseMeshColor,
}
