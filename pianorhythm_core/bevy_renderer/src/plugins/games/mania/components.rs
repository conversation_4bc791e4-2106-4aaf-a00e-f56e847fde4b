use crate::plugins::games::mania::constants::{
    JUDGEMENT_LINE_HEIGHT, JUDGEMENT_TOO_EARLY_THRESHOLD, THRESHOLD_EARLY_LATE, THRESHOLD_GOOD, THRESHOLD_GREAT, THRESHOLD_PERFECT
};
use crate::plugins::games::mania::resources::ManiaChartNote;
use bevy::prelude::*;

#[derive(Default, Component)]
pub struct ManiaComponent;

#[derive(Component, Copy, Clone, Debug, Reflect)]
#[require(ManiaComponent)]
pub enum PianoKeyType {
    WHITE,
    BLACK,
}

impl Default for PianoKeyType {
    fn default() -> Self {
        PianoKeyType::WHITE
    }
}

#[derive(Component, Default, Debug, PartialEq, Clone, Copy, Reflect)]
#[require(ManiaComponent, Visibility)]
pub struct PianoKeyboard;

#[derive(Compo<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Debug, Reflect, Default)]
#[require(ManiaComponent)]
pub struct PianoKeyboardSize(pub Vec2);

#[derive(Component, Default, Copy, Clone, Debug)]
#[require(ManiaComponent)]
pub struct PianoKey;

#[derive(Component, Copy, Clone, Debug, Reflect, Default)]
#[require(ManiaComponent)]
pub struct MidiNoteId(pub u8);

#[derive(Component, Clone, Debug, Reflect, Default)]
#[require(ManiaComponent)]
pub struct MidiNoteName(pub String);

#[derive(Component, Copy, Clone, Debug, Reflect, Default)]
#[require(ManiaComponent)]
pub struct KeyRootPosition(pub Vec2);

#[derive(Component, Copy, Clone, Debug, Reflect, Default)]
#[require(ManiaComponent)]
pub struct KeyIndexPosition(pub usize);

#[derive(Component, Copy, Clone, Debug, Reflect, Default)]
#[require(ManiaComponent)]
pub struct PianoKeySize(pub Vec2);

#[derive(Component, Copy, Clone, Debug, Reflect, Default)]
pub struct PianoNoteLane(pub u8);

#[derive(Component, Copy, Clone, Debug, Reflect, Default)]
#[require(ManiaComponent)]
pub struct PianoNoteLaneColor(pub Color);

#[derive(Component, Copy, Clone, Debug, Reflect, Default)]
#[require(ManiaComponent)]
pub struct PianoKeyColor(pub Color);

#[derive(Default, Component)]
#[require(
    ManiaComponent,
    PianoKey,
    PianoKeyType,
    MidiNoteId,
    KeyRootPosition,
    PianoKeySize,
    PianoKeyColor
)]
pub struct PianoKeyBundle;

#[derive(Default, Component)]
#[require(ManiaComponent)]
pub struct ManiaCurrentBeatTextNode;

#[derive(Default, Component)]
#[require(ManiaComponent)]
pub struct ManiaCurrentMeasureTextNode;

#[derive(Default, Component)]
#[require(ManiaComponent)]
pub struct ManiaGameStateTextNode;

#[derive(Default, Clone, PartialEq, Debug)]
pub enum ManiaSongNoteType {
    #[default]
    Single,
    Hold(f64),
}

impl ManiaSongNoteType {
    pub fn get_held_note_height(&self) -> f32 {
        match self {
            ManiaSongNoteType::Hold(release_beat) => {
                let height = *release_beat as f32 * JUDGEMENT_LINE_HEIGHT;
                height
            }
            _ => JUDGEMENT_LINE_HEIGHT,
        }
    }
}

impl From<ManiaChartNote> for ManiaSongNoteType {
    fn from(note: ManiaChartNote) -> Self {
        if note.release_beat == 0.0 {
            ManiaSongNoteType::Single
        } else {
            ManiaSongNoteType::Hold(note.release_beat as f64)
        }
    }
}

#[derive(Default, Clone, PartialEq, Debug)]
pub enum ManiaJudgementHitType {
    #[default]
    Miss,
    Perfect,
    Great,
    Good,
    Late,
    Early,
}

impl ManiaJudgementHitType {
    pub fn is_miss(self) -> bool {
        self == ManiaJudgementHitType::Miss
    }
}

#[derive(Default, Clone, Copy, Eq, PartialEq, Debug)]
pub struct ManiaGameHitTrack {
    pub perfect: usize,
    pub great: usize,
    pub good: usize,
    pub late: usize,
    pub early: usize,
    pub miss: usize,
}

#[derive(Default, Clone, PartialEq, Debug, Component)]
#[require(ManiaComponent)]
pub struct ManiaSongNote {
    pub note_index: usize,
    pub midi_index: u8,
    pub beat: f64,
    pub note_type: ManiaSongNoteType,
    pub held: bool,
    pub judged: bool,
}

impl ManiaSongNote {
    pub fn is_white_key(&self) -> bool {
        // Get the position within the octave (0-11)
        let octave_position = self.midi_index % 12;

        // White keys are at positions 0, 2, 4, 5, 7, 9, 11 (C, D, E, F, G, A, B)
        matches!(octave_position, 0 | 2 | 4 | 5 | 7 | 9 | 11)
    }

    pub fn is_black_key(&self) -> bool {
        !self.is_white_key()
    }

    pub fn get_note_height(&self) -> f32 {
        if self.is_held_note_type() {
            self.note_type.get_held_note_height()
        } else {
            JUDGEMENT_LINE_HEIGHT
        }
    }

    pub fn get_note_color(&self) -> Color {
        if self.is_white_key() {
            Color::srgb(0.0, 0.0, 1.0)
        } else {
            Color::srgb(1.0, 0.0, 0.0)
        }
    }

    pub fn is_held_note_type(&self) -> bool {
        match self.note_type {
            ManiaSongNoteType::Hold(_) => true,
            _ => false,
        }
    }

    pub fn calculate_beat_difference(&self, current_song_beat: f64, audio_output_latency: u128, bpm: usize) -> f64 {
        // (current_song_beat - self.beat).abs()
        // Convert latency from microseconds to beats
        let latency_in_beats = if bpm > 0 {
            // Beats per second = BPM / 60
            // Seconds per beat = 60 / BPM
            // Convert latency from microseconds to seconds, then to beats
            (audio_output_latency as f64 * 1_000.0) * (bpm as f64 / 60.0)
        } else {
            0.0
        };

        // Apply latency correction to current beat
        let adjusted_current_beat = current_song_beat - latency_in_beats;

        // Return signed difference (negative = early, positive = late)
        adjusted_current_beat - self.beat
    }

    pub fn calculate_judgement(beat_difference: f64) -> Option<ManiaJudgementHitType> {
        log::debug!("Beat difference: {}", beat_difference);

        if beat_difference < JUDGEMENT_TOO_EARLY_THRESHOLD {
            return None; // Too early to judge
        }

        // Perfect: centered around 0 (perfect timing)
        if beat_difference.abs() <= THRESHOLD_PERFECT {
            return Some(ManiaJudgementHitType::Perfect);
        }

        // Early: negative difference between -THRESHOLD_EARLY_LATE and -THRESHOLD_PERFECT
        if beat_difference < -THRESHOLD_PERFECT && beat_difference >= -THRESHOLD_EARLY_LATE {
            // Further refine into Great, Good, and Early based on how far from perfect
            if beat_difference >= -THRESHOLD_GREAT {
                return Some(ManiaJudgementHitType::Great);
            } else if beat_difference >= -THRESHOLD_GOOD {
                return Some(ManiaJudgementHitType::Good);
            } else {
                return Some(ManiaJudgementHitType::Early);
            }
        }

        // Late: positive difference between THRESHOLD_PERFECT and THRESHOLD_EARLY_LATE
        if beat_difference > THRESHOLD_PERFECT && beat_difference <= THRESHOLD_EARLY_LATE {
            // Late judgment can be refined similarly if needed
            return Some(ManiaJudgementHitType::Late);
        }

        // Beyond thresholds: Miss
        Some(ManiaJudgementHitType::Miss)
    }
}

#[derive(Default, Clone, PartialEq, Debug, Component)]
pub struct ManiaSongNoteJudged;

#[derive(Default, Clone, PartialEq, Debug, Component)]
#[require(ManiaComponent, ManiaSongNote, Mesh2d)]
pub struct ManiaSongNoteVisual;

#[derive(Default, Clone, Debug, Component)]
#[require(ManiaComponent)]
pub struct ManiaSongNoteLane {
    pub section_index: usize,
    pub lane_index: usize,
}

#[derive(Default, Clone, Debug, Component)]
#[require(ManiaComponent, ManiaSongNoteLane, Mesh2d)]
pub struct ManiaSongLaneVisual;

#[derive(Default, Component)]
#[require(ManiaComponent, Mesh2d)]
pub struct ManiaJudgmentLine;

#[derive(Default, Clone, PartialEq, Debug)]
pub struct ManiaBeatChange {
    pub crotchet: f32,
    pub bpm: usize,
}
