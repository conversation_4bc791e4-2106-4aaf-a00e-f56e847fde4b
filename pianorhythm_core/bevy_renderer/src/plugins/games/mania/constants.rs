pub const NOTE_SPEED: f32 = 0.1;
pub const NOTE_SPAWN_AHEAD_BEATS: f64 = 5.0;
pub const NOTE_SPAWN_AHEAD_POSITION_OFFSET: f64 = 10.0;

pub const OCTAVE: u8 = 12;
pub const KEYBOARD_LENGTH: u8 = OCTAVE * 2;
pub const STARTING_MIDI_NUMBER: u8 = 60;
pub const JUDGEMENT_LINE_HEIGHT: f32 = 10.;
pub const WHITE_KEY_Z_INDEX: f32 = 0.0;
pub const NOTE_LANE_Z_INDEX: f32 = -1.0;
pub const BLACK_KEY_Z_INDEX: f32 = WHITE_KEY_Z_INDEX + 0.2;
pub const JUDGEMENT_LINE_Z_INDEX: f32 = BLACK_KEY_Z_INDEX + 0.1;
pub const NOTES_Z_INDEX: f32 = JUDGEMENT_LINE_Z_INDEX + 2.;
pub const SONG_TIME_COUNTDOWN: f64 = 5.;
pub const NOTE_SPEED_BASE: f32 = 100.;

// Add these constants at the top of the file, with your other imports
use std::ops::RangeInclusive;

// Timing windows for different judgement types
//pub const JUDGEMENT_WINDOW_PERFECT: RangeInclusive<f64> = -0.1..=0.05;
//pub const JUDGEMENT_WINDOW_GREAT: RangeInclusive<f64> = -0.15..=-0.1;
//pub const JUDGEMENT_WINDOW_GOOD: RangeInclusive<f64> = -0.2..=-0.15;
//pub const JUDGEMENT_WINDOW_EARLY: RangeInclusive<f64> = -0.25..=-0.2;
//pub const JUDGEMENT_WINDOW_LATE: RangeInclusive<f64> = 0.05..=0.1;
//pub const JUDGEMENT_TOO_EARLY_THRESHOLD: f64 = -0.3;

// Define judgment thresholds - these are the boundaries between different judgments
pub const THRESHOLD_PERFECT: f64 = 0.01;  // Perfect window is -THRESHOLD_PERFECT..=THRESHOLD_PERFECT
pub const THRESHOLD_GREAT: f64 = THRESHOLD_PERFECT + 0.01;    // Great window extends beyond Perfect
pub const THRESHOLD_GOOD: f64 = THRESHOLD_GREAT + 0.01;     // Good window extends beyond Great
pub const THRESHOLD_EARLY_LATE: f64 = THRESHOLD_PERFECT + 0.05; // Early/Late extends beyond Good
pub const JUDGEMENT_TOO_EARLY_THRESHOLD: f64 = -0.1;  // Before this is "too early"