use crate::plugins::games::mania::components::{
    ManiaBeatChange, ManiaGameHitTrack, ManiaJudgementHitType, ManiaSongNote
};
use bevy::prelude::*;
use derivative::Derivative;

#[derive(Resource, Debug, PartialEq, Clone, Copy)]
pub struct KeyboardContainerWidth(pub f32);

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>)]
pub struct ManiaAudioContextCurrentTime(pub u128);

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>)]
pub struct ManiaAudioOutputLatency(pub u128);

#[derive(<PERSON>fault, <PERSON><PERSON>, <PERSON>)]
pub struct ManiaAudioCurrentSongPosition(pub f64);

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>)]
pub struct ManiaAudioCurrentSongPositionInBeats(pub f64);

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>)]
pub struct ManiaAudioEngineReady;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON><PERSON>, Debug)]
pub enum ManiaChartDifficulty {
    #[default]
    Easy = 0,
    Normal = 1,
    Hard = 2,
    Extreme = 3,
}

#[derive(<PERSON><PERSON>ult, <PERSON><PERSON>, Debug)]
pub struct ManiaChartNote {
    pub midi_id: usize,
    pub beat: f64,
    pub release_beat: f64,
}

impl ManiaChartNote {
    pub fn new(midi_id: usize, beat: f64) -> Self {
        Self {
            midi_id,
            beat,
            ..default()
        }
    }
}

#[derive(Derivative, Clone, Resource)]
#[derivative(Default)]
pub struct ManiaChartFile {
    // -- Song Info
    pub song_title: String,
    pub song_title_romanized: Option<String>,
    pub song_artist: Option<String>,
    pub song_album: Option<String>,
    pub song_genre: Option<String>,
    pub song_subgenre: Option<String>,
    pub song_comments: Option<String>,

    // -- Chart Info
    pub chart_author: Option<String>,
    pub chart_version: Option<String>,
    pub chart_audio_file_path: Option<String>,
    pub notes: Vec<ManiaChartNote>,

    // -- Meta
    pub time_offset: f32,
    #[derivative(Default(value = "120"))]
    pub base_bpm: usize,
    pub bpm_changes: Vec<ManiaBeatChange>,
    pub difficulty: ManiaChartDifficulty,
}

#[derive(Default, Clone, Eq, PartialEq, Debug)]
pub enum ManiaChartPlayingState {
    #[default]
    Init,
    Playing,
    Paused,
    Stopped,
    Stopping,
}

#[derive(Default, Clone, Eq, PartialEq, Debug)]
pub enum ManiaChartAudioType {
    #[default]
    Mp3,
    Midi,
}

#[derive(Derivative, Clone, Resource)]
#[derivative(Default)]
pub struct ManiaChartConductor {
    #[derivative(Default(value = "2"))]
    pub total_octaves_per_player: usize,
    #[derivative(Default(value = "1.0"))]
    pub speed_multiplier: f32,

    #[derivative(Default(value = "120"))]
    pub current_bpm: usize,
    pub song_position: f64,
    pub song_position_in_beats: f64,
    pub sec_per_beat: f32,
    pub last_reported_beat: f64,
    #[derivative(Default(value = "5.0"))]
    pub beats_before_start: f64,
    pub measure: usize,
    #[derivative(Default(value = "4"))]
    pub measures: usize,
    pub time_offset: f32,
    pub bpm_changes: Vec<ManiaBeatChange>,
    pub playing_state: ManiaChartPlayingState,
    pub notes: Vec<ManiaSongNote>,
    /// Beat division for the song. This is used to determine the granularity of the beat reporting.
    /// 1 = whole beats (1/1): 0.0, 1.0, 2.0, 3.0
    ///
    /// 2 = half beats (1/2): 0.0, 0.5, 1.0, 1.5, 2.0
    ///
    /// 3 = third beats (1/3): 0.0, 0.333, 0.667, 1.0, 1.333, etc.
    ///
    /// 4 = quarter beats (1/4): 0.0, 0.25, 0.5, 0.75, 1.0, etc.
    #[derivative(Default(value = "128"))]
    beat_division: usize,
}

impl ManiaChartConductor {
    pub fn new(file: &ManiaChartFile) -> Self {
        let mut notes = file
            .notes
            .iter()
            .map(|note| ManiaSongNote {
                note_index: 0,
                midi_index: note.midi_id as u8,
                beat: note.beat,
                note_type: note.clone().into(),
                ..default()
            })
            .collect::<Vec<_>>();

        notes.sort_by(|a, b| a.note_index.cmp(&b.note_index));

        Self {
            current_bpm: file.base_bpm,
            bpm_changes: file.bpm_changes.clone(),
            time_offset: file.time_offset,
            sec_per_beat: 60.0 / file.base_bpm as f32,
            notes,
            ..Default::default()
        }
    }

    pub fn process(&mut self, current_song_position: f64) -> Option<(f64, usize)> {
        if self.playing_state != ManiaChartPlayingState::Playing {
            return None;
        }

        self.song_position = current_song_position;
        self.song_position_in_beats =
            ((self.song_position / self.sec_per_beat as f64) + self.beats_before_start as f64);
        self.report_beat()
    }

    fn report_beat(&mut self) -> Option<(f64, usize)> {
        let mut reported_beat = None;
        let mut reported_measure = None;

        // Calculate the current beat at the specified division
        let current_divided_beat =
            (self.song_position_in_beats * self.beat_division as f64).floor() / self.beat_division as f64;

        if self.last_reported_beat < current_divided_beat {
            // Reset current measure when crossing a whole beat boundary
            let current_whole_beat = current_divided_beat.floor();
            let last_whole_beat = self.last_reported_beat.floor();

            if last_whole_beat < current_whole_beat {
                self.measure += 1;
            }

            if self.measure > self.measures {
                self.measure = 1;
            }

            reported_beat = Some(current_divided_beat);
            reported_measure = Some(self.measure);

            self.last_reported_beat = current_divided_beat;
        }

        match (reported_beat, reported_measure) {
            (Some(beat), Some(measure)) => Some((beat, measure)),
            _ => None,
        }
    }

    pub fn seek_song_by_beat(&mut self, beat: f64) {
        let target_song_position = (beat * self.sec_per_beat as f64) - self.beats_before_start as f64;
        self.seek_song_by_time(target_song_position);
    }

    pub fn seek_song_by_time(&mut self, time: f64) {
        // TODO: Emit event to update the song position in the audio engine
    }
}

#[derive(Derivative, Clone, Resource)]
#[derivative(Default)]
pub struct ManiaGameState {
    #[derivative(Default(value = "1"))]
    pub players: usize,
    pub score: usize,
    pub max_combo: usize,
    current_combo: usize,
    judgement_hits: Vec<ManiaJudgementHitType>,
    hit_track: ManiaGameHitTrack,
}

impl ManiaGameState {
    pub fn get_judgement_hits(&self) -> ManiaGameHitTrack {
        self.hit_track
    }

    pub fn add_judgement_hit(&mut self, judgement_type: ManiaJudgementHitType) {
        self.judgement_hits.push(judgement_type.clone());
        self.hit_track = self.calculate_judgement_hits();
        self.score = self.calculate_score();

        // Update the current combo and max combo
        if judgement_type.is_miss() {
            if self.current_combo > self.max_combo {
                self.max_combo = self.current_combo;
            }
            self.current_combo = 0;
        } else {
            self.current_combo += 1;
        }
    }

    fn calculate_judgement_hits(&self) -> ManiaGameHitTrack {
        let mut hit_track = ManiaGameHitTrack::default();

        for hit in &self.judgement_hits {
            match hit {
                ManiaJudgementHitType::Perfect => hit_track.perfect += 1,
                ManiaJudgementHitType::Great => hit_track.great += 1,
                ManiaJudgementHitType::Good => hit_track.good += 1,
                ManiaJudgementHitType::Miss => hit_track.miss += 1,
                ManiaJudgementHitType::Late => hit_track.late += 1,
                ManiaJudgementHitType::Early => hit_track.early += 1,
            }
        }

        hit_track
    }

    fn calculate_score(&self) -> usize {
        let mut score = 0;
        score += self.hit_track.perfect * 300;
        score += self.hit_track.great * 100;
        score += self.hit_track.good * 50;
        score += self.hit_track.late * 5;
        score += self.hit_track.miss * 0;
        score
    }
}
