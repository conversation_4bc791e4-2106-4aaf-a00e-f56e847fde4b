mod components;
mod constants;
pub mod events;
mod resources;
mod systems;

use crate::core::events::AppExtensions;
use crate::plugins::games::mania::components::*;
use crate::plugins::games::mania::events::{
    ManiaEvent, ManiaAudioEvent, ManiaConductorEvent, AUDIO_EVENTS_CHANNEL, MANIA_EVENTS_CHANNEL
};
use crate::plugins::games::mania::resources::{KeyboardContainer<PERSON>idth, ManiaAudioContextCurrentTime, ManiaAudioCurrentSongPosition, ManiaAudioCurrentSongPositionInBeats, ManiaAudioEngineReady, ManiaAudioOutputLatency, ManiaChartConductor, ManiaChartFile, ManiaChartNote, ManiaChartPlayingState, ManiaGameState};
use crate::plugins::games::mania::systems::*;
use bevy::prelude::*;
use crossbeam_channel::unbounded;
use crate::plugins::games::mania::systems::piano::create_keyboard;

pub struct ManiaPlugin;

impl Plugin for ManiaPlugin {
    fn build(&self, app: &mut App) {
        app.init_resource::<ManiaAudioContextCurrentTime>()
            .add_event::<ManiaConductorEvent>()
            .init_resource::<ManiaGameState>()
            .init_resource::<ManiaAudioOutputLatency>()
            .init_resource::<ManiaAudioCurrentSongPositionInBeats>()
            .init_resource::<ManiaAudioCurrentSongPosition>();

        app
            .insert_resource(KeyboardContainerWidth(500.0));
        app
            .add_systems(Startup, setup_camera)
            .add_systems(Startup, create_keyboard)
            .add_systems(Startup, setup2);

        unsafe {
            _ = AUDIO_EVENTS_CHANNEL.set(unbounded());
            _ = MANIA_EVENTS_CHANNEL.set(unbounded());

            if let Some((sender, rec)) = AUDIO_EVENTS_CHANNEL.get() {
                app.add_event_channel::<ManiaAudioEvent>(sender.clone(), rec.clone());
            }

            app.add_systems(First, audio_events);
        }

        app.add_systems(
            Update,
            (update_notes_position, spawn_notes, #[cfg(debug_assertions)] update_debug_ui_text)
                .run_if(resource_exists_and_changed::<ManiaAudioCurrentSongPosition>)
                .run_if(resource_exists::<ManiaChartConductor>)
                .after(audio_events),
        );

        app.add_systems(Update, handle_input.run_if(resource_exists::<ManiaChartConductor>));

        #[cfg(debug_assertions)]
        app.add_systems(Update, (debug_timer, beat_diff_debug_text));

        app.add_systems(
            Update,
            (|mania_chart_file: Res<ManiaChartFile>, mut mania_chart_conductor: ResMut<ManiaChartConductor>| {
                if mania_chart_file.chart_audio_file_path.is_none()
                    || mania_chart_conductor.playing_state == ManiaChartPlayingState::Playing
                {
                    return;
                }

                mania_chart_conductor.playing_state = ManiaChartPlayingState::Playing;
                ManiaEvent::send_play_song_from_path(mania_chart_file.chart_audio_file_path.clone().unwrap());
            })
            // .run_if(resource_added::<ManiaAudioEngineReady>.and(resource_exists::<ManiaChartFile>))
            .run_if(resource_exists::<ManiaChartConductor>)
            .run_if(resource_exists::<ManiaAudioEngineReady>)
            .run_if(resource_exists::<ManiaChartFile>),
        );
    }

    fn finish(&self, _app: &mut App) {
        log::info!("{:?} finished.", &self.name());
    }
}

const X_EXTENT: f32 = 900.;
const NUM_KEYS_PER_OCTAVE: usize = 12;
const NUM_WHITE_KEYS_PER_OCTAVE: usize = 8;

fn setup(mut commands: Commands, mut meshes: ResMut<Assets<Mesh>>, mut materials: ResMut<Assets<ColorMaterial>>) {
    let shapes = [
        meshes.add(Circle::new(50.0)),
        meshes.add(CircularSector::new(50.0, 1.0)),
        meshes.add(CircularSegment::new(50.0, 1.25)),
        meshes.add(Ellipse::new(25.0, 50.0)),
        meshes.add(Annulus::new(25.0, 50.0)),
        meshes.add(Capsule2d::new(25.0, 50.0)),
        meshes.add(Rhombus::new(75.0, 100.0)),
        meshes.add(Rectangle::new(50.0, 100.0)),
        meshes.add(RegularPolygon::new(50.0, 6)),
        meshes.add(Triangle2d::new(Vec2::Y * 50.0, Vec2::new(-50.0, -50.0), Vec2::new(50.0, -50.0))),
    ];
    let num_shapes = shapes.len();

    for (i, shape) in shapes.into_iter().enumerate() {
        // Distribute colors evenly across the rainbow.
        let color = Color::hsl(360. * i as f32 / num_shapes as f32, 0.95, 0.7);

        commands.spawn((
            Mesh2d(shape),
            MeshMaterial2d(materials.add(color)),
            Transform::from_xyz(
                // Distribute shapes from -X_EXTENT/2 to +X_EXTENT/2.
                -X_EXTENT / 2. + i as f32 / (num_shapes - 1) as f32 * X_EXTENT,
                0.0,
                0.0,
            ),
        ));
    }
}
