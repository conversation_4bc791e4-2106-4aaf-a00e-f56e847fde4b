use bevy::prelude::*;
use crossbeam_channel::{Receiver, Sender};
use std::sync::OnceLock;
use crate::plugins::games::mania::resources::ManiaChartPlayingState;

pub static mut AUDIO_EVENTS_CHANNEL: OnceLock<(Sender<ManiaAudioEvent>, Receiver<ManiaAudioEvent>)> = OnceLock::new();
pub static mut MANIA_EVENTS_CHANNEL: OnceLock<(Sender<ManiaEvent>, Receiver<ManiaEvent>)> = OnceLock::new();

#[derive(Debug, Copy, Clone, Eq, PartialEq)]
pub enum ManiaAudioSongState {
    Playing,
    Paused,
    Stopping,
    Stopped,
}

impl Into<ManiaChartPlayingState> for ManiaAudioSongState {
    fn into(self) -> ManiaChartPlayingState {
        match self {
            ManiaAudioSongState::Playing => ManiaChartPlayingState::Playing,
            ManiaAudioSongState::Paused => ManiaChartPlayingState::Paused,
            ManiaAudioSongState::Stopping => ManiaChartPlayingState::Stopping,
            ManiaAudioSongState::Stopped => ManiaChartPlayingState::Stopped,
            // _ => ManiaChartPlayingState::Init
        }
    }
}

#[derive(Event, Debug, Copy, Clone)]
pub enum ManiaAudioEvent {
    AudioEngineReady,
    CurrentTime(u128),
    OutputLatency(u128),
    CurrentSongPosition(f64),
    CurrentSongState(ManiaAudioSongState),
}

#[derive(Eq, PartialEq, Debug, Clone)]
pub enum ManiaEvent {
    PlaySongFromPath(String),
}

#[derive(Event, Debug, Clone)]
pub enum ManiaConductorEvent {
    BeatTriggered(f64),
    MeasureTriggered(usize),
}

impl ManiaAudioEvent {
    // Private helper method to handle the common sending logic
    fn send_event(event: ManiaAudioEvent) {
        unsafe {
            if let Some((sender, _)) = AUDIO_EVENTS_CHANNEL.get() {
                let _ = sender.send(event);
            }
        }
    }

    pub fn send_song_state(state: ManiaAudioSongState) {
        Self::send_event(ManiaAudioEvent::CurrentSongState(state))
    }

    pub fn send_current_time(value: u128) {
        Self::send_event(ManiaAudioEvent::CurrentTime(value))
    }

    pub fn send_output_latency(value: u128) {
        Self::send_event(ManiaAudioEvent::OutputLatency(value))
    }

    pub fn send_current_song_position(value: f64) {
        Self::send_event(ManiaAudioEvent::CurrentSongPosition(value))
    }

    pub fn send_audio_engine_ready() {
        Self::send_event(ManiaAudioEvent::AudioEngineReady)
    }
}

impl ManiaEvent {
    pub fn send_play_song_from_path(path: String) {
        unsafe {
            if let Some((sender, _)) = MANIA_EVENTS_CHANNEL.get() {
                let _ = sender.send(ManiaEvent::PlaySongFromPath(path));
            }
        }
    }
}
