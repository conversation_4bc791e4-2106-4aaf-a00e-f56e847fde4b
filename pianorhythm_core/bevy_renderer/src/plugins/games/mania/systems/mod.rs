pub mod piano;

use crate::components::fps_counter::{FpsRoot, FpsText};
use crate::components::{AnimationDown, OnActiveNote};
use crate::core::events::ECSSynthEventsAction;
use crate::plugins::games::mania::components::{
    ManiaCurrentBeatTextNode, ManiaCurrentMeasureTextNode, ManiaGameStateTextNode, ManiaJudgementHitType, ManiaJudgmentLine, ManiaSongNote, ManiaSongNoteJudged, ManiaSongNoteType, MidiNoteId, <PERSON>K<PERSON>, PianoKeySize
};
use crate::plugins::games::mania::constants::{
    KEYBOARD_LENGTH, NOTE_SPAWN_AHEAD_BEATS, NOTE_SPAWN_AHEAD_POSITION_OFFSET, STARTING_MIDI_NUMBER, WHITE_KEY_Z_INDEX
};
use crate::plugins::games::mania::events::{ManiaAudioEvent, ManiaConductorEvent};
use crate::plugins::games::mania::resources::{
    KeyboardContainer<PERSON>id<PERSON>, ManiaAudioContextCurrentTime, ManiaAudioCurrentSongPosition, ManiaAudioCurrentSongPositionInBeats, ManiaAudioEngineReady, ManiaAudioOutputLatency, ManiaChartConductor, ManiaChartFile, ManiaChartNote, ManiaGameState
};
use crate::plugins::games::mania::{NUM_KEYS_PER_OCTAVE, NUM_WHITE_KEYS_PER_OCTAVE};
use bevy::prelude::*;
use bevy::window::PrimaryWindow;
use music_note::midi::MidiNote;
use pianorhythm_shared::midi::DRUM_CHANNEL;
use std::time::Duration;

#[derive(Component)]
pub struct BeatDiffDebugText;

pub fn beat_diff_debug_text(
    mut commands: Commands,
    mut query: Query<(&ChildOf, &mut Text2d), With<BeatDiffDebugText>>,
    q_parent: Query<&ManiaSongNote>,
    chart_state: ResMut<ManiaChartConductor>,
    current_song_beat: Res<ManiaAudioCurrentSongPositionInBeats>,
    mania_audio_output_latency: Res<ManiaAudioOutputLatency>,
) {
    for (parent, mut text) in query.iter_mut() {
        if let Ok(note) = q_parent.get(parent.get()) {
            let beat_difference = note.calculate_beat_difference(
                current_song_beat.0,
                mania_audio_output_latency.0,
                chart_state.current_bpm,
            );
            text.0 = format!("{}", beat_difference);
        }
    }
}

#[derive(Component)]
pub struct NoteDebugTimer(pub Timer);

impl Default for NoteDebugTimer {
    fn default() -> Self {
        Self(Timer::new(Duration::from_secs(5), TimerMode::Once))
    }
}

pub fn debug_timer(mut commands: Commands, mut query: Query<(Entity, &mut NoteDebugTimer)>, time: Res<Time>) {
    for (entity, mut timer) in query.iter_mut() {
        timer.0.tick(time.delta());

        if timer.0.finished() {
            commands.entity(entity).despawn();
        }
    }
}

pub(super) fn setup_camera(mut commands: Commands, app_settings: Res<crate::resources::ClientAppSettings>) {
    commands.spawn((
        Camera2d::default(),
        Camera {
            order: 1,
            // clear_color: ClearColorConfig::None,
            ..default()
        },
        crate::components::Main2DCamera,
    ));

    commands.spawn((
        ManiaCurrentBeatTextNode,
        ZIndex(i32::MAX),
        GlobalZIndex(i32::MAX),
        Text::new("Beat:"),
        TextLayout::default(),
        TextFont {
            font_size: 12.0,
            ..default()
        },
        TextColor::from(Color::WHITE),
        Node {
            position_type: PositionType::Absolute,
            right: Val::Percent(1.),
            top: Val::Percent(3.),
            bottom: Val::Auto,
            left: Val::Auto,
            padding: UiRect::all(Val::Px(4.0)),
            ..Default::default()
        },
    ));

    commands.spawn((
        ManiaCurrentMeasureTextNode,
        ZIndex(i32::MAX),
        GlobalZIndex(i32::MAX),
        Text::new("Measure:"),
        TextLayout::default(),
        TextFont {
            font_size: 12.0,
            ..default()
        },
        TextColor::from(Color::WHITE),
        Node {
            position_type: PositionType::Absolute,
            right: Val::Percent(1.),
            top: Val::Percent(5.),
            bottom: Val::Auto,
            left: Val::Auto,
            padding: UiRect::all(Val::Px(4.0)),
            ..Default::default()
        },
    ));

    commands.spawn((
        ManiaGameStateTextNode,
        ZIndex(i32::MAX),
        GlobalZIndex(i32::MAX),
        Text::new("Score:"),
        TextLayout::default(),
        TextFont {
            font_size: 10.0,
            ..default()
        },
        TextColor::from(Color::WHITE),
        Node {
            position_type: PositionType::Absolute,
            right: Val::Percent(50.),
            top: Val::Percent(3.),
            bottom: Val::Auto,
            left: Val::Auto,
            padding: UiRect::all(Val::Px(4.0)),
            ..Default::default()
        },
    ));
}

pub fn update_debug_ui_text(
    mut set: ParamSet<(
        Query<(&mut Text), With<ManiaCurrentBeatTextNode>>,
        Query<(&mut Text), With<ManiaCurrentMeasureTextNode>>,
        Query<(&mut Text), With<ManiaGameStateTextNode>>,
    )>,
    mania_chart_conductor: Res<ManiaChartConductor>,
    game_state: Res<ManiaGameState>,
    latency: Res<ManiaAudioOutputLatency>,
) {
    for (mut text) in &mut set.p0() {
        **text = format!("Beat: {}", mania_chart_conductor.song_position_in_beats).to_string();
    }

    for (mut text) in &mut set.p1() {
        **text = format!(
            "Bpm: {} | Measure: {} / {}",
            mania_chart_conductor.current_bpm, mania_chart_conductor.measure, mania_chart_conductor.measures
        )
        .to_string();
    }

    let hits = game_state.get_judgement_hits();
    let max_combo = game_state.max_combo;
    let score = game_state.score;
    for mut text in &mut set.p2() {
        **text = format!("{:?} \nMax Combo: {max_combo} \nScore: {score} \nLatency: {}", hits, latency.0).to_string();
    }
}

pub(super) fn audio_events(
    mut commands: Commands,
    mut event_reader: EventReader<ManiaAudioEvent>,
    mut mania_chart_conductor: Option<ResMut<ManiaChartConductor>>,
    mut event_writer: EventWriter<ManiaConductorEvent>,
) {
    for event in event_reader.read() {
        match event {
            ManiaAudioEvent::AudioEngineReady => commands.insert_resource(ManiaAudioEngineReady),
            ManiaAudioEvent::CurrentTime(value) => {
                commands.insert_resource(ManiaAudioContextCurrentTime(*value));
            }
            ManiaAudioEvent::OutputLatency(value) => {
                commands.insert_resource(ManiaAudioOutputLatency(*value));
            }
            ManiaAudioEvent::CurrentSongState(value) if mania_chart_conductor.is_some() => {
                let mut conductor = mania_chart_conductor.as_mut().unwrap();
                conductor.playing_state = (*value).into();
            }
            ManiaAudioEvent::CurrentSongPosition(value) if mania_chart_conductor.is_some() => {
                let mut conductor = mania_chart_conductor.as_mut().unwrap();
                if let Some((beat, measure)) = conductor.process(*value) {
                    event_writer.write(ManiaConductorEvent::BeatTriggered(beat));
                    event_writer.write(ManiaConductorEvent::MeasureTriggered(measure));
                    commands.insert_resource(ManiaAudioCurrentSongPositionInBeats(beat));
                }
                commands.insert_resource(ManiaAudioCurrentSongPosition(*value));
            }
            _ => {}
        }
    }
}

pub(super) fn spawn_notes(
    mania_chart_conductor: Res<ManiaChartConductor>,
    keys: Query<(&MidiNoteId, &PianoKeySize, &Transform), With<PianoKey>>,
    mut commands: Commands,
    mut events: EventReader<ManiaConductorEvent>,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<ColorMaterial>>,
) {
    for event in events.read() {
        match event {
            ManiaConductorEvent::BeatTriggered(beat) => {
                if let Some(note) = mania_chart_conductor
                    .notes
                    .iter()
                    .find(|note| *beat == note.beat as f64 - NOTE_SPAWN_AHEAD_BEATS)
                {
                    let Some((_, target_key_size, target_key_transform)) =
                        keys.iter().find(|(x, _, _)| x.0 == note.midi_index)
                    else {
                        continue;
                    };

                    commands
                        .spawn((
                            note.clone(),
                            Transform::from_xyz(
                                target_key_transform.translation.x,
                                0.0,
                                target_key_transform.translation.z + 1.0,
                            ),
                            Mesh2d(meshes.add(Rectangle::new(target_key_size.0.x, note.get_note_height()))),
                            MeshMaterial2d(materials.add(note.get_note_color())),
                        ))
                        .with_children(|builder| {
                            builder.spawn((
                                BeatDiffDebugText,
                                Text2d::new("Beat Diff:"),
                                TextFont {
                                    font_size: 12.0,
                                    ..default()
                                },
                                TextLayout::new_with_justify(JustifyText::Center),
                                Transform::from_translation(Vec3::Z),
                            ));
                        });
                }
            }
            _ => {}
        }
    }
}

pub(super) fn setup2(mut commands: Commands) {
    let mut notes: Vec<ManiaChartNote> = vec![];

    for index in 0..200 {
        if index as f32 % 2.0 == 0.0 {
            let mut note = ManiaChartNote::new(60, index as f64);
            notes.push(note);
        }

        //if index as f32 % 3.0 == 0.0 {
        //    let mut note = ManiaChartNote::new(61, index as f64);
        //    note.release_beat = index as f64 + 1.0;
        //    notes.push(note);
        //}
    }

    let mut chart_file = ManiaChartFile {
        song_title: "test".to_string(),
        base_bpm: 109,
        //base_bpm: 50,
        chart_audio_file_path: Some(
            "C:\\Projects\\pianorhythm-ssr\\pianorhythm_core\\bevy_renderer\\assets\\test_audio.ogg".to_string(),
        ),
        notes,
        ..default()
    };
    let mut chart_state = ManiaChartConductor::new(&chart_file);

    commands.insert_resource(chart_file);
    commands.insert_resource(chart_state);
    log::info!("Starting chart...");
}

pub(super) fn update_notes_position(
    mut commands: Commands,
    mut game_state: ResMut<ManiaGameState>,
    song_position: Res<ManiaAudioCurrentSongPositionInBeats>,
    window: Single<&Window, With<PrimaryWindow>>,
    keys: Query<(&MidiNoteId, &Transform), With<PianoKey>>,
    mut chart_state: ResMut<ManiaChartConductor>,
    mut spawned_notes: Query<(Entity, &ManiaSongNote, Option<&ManiaSongNoteJudged>, &mut Transform), Without<PianoKey>>,
) {
    let window = window.into_inner();

    for (entity, note, judged, mut transform) in spawned_notes.iter_mut() {
        let Some((_, target_key_transform)) = keys.iter().find(|(x, _)| x.0 == note.midi_index) else {
            continue;
        };

        let t = note.beat - song_position.0;
        let o = window.height() as f64 + NOTE_SPAWN_AHEAD_POSITION_OFFSET;
        let a = NOTE_SPAWN_AHEAD_BEATS;
        let p = t * o / a;

        transform.translation.y = p as f32;
        transform.translation.x = target_key_transform.translation.x;

        if p + note.get_note_height() as f64 <= -1.0 {
            game_state.add_judgement_hit(ManiaJudgementHitType::Miss);
            commands.entity(entity).despawn();
        }
    }
}

pub fn handle_input(
    mut commands: Commands,
    mut event_reader: EventReader<ECSSynthEventsAction>,
    mut game_state: ResMut<ManiaGameState>,
    mut spawned_notes: Query<(Entity, &mut ManiaSongNote, &Transform)>,

    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<ColorMaterial>>,

    chart_state: ResMut<ManiaChartConductor>,
    current_song_beat: Res<ManiaAudioCurrentSongPositionInBeats>,
    mania_audio_output_latency: Res<ManiaAudioOutputLatency>,
) {
    let mut any_note = false;

    #[cfg(debug_assertions)]
    {
        any_note = true;
    }

    for my_event in event_reader.read() {
        match &my_event {
            ECSSynthEventsAction::NoteOn(data) if data.channel != DRUM_CHANNEL => {
                // First find notes that match the pressed key
                for (entity, mut note, trans) in spawned_notes
                    .iter_mut()
                    .filter(|(_, note, _)| (note.midi_index == data.note || any_note) && !note.held)
                {
                    // Calculate timing difference between current beat and note beat
                    let beat_difference = note.calculate_beat_difference(
                        current_song_beat.0,
                        mania_audio_output_latency.0,
                        chart_state.current_bpm,
                    );

                    if let Some(judgment_hit_type) = ManiaSongNote::calculate_judgement(beat_difference) {
                        commands.spawn((
                            NoteDebugTimer::default(),
                            trans.clone(),
                            Mesh2d(meshes.add(Rectangle::new(50.0, note.get_note_height()))),
                            MeshMaterial2d(materials.add(Color::BLACK)),
                        ));

                        note.judged = true;
                        commands.entity(entity).insert(ManiaSongNoteJudged);

                        #[cfg(debug_assertions)]
                        log::debug!(
                            "{judgment_hit_type:?} on note {}! Difference: {:.3} beats",
                            note.midi_index,
                            beat_difference
                        );
                        game_state.add_judgement_hit(judgment_hit_type.clone());

                        if note.is_held_note_type() && !judgment_hit_type.is_miss() {
                            note.held = true;
                        } else {
                            commands.entity(entity).despawn();
                        }
                    }

                    // Notes beyond 1.0 beat difference are ignored
                }
            }
            ECSSynthEventsAction::NoteOff(data) if data.channel != DRUM_CHANNEL => {
                for (entity, mut note, _) in spawned_notes.iter_mut().filter(|(_, note, _)| {
                    (note.midi_index == data.note || any_note) && note.is_held_note_type() && note.held
                }) {
                    let beat_difference = note.calculate_beat_difference(
                        current_song_beat.0,
                        mania_audio_output_latency.0,
                        chart_state.current_bpm,
                    );

                    if let Some(judgment_hit_type) = ManiaSongNote::calculate_judgement(beat_difference) {
                        log::info!(
                            "{judgment_hit_type:?} on release note {}! Difference: {:.3} beats",
                            note.midi_index,
                            beat_difference
                        );

                        note.held = true;
                        game_state.add_judgement_hit(judgment_hit_type);
                        commands.entity(entity).despawn();
                    }
                }
            }
            _ => {}
        }
    }
}
