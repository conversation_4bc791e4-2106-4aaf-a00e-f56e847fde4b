use crate::plugins::games::mania::components::{
    ManiaJudgmentLine, MidiNoteId, PianoKeyBundle, PianoKeyColor, PianoKeySize, PianoKeyType, PianoKeyboard, PianoKeyboardSize, PianoNoteLane, PianoNoteLaneColor
};
use crate::plugins::games::mania::constants::{
    BLACK_KEY_Z_INDEX, JUD<PERSON>MENT_LINE_HEIGHT, JUDGEMENT_LINE_Z_INDEX, KEYBOARD_LENGTH, NOTE_LANE_Z_INDEX, STARTING_MIDI_NUMBER, WHITE_KEY_Z_INDEX
};
use crate::plugins::games::mania::resources::KeyboardContainerWidth;
use bevy::color::palettes::css::DARK_GRAY;
use bevy::prelude::*;
use music_note::midi::MidiNote;

pub fn create_keyboard(
    container_width: Res<KeyboardContainerWidth>,
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<ColorMaterial>>,
) {
    let mut num_white_keys: f32 = 0.0;
    let num_keys = KEYBOARD_LENGTH;

    let total_keys = STARTING_MIDI_NUMBER + num_keys;
    for key_index in STARTING_MIDI_NUMBER..total_keys {
        let is_black_key = MidiNote::from_byte(key_index).pitch().to_string().contains("#");
        if !is_black_key {
            num_white_keys += 1.0
        }
    }

    let white_key_width = container_width.0 / num_white_keys;
    let black_key_width = (white_key_width / 3.) * 2.;
    let white_key_height = white_key_width * 5.5;
    let black_key_height = (white_key_height / 3.) * 1.8;
    let notes_with_light_bg: Vec<String> = vec!["C".to_string(), "D".to_string(), "E".to_string()];

    let keyboard_transform = Transform::default();

    // Create a parent entity
    commands
        .spawn((
            PianoKeyboard,
            PianoKeyboardSize(Vec2::new(white_key_width, white_key_height)),
            keyboard_transform,
            Name::new("Piano-Keyboard".to_string()),
        ))
        .with_children(|parent| {
            // Keys
            let mut cur_x_pos = 0.0;
            let mut prev_key_was_black = None;
            for key_index in STARTING_MIDI_NUMBER..total_keys {
                let midi_note = MidiNote::from_byte(key_index);
                let is_black_key = midi_note.pitch().to_string().contains("#");
                 //log::info!("Note: {} | Midi {}", midi_note, key_index);

                // Draw white keys
                if !is_black_key {
                    let spawn_position = cur_x_pos as f32 + white_key_width / 2.0;
                    let transform = Transform::from_xyz(spawn_position, 0., WHITE_KEY_Z_INDEX);
                    //log::debug!("White key {key_index} created");

                    parent
                        .spawn((
                            PianoKeyBundle::default(),
                            MidiNoteId(key_index),
                            PianoKeyType::WHITE,
                            PianoKeySize(Vec2::new(white_key_width, white_key_height)),
                            PianoKeyColor(Color::WHITE.with_alpha(0.9)),
                            transform,
                            Mesh2d(meshes.add(Rectangle::new(white_key_width, white_key_height))),
                            MeshMaterial2d(materials.add(Color::WHITE.with_alpha(0.9))),
                            Name::new(format!("PianoKey-White-{key_index}")),
                        ))
                        .with_children(|key_parent| {
                            let mut lane_bg_color = Color::from(DARK_GRAY);
                            let pitch = midi_note.pitch().to_string();
                            let lane_bg_color_alpha = if notes_with_light_bg.contains(&pitch) { 0.5 } else { 0.2 };
                            lane_bg_color.set_alpha(lane_bg_color_alpha);

                            let transform = Transform::from_xyz(0., 0., NOTE_LANE_Z_INDEX);
                            key_parent.spawn((
                                PianoNoteLane(key_index),
                                PianoNoteLaneColor(lane_bg_color),
                                transform,
                                Mesh2d(meshes.add(Rectangle::new(white_key_width, white_key_height * 4.0))),
                                MeshMaterial2d(materials.add(lane_bg_color)),
                                Name::new(format!("Piano-NoteLane-{key_index}")),
                            ));
                        });

                    // Draw black key
                    if let Some(key_index) = prev_key_was_black {
                        let spawn_position = cur_x_pos as f32;
                        let target_y = (white_key_height - black_key_height) / 2.;
                        let transform = Transform::from_xyz(spawn_position, target_y, BLACK_KEY_Z_INDEX);
                        //log::debug!("Black key {key_index} created");

                        parent.spawn((
                            PianoKeyBundle::default(),
                            MidiNoteId(key_index),
                            PianoKeySize(Vec2::new(black_key_width, black_key_height)),
                            PianoKeyColor(Color::from(DARK_GRAY)),
                            PianoKeyType::BLACK,
                            transform,
                            Mesh2d(meshes.add(Rectangle::new(black_key_width, black_key_height))),
                            MeshMaterial2d(materials.add(Color::from(DARK_GRAY))),
                            Name::new(format!("PianoKey-Black-{key_index}")),
                        ));
                    }

                    prev_key_was_black = None;
                    cur_x_pos += white_key_width;
                } else {
                    prev_key_was_black = Some(key_index);
                }
            }

            // Judgement Line
            parent.spawn((
                Name::new("Judgement-Line".to_string()),
                ManiaJudgmentLine,
                Mesh2d(meshes.add(Rectangle::new(container_width.0, JUDGEMENT_LINE_HEIGHT))),
                MeshMaterial2d(materials.add(Color::srgb(1.0, 0.0, 0.0))),
                Transform::from_xyz(
                    keyboard_transform.translation.x + container_width.0 / 2.,
                    0.0,
                    JUDGEMENT_LINE_Z_INDEX,
                ),
            ));
        });
}

//pub fn draw_judgement_lines(
//    time: Res<Time>,
//    container_width: Res<KeyboardContainerWidth>,
//    q_children: Query<(&Parent, &Transform, &mut ColorMaterial, &ViewVisibility), With<ManiaJudgmentLine>>,
//    q_parent: Query<&GlobalTransform>,
//    mut materials: ResMut<Assets<ColorMaterial>>
//) {
//    for (parent, transform, mut material, visibility) in q_children.iter_mut() {
//        if !visibility.get() {
//            continue;
//        }
//
//        let alpha = time.into_inner().elapsed_secs().sin() * 0.35 + 0.65;
//        material.color.set_alpha(alpha);
//    }
//}