use bevy::prelude::*;
use bevy_asset_loader::loading_state::{LoadingState, LoadingStateAppExt};
use bevy_asset_loader::prelude::{ConfigureLoadingState, StandardDynamicAssetCollection};

use crate::components::{AnimationState, BaseMeshColor, DefaultMeshColor, DrumsMeshType, DrumsScene, InstrumentType, MainInstrument};
use crate::core::AppState;
use crate::plugins::drums::components::{
    BassDrum, BassDrumPedal, CrashCymbal, FloorTom, HiHatBottom, HiHatTop, HighTom, MediumTom, PercussionInstrument, PercussionInstrumentType, RideCymbal, SnareDrum
};
use crate::plugins::drums::resources::DrumModelAssets;
use crate::utils;

mod components;
mod events;
mod resources;
mod systems;

#[derive(Default, Resource)]
struct DrumsMeshLoaded;

#[derive(Default, Resource)]
struct MeshSetup;

#[derive(Component)]
struct ModelPath(pub String);

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Eq, PartialEq, Hash, States, Reflect)]
enum PluginState {
    #[default]
    AssetLoading,
    Ready,
}

const MODEL_PATH: &str = "/models/Drum_Set.glb";
const LOW_POLY_MODEL_PATH: &str = "/models/Drum_Set_Low_Poly.glb";

#[derive(Default)]
pub struct DrumsModelPlugin;

impl Plugin for DrumsModelPlugin {
    fn build(&self, app: &mut App) {
        app.init_state::<PluginState>()
            .add_loading_state(
                LoadingState::new(PluginState::AssetLoading)
                    .continue_to_state(PluginState::Ready)
                    .with_dynamic_assets_file::<StandardDynamicAssetCollection>(utils::get_model_assets_file_path())
                    .load_collection::<DrumModelAssets>(),
            )
            .add_systems(OnEnter(PluginState::Ready), load_gltf)
            .add_systems(OnEnter(PluginState::Ready), |mut assets_loaded: ResMut<crate::resources::AppAssetsLoaded>| {
                assets_loaded.drum_models = true;
            })
            .add_systems(PreUpdate, events::receiving_events)
            .add_systems(PostUpdate, (systems::mesh_on_all_notes_off, systems::on_tween_on_complete).distributive_run_if(in_state(AppState::InGame)))
            .add_systems(
                Update,
                (
                    setup_after_load.run_if(resource_exists::<MeshSetup>),
                    systems::on_mesh_animation_down,
                    systems::on_drums_display.run_if(resource_changed::<crate::resources::DrumsDisplayed>),
                ),
            );

        app.register_type::<PercussionInstrument>().register_type::<PercussionInstrumentType>();

        log::info!("Built {:?}", self.name());
    }

    fn cleanup(&self, app: &mut App) {
        app.world_mut().remove_resource::<DrumsMeshLoaded>();
    }

    fn finish(&self, _app: &mut App) {
        log::info!("{:?} finished.", &self.name());
    }
}

fn load_gltf(mut commands: Commands, assets: Res<DrumModelAssets>, assets_gltf: Res<Assets<Gltf>>) {
    let Some(high_poly_gltf) = assets_gltf.get(&assets.high_poly_drums_model) else {
        return;
    };
    let Some(low_poly_gltf) = assets_gltf.get(&assets.low_poly_drums_model) else {
        return;
    };

    let handle = high_poly_gltf.scenes[0].clone();
    let low_poly_handle = low_poly_gltf.scenes[0].clone();

    let transform = Transform::from_xyz(2.5, 0.0, -2.2)
        .with_rotation(Quat::from_euler(EulerRot::XYZ, 0., -1.1, 0.))
        .with_scale(Vec3::splat(1.8));

    commands.spawn((
        Name::new("Drums"),
        MainInstrument(InstrumentType::DRUMS),
        ModelPath(MODEL_PATH.to_string()),
        DrumsScene,
        SceneRoot(handle),
        Visibility::Hidden,
        transform.clone(),
    ));

    commands.spawn((
        Name::new("Drums (Low Poly)"),
        MainInstrument(InstrumentType::DRUMS),
        ModelPath(LOW_POLY_MODEL_PATH.to_string()),
        DrumsScene,
        crate::components::LowPolyModel,
        SceneRoot(low_poly_handle),
        Visibility::Hidden,
        transform.clone(),
    ));

    commands.init_resource::<MeshSetup>();
}

fn setup_after_load(
    mut commands: Commands, mut setup: Local<bool>, mut materials: ResMut<Assets<StandardMaterial>>,
    scene_query: Query<(Entity, &ModelPath), With<DrumsScene>>, mesh_query: Query<(&Name, &MeshMaterial3d<StandardMaterial>), With<Mesh3d>>,
    children: Query<&Children>,
) {
    if !*setup {
        let mut high_poly_processed = false;
        let mut low_poly_processed = false;

        for (entity, model_path) in scene_query.iter() {
            for entity in children.iter_descendants(entity) {
                if let Ok((name, mat_handle)) = mesh_query.get(entity) {
                    if let Ok(mut cmd) = commands.get_entity(entity) {
                        let entity_name = name.to_lowercase();

                        // let _insert_pointer_up = || {
                        //     bevy::picking::On::<Pointer<Up>>::target_component_mut::<AnimationState>(|event, state| {
                        //         if event.button != PointerButton::Primary { return; }
                        //         state.is_down = false;
                        //     })
                        // };
                        //
                        // let _insert_pointer_out = || {
                        //     On::<Pointer<Up>>::target_component_mut::<AnimationState>(|event, state| {
                        //         if event.button != PointerButton::Primary { return; }
                        //         state.is_down = false;
                        //     })
                        // };
                        //
                        // let _insert_pointer_down = |midi_id: u8| {
                        //     On::<Pointer<Down>>::target_commands_mut(move |event, commands| {
                        //         if event.button != PointerButton::Primary {
                        //             return;
                        //         }
                        //
                        //         commands.insert(OnActiveNote(UserNoteData {
                        //             socket_id: None,
                        //             channel: DRUM_CHANNEL,
                        //             note: midi_id,
                        //             vel: 100,
                        //             source: MidiNoteSource::MOUSE,
                        //             ..default()
                        //         }));
                        //         commands.insert(AnimationState { is_down: true });
                        //     })
                        // };

                        match entity_name.as_str() {
                            "bass_foot" => {
                                cmd.insert(BassDrumPedal);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::BassPedal));
                            }
                            "bass_drum" => {
                                cmd.insert(BassDrum);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::BassDrum));
                                // cmd.insert(insert_pointer_down(36));
                                // cmd.insert(insert_pointer_up());
                                // cmd.insert(insert_pointer_out());
                            }
                            "snare_drum" => {
                                cmd.insert(SnareDrum);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::SnareDrum));
                                // cmd.insert(insert_pointer_down(38));
                                // cmd.insert(insert_pointer_up());
                                // cmd.insert(insert_pointer_out());
                            }
                            "crash_cymbal" => {
                                cmd.insert(CrashCymbal);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::CrashCymbal));
                                // cmd.insert(insert_pointer_down(49));
                                // cmd.insert(insert_pointer_up());
                                // cmd.insert(insert_pointer_out());
                            }
                            "ride_cymbal" => {
                                cmd.insert(RideCymbal);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::RideCymbal));
                                // cmd.insert(insert_pointer_down(49));
                                // cmd.insert(insert_pointer_up());
                                // cmd.insert(insert_pointer_out());
                            }
                            "medium_tom" => {
                                cmd.insert(MediumTom);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::MediumTom));
                                // cmd.insert(insert_pointer_down(47));
                                // cmd.insert(insert_pointer_up());
                                // cmd.insert(insert_pointer_out());
                            }
                            "high_tom" => {
                                cmd.insert(HighTom);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::HighTom));
                                // cmd.insert(insert_pointer_down(50));
                                // cmd.insert(insert_pointer_up());
                                // cmd.insert(insert_pointer_out());
                            }
                            "floor_tom" => {
                                cmd.insert(FloorTom);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::FloorTom));
                                // cmd.insert(insert_pointer_down(41));
                                // cmd.insert(insert_pointer_up());
                                // cmd.insert(insert_pointer_out());
                            }
                            "hi_hats_1" => {
                                cmd.insert(HiHatTop);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::HiHatTopOpen));
                                // cmd.insert(insert_pointer_down(46));
                                // cmd.insert(insert_pointer_up());
                                // cmd.insert(insert_pointer_out());
                            }
                            "hi_hats_2" => {
                                cmd.insert(HiHatBottom);
                                cmd.insert(PercussionInstrumentType(PercussionInstrument::HiHatTopClosed));
                                // cmd.insert(insert_pointer_down(42));
                                // cmd.insert(insert_pointer_up());
                                // cmd.insert(insert_pointer_out());
                            }
                            _ => {}
                        }

                        if let Some(material) = materials.get_mut(mat_handle) {
                            if model_path.0 == MODEL_PATH {
                                high_poly_processed = true;
                            }
                            if model_path.0 == LOW_POLY_MODEL_PATH {
                                low_poly_processed = true;
                            }

                            material.clearcoat = 1.;
                            material.emissive_exposure_weight = -10.;

                            // Set materials
                            match entity_name.as_str() {
                                // Ignore these models
                                "hi_hats_1" | "hi_hats_2" | "crash_cymbal" | "ride_cymbal" | "logo" => {}
                                _ => {
                                    material.base_color = Color::LinearRgba(LinearRgba::new(0.007, 0.007, 0.007, 1.)).into();
                                }
                            }

                            cmd.insert(BaseMeshColor(material.base_color.clone()));
                            cmd.insert(DefaultMeshColor(material.base_color.clone()));
                        }

                        cmd.insert(DrumsMeshType::default()).insert(AnimationState::default());
                    }
                }
            }
        }

        if high_poly_processed && low_poly_processed {
            *setup = true;
        }

        if *setup {
            commands.init_resource::<DrumsMeshLoaded>();
            commands.remove_resource::<MeshSetup>();
        }
    }
}
