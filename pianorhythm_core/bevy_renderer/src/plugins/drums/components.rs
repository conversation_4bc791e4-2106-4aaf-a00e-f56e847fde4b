use bevy::prelude::*;
use enum_as_inner::EnumAsInner;

use pianorhythm_shared::GENERAL_MIDI;
use pianorhythm_shared::GENERAL_MIDI::GMPercussionMap;

#[derive(Default, Debug, Clone, Component)]
pub struct BassDrum;

#[derive(<PERSON><PERSON>ult, Debug, Clone, Component)]
pub struct BassDrumPedal;

#[derive(<PERSON><PERSON><PERSON>, Debu<PERSON>, <PERSON>lone, Component)]
pub struct SnareDrum;

#[derive(<PERSON><PERSON>ult, Debug, Clone, Component)]
pub struct HighTom;

#[derive(Default, Debug, Clone, Component)]
pub struct MediumTom;

#[derive(Default, Debug, Clone, Component)]
pub struct FloorTom;

#[derive(Default, Debug, Clone, Component)]
pub struct RideCymbal;

#[derive(Default, Debug, Clone, Component)]
pub struct CrashCymbal;

#[derive(<PERSON><PERSON><PERSON>, Debu<PERSON>, <PERSON><PERSON>, Component)]
pub struct HiHatTop;

#[derive(De<PERSON>ult, Debug, <PERSON>lone, Component)]
pub struct HiHatBottom;

#[derive(Debug, <PERSON>lone, PartialEq, Eq, Reflect)]
pub(super) enum PercussionInstrument {
    BassDrum,
    BassPedal,
    CrashCymbal,
    HiHatTopOpen,
    HiHatTopClosed,
    SnareDrum,
    RideCymbal,
    HighTom,
    FloorTom,
    MediumTom,
}

#[derive(Debug, Clone, Component, PartialEq, Eq, Reflect)]
pub struct PercussionInstrumentType(pub PercussionInstrument);

#[derive(EnumAsInner)]
pub(super) enum PercussionEntityTypes<'a> {
    Double((Option<EntityMut<'a>>, Option<EntityMut<'a>>)),
    Single(EntityMut<'a>),
}

#[derive(Debug, Clone)]
pub(super) struct InstrumentInfo {
    pub instrument: PercussionInstrument,
    pub act_on_velocity: bool,
}

pub(super) struct PercussionEntity<'a> {
    info: InstrumentInfo,
    entity: PercussionEntityTypes<'a>,
}

impl<'a> PercussionEntity<'a> {
    pub fn new_single_from_tuple(x: (EntityMut<'a>, InstrumentInfo)) -> Self {
        Self { info: x.1, entity: PercussionEntityTypes::Single(x.0) }
    }
}

pub(super) fn get_percussion_info_from_note(note: i32) -> Option<InstrumentInfo> {
    if let Some(val) = GENERAL_MIDI::get_gm_percussion_from_note(note as u8) {
        match val {
            GMPercussionMap::AcousticBassDrum |
            GMPercussionMap::BassDrum1
            => Some(InstrumentInfo { instrument: PercussionInstrument::BassDrum, act_on_velocity: false }),

            GMPercussionMap::CrashCymbal1 |
            GMPercussionMap::CrashCymbal2 |
            GMPercussionMap::SplashCymbal
            => Some(InstrumentInfo { instrument: PercussionInstrument::CrashCymbal, act_on_velocity: false }),

            GMPercussionMap::OpenHiHat
            => Some(InstrumentInfo { instrument: PercussionInstrument::HiHatTopOpen, act_on_velocity: true }),

            GMPercussionMap::ClosedHiHat |
            GMPercussionMap::PedalHiHat
            => Some(InstrumentInfo { instrument: PercussionInstrument::HiHatTopClosed, act_on_velocity: true }),

            GMPercussionMap::AcousticSnare |
            GMPercussionMap::ElectricSnare
            => Some(InstrumentInfo { instrument: PercussionInstrument::SnareDrum, act_on_velocity: false }),

            GMPercussionMap::RideCymbal1 |
            GMPercussionMap::RideCymbal2 |
            GMPercussionMap::ChineseCymbal
            => Some(InstrumentInfo { instrument: PercussionInstrument::RideCymbal, act_on_velocity: false }),

            GMPercussionMap::HighTom
            => Some(InstrumentInfo { instrument: PercussionInstrument::HighTom, act_on_velocity: false }),

            GMPercussionMap::LowTom |
            GMPercussionMap::LowFloorTom |
            GMPercussionMap::HighFloorTom
            => Some(InstrumentInfo { instrument: PercussionInstrument::FloorTom, act_on_velocity: false }),

            GMPercussionMap::LowMidTom |
            GMPercussionMap::HiMidTom
            => Some(InstrumentInfo { instrument: PercussionInstrument::MediumTom, act_on_velocity: false }),

            _ => None,
        }
    } else {
        None
    }
}