use bevy::prelude::*;
use bevy_tweening::{An<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>or, <PERSON>eatStrategy, Tween, TweenCompleted};
use bevy_tweening::lens::{TransformPositionLens, TransformRotationLens, TransformScaleLens};

use pianorhythm_proto::midi_renditions::MidiNoteSource;
use pianorhythm_shared::util;

use crate::{components, resources};
use crate::components::{AllSoundOffTriggered, AnimationState, BaseMeshColor, DrumsMeshType, OnActiveNote};
use crate::components::{DrumsScene, LowPolyModel};
use crate::components::tween_lens::StandardMaterialBaseAndEmissiveColorLens;
use crate::plugins::drums::components::{PercussionInstrument, PercussionInstrumentType};
use crate::resources::DrumsDisplayed;

const TWEEN_DURATION: u64 = 300;

pub fn on_drums_display(
    mut commands: Commands,
    drums_displayed: Res<DrumsDisplayed>,
    scene: Query<(Entity, Option<&LowPolyModel>), With<DrumsScene>>,
    app_settings: Res<resources::ClientAppSettings>,
) {
    for (entity, low_poly) in scene.iter() {
        let mut cmd = commands.entity(entity);

        if drums_displayed.0 {
            let hide_high_poly_model = app_settings.0.GRAPHICS_USE_LOW_POLY_MODELS && low_poly.is_none();
            let hide_low_poly_model = !app_settings.0.GRAPHICS_USE_LOW_POLY_MODELS && low_poly.is_some();

            if hide_high_poly_model || hide_low_poly_model {
                cmd.insert(Visibility::Hidden);
                continue;
            }
        }

        const TARGET_SCALE: f32 = 1.8;
        let v = if drums_displayed.0 { Visibility::Visible } else { Visibility::Hidden };
        cmd.insert(v);

        if drums_displayed.0 {
            let t1 = Tween::new(
                EaseFunction::ElasticOut,
                std::time::Duration::from_millis(100),
                TransformScaleLens {
                    start: Vec3::ZERO,
                    end: Vec3::splat(TARGET_SCALE + 0.1),
                },
            );

            let t2 = Tween::new(
                EaseFunction::ElasticOut,
                std::time::Duration::from_millis(100),
                TransformScaleLens {
                    start: Vec3::splat(TARGET_SCALE + 0.1),
                    end: Vec3::splat(TARGET_SCALE),
                },
            );
            cmd.insert(Animator::new(t1.then(t2)));
        }

        //TODO: Doesn't actually animate on hidden. Figure this out.
        // else {
        //     cmd.insert(Animator::new(
        //         Tween::new(
        //             EaseFunction::ElasticOut,
        //             std::time::Duration::from_millis(100),
        //             TransformScaleLens {
        //                 start: Vec3::splat(TARGET_SCALE + 0.1),
        //                 end: Vec3::ZERO,
        //             },
        //         )));
        // }
    }
}

pub(super) fn on_mesh_animation_down(
    mut commands: Commands,
    query: Query<
        (Entity, &Visibility, &PercussionInstrumentType, &Transform, &BaseMeshColor, &OnActiveNote, &AnimationState),
        (Changed<AnimationState>, With<DrumsMeshType>, Without<AllSoundOffTriggered>)
    >,
    users_list: Res<resources::UserColors>,
    client_socket_id: Res<resources::ClientSocketID>,
    app_settings: Res<resources::ClientAppSettings>,
) {
    if !app_settings.0.GRAPHICS_ENABLE_ANIMATIONS {
        return;
    }

    for (entity, visible, inst_type, transform, base_mesh_color, active_info, state) in query.iter() {
        if visible == Visibility::Hidden {
            continue;
        }

        // TODO: Add particle effects on hit
        if state.is_down {
            let mut socket_id = active_info.0.socket_id.unwrap_or_default();
            if active_info.0.source == MidiNoteSource::MOUSE && active_info.0.socket_id.is_none() {
                socket_id = client_socket_id.0;
            }
            let target_color = users_list.get_color(&socket_id).unwrap_or(Color::NONE);

            let deg_from_vel = util::map_velocity(active_info.0.vel as f32, 0., 127., 0.6, 0.1);
            let rot_from_vel = util::map_velocity(active_info.0.vel as f32, 0., 127., 0.2, 1.);
            let scale_from_deg = Vec3::new(1., deg_from_vel, 1.);

            match inst_type.0 {
                PercussionInstrument::HighTom |
                PercussionInstrument::FloorTom |
                PercussionInstrument::MediumTom |
                PercussionInstrument::SnareDrum |
                PercussionInstrument::BassDrum => {
                    commands.entity(entity)
                        .insert(animate_scale(scale_from_deg));
                }
                PercussionInstrument::RideCymbal |
                PercussionInstrument::CrashCymbal => {
                    commands.entity(entity)
                        .insert(animate_rot(Vec3::new(0., 0., rot_from_vel)));
                }
                PercussionInstrument::HiHatTopOpen => {
                    let trans_from_vel = util::map_velocity(active_info.0.vel as f32, 0., 127., 0.01, 0.02);
                    commands.entity(entity)
                        .insert(animate_trans(Vec3::ZERO, Vec3::new(0., trans_from_vel, 0.)));
                }
                PercussionInstrument::HiHatTopClosed => {
                    commands.entity(entity)
                        .insert(animate_scale(scale_from_deg))
                        .insert(animate_rot(Vec3::new(0., 0., rot_from_vel)));
                }
                _ => {}
            }

            commands.entity(entity)
                .insert(
                    AssetAnimator::new(Tween::new(
                        EaseFunction::Linear,
                        std::time::Duration::from_millis(TWEEN_DURATION),
                        StandardMaterialBaseAndEmissiveColorLens {
                            base_start: target_color,
                            base_end: base_mesh_color.0,
                            emissive_start: target_color,
                            emissive_end: Color::BLACK,
                        },
                    )
                        .with_repeat_strategy(RepeatStrategy::MirroredRepeat)
                        .with_completed_event(101)
                    )
                );
        } else {
            match inst_type.0 {
                PercussionInstrument::HiHatTopOpen => {
                    commands.entity(entity)
                        .insert(animate_trans(transform.translation, Vec3::new(0., 0., 0.)));
                }
                PercussionInstrument::HiHatTopClosed => {
                    commands.entity(entity)
                        .insert(animate_rot(Vec3::new(0., 0., 0.)));
                }
                _ => {}
            }

            // commands.entity(entity)
            //     .remove::<OnActiveNote>();
            //TODO: Work on this more
            // .insert(
            //     AssetAnimator::new(Tween::new(
            //         Linear,
            //         std::time::Duration::from_millis(TWEEN_DURATION),
            //         StandardMaterialBaseAndEmissiveColorLens {
            //             base_start: base_mesh_color.0,
            //             base_end: base_mesh_color.0,
            //             emissive_start: Color::BLACK,
            //             emissive_end: Color::BLACK,
            //         },
            //     ))
            // )
            // .insert(
            //     Animator::new(
            //         Tween::new(
            //             Linear,
            //             std::time::Duration::from_millis(TWEEN_DURATION),
            //             TransformScaleLens { start: Vec3::ONE, end: Vec3::ONE },
            //         )
            //     )
            // )
            // .insert(
            //     Animator::new(
            //         Tween::new(
            //             Linear,
            //             std::time::Duration::from_millis(TWEEN_DURATION),
            //             TransformRotationLens { start: Quat::IDENTITY, end: Quat::IDENTITY },
            //         )
            //     )
            // )
            // .insert(
            //     Animator::new(
            //         Tween::new(
            //             Linear,
            //             std::time::Duration::from_millis(TWEEN_DURATION),
            //             TransformPositionLens { start: Vec3::ZERO, end: Vec3::ZERO },
            //         )
            //     )
            // );
        }
    }
}

pub(super) fn on_tween_on_complete(
    mut commands: Commands,
    mut reader: EventReader<TweenCompleted>,
) {
    for ev in reader.read() {
        if ev.user_data == 100 {
            commands.entity(ev.entity).remove::<Animator<Transform>>();
        }

        if ev.user_data == 101 {
            commands.entity(ev.entity).remove::<AssetAnimator<StandardMaterial>>();
        }
    }
}

/// System is called when a [AllSoundOffTriggered] is added.
pub(in super) fn mesh_on_all_notes_off(
    mut commands: Commands,
    mut query: Query<(Entity, &BaseMeshColor, &MeshMaterial3d<StandardMaterial>, &mut AnimationState), (With<DrumsMeshType>, Added<components::AllSoundOffTriggered>)>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    for (entity, base_mesh_color, material_handle, mut state) in query.iter_mut() {
        state.is_down = false;
        commands.entity(entity)
            .remove::<OnActiveNote>()
            .remove::<Animator<Transform>>()
            .remove::<AssetAnimator<StandardMaterial>>()
            .remove::<components::AllSoundOffTriggered>();

        let Some(material) = materials.get_mut(material_handle) else { continue; };
        material.base_color = base_mesh_color.0.clone();
        material.emissive = Color::BLACK.into();
    }
}

fn animate_scale(scale: Vec3) -> Animator<Transform> {
    Animator::new(
        Tween::new(
            EaseFunction::ElasticOut,
            std::time::Duration::from_millis(TWEEN_DURATION),
            TransformScaleLens {
                start: scale,
                end: Vec3::ONE,
            },
        ).with_repeat_strategy(RepeatStrategy::MirroredRepeat).with_completed_event(100)
    )
}

fn animate_rot(value: Vec3) -> Animator<Transform> {
    Animator::new(
        Tween::new(
            EaseFunction::ElasticOut,
            std::time::Duration::from_millis(TWEEN_DURATION),
            TransformRotationLens {
                start: Quat::from_scaled_axis(value),
                end: Quat::from_scaled_axis(Vec3::ZERO),
            },
        ).with_repeat_strategy(RepeatStrategy::MirroredRepeat).with_completed_event(100)
    )
}

fn animate_trans(start: Vec3, value: Vec3) -> Animator<Transform> {
    Animator::new(
        Tween::new(
            EaseFunction::ElasticOut,
            std::time::Duration::from_millis(TWEEN_DURATION),
            TransformPositionLens {
                start: start,
                end: value,
            },
        ).with_completed_event(100)
    )
}
