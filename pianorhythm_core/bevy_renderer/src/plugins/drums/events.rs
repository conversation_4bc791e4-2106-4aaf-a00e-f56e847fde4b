use bevy::asset::Handle;
use bevy::prelude::*;

use pianorhythm_shared::midi::DRUM_CHANNEL;

use crate::components::{AnimationState, DrumsMeshType, OnActiveNote};
use crate::core::events::ECSSynthEventsAction;
use crate::plugins::drums::components::{get_percussion_info_from_note, PercussionInstrumentType};
use crate::resources::{ClientAppSettings, DrumsDisplayed};

pub fn receiving_events(
    mut commands: Commands,
    mut event_reader: EventReader<ECSSynthEventsAction>,
    mut query: Query<(Entity, &PercussionInstrumentType, &mut AnimationState), (With<Mesh3d>, With<DrumsMeshType>)>,
    client_app_settings: Res<ClientAppSettings>,
    drums_displayed: Res<DrumsDisplayed>,
) {
    if !client_app_settings.0.GRAPHICS_ENABLE_DRUMS {
        return;
    }

    for my_event in event_reader.read() {
        match &my_event {
            ECSSynthEventsAction::NoteOn(data) if data.channel == DRUM_CHANNEL => {
                if !drums_displayed.0 {
                    commands.insert_resource(DrumsDisplayed(true));
                }

                if let Some(info) = get_percussion_info_from_note(data.note as i32) {
                    for (entity, _, mut state) in query.iter_mut().filter(|(_, inst_type, ..)| inst_type.0 == info.instrument) {
                        state.is_down = true;
                        commands.entity(entity)
                            .insert(OnActiveNote(data.clone()));
                    }
                }
            }
            ECSSynthEventsAction::NoteOff(data) if data.channel == DRUM_CHANNEL => {
                if let Some(info) = get_percussion_info_from_note(data.note as i32) {
                    for (_entity, _, mut state) in query.iter_mut().filter(|(_, inst_type, ..)| inst_type.0 == info.instrument) {
                        state.is_down = false;
                    }
                }
            }
            ECSSynthEventsAction::AllNoteOff(channel) if *channel == DRUM_CHANNEL => {
                for (entity, ..) in query.iter() {
                    commands.entity(entity).insert(crate::components::AllSoundOffTriggered);
                }
            }
            _ => {}
        }
    }
}