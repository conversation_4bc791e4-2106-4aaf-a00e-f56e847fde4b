use bevy::prelude::*;
use bevy_asset_loader::loading_state::{LoadingState, LoadingStateAppExt};
use bevy_asset_loader::prelude::{ConfigureLoadingState, StandardDynamicAssetCollection};

use crate::components::{InstrumentType, MainInstrument};
use crate::utils;

mod resources;
mod components;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Hash, States, Reflect)]
enum PluginState {
    #[default]
    AssetLoading,
    Ready,
}

#[derive(Default, Resource)]
struct MeshLoaded;

#[derive(Default, Resource)]
struct MeshSetup;

#[derive(Component)]
struct ModelPath(pub String);

#[derive(Default)]
pub struct GuitarsModelPlugin;

impl Plugin for GuitarsModelPlugin {
    fn build(&self, app: &mut App) {
        app
            .init_state::<PluginState>()
            .add_loading_state(
                LoadingState::new(PluginState::AssetLoading)
                    .continue_to_state(PluginState::Ready)
                    .with_dynamic_assets_file::<StandardDynamicAssetCollection>(
                        utils::get_model_assets_file_path(),
                    )
                    .load_collection::<resources::GuitarModelAssets>(),
            )
            .add_systems(OnEnter(PluginState::Ready), |mut assets_loaded: ResMut<crate::resources::AppAssetsLoaded>| {
                assets_loaded.guitar_models = true;
            })
            .add_systems(OnEnter(PluginState::Ready), load_gltf)
            .add_systems(OnEnter(PluginState::Ready), setup_after_load.after(load_gltf));

        log::info!("Built {:?}", self.name());
    }

    fn cleanup(&self, app: &mut App) {
        app.world_mut().remove_resource::<MeshLoaded>();
    }
}

fn load_gltf(
    mut commands: Commands,
    assets: Res<resources::GuitarModelAssets>,
    assets_gltf: Res<Assets<Gltf>>,
) {
    let Some(high_poly_gltf) = assets_gltf.get(&assets.high_poly_guitars_model) else { return; };

    let handle = high_poly_gltf.scenes[0].clone();

    commands.spawn((
        Name::new("Guitars"),
        MainInstrument(InstrumentType::GUITARS),
        components::GuitarsScene,
        SceneRoot(handle),
        Transform::from_xyz(0., 0., 0.)
            .with_rotation(Quat::from_euler(EulerRot::XYZ, 1.57, 0., 0.))
            .with_scale(Vec3::splat(0.20)),
    ));

    commands.init_resource::<MeshSetup>();
}

fn setup_after_load(
    mut commands: Commands,
    mut setup: Local<bool>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    scene_query: Query<Entity, With<components::GuitarsScene>>,
    mesh_query: Query<(&Name, &MeshMaterial3d<StandardMaterial>), With<Mesh3d>>,
    children: Query<&Children>,
) {
    if !*setup {
        for entity in scene_query.iter() {
            for entity in children.iter_descendants(entity) {
                if let Ok((_name, mat_handle)) = mesh_query.get(entity) {
                    if let Ok(mut _cmd) = commands.get_entity(entity) {
                        *setup = true;

                        if let Some(material) = materials.get_mut(mat_handle) {
                            material.clearcoat = 1.;
                            material.emissive_exposure_weight = -10.;
                        }
                    }
                }
            }
        }
    }

    if *setup {
        commands.init_resource::<MeshLoaded>();
        commands.remove_resource::<MeshSetup>();
    }
}
