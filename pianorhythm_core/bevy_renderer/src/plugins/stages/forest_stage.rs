use bevy::asset::{Assets};
use bevy::math::{<PERSON><PERSON><PERSON><PERSON>ot, Quat, Vec3};
use bevy::pbr::{PointLight, StandardMaterial};
use bevy::prelude::*;

use crate::components::{StageFloor, StagePart};
use crate::plugins::stages::resources::StageModelAssets;
use crate::resources;
use crate::resources::ActiveStageSettings;

pub(super) fn load(
    stage_assets: Res<StageModelAssets>, assets_gltf: Res<Assets<Gltf>>, app_settings: Res<resources::ClientAppSettings>,
    mut active_stage_settings: ResMut<ActiveStageSettings>, mut commands: Commands, mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    ::log::info!("Loading stage: Forest");

    // environment
    commands.insert_resource(ClearColor(Color::srgb_u8(70, 91, 58)));
    active_stage_settings.fog_color = Some(Color::srgb_u8(60, 83, 45));
    active_stage_settings.fog_falloff = FogFalloff::Linear { start: 5., end: 40. };

    // lights
    commands.spawn((
        Name::new("Forest Stage Point Light"),
        PointLight {
            color: Color::srgb_u8(232, 233, 111),
            shadows_enabled: app_settings.0.GRAPHICS_ENABLE_SHADOWS,
            range: 1000.,
            radius: 3.,
            ..default()
        },
        Transform::from_xyz(0.0, 5.0, 0.0),
        StagePart,
    ));

    // stage
    if let Some(stage_gltf) = assets_gltf.get(&stage_assets.forest_stage) {
        commands.spawn((
            Name::new("Forest Stage"),
            StagePart,
            SceneRoot(stage_gltf.scenes[0].clone()),
            Transform::from_xyz(-1.7, 11.5, 4.3)
                .with_rotation(Quat::from_euler(EulerRot::XYZ, 0., 0., 0.))
                .with_scale(Vec3::splat(0.4)),
        ));
    };

    commands.spawn((
        Name::new("Forest floor"),
        Mesh3d(meshes.add(Circle::new(1000.0))),
        MeshMaterial3d(materials.add(StandardMaterial {
            base_color: Color::srgb_u8(156, 196, 115),
            perceptual_roughness: 1.0,
            metallic: 1.0,
            fog_enabled: false,
            ..default()
        })),
        Transform::from_rotation(Quat::from_rotation_x(-std::f32::consts::FRAC_PI_2)).with_translation(Vec3::new(0., -0.2, 0.)),
        StageFloor,
        StagePart,
        // TODO
        //PickingBehavior::IGNORE,
    ));
}
