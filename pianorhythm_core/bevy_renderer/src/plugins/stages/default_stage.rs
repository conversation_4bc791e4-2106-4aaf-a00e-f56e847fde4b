use crate::components::{StageFloor, StagePart};
use crate::resources;
use crate::resources::{ActiveStageSettings, DefaultFogColor};
use bevy::pbr::CascadeShadowConfigBuilder;
use bevy::prelude::*;

pub(super) fn load(
    mut commands: Commands, mut active_stage_settings: ResMut<ActiveStageSettings>, mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>, app_settings: Res<resources::ClientAppSettings>,
) {
    ::log::info!("Loading stage: Default");

    // environment
    active_stage_settings.fog_color = Some(DefaultFogColor::default().0);
    commands.insert_resource(ClearColor(Color::linear_rgb(0.003, 0.003, 0.005)));

    // floor
    commands.spawn((
        Name::new("Stage floor"),
        Mesh3d(meshes.add(Circle::new(1000.0))),
        MeshMaterial3d(materials.add(StandardMaterial {
            base_color: Color::Srgba(Srgba::hex("#363942").unwrap()),
            perceptual_roughness: 1.0,
            metallic: 1.0,
            fog_enabled: false,
            ..default()
        })),
        Transform::from_rotation(Quat::from_rotation_x(-std::f32::consts::FRAC_PI_2)),
        StageFloor,
        StagePart,
        // TODO
        //PickingBehavior::IGNORE,
    ));

    // lights
    commands.spawn((
        Name::new("Stage Point Light"),
        PointLight {
            shadows_enabled: app_settings.0.GRAPHICS_ENABLE_SHADOWS,
            radius: 3.,
            ..default()
        },
        CascadeShadowConfigBuilder {
            num_cascades: 1,
            maximum_distance: 1.6,
            ..default()
        }
        .build(),
        Transform::from_xyz(0.0, 5.0, 0.0),
        StagePart,
    ));
}
