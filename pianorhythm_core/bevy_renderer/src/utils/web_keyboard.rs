use bevy::input::keyboard::{<PERSON>, <PERSON><PERSON><PERSON>, NativeKeyCode};
use bevy::prelude::KeyCode;

pub fn str_to_key_code(kcav: &str) -> KeyCode {
    match kcav {
        "Backquote" => KeyCode::Backquote,
        "Backslash" => KeyCode::Backslash,
        "BracketLeft" => KeyCode::BracketLeft,
        "BracketRight" => KeyCode::BracketRight,
        "Comma" => KeyCode::Comma,
        "Digit0" => KeyCode::Digit0,
        "Digit1" => KeyCode::Digit1,
        "Digit2" => KeyCode::Digit2,
        "Digit3" => KeyCode::Digit3,
        "Digit4" => KeyCode::Digit4,
        "Digit5" => KeyCode::Digit5,
        "Digit6" => KeyCode::Digit6,
        "Digit7" => KeyCode::Digit7,
        "Digit8" => KeyCode::Digit8,
        "Digit9" => KeyCode::Digit9,
        "Equal" => KeyCode::Equal,
        "IntlBackslash" => KeyCode::IntlBackslash,
        "IntlRo" => KeyCode::IntlRo,
        "IntlYen" => KeyCode::IntlYen,
        "KeyA" => KeyCode::KeyA,
        "KeyB" => KeyCode::KeyB,
        "KeyC" => KeyCode::KeyC,
        "KeyD" => KeyCode::KeyD,
        "KeyE" => KeyCode::KeyE,
        "KeyF" => KeyCode::KeyF,
        "KeyG" => KeyCode::KeyG,
        "KeyH" => KeyCode::KeyH,
        "KeyI" => KeyCode::KeyI,
        "KeyJ" => KeyCode::KeyJ,
        "KeyK" => KeyCode::KeyK,
        "KeyL" => KeyCode::KeyL,
        "KeyM" => KeyCode::KeyM,
        "KeyN" => KeyCode::KeyN,
        "KeyO" => KeyCode::KeyO,
        "KeyP" => KeyCode::KeyP,
        "KeyQ" => KeyCode::KeyQ,
        "KeyR" => KeyCode::KeyR,
        "KeyS" => KeyCode::KeyS,
        "KeyT" => KeyCode::KeyT,
        "KeyU" => KeyCode::KeyU,
        "KeyV" => KeyCode::KeyV,
        "KeyW" => KeyCode::KeyW,
        "KeyX" => KeyCode::KeyX,
        "KeyY" => KeyCode::KeyY,
        "KeyZ" => KeyCode::KeyZ,
        "Minus" => KeyCode::Minus,
        "Period" => KeyCode::Period,
        "Quote" => KeyCode::Quote,
        "Semicolon" => KeyCode::Semicolon,
        "Slash" => KeyCode::Slash,
        "AltLeft" => KeyCode::AltLeft,
        "AltRight" => KeyCode::AltRight,
        "Backspace" => KeyCode::Backspace,
        "CapsLock" => KeyCode::CapsLock,
        "ContextMenu" => KeyCode::ContextMenu,
        "ControlLeft" => KeyCode::ControlLeft,
        "ControlRight" => KeyCode::ControlRight,
        "Enter" => KeyCode::Enter,
        "MetaLeft" => KeyCode::SuperLeft,
        "MetaRight" => KeyCode::SuperRight,
        "ShiftLeft" => KeyCode::ShiftLeft,
        "ShiftRight" => KeyCode::ShiftRight,
        "Space" => KeyCode::Space,
        "Tab" => KeyCode::Tab,
        "Convert" => KeyCode::Convert,
        "KanaMode" => KeyCode::KanaMode,
        "Lang1" => KeyCode::Lang1,
        "Lang2" => KeyCode::Lang2,
        "Lang3" => KeyCode::Lang3,
        "Lang4" => KeyCode::Lang4,
        "Lang5" => KeyCode::Lang5,
        "NonConvert" => KeyCode::NonConvert,
        "Delete" => KeyCode::Delete,
        "End" => KeyCode::End,
        "Help" => KeyCode::Help,
        "Home" => KeyCode::Home,
        "Insert" => KeyCode::Insert,
        "PageDown" => KeyCode::PageDown,
        "PageUp" => KeyCode::PageUp,
        "ArrowDown" => KeyCode::ArrowDown,
        "ArrowLeft" => KeyCode::ArrowLeft,
        "ArrowRight" => KeyCode::ArrowRight,
        "ArrowUp" => KeyCode::ArrowUp,
        "NumLock" => KeyCode::NumLock,
        "Numpad0" => KeyCode::Numpad0,
        "Numpad1" => KeyCode::Numpad1,
        "Numpad2" => KeyCode::Numpad2,
        "Numpad3" => KeyCode::Numpad3,
        "Numpad4" => KeyCode::Numpad4,
        "Numpad5" => KeyCode::Numpad5,
        "Numpad6" => KeyCode::Numpad6,
        "Numpad7" => KeyCode::Numpad7,
        "Numpad8" => KeyCode::Numpad8,
        "Numpad9" => KeyCode::Numpad9,
        "NumpadAdd" => KeyCode::NumpadAdd,
        "NumpadBackspace" => KeyCode::NumpadBackspace,
        "NumpadClear" => KeyCode::NumpadClear,
        "NumpadClearEntry" => KeyCode::NumpadClearEntry,
        "NumpadComma" => KeyCode::NumpadComma,
        "NumpadDecimal" => KeyCode::NumpadDecimal,
        "NumpadDivide" => KeyCode::NumpadDivide,
        "NumpadEnter" => KeyCode::NumpadEnter,
        "NumpadEqual" => KeyCode::NumpadEqual,
        "NumpadHash" => KeyCode::NumpadHash,
        "NumpadMemoryAdd" => KeyCode::NumpadMemoryAdd,
        "NumpadMemoryClear" => KeyCode::NumpadMemoryClear,
        "NumpadMemoryRecall" => KeyCode::NumpadMemoryRecall,
        "NumpadMemoryStore" => KeyCode::NumpadMemoryStore,
        "NumpadMemorySubtract" => KeyCode::NumpadMemorySubtract,
        "NumpadMultiply" => KeyCode::NumpadMultiply,
        "NumpadParenLeft" => KeyCode::NumpadParenLeft,
        "NumpadParenRight" => KeyCode::NumpadParenRight,
        "NumpadStar" => KeyCode::NumpadStar,
        "NumpadSubtract" => KeyCode::NumpadSubtract,
        "Escape" => KeyCode::Escape,
        "Fn" => KeyCode::Fn,
        "FnLock" => KeyCode::FnLock,
        "PrintScreen" => KeyCode::PrintScreen,
        "ScrollLock" => KeyCode::ScrollLock,
        "Pause" => KeyCode::Pause,
        "BrowserBack" => KeyCode::BrowserBack,
        "BrowserFavorites" => KeyCode::BrowserFavorites,
        "BrowserForward" => KeyCode::BrowserForward,
        "BrowserHome" => KeyCode::BrowserHome,
        "BrowserRefresh" => KeyCode::BrowserRefresh,
        "BrowserSearch" => KeyCode::BrowserSearch,
        "BrowserStop" => KeyCode::BrowserStop,
        "Eject" => KeyCode::Eject,
        "LaunchApp1" => KeyCode::LaunchApp1,
        "LaunchApp2" => KeyCode::LaunchApp2,
        "LaunchMail" => KeyCode::LaunchMail,
        "MediaPlayPause" => KeyCode::MediaPlayPause,
        "MediaSelect" => KeyCode::MediaSelect,
        "MediaStop" => KeyCode::MediaStop,
        "MediaTrackNext" => KeyCode::MediaTrackNext,
        "MediaTrackPrevious" => KeyCode::MediaTrackPrevious,
        "Power" => KeyCode::Power,
        "Sleep" => KeyCode::Sleep,
        "AudioVolumeDown" => KeyCode::AudioVolumeDown,
        "AudioVolumeMute" => KeyCode::AudioVolumeMute,
        "AudioVolumeUp" => KeyCode::AudioVolumeUp,
        "WakeUp" => KeyCode::WakeUp,
        "Hyper" => KeyCode::Hyper,
        "Turbo" => KeyCode::Turbo,
        "Abort" => KeyCode::Abort,
        "Resume" => KeyCode::Resume,
        "Suspend" => KeyCode::Suspend,
        "Again" => KeyCode::Again,
        "Copy" => KeyCode::Copy,
        "Cut" => KeyCode::Cut,
        "Find" => KeyCode::Find,
        "Open" => KeyCode::Open,
        "Paste" => KeyCode::Paste,
        "Props" => KeyCode::Props,
        "Select" => KeyCode::Select,
        "Undo" => KeyCode::Undo,
        "Hiragana" => KeyCode::Hiragana,
        "Katakana" => KeyCode::Katakana,
        "F1" => KeyCode::F1,
        "F2" => KeyCode::F2,
        "F3" => KeyCode::F3,
        "F4" => KeyCode::F4,
        "F5" => KeyCode::F5,
        "F6" => KeyCode::F6,
        "F7" => KeyCode::F7,
        "F8" => KeyCode::F8,
        "F9" => KeyCode::F9,
        "F10" => KeyCode::F10,
        "F11" => KeyCode::F11,
        "F12" => KeyCode::F12,
        "F13" => KeyCode::F13,
        "F14" => KeyCode::F14,
        "F15" => KeyCode::F15,
        "F16" => KeyCode::F16,
        "F17" => KeyCode::F17,
        "F18" => KeyCode::F18,
        "F19" => KeyCode::F19,
        "F20" => KeyCode::F20,
        "F21" => KeyCode::F21,
        "F22" => KeyCode::F22,
        "F23" => KeyCode::F23,
        "F24" => KeyCode::F24,
        "F25" => KeyCode::F25,
        "F26" => KeyCode::F26,
        "F27" => KeyCode::F27,
        "F28" => KeyCode::F28,
        "F29" => KeyCode::F29,
        "F30" => KeyCode::F30,
        "F31" => KeyCode::F31,
        "F32" => KeyCode::F32,
        "F33" => KeyCode::F33,
        "F34" => KeyCode::F34,
        "F35" => KeyCode::F35,
        _ => KeyCode::Unidentified(NativeKeyCode::Unidentified)
    }
}

pub fn str_to_key(kav: &str) -> Key {
    match kav {
        "Unidentified" => return Key::Unidentified(NativeKey::Web(kav.into())),
        "Dead" => return Key::Dead(None),
        "Alt" => Key::Alt,
        "AltGraph" => Key::AltGraph,
        "CapsLock" => Key::CapsLock,
        "Control" => Key::Control,
        "Fn" => Key::Fn,
        "FnLock" => Key::FnLock,
        "NumLock" => Key::NumLock,
        "ScrollLock" => Key::ScrollLock,
        "Shift" => Key::Shift,
        "Symbol" => Key::Symbol,
        "SymbolLock" => Key::SymbolLock,
        "Hyper" => Key::Hyper,
        "Meta" => Key::Super,
        "Enter" => Key::Enter,
        "Tab" => Key::Tab,
        " " => Key::Space,
        "ArrowDown" => Key::ArrowDown,
        "ArrowLeft" => Key::ArrowLeft,
        "ArrowRight" => Key::ArrowRight,
        "ArrowUp" => Key::ArrowUp,
        "End" => Key::End,
        "Home" => Key::Home,
        "PageDown" => Key::PageDown,
        "PageUp" => Key::PageUp,
        "Backspace" => Key::Backspace,
        "Clear" => Key::Clear,
        "Copy" => Key::Copy,
        "CrSel" => Key::CrSel,
        "Cut" => Key::Cut,
        "Delete" => Key::Delete,
        "EraseEof" => Key::EraseEof,
        "ExSel" => Key::ExSel,
        "Insert" => Key::Insert,
        "Paste" => Key::Paste,
        "Redo" => Key::Redo,
        "Undo" => Key::Undo,
        "Accept" => Key::Accept,
        "Again" => Key::Again,
        "Attn" => Key::Attn,
        "Cancel" => Key::Cancel,
        "ContextMenu" => Key::ContextMenu,
        "Escape" => Key::Escape,
        "Execute" => Key::Execute,
        "Find" => Key::Find,
        "Help" => Key::Help,
        "Pause" => Key::Pause,
        "Play" => Key::Play,
        "Props" => Key::Props,
        "Select" => Key::Select,
        "ZoomIn" => Key::ZoomIn,
        "ZoomOut" => Key::ZoomOut,
        "BrightnessDown" => Key::BrightnessDown,
        "BrightnessUp" => Key::BrightnessUp,
        "Eject" => Key::Eject,
        "LogOff" => Key::LogOff,
        "Power" => Key::Power,
        "PowerOff" => Key::PowerOff,
        "PrintScreen" => Key::PrintScreen,
        "Hibernate" => Key::Hibernate,
        "Standby" => Key::Standby,
        "WakeUp" => Key::WakeUp,
        "AllCandidates" => Key::AllCandidates,
        "Alphanumeric" => Key::Alphanumeric,
        "CodeInput" => Key::CodeInput,
        "Compose" => Key::Compose,
        "Convert" => Key::Convert,
        "FinalMode" => Key::FinalMode,
        "GroupFirst" => Key::GroupFirst,
        "GroupLast" => Key::GroupLast,
        "GroupNext" => Key::GroupNext,
        "GroupPrevious" => Key::GroupPrevious,
        "ModeChange" => Key::ModeChange,
        "NextCandidate" => Key::NextCandidate,
        "NonConvert" => Key::NonConvert,
        "PreviousCandidate" => Key::PreviousCandidate,
        "Process" => Key::Process,
        "SingleCandidate" => Key::SingleCandidate,
        "HangulMode" => Key::HangulMode,
        "HanjaMode" => Key::HanjaMode,
        "JunjaMode" => Key::JunjaMode,
        "Eisu" => Key::Eisu,
        "Hankaku" => Key::Hankaku,
        "Hiragana" => Key::Hiragana,
        "HiraganaKatakana" => Key::HiraganaKatakana,
        "KanaMode" => Key::KanaMode,
        "KanjiMode" => Key::KanjiMode,
        "Katakana" => Key::Katakana,
        "Romaji" => Key::Romaji,
        "Zenkaku" => Key::Zenkaku,
        "ZenkakuHankaku" => Key::ZenkakuHankaku,
        "Soft1" => Key::Soft1,
        "Soft2" => Key::Soft2,
        "Soft3" => Key::Soft3,
        "Soft4" => Key::Soft4,
        "ChannelDown" => Key::ChannelDown,
        "ChannelUp" => Key::ChannelUp,
        "Close" => Key::Close,
        "MailForward" => Key::MailForward,
        "MailReply" => Key::MailReply,
        "MailSend" => Key::MailSend,
        "MediaClose" => Key::MediaClose,
        "MediaFastForward" => Key::MediaFastForward,
        "MediaPause" => Key::MediaPause,
        "MediaPlay" => Key::MediaPlay,
        "MediaPlayPause" => Key::MediaPlayPause,
        "MediaRecord" => Key::MediaRecord,
        "MediaRewind" => Key::MediaRewind,
        "MediaStop" => Key::MediaStop,
        "MediaTrackNext" => Key::MediaTrackNext,
        "MediaTrackPrevious" => Key::MediaTrackPrevious,
        "New" => Key::New,
        "Open" => Key::Open,
        "Print" => Key::Print,
        "Save" => Key::Save,
        "SpellCheck" => Key::SpellCheck,
        "Key11" => Key::Key11,
        "Key12" => Key::Key12,
        "AudioBalanceLeft" => Key::AudioBalanceLeft,
        "AudioBalanceRight" => Key::AudioBalanceRight,
        "AudioBassBoostDown" => Key::AudioBassBoostDown,
        "AudioBassBoostToggle" => Key::AudioBassBoostToggle,
        "AudioBassBoostUp" => Key::AudioBassBoostUp,
        "AudioFaderFront" => Key::AudioFaderFront,
        "AudioFaderRear" => Key::AudioFaderRear,
        "AudioSurroundModeNext" => Key::AudioSurroundModeNext,
        "AudioTrebleDown" => Key::AudioTrebleDown,
        "AudioTrebleUp" => Key::AudioTrebleUp,
        "AudioVolumeDown" => Key::AudioVolumeDown,
        "AudioVolumeUp" => Key::AudioVolumeUp,
        "AudioVolumeMute" => Key::AudioVolumeMute,
        "MicrophoneToggle" => Key::MicrophoneToggle,
        "MicrophoneVolumeDown" => Key::MicrophoneVolumeDown,
        "MicrophoneVolumeUp" => Key::MicrophoneVolumeUp,
        "MicrophoneVolumeMute" => Key::MicrophoneVolumeMute,
        "SpeechCorrectionList" => Key::SpeechCorrectionList,
        "SpeechInputToggle" => Key::SpeechInputToggle,
        "LaunchApplication1" => Key::LaunchApplication1,
        "LaunchApplication2" => Key::LaunchApplication2,
        "LaunchCalendar" => Key::LaunchCalendar,
        "LaunchContacts" => Key::LaunchContacts,
        "LaunchMail" => Key::LaunchMail,
        "LaunchMediaPlayer" => Key::LaunchMediaPlayer,
        "LaunchMusicPlayer" => Key::LaunchMusicPlayer,
        "LaunchPhone" => Key::LaunchPhone,
        "LaunchScreenSaver" => Key::LaunchScreenSaver,
        "LaunchSpreadsheet" => Key::LaunchSpreadsheet,
        "LaunchWebBrowser" => Key::LaunchWebBrowser,
        "LaunchWebCam" => Key::LaunchWebCam,
        "LaunchWordProcessor" => Key::LaunchWordProcessor,
        "BrowserBack" => Key::BrowserBack,
        "BrowserFavorites" => Key::BrowserFavorites,
        "BrowserForward" => Key::BrowserForward,
        "BrowserHome" => Key::BrowserHome,
        "BrowserRefresh" => Key::BrowserRefresh,
        "BrowserSearch" => Key::BrowserSearch,
        "BrowserStop" => Key::BrowserStop,
        "AppSwitch" => Key::AppSwitch,
        "Call" => Key::Call,
        "Camera" => Key::Camera,
        "CameraFocus" => Key::CameraFocus,
        "EndCall" => Key::EndCall,
        "GoBack" => Key::GoBack,
        "GoHome" => Key::GoHome,
        "HeadsetHook" => Key::HeadsetHook,
        "LastNumberRedial" => Key::LastNumberRedial,
        "Notification" => Key::Notification,
        "MannerMode" => Key::MannerMode,
        "VoiceDial" => Key::VoiceDial,
        "TV" => Key::TV,
        "TV3DMode" => Key::TV3DMode,
        "TVAntennaCable" => Key::TVAntennaCable,
        "TVAudioDescription" => Key::TVAudioDescription,
        "TVAudioDescriptionMixDown" => Key::TVAudioDescriptionMixDown,
        "TVAudioDescriptionMixUp" => Key::TVAudioDescriptionMixUp,
        "TVContentsMenu" => Key::TVContentsMenu,
        "TVDataService" => Key::TVDataService,
        "TVInput" => Key::TVInput,
        "TVInputComponent1" => Key::TVInputComponent1,
        "TVInputComponent2" => Key::TVInputComponent2,
        "TVInputComposite1" => Key::TVInputComposite1,
        "TVInputComposite2" => Key::TVInputComposite2,
        "TVInputHDMI1" => Key::TVInputHDMI1,
        "TVInputHDMI2" => Key::TVInputHDMI2,
        "TVInputHDMI3" => Key::TVInputHDMI3,
        "TVInputHDMI4" => Key::TVInputHDMI4,
        "TVInputVGA1" => Key::TVInputVGA1,
        "TVMediaContext" => Key::TVMediaContext,
        "TVNetwork" => Key::TVNetwork,
        "TVNumberEntry" => Key::TVNumberEntry,
        "TVPower" => Key::TVPower,
        "TVRadioService" => Key::TVRadioService,
        "TVSatellite" => Key::TVSatellite,
        "TVSatelliteBS" => Key::TVSatelliteBS,
        "TVSatelliteCS" => Key::TVSatelliteCS,
        "TVSatelliteToggle" => Key::TVSatelliteToggle,
        "TVTerrestrialAnalog" => Key::TVTerrestrialAnalog,
        "TVTerrestrialDigital" => Key::TVTerrestrialDigital,
        "TVTimer" => Key::TVTimer,
        "AVRInput" => Key::AVRInput,
        "AVRPower" => Key::AVRPower,
        "ColorF0Red" => Key::ColorF0Red,
        "ColorF1Green" => Key::ColorF1Green,
        "ColorF2Yellow" => Key::ColorF2Yellow,
        "ColorF3Blue" => Key::ColorF3Blue,
        "ColorF4Grey" => Key::ColorF4Grey,
        "ColorF5Brown" => Key::ColorF5Brown,
        "ClosedCaptionToggle" => Key::ClosedCaptionToggle,
        "Dimmer" => Key::Dimmer,
        "DisplaySwap" => Key::DisplaySwap,
        "DVR" => Key::DVR,
        "Exit" => Key::Exit,
        "FavoriteClear0" => Key::FavoriteClear0,
        "FavoriteClear1" => Key::FavoriteClear1,
        "FavoriteClear2" => Key::FavoriteClear2,
        "FavoriteClear3" => Key::FavoriteClear3,
        "FavoriteRecall0" => Key::FavoriteRecall0,
        "FavoriteRecall1" => Key::FavoriteRecall1,
        "FavoriteRecall2" => Key::FavoriteRecall2,
        "FavoriteRecall3" => Key::FavoriteRecall3,
        "FavoriteStore0" => Key::FavoriteStore0,
        "FavoriteStore1" => Key::FavoriteStore1,
        "FavoriteStore2" => Key::FavoriteStore2,
        "FavoriteStore3" => Key::FavoriteStore3,
        "Guide" => Key::Guide,
        "GuideNextDay" => Key::GuideNextDay,
        "GuidePreviousDay" => Key::GuidePreviousDay,
        "Info" => Key::Info,
        "InstantReplay" => Key::InstantReplay,
        "Link" => Key::Link,
        "ListProgram" => Key::ListProgram,
        "LiveContent" => Key::LiveContent,
        "Lock" => Key::Lock,
        "MediaApps" => Key::MediaApps,
        "MediaAudioTrack" => Key::MediaAudioTrack,
        "MediaLast" => Key::MediaLast,
        "MediaSkipBackward" => Key::MediaSkipBackward,
        "MediaSkipForward" => Key::MediaSkipForward,
        "MediaStepBackward" => Key::MediaStepBackward,
        "MediaStepForward" => Key::MediaStepForward,
        "MediaTopMenu" => Key::MediaTopMenu,
        "NavigateIn" => Key::NavigateIn,
        "NavigateNext" => Key::NavigateNext,
        "NavigateOut" => Key::NavigateOut,
        "NavigatePrevious" => Key::NavigatePrevious,
        "NextFavoriteChannel" => Key::NextFavoriteChannel,
        "NextUserProfile" => Key::NextUserProfile,
        "OnDemand" => Key::OnDemand,
        "Pairing" => Key::Pairing,
        "PinPDown" => Key::PinPDown,
        "PinPMove" => Key::PinPMove,
        "PinPToggle" => Key::PinPToggle,
        "PinPUp" => Key::PinPUp,
        "PlaySpeedDown" => Key::PlaySpeedDown,
        "PlaySpeedReset" => Key::PlaySpeedReset,
        "PlaySpeedUp" => Key::PlaySpeedUp,
        "RandomToggle" => Key::RandomToggle,
        "RcLowBattery" => Key::RcLowBattery,
        "RecordSpeedNext" => Key::RecordSpeedNext,
        "RfBypass" => Key::RfBypass,
        "ScanChannelsToggle" => Key::ScanChannelsToggle,
        "ScreenModeNext" => Key::ScreenModeNext,
        "Settings" => Key::Settings,
        "SplitScreenToggle" => Key::SplitScreenToggle,
        "STBInput" => Key::STBInput,
        "STBPower" => Key::STBPower,
        "Subtitle" => Key::Subtitle,
        "Teletext" => Key::Teletext,
        "VideoModeNext" => Key::VideoModeNext,
        "Wink" => Key::Wink,
        "ZoomToggle" => Key::ZoomToggle,
        "F1" => Key::F1,
        "F2" => Key::F2,
        "F3" => Key::F3,
        "F4" => Key::F4,
        "F5" => Key::F5,
        "F6" => Key::F6,
        "F7" => Key::F7,
        "F8" => Key::F8,
        "F9" => Key::F9,
        "F10" => Key::F10,
        "F11" => Key::F11,
        "F12" => Key::F12,
        "F13" => Key::F13,
        "F14" => Key::F14,
        "F15" => Key::F15,
        "F16" => Key::F16,
        "F17" => Key::F17,
        "F18" => Key::F18,
        "F19" => Key::F19,
        "F20" => Key::F20,
        "F21" => Key::F21,
        "F22" => Key::F22,
        "F23" => Key::F23,
        "F24" => Key::F24,
        "F25" => Key::F25,
        "F26" => Key::F26,
        "F27" => Key::F27,
        "F28" => Key::F28,
        "F29" => Key::F29,
        "F30" => Key::F30,
        "F31" => Key::F31,
        "F32" => Key::F32,
        "F33" => Key::F33,
        "F34" => Key::F34,
        "F35" => Key::F35,
        string => return Key::Character(string.into()),
    }
}
