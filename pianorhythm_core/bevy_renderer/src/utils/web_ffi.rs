use bevy::app::PluginsState;
use bevy::ecs::system::SystemState;
use bevy::input::mouse::MouseScrollUnit;
use bevy::input::touch::TouchPhase;
use bevy::input::ButtonState;
use bevy::math::DVec2;
use bevy::prelude::*;
#[cfg(not(target_arch = "wasm32"))]
use bevy::tasks::tick_global_task_pools_on_main_thread;
use bevy::window::{
    CursorEntered, CursorLeft, WindowBackendScaleFactorChanged, WindowFocused, WindowOccluded, WindowResized, WindowScaleFactorChanged
};
use std::sync::atomic::Ordering;

#[cfg(target_arch = "wasm32")]
use wasm_bindgen::prelude::*;

use crate::app::root_app;
#[cfg(target_arch = "wasm32")]
use crate::create_canvas_window;
use crate::utils::web_keyboard;
#[cfg(target_arch = "wasm32")]
use crate::{canvas::*, canvas_view};
use crate::{plugins, WorkerApp};

fn create_worker_app(
    #[cfg(target_arch = "wasm32")]
    synth_events_shared_buffer: Option<js_sys::SharedArrayBuffer>
) -> WorkerApp {
    #[cfg(target_arch = "wasm32")]
    let app = root_app(synth_events_shared_buffer);

    #[cfg(not(target_arch = "wasm32"))]
    let app = root_app();

    WorkerApp::new(app)
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn create_app(disable_canvas_view: bool, synth_events_shared_buffer: Option<js_sys::SharedArrayBuffer>) -> u64 {
    let mut app = create_worker_app(synth_events_shared_buffer);

    if !disable_canvas_view {
        app.add_plugins(canvas_view::CanvasViewPlugin);
    }

    Box::into_raw(Box::new(app)) as u64
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn start_app(ptr: u64) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };
    plugins::SHOULD_EXIT.store(false, Ordering::SeqCst);
    app.run();
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn create_window_by_canvas(ptr: u64, canvas_id: &str, scale_factor: f32) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };
    app.scale_factor = scale_factor;

    let canvas = Canvas::new(canvas_id, 1);
    let view_obj = ViewObj::from_canvas(canvas);

    create_window(app, view_obj, false);
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn create_window_by_offscreen_canvas(ptr: u64, canvas: web_sys::OffscreenCanvas, scale_factor: f32) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };
    app.scale_factor = scale_factor;

    let offscreen_canvas = OffscreenCanvas::new(canvas, scale_factor, 1);
    let view_obj = ViewObj::from_offscreen_canvas(offscreen_canvas);

    create_window(app, view_obj, true);
}

#[cfg(target_arch = "wasm32")]
fn create_window(app: &mut WorkerApp, view_obj: ViewObj, _is_in_worker: bool) {
    app.insert_non_send_resource(view_obj);
    create_canvas_window(app);
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn is_preparation_completed(ptr: u64) -> u32 {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    if app.plugins_ready {
        return 1;
    }

    if app.plugins_state() == PluginsState::Ready {
        app.finish();
        app.cleanup();

        let mut windows_system_state: SystemState<Query<(Entity, &Window)>> = SystemState::from_world(app.world_mut());

        let Ok((entity, _)) = windows_system_state.get(app.world_mut()).single() else {
            return 0;
        };
        app.window = entity;
        app.plugins_ready = true;

        return 1;
    }

    0
}

#[cfg(not(target_arch = "wasm32"))]
pub fn prepare_app(ptr: u64) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    if app.plugins_state() != PluginsState::Ready {
        tick_global_task_pools_on_main_thread();
    } else {
        app.finish();
        app.cleanup();
    }
}

#[cfg(not(target_arch = "wasm32"))]
pub fn check_preparation_completed(ptr: u64) -> u32 {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    if app.plugins_ready {
        return 1;
    }

    if app.plugins_state() == PluginsState::Ready {
        let mut windows_system_state: SystemState<Query<(Entity, &Window)>> = SystemState::from_world(app.world_mut());

        let Ok((entity, _)) = windows_system_state.get(app.world_mut()).single() else {
            return 0;
        };
        app.window = entity;
        app.plugins_ready = true;

        return 1;
    }

    0
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn window_focused(ptr: u64, focused: bool) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();
    app.world_mut().send_event(WindowFocused { window, focused });
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn window_occluded(ptr: u64, occluded: bool) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();
    app.world_mut().send_event(WindowOccluded { window, occluded });
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn window_cursor_entered(ptr: u64) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();
    app.world_mut().send_event(CursorEntered { window });
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn window_cursor_left(ptr: u64) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();
    app.world_mut().send_event(CursorLeft { window });
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn canvas_keyboard_focus_lost(ptr: u64) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };
    app.world_mut().send_event(bevy::input::keyboard::KeyboardFocusLost);
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn mouse_move(ptr: u64, offset_x: f32, offset_y: f32, delta_x: f32, delta_y: f32) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let cursor_move = CursorMoved {
        window: app.window.clone(),
        position: Vec2::new(offset_x, offset_y),
        delta: Some(Vec2::new(delta_x, delta_y)),
    };
    app.world_mut()
        .send_event(bevy::window::WindowEvent::CursorMoved(cursor_move));
    app.world_mut().send_event(bevy::input::mouse::MouseMotion {
        delta: Vec2::new(delta_x, delta_y),
    });
    // app.world_mut().send_event(bevy::window::WindowEvent::MouseButtonInput(mouse_event));
}

fn get_button_from_index(index: usize) -> MouseButton {
    match index {
        0 => MouseButton::Left,
        1 => MouseButton::Middle,
        2 => MouseButton::Right,
        _ => MouseButton::Other(index as u16),
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn mouse_bt_down(ptr: u64, index: usize, x: f32, y: f32) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };
    let window = app.window.clone();

    let scale_factor = app.scale_factor;
    let mut windows_state: SystemState<Query<&mut Window>> = SystemState::from_world(app.world_mut());

    let mut windows = windows_state.get_mut(app.world_mut());
    if let Ok(mut window) = windows.get_mut(window.clone()) {
        window.set_physical_cursor_position(Some(DVec2::new((x * scale_factor) as f64, (y * scale_factor) as f64)));
        // window.set_cursor_position(Some(Vec2::new(x * scale_factor, y * scale_factor)));
    }

    let mouse_event = bevy::input::mouse::MouseButtonInput {
        button: get_button_from_index(index),
        state: ButtonState::Pressed,
        window: window.clone(),
    };

    app.world_mut().send_event(mouse_event.clone());
    app.world_mut()
        .send_event(bevy::window::WindowEvent::MouseButtonInput(mouse_event));

    //     log::info!("mouse_bt_down {:?} | {:?} | {:?}", x, y, true);
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn mouse_bt_up(ptr: u64, index: usize) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();

    let mouse_event = bevy::input::mouse::MouseButtonInput {
        button: get_button_from_index(index),
        state: ButtonState::Released,
        window: window,
    };

    app.world_mut().send_event(mouse_event.clone());
    app.world_mut()
        .send_event(bevy::window::WindowEvent::MouseButtonInput(mouse_event));
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn mouse_wheel(ptr: u64, delta_x: f32, delta_y: f32, _delta_z: f32) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();
    app.world_mut().send_event(bevy::input::mouse::MouseWheel {
        unit: MouseScrollUnit::Pixel,
        x: delta_x,
        y: delta_y,
        window,
    });
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn key_down(ptr: u64, code: &str, key: &str) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();
    app.world_mut().send_event(bevy::input::keyboard::KeyboardInput {
        key_code: web_keyboard::str_to_key_code(code),
        logical_key: web_keyboard::str_to_key(key),
        state: ButtonState::Pressed,
        window: window.clone(),
        repeat: false,
        text: None,
    });
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn key_up(ptr: u64, code: &str, key: &str) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();
    app.world_mut().send_event(bevy::input::keyboard::KeyboardInput {
        key_code: web_keyboard::str_to_key_code(code),
        logical_key: web_keyboard::str_to_key(key),
        state: ButtonState::Released,
        window: window.clone(),
        repeat: false,
        text: None,
    });
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn touch_end(ptr: u64) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();
    app.world_mut().send_event(bevy::input::touch::TouchInput {
        id: 0,
        phase: TouchPhase::Ended,
        position: Vec2::default(),
        force: None,
        window,
    });
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn touch_canceled(ptr: u64) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };

    let window = app.window.clone();
    app.world_mut().send_event(bevy::input::touch::TouchInput {
        id: 0,
        phase: TouchPhase::Canceled,
        position: Vec2::default(),
        force: None,
        window,
    });
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn window_resize(ptr: u64, physical_width: f32, physical_height: f32, _logical_width: f32, _logical_height: f32) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };
    let scale_factor = app.scale_factor;

    let mut windows_state: SystemState<Query<(Entity, &mut Window)>> = SystemState::from_world(app.world_mut());

    let mut windows = windows_state.get_mut(app.world_mut());
    let mut resize_event: Option<WindowResized> = None;
    if let Some((entity, mut window)) = windows.iter_mut().last() {
        window.resolution.set(physical_width, physical_height);
        // .set_physical_resolution(physical_width as u32, physical_height as u32);

        resize_event = Some(WindowResized {
            width: window.width(),
            height: window.height(),
            window: entity,
        });
    }

    if let Some(event) = resize_event {
        app.world_mut().send_event(event);
    }

    scale_factor_change(ptr, scale_factor);
    windows_state.apply(app.world_mut());
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn scale_factor_change(ptr: u64, scale_factor: f32) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };
    app.scale_factor = scale_factor;

    let mut world_mut = app.world_mut();
    let mut windows_state: SystemState<Query<(Entity, &mut Window)>> = SystemState::from_world(&mut world_mut);

    let mut windows = windows_state.get_mut(&mut world_mut);
    let Some((entity, mut window)) = windows.iter_mut().last() else {
        return;
    };

    window.resolution.set_scale_factor(scale_factor as f32);

    let prior_factor = window.resolution.scale_factor();
    let scale_factor_override = window.resolution.scale_factor_override();

    world_mut.send_event(WindowBackendScaleFactorChanged {
        window: entity.clone(),
        scale_factor: scale_factor as f64,
    });

    if scale_factor_override.is_none() && (scale_factor as f32 != prior_factor) {
        app.world_mut().send_event(WindowScaleFactorChanged {
            window: entity.clone(),
            scale_factor: scale_factor as f64,
        });
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn enter_frame(ptr: u64) {
    let app = unsafe { &mut *(ptr as *mut WorkerApp) };
    if !app.plugins_ready {
        return;
    }

    app.update();
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn release_app(ptr: u64) {
    let app: Box<App> = unsafe { Box::from_raw(ptr as *mut _) };
    crate::close_bevy_window(app);
}
