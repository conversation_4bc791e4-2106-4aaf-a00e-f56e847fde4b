use std::sync::OnceLock;

use bevy::prelude::*;
use crossbeam_channel::{unbounded, Receiver, Sender};
use pianorhythm_proto::pianorhythm_actions::AppStateActions;
use pianorhythm_proto::pianorhythm_effects::AppStateEffects;
use pianorhythm_proto::pianorhythm_events::AppStateEvents;

use crate::types::PianoRhythmSynthEvent;

#[derive(Debug, Copy, Clone, Eq, PartialEq, Ord, PartialOrd, Default, Hash)]
pub struct PhysicalPosition<P> {
    pub x: P,
    pub y: P,
}

#[derive(Debug, Clone)]
pub enum CustomWindowEvents {
    MouseWheel((f64, f64)),
    MouseMoved(PhysicalPosition<f64>, (f64, f64)),
    MouseDown((u32, f32, f32)),
    MouseUp(u32),
    Occluded(bool),
    Focused(bool),
    CursorEntered,
    <PERSON>ursor<PERSON><PERSON>t,
    ScaleFactorChange(f64),
    KeyboardFocusLost,
    KeyboardEvent((bool, String, String)),
    WindowResized((usize, usize)),
}

#[derive(Event, Debug, Clone)]
pub enum RendererEvent {
    AppEffect(AppStateEffects),
    AppAction(AppStateActions),
    BroadcastAppAction(AppStateActions),
    AppActionRaw(Vec<u8>),
    AppEvent(Vec<AppStateEvents>),
    SynthEvent(PianoRhythmSynthEvent),
    WindowEvent(CustomWindowEvents),
}

pub static mut UNIFIED_EVENT_CHANNEL: OnceLock<(Sender<RendererEvent>, Receiver<RendererEvent>)> = OnceLock::new();
pub static mut BROADCAST_APP_ACTION_EVENT_CHANNEL: OnceLock<(Sender<AppStateActions>, Receiver<AppStateActions>)> = OnceLock::new();
pub static mut BROADCAST_APP_EFFECTS_EVENT_CHANNEL: OnceLock<(Sender<AppStateEffects>, Receiver<AppStateEffects>)> = OnceLock::new();
pub static mut BROADCAST_APP_EVENTS_EVENT_CHANNEL: OnceLock<(Sender<Vec<AppStateEvents>>, Receiver<Vec<AppStateEvents>>)> = OnceLock::new();

pub fn init() {
    unsafe {
        if UNIFIED_EVENT_CHANNEL.get().is_some() {
            return;
        }
        
        _ = UNIFIED_EVENT_CHANNEL.set(unbounded());
        _ = BROADCAST_APP_ACTION_EVENT_CHANNEL.set(unbounded());
        _ = BROADCAST_APP_EFFECTS_EVENT_CHANNEL.set(unbounded());
        _ = BROADCAST_APP_EVENTS_EVENT_CHANNEL.set(unbounded());
    }
}

pub fn handle_synth_events(synth_events: crate::types::PianoRhythmSynthEvent) {
    unsafe {
        if let Some(channels) = UNIFIED_EVENT_CHANNEL.get() {
            _ = channels.0.try_send(RendererEvent::SynthEvent(synth_events));
        }
    }
}

pub fn handle_app_events(events: &Vec<AppStateEvents>) {
    unsafe {
        if let Some(channels) = UNIFIED_EVENT_CHANNEL.get() {
            _ = channels.0.try_send(RendererEvent::AppEvent(events.to_owned()));
        }
    }
}

pub fn handle_app_state_effects(app_state_effects: &AppStateEffects) {
    unsafe {
        if let Some(channels) = UNIFIED_EVENT_CHANNEL.get() {
            _ = channels.0.try_send(RendererEvent::AppEffect(app_state_effects.to_owned()));
        }
    }
}

pub fn handle_app_actions(action: &AppStateActions) {
    unsafe {
        if let Some(channels) = UNIFIED_EVENT_CHANNEL.get() {
            _ = channels.0.try_send(RendererEvent::AppAction(action.to_owned()));
        }
    }
}

pub fn handle_app_actions_raw(bytes: &[u8]) {
    unsafe {
        if let Some(channels) = UNIFIED_EVENT_CHANNEL.get() {
            _ = channels.0.try_send(RendererEvent::AppActionRaw(bytes.to_owned()));
        }
    }
}

pub fn handle_window_events(event: CustomWindowEvents) {
    unsafe {
        if let Some(channels) = UNIFIED_EVENT_CHANNEL.get() {
            _ = channels.0.try_send(RendererEvent::WindowEvent(event));
        }
    }
}