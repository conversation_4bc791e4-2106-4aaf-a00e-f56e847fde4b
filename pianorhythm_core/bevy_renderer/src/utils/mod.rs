#[cfg(target_arch = "wasm32")]
use js_sys::Reflect;
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::JsCast;
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::JsValue;
#[cfg(target_arch = "wasm32")]
use web_sys::{CustomEvent, WorkerGlobalScope};

// #[cfg(target_arch = "wasm32")]
pub mod web_ffi;
pub mod common_traits;
pub mod web_keyboard;

#[cfg(feature = "desktop")]
pub mod desktop_ffi;

pub fn get_model_assets_file_path() -> &'static str {
    #[cfg(not(target_arch = "wasm32"))]
    return "models.assets.ron";

    #[cfg(target_arch = "wasm32")]
    return "/models/models.assets.ron";
}

pub fn note_to_midi(note: &str) -> Option<u8> {
    let (note, octave) = note.split_at(note.len() - 1);

    let note = match note.to_uppercase().as_str() {
        "C" => 0,
        "C#" | "DB" => 1,
        "D" => 2,
        "D#" | "EB" => 3,
        "E" => 4,
        "F" => 5,
        "F#" | "GB" => 6,
        "G" => 7,
        "G#" | "AB" => 8,
        "A" => 9,
        "A#" | "BB" => 10,
        "B" => 11,
        _ => return None,
    };

    let octave: u8 = octave.parse().ok()?;
    Some(12 * (octave + 1) + note)
}

#[cfg(target_arch = "wasm32")]
pub fn get_window() -> web_sys::Window {
    if crate::utils::is_worker_global_scope() {
        let worker_global_scope = js_sys::global().dyn_into::<web_sys::WorkerGlobalScope>().expect("expect worker scope");
        Reflect::get(&worker_global_scope, &JsValue::from_str("window")).unwrap().unchecked_into::<web_sys::Window>()
    } else {
        web_sys::window().unwrap()
    }
}

#[cfg(target_arch = "wasm32")]
pub fn get_worker_global_scope() -> web_sys::WorkerGlobalScope {
    js_sys::global().dyn_into::<web_sys::WorkerGlobalScope>().expect("expected worker scope")
}

#[cfg(target_arch = "wasm32")]
pub fn get_main_global_scope() -> web_sys::Window {
    js_sys::global().dyn_into::<web_sys::Window>().expect("expected main thread scope")
}

#[cfg(target_arch = "wasm32")]
pub fn is_worker_global_scope() -> bool {
    js_sys::global().is_instance_of::<web_sys::WorkerGlobalScope>()
}

#[cfg(target_arch = "wasm32")]
pub fn is_main_thread_scope() -> bool {
    js_sys::global().is_instance_of::<web_sys::Window>()
}

#[cfg(target_arch = "wasm32")]
pub fn emit_custom_event(event_name: &str, js_value: &JsValue) -> Result<bool, wasm_bindgen::JsValue> {
    let event = CustomEvent::new(event_name).expect("expect custom event");
    event.init_custom_event_with_can_bubble_and_cancelable_and_detail(event_name, true, true, &js_value);

    if is_worker_global_scope() {
        let worker_global_scope = js_sys::global().dyn_into::<WorkerGlobalScope>()?;
        return worker_global_scope.dispatch_event(&event);
    } else if is_main_thread_scope() {
        let main_global_scope = js_sys::global().dyn_into::<web_sys::Window>()?;
        return main_global_scope.dispatch_event(&event);
    }

    Ok(false)
}