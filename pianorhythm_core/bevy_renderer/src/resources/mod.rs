use bevy::core_pipeline::dof::DepthOfField;
use bevy::ecs::system::SystemId;
use bevy::platform::collections::HashMap;
use bevy::prelude::*;
use bevy_inspector_egui::prelude::ReflectInspectorOptions;
use bevy_inspector_egui::InspectorOptions;
use crossbeam_channel::{Receiver, Sender};
use hex_color::HexColor;
use pianorhythm_proto::midi_renditions::{ActiveChannelsMode as MidiActiveChannelsMode, AudioChannel};
use pianorhythm_proto::pianorhythm_actions::AppStateActions;
use pianorhythm_proto::pianorhythm_app_renditions;
use pianorhythm_proto::pianorhythm_app_renditions::AppSettings;
use pianorhythm_proto::pianorhythm_effects::AppStateEffects_LoadRoomStageDetails;
use pianorhythm_proto::room_renditions::RoomType;
use pianorhythm_proto::user_renditions::{ClientSideUserDto, UserClientDto, UserDto};
use rustc_hash::FxHashMap;
use smooth_bevy_cameras::LookTransform;

use crate::components::fps_counter::fps_counter_showhide;
use crate::components::MainCamera;

#[derive(Debug, Resource)]
pub struct CurrentRoomType(pub RoomType);

impl Default for CurrentRoomType {
    fn default() -> Self {
        Self(RoomType::Unknown)
    }
}

#[derive(Debug, Resource, Default)]
pub struct AppAssetsLoaded {
    pub piano_models: bool,
    pub drum_models: bool,
    pub avatar_models: bool,
    pub guitar_models: bool,
}

impl AppAssetsLoaded {
    pub fn all_loaded(&self) -> bool {
        self.piano_models && self.drum_models
        // && self.avatar_models
        // && self.guitar_models
    }
}

#[derive(Resource)]
pub struct AppStateActionsChannel(pub (Sender<AppStateActions>, Receiver<AppStateActions>));

#[derive(Debug, Resource, Clone)]
pub struct Client(pub UserClientDto);

#[derive(Debug, Resource, Clone)]
pub struct RoomStage(pub AppStateEffects_LoadRoomStageDetails);

#[derive(Debug, Resource, Clone)]
pub struct ClientAppSettings(pub AppSettings);

#[derive(Reflect, Default, Debug, Resource, Clone, Copy, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct ClientIsMobile(pub bool);

#[derive(Reflect, Debug, Resource, Clone, Copy, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct ClientAvatarEntity(pub Entity);

impl Default for ClientAvatarEntity {
    fn default() -> Self {
        Self(Entity::PLACEHOLDER)
    }
}

#[derive(Reflect, Default, Debug, Resource, Clone, Copy, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct ClientSocketID(pub u32);

impl ClientSocketID {
    pub fn is_client(&self, socket_id: &u32) -> bool {
        self.0 == *socket_id
    }
}

#[derive(Reflect, Default, Debug, Resource, Clone, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct ClientSocketIDStr(pub String);

#[derive(Reflect, Default, Debug, Resource, Clone, Copy, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct DrumsDisplayed(pub bool);

#[derive(Reflect, Default, Debug, Resource, Clone, Copy, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct PianoDisplayed(pub bool);

#[derive(Reflect, Default, Debug, Resource, Clone, Copy, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct PianoBenchDisplayed(pub bool);

/// Resource that indicates whether the camera is currently locked.
/// When `true`, camera movement is restricted.
#[derive(Reflect, Debug, Resource, Clone)]
#[reflect(Resource)]
pub struct CameraLock(pub bool);

#[derive(Reflect, Debug, Resource, Clone)]
#[reflect(Resource)]
pub struct DisplayKeysInputMapping(pub bool);

#[derive(Default, Debug, Resource, Clone)]
pub struct KeysInputMapping(pub pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec);

#[derive(Reflect, Default, Debug, Resource, Clone, Copy, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct CameraBounds {
    pub min_x: f32,
    pub max_x: f32,
    pub min_y: f32,
    pub max_y: f32,
    pub min_z: f32,
    pub max_z: f32,
}

#[cfg(feature = "desktop")]
#[derive(Debug, Resource, Default)]
pub struct CursorPositionDesktop(pub Option<Vec2>);

#[derive(Default, Debug, Resource, Clone, Copy, Reflect, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct PrimaryAudioChannel(pub u8);

#[derive(Debug, Resource, Default)]
pub struct SynthAudioChannels(pub FxHashMap<u32, AudioChannel>);

#[derive(Debug, Resource, Default)]
pub struct SynthAudioChannelsActiveState(pub FxHashMap<u32, bool>);

#[derive(Debug, Resource, Default)]
pub struct DefaultCameraPosition {
    pub target: Vec3,
    pub eye: Vec3,
}

#[derive(Debug, Resource, Default)]
pub struct DefaultCameraTopPosition {
    pub target: Vec3,
    pub eye: Vec3,
}

#[derive(Debug, Resource, Clone)]
pub struct ActiveStageSettings {
    pub fog_color: Option<Color>,
    pub fog_falloff: FogFalloff,
}

impl Default for ActiveStageSettings {
    fn default() -> Self {
        Self {
            fog_color: None,
            fog_falloff: FogFalloff::Linear { start: 5.0, end: 20.0 },
        }
    }
}

pub struct DefaultFogColor(pub Color);

impl Default for DefaultFogColor {
    fn default() -> Self {
        Self(Color::Srgba(Srgba::hex("#363942").unwrap()))
    }
}

#[derive(Default, Debug, Resource)]
pub struct SpawnedAvatarUsers(pub FxHashMap<u32, Entity>);

#[derive(Default, Debug, Resource)]
pub struct Users(pub FxHashMap<u32, ClientSideUserDto>);

impl Users {
    pub fn get_user_dto(&self, socket_id: &u32) -> Option<&UserDto> {
        self.0.get(&socket_id).map(|x| x.get_userDto())
    }
}

#[derive(Debug, Resource)]
pub struct UserColors(pub FxHashMap<u32, Color>);

impl UserColors {
    pub fn create_color(user_dto: &UserDto) -> Color {
        let user_color = user_dto.get_color();

        if user_color.to_lowercase().as_str() == "rainbow" {
            return Color::BLACK;
        }

        UserColors::from_str(&user_color)
    }

    pub fn from_str(color: &str) -> Color {
        UserColors::from_hex(HexColor::parse(&color).unwrap_or_default())
    }

    pub fn from_hex(target_color: HexColor) -> Color {
        Color::srgba(
            target_color.r as f32 / 255.0,
            target_color.g as f32 / 255.0,
            target_color.b as f32 / 255.0,
            target_color.a as f32 / 255.0,
        )
    }

    pub fn get_color(&self, socket_id: &u32) -> Option<Color> {
        self.0.get(&socket_id).cloned()
    }
}

#[derive(Resource, Debug, Clone, PartialEq, Eq)]
pub struct ChannelsMode(pub MidiActiveChannelsMode);

impl Default for ChannelsMode {
    fn default() -> Self {
        Self(MidiActiveChannelsMode::ALL)
    }
}

impl ChannelsMode {
    pub fn is_single(&self) -> bool {
        self.0 == MidiActiveChannelsMode::SINGLE
    }

    pub fn is_multi(&self) -> bool {
        self.0 == MidiActiveChannelsMode::MULTI
    }

    pub fn is_all(&self) -> bool {
        self.0 == MidiActiveChannelsMode::ALL
    }

    pub fn is_split(&self) -> bool {
        match self.0 {
            MidiActiveChannelsMode::SPLIT2 | MidiActiveChannelsMode::SPLIT4 | MidiActiveChannelsMode::SPLIT8 => true,
            _ => false,
        }
    }
}

/// A resource that stores the settings that the user can change.
#[derive(Reflect, Resource, Clone, Copy, InspectorOptions)]
#[reflect(Resource, InspectorOptions)]
pub struct DOFAppSettings {
    /// The distance from the camera to the area in the most focus.
    focal_distance: f32,

    /// The [f-number]. Lower numbers cause objects outside the focal distance
    /// to be blurred more.
    ///
    /// [f-number]: https://en.wikipedia.org/wiki/F-number
    aperture_f_stops: f32,

    /// Whether depth of field is on, and, if so, whether we're in Gaussian or
    /// bokeh mode.
    #[reflect(ignore)]
    mode: Option<bevy::core_pipeline::dof::DepthOfFieldMode>,
}

impl Default for DOFAppSettings {
    fn default() -> Self {
        Self {
            // Objects 7 meters away will be in full focus.
            focal_distance: 7.0,

            // Set a nice blur level.
            //
            // This is a really low F-number, but we want to demonstrate the
            // effect, even if it's kind of unrealistic.
            aperture_f_stops: 1.0 / 8.0,

            // Turn on bokeh by default, as it's the nicest-looking technique.
            mode: Some(bevy::core_pipeline::dof::DepthOfFieldMode::Gaussian),
        }
    }
}

impl From<DOFAppSettings> for Option<DepthOfField> {
    fn from(app_settings: DOFAppSettings) -> Self {
        app_settings.mode.map(|mode| DepthOfField {
            mode,
            focal_distance: app_settings.focal_distance,
            aperture_f_stops: app_settings.aperture_f_stops,
            max_depth: 14.0,
            ..default()
        })
    }
}

/// Writes the depth of field settings into the camera.
pub fn update_dof_settings(
    mut commands: Commands,
    view_targets: Query<Entity, With<Camera>>,
    app_settings: Res<DOFAppSettings>,
) {
    let dof_settings: Option<DepthOfField> = (*app_settings).into();
    for view in view_targets.iter() {
        match dof_settings {
            None => {
                commands.entity(view).remove::<bevy::core_pipeline::dof::DepthOfField>();
            }
            Some(dof_settings) => {
                commands.entity(view).insert(dof_settings);
            }
        }
    }
}

#[derive(Copy, Clone, PartialEq, Eq, Hash)]
pub enum CoreSystemsID {
    HandleFpsCounterVisibility,
    ResetCamera,
    SetCameraTopPosition,
    ToggleCameraLock,
    ToggleDisplayKeyInputMapping,
}

#[derive(Resource)]
pub struct CoreSystems(pub HashMap<CoreSystemsID, SystemId>);

impl FromWorld for CoreSystems {
    fn from_world(world: &mut World) -> Self {
        let mut system = CoreSystems(HashMap::new());

        system
            .0
            .insert(CoreSystemsID::HandleFpsCounterVisibility, world.register_system(fps_counter_showhide));

        system.0.insert(
            CoreSystemsID::ToggleCameraLock,
            world.register_system(|mut value: ResMut<CameraLock>| {
                value.0 = !value.0;
            }),
        );

        system.0.insert(
            CoreSystemsID::ToggleDisplayKeyInputMapping,
            world.register_system(|mut value: ResMut<DisplayKeysInputMapping>| {
                value.0 = !value.0;
            }),
        );

        system.0.insert(
            CoreSystemsID::ResetCamera,
            world.register_system(
                |mut camera: Query<&mut LookTransform, (With<MainCamera>, With<LookTransform>)>,
                 camera_position: Res<DefaultCameraPosition>| {
                    let Ok(mut look_transform) = camera.single_mut() else {
                        return;
                    };

                    look_transform.target = camera_position.target;
                    look_transform.eye = camera_position.eye;
                },
            ),
        );

        system.0.insert(
            CoreSystemsID::SetCameraTopPosition,
            world.register_system(
                |mut camera: Query<&mut LookTransform, (With<MainCamera>, With<LookTransform>)>,
                 camera_position: Res<DefaultCameraTopPosition>| {
                    let Ok(mut look_transform) = camera.single_mut() else {
                        return;
                    };

                    look_transform.target = camera_position.target;
                    look_transform.eye = camera_position.eye;
                },
            ),
        );

        system
    }
}
