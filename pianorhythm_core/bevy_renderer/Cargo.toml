[package]
name = "pianorhythm_bevy_renderer"
version = "0.1.0"
authors.workspace = true
edition.workspace = true
rust-version.workspace = true
build = "../build-renderer.rs"

[lib]
crate-type = ["lib", "cdylib", "staticlib"]

[features]
default = [
    "console_log",
]
standalone = [
    "wry",
    "rmp",
    "rmp-serde",
    "bevy/bevy_winit",
    "bevy/webgpu",
    "bevy/multi_threaded"
]
run_bevy_in_web_worker = []
# Note: run_bevy_in_web_worker does not work with webgl2
webgl2 = [
    "bevy/webgl2",
    "bevy/bevy_winit"
]
webgpu = [
    "bevy/webgpu",
    "run_bevy_in_web_worker"
]
desktop = [
    "winit",
    "tao",
    "approx",
    "bevy-async-ecs",
    "bevy/webgpu",
    "bevy/bevy_winit",
    "bevy/multi_threaded",
    "bevy_kira_audio",
    "kira"
]

[dependencies]
raw-window-handle = "0.6.2"
pianorhythm_shared = { path = "../shared" }
pianorhythm_proto = { path = "../proto" }
log = { workspace = true }
rustc-hash = { workspace = true }
midly = { workspace = true }
protobuf = { workspace = true }
serde = { workspace = true }
uuid = { workspace = true, features = ["v4"] }
enum-as-inner = "0.6.0"
tween = { version = "2.0.2" }
hex_color = { version = "3.0.0" }
crossbeam-channel = { workspace = true }
bevy_tweening = { version = "0.13.0", default-features = false, features = [
    "bevy_asset",
]}
bevy_panorbit_camera = "0.26.0"
bevy_egui = "0.34"
bevy-inspector-egui = { version = "0.31.0", default-features = false, features = ["bevy_pbr", "bevy_render", "bevy_image"] }
bevy_hanabi = { version = "0.16.0", default-features = false, features = ["3d"] }
smooth-bevy-cameras = "0.14.0"
bevy-sequential-actions = "0.13.0"
bevy_kira_audio = { version = "0.23.0", optional = true }
bevy_asset_loader = { version = "0.23.0", features = ["standard_dynamic_assets"] }
bevy_descendant_collector = "0.4.0"
derivative = "2.2.0"
kira = { version = "0.10.4", optional = true }
music-note = "0.3.1"
smallvec = "1.11.0"
bitflags = "2.3"

[target.'cfg(all(target_arch = "wasm32", target_os = "unknown"))'.dependencies]
console_log = { workspace = true, optional = true }
js-sys = { workspace = true }
wasm-bindgen = { workspace = true }
wasm-bindgen-futures = { workspace = true }
web-sys = { workspace = true }
serde-wasm-bindgen = { workspace = true }
console_error_panic_hook = { version = "0.1.7" }
serde_json = { workspace = true }

[target.'cfg(target_arch = "x86_64")'.dependencies]
bevy-async-ecs = { version = "0.8.1", optional = true }
once_cell = "^1"
rmp = { version = "^0.8", optional = true }
rmp-serde = { version = "1.1.2", optional = true }
winit = { version = "0.30.5", optional = true }
tao = { version = "0.30.3", optional = true }
approx = { version = "0.5.1", optional = true }
tracing = { version = "0.1.40", optional = true }
wry = { git = "https://github.com/tauri-apps/wry", default-features = false, optional = true, features = [
    "protocol",
    "transparent"] }

[dependencies.bevy]
version = "=0.16.0"
default-features = false
features = [
    "bevy_sprite",
    "bevy_asset",
    "bevy_color",
    "bevy_core_pipeline",
    "animation",
    "bevy_animation",
    "bevy_pbr",
    "bevy_gltf",
    "bevy_render",
    "bevy_picking",
    "bevy_ui_picking_backend",
    "bevy_mesh_picking_backend",
    "bevy_text",
    "bevy_state",
    "bevy_ui",
    "default_font",
    "smaa_luts",
    "png",
    "tonemapping_luts",
]

[patch.crates-io]
# At the moment http disallows empty authority and invalidates uris like: "file:///path/to/file"
http = { git = "https://github.com/PawelBis/http", branch = "feature/empty-authority" }

# Enable a small amount of optimization in debug mode
[profile.dev]
opt-level = 1

# Enable high optimizations for dependencies (incl. Bevy), but not for our code:
[profile.dev.package."*"]
opt-level = 3

[profile.release]
opt-level = 's'
lto = 'thin'
codegen-units = 1

[profile.wasm-release]
inherits = "release"
opt-level = "s"
lto = 'thin'
codegen-units = 1