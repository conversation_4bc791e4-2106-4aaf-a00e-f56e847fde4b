export RUSTFLAGS="-C target-feature=+atomics,+bulk-memory,+mutable-globals -C link-arg=--max-memory=4294967296"

cargo rustc --package pianorhythm_bevy_renderer --features="webgpu" --lib --crate-type cdylib --target wasm32-unknown-unknown --release -Z build-std=std,panic_abort

wasm-bindgen ./target/wasm32-unknown-unknown/release/pianorhythm_bevy_renderer.wasm \
  --out-dir ./pkg/webgpu \
  --typescript \
  --target web