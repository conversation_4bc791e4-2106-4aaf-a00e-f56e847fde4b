use std::rc::Rc;
use pianorhythm_proto::client_message::MidiMessageOutputDto;
use crate::midi::{handle_ws_midi_message, HandleWsMidiMessageOutput, HandleWebsocketMidiMessage, BatchedNoteSchedule, ScheduledNote};
use crate::reducers::app_state::AppState;
use crate::types::CoreClientApiType;

pub struct HandleMidiMessage<'c> {
    pub core_api: &'c CoreClientApiType,
    pub on_handle: HandleWsMidiMessageOutput
}

impl<'c> HandleWebsocketMidiMessage for HandleMidiMessage<'c> {
    fn handle(&self, message: &MidiMessageOutputDto, state: Rc<AppState>) -> () {
        if let Some(output) = handle_ws_midi_message(&message, state) {
            (self.on_handle)(output);
        }
    }

    fn schedule_note_batch_optimized(&self, batch: BatchedNoteSchedule) {
        // TODO: Implement optimized batch scheduling
        // For now, this is a placeholder implementation
        log::warn!("schedule_note_batch_optimized not yet implemented");
    }
}