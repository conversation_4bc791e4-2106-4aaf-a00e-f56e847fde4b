use std::rc::Rc;

use pianorhythm_proto::client_message::{MidiDto, MidiMessageOutputDto};
use pianorhythm_proto::midi_renditions::MidiDtoType;
#[cfg(feature = "use_synth")]
use pianorhythm_synth::{NoteSourceType, PianoRhythmWebSocketMidiNoteOn, PianoRhythmWebSocketMidiPitchBend};

use crate::reducers::app_state::AppState;
use crate::utils::hash_socket_id;

#[cfg(target_arch = "wasm32")]
pub mod wasm_handle_ws_midi;

#[cfg(target_arch = "wasm32")]
pub mod audio_scheduler;

#[cfg(target_arch = "wasm32")]
pub mod audio_scheduler_test;

#[cfg(not(target_arch = "wasm32"))]
pub mod handle_ws_midi;

#[cfg(target_arch = "wasm32")]
pub mod audio_scheduler_raf;

pub trait HandleWebsocketMidiMessage {
    fn handle(&self, message: &MidiMessageOutputDto, state: Rc<AppState>) -> ();

    fn schedule_note_batch_optimized(&self, batch: BatchedNoteSchedule);
}

#[derive(Debug, Clone)]
pub enum EngineEvent {
    #[cfg(feature = "use_synth")]
    NoteOn(PianoRhythmWebSocketMidiNoteOn, Option<u32> /*socket_hash*/),
    RawMidi([u8; 3], Option<u8> /*source*/, Option<u32> /*socket_hash*/),
    PitchBend {
        channel: u8,
        value: u32,
        socket_hash: Option<u32>,
    },
}

#[derive(Debug, Clone)]
pub struct ScheduledEngineEvent {
    pub delay_ms: f64,
    pub ev: EngineEvent,
}

fn get_velocity_percentage(velocity: f32, percentage: f32) -> f32 {
    ((percentage / 100.0) * velocity).round()
}

fn calculate_velocity_from_percentage(
    velocity: f32,
    global_velocity_percentage: Option<f32>,
    user_velocity_percentage: Option<f32>,
) -> f32 {
    match global_velocity_percentage {
        None => velocity,
        Some(global_velocity_percentage) => match user_velocity_percentage {
            Some(user_velocity_percentage)
                if user_velocity_percentage != pianorhythm_shared::audio::MAX_VELOCITY_USER_PERCENTAGE as f32 =>
            {
                get_velocity_percentage(velocity, user_velocity_percentage)
            }
            _ => get_velocity_percentage(velocity, global_velocity_percentage),
        },
    }
}

pub type HandleWsMidiMessageClosure = Box<dyn FnOnce() + Send>;

pub type HandleWsMidiMessageOutput = Box<dyn Fn(Vec<ScheduledEngineEvent>) + Send>;

/// Handles an incoming MIDI message from a websocket, processes it according to the current
/// application audio state, and returns a vector of closures to be executed at specific times.
///
/// # Arguments
/// * `message` - The MIDI message received from the websocket.
/// * `state` - The current application state wrapped in an `Rc`.
///
/// # Returns
/// * `Option<Vec<(f64, HandleWsMidiMessageClosure)>>` - Returns `None` if the message should not be processed,
///   otherwise returns a vector of tuples containing the scheduled time (in ms) and the closure to execute.
pub fn handle_ws_midi_message(
    message: &MidiMessageOutputDto,
    state: Rc<AppState>,
) -> Option<Vec<ScheduledEngineEvent>> {
    #[cfg(not(feature = "use_synth"))]
    return None;

    let current_audio_state = &state.audio_process_state;

    if current_audio_state.muted_everyone_else {
        return None;
    }

    let message_socket_id = message.get_socketID();
    if message_socket_id.is_empty() {
        return None;
    }

    if current_audio_state.muted_users.contains(&message_socket_id.to_string()) {
        return None;
    }

    let socket_id_hash = hash_socket_id(&message_socket_id);

    // Compute a base delay using a monotonic anchor to avoid wall-clock jitter
    // (reuses your existing pattern from the optimized code)
    let timing = TimingContext {
        message_received_at: instant::Instant::now(),
        message_wall_time: chrono::Utc::now().timestamp_millis() as f64,
        server_time_offset: current_audio_state.server_time_offset as f64,
    };

    let parsed_time = message.get_time().parse::<f64>().unwrap_or(timing.message_wall_time);

    // Use the same optimized timing function you already wrote
    let base_delay =
        calculate_note_timing_optimized(parsed_time, current_audio_state.server_time_offset as f64, &timing);

    let socket_hash = hash_socket_id(&message_socket_id);

    // Preallocate reasonably (avoid re-allocs)
    let buffers = message.get_data();

    let mut out = Vec::with_capacity(buffers.len());

    #[cfg(feature = "use_synth")]
    for buffer in buffers.iter().filter(|b| b.data.is_some()) {
        // Clamp per-note additional delay and total delay
        let per_note = buffer.get_delay().min(1000.0);
        let mut delay_ms = (base_delay + per_note).clamp(0.0, 5000.0) + 100.0;
        //log::debug!("ms: {} | per_note: {} | base_delay: {}", delay_ms, per_note, base_delay);

        if delay_ms > 5000.0 {
            continue; // skip way-too-far future events
        }

        // Move/clone only once per buffer
        let data = buffer.get_data().to_owned();
        let src = NoteSourceType::from_proto_source(data.get_noteSource());
        let src_u8 = src.to_u8();

        match data.messageType {
            MidiDtoType::NoteOn if data.has_noteOn() => {
                let v = data.get_noteOn();
                out.push(ScheduledEngineEvent {
                    delay_ms,
                    ev: EngineEvent::NoteOn(
                        PianoRhythmWebSocketMidiNoteOn {
                            channel: v.get_channel() as u8,
                            note: v.get_note() as u8,
                            velocity: v.get_velocity() as u8,
                            program: Some(v.get_program() as u8),
                            bank: Some(v.get_bank() as u32),
                            volume: Some(v.get_volume() as u8),
                            pan: Some(v.get_pan() as u8),
                            source: Some(src_u8),
                            ..Default::default()
                        },
                        socket_hash,
                    ),
                });
            }
            MidiDtoType::NoteOff if data.has_noteOff() => {
                let v = data.get_noteOff();
                out.push(ScheduledEngineEvent {
                    delay_ms,
                    ev: EngineEvent::RawMidi(
                        [
                            pianorhythm_shared::midi::NOTE_OFF_BYTE + v.get_channel() as u8,
                            v.get_note() as u8,
                            0,
                        ],
                        Some(src_u8),
                        socket_hash,
                    ),
                });
            }
            MidiDtoType::Sustain if data.has_sustain() => {
                let sustain_on = data.get_sustain().value;
                out.push(ScheduledEngineEvent {
                    delay_ms,
                    ev: EngineEvent::RawMidi(
                        [
                            pianorhythm_shared::midi::CONTROLLER_BYTE,
                            64,
                            if sustain_on { 64 } else { 0 },
                        ],
                        Some(src_u8),
                        socket_hash,
                    ),
                });
            }
            MidiDtoType::AllSoundOff if data.has_allSoundOff() => {
                let ch = data.get_allSoundOff().get_channel() as u8;
                out.push(ScheduledEngineEvent {
                    delay_ms,
                    ev: EngineEvent::RawMidi(
                        [pianorhythm_shared::midi::CONTROLLER_BYTE + ch, 0x78, 0],
                        Some(src_u8),
                        socket_hash,
                    ),
                });
            }
            MidiDtoType::PitchBend if data.has_pitchBend() => {
                let v = data.get_pitchBend();
                out.push(ScheduledEngineEvent {
                    delay_ms,
                    ev: EngineEvent::PitchBend {
                        channel: v.get_channel() as u8,
                        value: v.get_value(),
                        socket_hash,
                    },
                });
            }
            _ => {}
        }
    }

    Some(out)
}

pub struct TimingContext {
    pub message_received_at: instant::Instant,
    pub message_wall_time: f64,
    pub server_time_offset: f64,
}

pub fn calculate_note_timing_optimized(
    message_time: f64,
    server_time_offset: f64,
    timing_context: &TimingContext,
) -> f64 {
    // Use wall-clock time for server synchronization
    let server_adjusted_time = message_time + server_time_offset;

    // Calculate delay from when message was received (monotonic)
    let processing_delay = timing_context.message_received_at.elapsed().as_millis() as f64;

    // Base delay calculation using wall-clock times
    let base_delay = server_adjusted_time - timing_context.message_wall_time;

    // Subtract processing time to maintain accuracy
    let adjusted_delay = base_delay - processing_delay;

    // Clamp to reasonable bounds (0-5000ms)
    adjusted_delay.max(0.0).min(5000.0)
}

#[derive(Debug, Clone)]
pub struct ScheduledNote {
    pub delay_ms: f64,
    pub note_data: MidiDto,
    #[cfg(feature = "use_synth")]
    pub note_source: NoteSourceType,
    pub socket_id_hash: Option<u32>,
}

#[derive(Debug)]
pub struct BatchedNoteSchedule {
    pub notes: Vec<ScheduledNote>,
    pub base_time: f64,
}

pub fn handle_ws_midi_message_optimized(
    message: &MidiMessageOutputDto,
    state: Rc<AppState>,
) -> Option<BatchedNoteSchedule> {
    let current_audio_state = &state.audio_process_state;

    if current_audio_state.muted_everyone_else {
        return None;
    }

    let midi_message = message.clone();
    let message_socket_id = midi_message.get_socketID();

    if message_socket_id.is_empty() {
        return None;
    }

    let socket_id_hash = hash_socket_id(&message_socket_id);
    //log::info!("socket_id_hash: {:?} | socket_id: {:?}", &socket_id_hash, &message_socket_id);

    #[cfg(feature = "use_synth")]
    if !current_audio_state
        .muted_users
        .contains(&message_socket_id.to_lowercase())
    {
        // Create timing context with monotonic clock
        let timing_context = TimingContext {
            message_received_at: instant::Instant::now(),
            message_wall_time: chrono::Utc::now().timestamp_millis() as f64,
            server_time_offset: current_audio_state.server_time_offset as f64,
        };

        let message_time = midi_message
            .get_time()
            .parse::<f64>()
            .unwrap_or(timing_context.message_wall_time);

        // Use optimized timing calculation with monotonic clock support
        let base_delay = calculate_note_timing_optimized(
            message_time,
            current_audio_state.server_time_offset as f64,
            &timing_context,
        );

        let mut batched_notes = Vec::new();

        for buffer in midi_message
            .get_data()
            .into_iter()
            .filter(|buffer| buffer.data.is_some())
        {
            let note_delay = buffer.get_delay().min(1000.0);
            let total_delay = base_delay + note_delay;

            // Skip notes scheduled too far in the future
            if total_delay > 5000.0 {
                continue;
            }

            let buffer_data = buffer.get_data().to_owned();
            let note_source = NoteSourceType::from_proto_source(buffer_data.get_noteSource());

            batched_notes.push(ScheduledNote {
                delay_ms: total_delay,
                note_data: buffer_data,
                note_source,
                socket_id_hash,
            });
        }

        if !batched_notes.is_empty() {
            return Some(BatchedNoteSchedule {
                notes: batched_notes,
                base_time: timing_context.message_wall_time,
            });
        }
    }

    None
}
