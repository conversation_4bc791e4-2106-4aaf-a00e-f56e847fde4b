#![cfg(target_arch = "wasm32")]
use std::cell::{Cell, RefCell};
use std::cmp::Ordering;
use std::rc::{Rc, Weak};
use std::sync::Arc;

use js_sys::{Function, Reflect};
use wasm_bindgen::prelude::*;
use wasm_bindgen::JsCast;
use web_sys::Window;

use crate::midi::HandleWsMidiMessageClosure; // Box<dyn Fn() + Send>

const DEFAULT_LOOKAHEAD_MS: f64 = 70.0; // tune 20–80ms
const MAX_DRAIN_PER_TICK: usize = 512; // avoid long GC pauses on massive bursts
const DROP_TOO_LATE_MS: f64 = 120.0; // guard: skip events far behind

struct Scheduled {
    due_ms: f64, // performance.now()-based absolute time in ms
    f: HandleWsMidiMessageClosure,
}

impl Scheduled {
    fn new(due_ms: f64, f: HandleWsMidiMessageClosure) -> Self {
        Self { due_ms, f }
    }
}

// Min-heap by due_ms
struct MinHeap(Vec<Scheduled>);
impl MinHeap {
    fn new() -> Self {
        Self(Vec::new())
    }

    fn push(&mut self, item: Scheduled) {
        self.0.push(item);
        self.sift_up(self.0.len() - 1);
    }

    fn peek_due(&self) -> Option<f64> {
        self.0.first().map(|s| s.due_ms)
    }

    fn pop(&mut self) -> Option<Scheduled> {
        if self.0.is_empty() {
            return None;
        }
        let last = self.0.pop().unwrap();
        if self.0.is_empty() {
            return Some(last);
        }
        let mut ret = std::mem::replace(&mut self.0[0], last);
        self.sift_down(0);
        Some(std::mem::replace(&mut ret, Scheduled::new(0.0, Box::new(|| {}))))
    }

    fn len(&self) -> usize {
        self.0.len()
    }

    fn sift_up(&mut self, mut idx: usize) {
        while idx > 0 {
            let parent = (idx - 1) / 2;
            if self.0[idx].due_ms < self.0[parent].due_ms {
                self.0.swap(idx, parent);
                idx = parent;
            } else {
                break;
            }
        }
    }

    fn sift_down(&mut self, mut idx: usize) {
        let len = self.0.len();
        loop {
            let l = idx * 2 + 1;
            let r = l + 1;
            if l >= len {
                break;
            }
            let mut smallest = l;
            if r < len && self.0[r].due_ms < self.0[l].due_ms {
                smallest = r;
            }
            if self.0[smallest].due_ms < self.0[idx].due_ms {
                self.0.swap(idx, smallest);
                idx = smallest;
            } else {
                break;
            }
        }
    }
}

pub struct RafScheduler {
    heap: RefCell<MinHeap>,
    lookahead_ms: Cell<f64>,
    ticking: Cell<bool>,
    // keep the closure alive across frames
    tick_closure: RefCell<Option<Closure<dyn FnMut(f64)>>>,
}

impl RafScheduler {
    pub fn new() -> Rc<Self> {
        Rc::new(Self {
            heap: RefCell::new(MinHeap::new()),
            lookahead_ms: Cell::new(DEFAULT_LOOKAHEAD_MS),
            ticking: Cell::new(false),
            tick_closure: RefCell::new(None),
        })
    }

    pub fn set_lookahead_ms(&self, v: f64) {
        self.lookahead_ms.set(v.max(0.0).min(200.0));
    }

    pub fn schedule(&self, delay_ms: f64, f: HandleWsMidiMessageClosure) {
        let due = now_ms() + delay_ms.max(0.0);
        self.heap.borrow_mut().push(Scheduled::new(due, f));
        self.ensure_tick();
    }

    pub fn schedule_batch(&self, batch: Vec<(f64, HandleWsMidiMessageClosure)>) {
        if batch.is_empty() {
            return;
        }

        let base = now_ms();
        let mut heap = self.heap.borrow_mut();

        for (delay_ms, f) in batch.into_iter() {
            heap.push(Scheduled::new(base + delay_ms.max(0.0), f));
        }

        drop(heap);
        self.ensure_tick();
    }

    pub fn schedule_map_batch<T: 'static + Send>(
        &self,
        batch: Vec<(f64, T)>,
        dispatcher: Arc<dyn Fn(&T) + Send + Sync>,
    ) {
        if batch.is_empty() {
            return;
        }

        let mut items = Vec::with_capacity(batch.len());
        for (delay, data) in batch {
            let f = dispatcher.clone();
            // captures `data` by value, but CALLS with &data → closure is Fn (not FnOnce)
            let cb = Box::new(move || (f)(&data)) as crate::midi::HandleWsMidiMessageClosure;
            items.push((delay.max(0.0), cb));
        }

        self.schedule_batch(items);
    }

    fn ensure_tick(&self) {
        if self.ticking.get() {
            return;
        }
        self.ticking.set(true);

        // Create a self-referencing rAF closure. Keep it alive in `tick_closure`.
        let weak_self: Weak<RafScheduler> = RAF_SCHEDULER.with(|r| Rc::downgrade(&r));

        let cb = Closure::wrap(Box::new(move |ts: f64| {
            if let Some(rc) = weak_self.upgrade() {
                rc.on_tick(ts);
                // Keep pumping frames while we're ticking
                if rc.ticking.get() {
                    if let Some(cb) = rc.tick_closure.borrow().as_ref() {
                        request_animation_frame_raw(cb.as_ref());
                    }
                }
            }
        }) as Box<dyn FnMut(f64)>);

        // Store so it isn't dropped
        *self.tick_closure.borrow_mut() = Some(cb);

        // Kick the first frame
        if let Some(cb) = self.tick_closure.borrow().as_ref() {
            request_animation_frame_raw(cb.as_ref());
        }
    }

    // <<< Missing before — now provided >>>
    fn on_tick(&self, frame_ts_ms: f64) {
        let horizon = frame_ts_ms + self.lookahead_ms.get();
        let mut drained = 0usize;
        let mut heap = self.heap.borrow_mut();

        while heap.len() > 0 && drained < MAX_DRAIN_PER_TICK {
            if let Some(due) = heap.peek_due() {
                if due > horizon {
                    break;
                }
            }

            let scheduled = match heap.pop() {
                Some(s) => s,
                None => break,
            };

            let lateness = frame_ts_ms - scheduled.due_ms;
            if lateness > DROP_TOO_LATE_MS {
                // Skip events that are way too late to avoid flams
                continue;
            }

            (scheduled.f)();
            drained += 1;
        }

        // Stop ticking if no pending work
        if heap.len() == 0 {
            self.ticking.set(false);
        }
    }
}

thread_local! {
    pub static RAF_SCHEDULER: Rc<RafScheduler> = RafScheduler::new();
}

// ——— utilities: requestAnimationFrame + performance.now() from either Window or Worker ———
fn now_ms() -> f64 {
    if let Some(win) = web_sys::window() {
        // Window path
        win.performance().map(|p| p.now()).unwrap_or(js_now())
    } else {
        js_now()
    }
}

fn js_now() -> f64 {
    let global = js_sys::global();
    let perf = Reflect::get(&global, &JsValue::from_str("performance")).unwrap();
    let now_fn: Function = Reflect::get(&perf, &JsValue::from_str("now"))
        .unwrap()
        .dyn_into()
        .unwrap();
    now_fn.call0(&perf).unwrap().as_f64().unwrap()
}

fn request_animation_frame_raw(cb: &wasm_bindgen::JsValue) -> i32 {
    if let Some(win) = web_sys::window() {
        // Window context
        let win: Window = win;
        return win.request_animation_frame(cb.unchecked_ref()).unwrap();
    }

    // Worker/global context
    let global = js_sys::global();
    let raf: Function = Reflect::get(&global, &JsValue::from_str("requestAnimationFrame"))
        .unwrap()
        .dyn_into()
        .unwrap();
    let id = raf.call1(&global, cb).unwrap();
    id.as_f64().unwrap_or(0.0) as i32
}
