use std::rc::Rc;

use crate::midi::audio_scheduler::schedule_midi_batch_global;
use crate::midi::{
    handle_ws_midi_message, handle_ws_midi_message_optimized, BatchedNoteSchedule, EngineEvent, HandleWebsocketMidiMessage, ScheduledNote
};
use crate::reducers::app_state::AppState;
use crate::types::CoreClientApiType;
use crate::PianoRhythmWebSocketMidiPitchBend;
use pianorhythm_proto::client_message::MidiMessageOutputDto;
use pianorhythm_proto::midi_renditions::MidiDtoType;
use pianorhythm_synth::PianoRhythmWebSocketMidiNoteOn;

#[cfg(target_arch = "wasm32")]
use crate::midi::audio_scheduler_raf::RAF_SCHEDULER;

pub struct WasmHandleMidiMessage<'c> {
    pub core_api: &'c CoreClientApiType,
}

#[inline]
fn dispatch_engine_event(ev: &EngineEvent) {
    use pianorhythm_synth::*;
    match ev {
        EngineEvent::NoteOn(note_on, socket) => {
            synth_ws_socket_note_on(*note_on, *socket);
        }
        EngineEvent::RawMidi(bytes, src, socket) => {
            let _ = parse_midi_data(bytes, *socket, *src, None);
        }
        EngineEvent::PitchBend {
            channel,
            value,
            socket_hash,
        } => {
            let _ = synth_ws_socket_pitch(
                PianoRhythmWebSocketMidiPitchBend {
                    channel: *channel,
                    value: *value,
                },
                *socket_hash,
            );
        }
    }
}

#[cfg(target_arch = "wasm32")]
pub fn schedule_engine_events_via_raf(batch: Vec<crate::midi::ScheduledEngineEvent>) {
    use std::rc::Rc;
    use std::sync::Arc;

    let dispatcher = Arc::new(|ev: &EngineEvent| dispatch_engine_event(&ev));

    let mapped: Vec<(f64, EngineEvent)> = batch.into_iter().map(|e| (e.delay_ms, e.ev)).collect();

    RAF_SCHEDULER.with(|s| s.schedule_map_batch(mapped, dispatcher));
}

impl<'c> HandleWebsocketMidiMessage for WasmHandleMidiMessage<'c> {
    fn handle(&self, message: &MidiMessageOutputDto, state: Rc<AppState>) -> () {
        //log::info!("Handling midi message: {:?}", &message);

        if let Some(events) = handle_ws_midi_message(&message, state) {
            #[cfg(target_arch = "wasm32")]
            schedule_engine_events_via_raf(events);

            #[cfg(not(target_arch = "wasm32"))]
            for e in events {
                dispatch_engine_event(e.ev);
            }
        }
    }

    fn schedule_note_batch_optimized(&self, batch: BatchedNoteSchedule) {
        //// Use audio-context-synchronized scheduling instead of timeouts
        //let notes_with_timing: Vec<(f64, ScheduledNote)> =
        //    batch.notes.into_iter().map(|note| (note.delay_ms, note)).collect();
        //
        //// Schedule the entire batch using sample-accurate timing
        //schedule_midi_batch_global(notes_with_timing);
    }
}
