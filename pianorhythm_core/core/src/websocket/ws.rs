use async_trait::async_trait;
use ezsockets::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, WSError};
use ezsockets::client::ClientCloseMode;
use pianorhythm_proto::pianorhythm_effects::AppStateEffects_Action;

use crate::{handle_client_message};
use crate::handle_connection_failed;
use crate::handle_connection_retry;
use crate::types::CoreClientApiType;

pub struct WebSocketClient<'c> {
    pub core_api: &'c CoreClientApiType,
    pub handle: ezsockets::Client<Self>,
    pub connect_retry: usize,
}

#[async_trait]
impl<'c> ezsockets::ClientExt for WebSocketClient<'c> {
    type Call = ();

    async fn on_connect(&mut self) -> Result<(), Error> {
        log::debug!("Socket connected.");
        self.connect_retry = 0;
        self.core_api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ConnectionState, |effect| {
            effect.set_stringValue("Online".to_string());
        }));
        Ok(())
    }

    async fn on_close(&mut self, _frame: Option<CloseFrame>) -> Result<ClientCloseMode, Error> {
        log::warn!("Socket closed called.");
        self.connect_retry = 0;
        self.core_api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ConnectionState, |effect| {
            effect.set_stringValue("Closed".to_string());
        }));
        Ok(ClientCloseMode::Reconnect)
    }

    async fn on_connect_fail(&mut self, error: WSError) -> Result<ClientCloseMode, Error> {
        log::error!("Socket connect failed called: {:?}", &error);

        if self.connect_retry >= 10 {
            handle_connection_failed();
            self.core_api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ConnectionState, |effect| {
                effect.set_stringValue("Max Retry Reached".to_string());
            }));
            return Ok(ClientCloseMode::Close);
        }

        self.connect_retry += 1;
        handle_connection_retry();

        self.core_api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ConnectionState, |effect| {
            effect.set_stringValue("Retrying".to_string());
        }));

        Ok(ClientCloseMode::Reconnect)
    }

    async fn on_disconnect(&mut self) -> Result<ClientCloseMode, Error> {
        log::debug!("Socket disconnect called.");

        self.core_api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ConnectionState, |effect| {
            effect.set_stringValue("Disconnected".to_string());
        }));
        Ok(ClientCloseMode::Reconnect)
    }

    async fn on_text(&mut self, _: String) -> Result<(), Error> {
        // log::trace!("received message: {text}");
        Ok(())
    }

    async fn on_binary(&mut self, bytes: Vec<u8>) -> Result<(), Error> {
        handle_client_message(&bytes);
        Ok(())
    }

    async fn on_call(&mut self, call: Self::Call) -> Result<(), Error> {
        let () = call;
        Ok(())
    }
}
