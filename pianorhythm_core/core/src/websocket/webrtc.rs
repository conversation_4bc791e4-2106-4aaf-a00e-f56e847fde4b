use std::sync::Mutex;
use std::time::Duration;

#[cfg(target_arch = "wasm32")]
use async_wasm_task::spawn;
use crossbeam_channel::{Receiver, Sender, unbounded};
use futures::{FutureExt, select};
use futures_timer::Delay;
use lazy_static::lazy_static;
use matchbox_socket::{PeerState, WebRtcSocket};
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::prelude::wasm_bindgen;
#[cfg(target_arch = "wasm32")]
use wasm_bindgen_futures::spawn_local;

pub enum WebRtcChannelMessages {
    Disconnect,
    Message(Vec<u8>),
}

lazy_static! {
    pub static ref WEBRTC_CHANNEL: Mutex<Option<Sender<WebRtcChannelMessages>>> = Mutex::new(None);
}

const TIMEOUT_DURATION: u64 = 100;

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn webrtc_connect(room_id: String) {
    if room_id.is_empty() {
        return;
    }

    let path = format!("ws://localhost:3536/{}", room_id);
    let (d_sender, d_receiver) = unbounded::<WebRtcChannelMessages>();

    let mut d_state = WEBRTC_CHANNEL.lock().expect("Could not lock ws handle mutex");
    *d_state = Some(d_sender);

    #[cfg(target_arch = "wasm32")]
    {
        spawn_local(async_main(d_receiver, path));
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn webrtc_disconnect() {
    let mut state = WEBRTC_CHANNEL.lock().expect("Could not lock handle mutex");
    if let Some(sender) = state.as_mut() {
        _ = sender.send(WebRtcChannelMessages::Disconnect);
    }
}

async fn async_main(r: Receiver<WebRtcChannelMessages>, path: String) {
    let (mut socket, loop_fut) = WebRtcSocket::new_reliable(path);

    let loop_fut = loop_fut.fuse();
    let timeout = Delay::new(Duration::from_millis(TIMEOUT_DURATION));
    futures::pin_mut!(loop_fut, timeout);

    loop {
        if let Ok(c) = r.try_recv() {
            match c {
                WebRtcChannelMessages::Disconnect => {
                    log::info!("Closing webrtc connection.");
                    socket.close();
                    break;
                }
                WebRtcChannelMessages::Message(data) => {
                    let packet = data.into_boxed_slice().clone();
                    let peers = socket.connected_peers().collect::<Vec<_>>();
                    if let Ok(channel) = socket.get_channel_mut(0) {
                        for peer in peers.iter() {
                            channel.send(packet.clone(), peer.clone());
                        }
                    }
                }
            }
        }

        // Handle any new peers
        for (peer, state) in socket.update_peers() {
            match state {
                PeerState::Connected => {
                    log::info!("Peer joined: {peer}");
                    let packet = "hello friend!".as_bytes().to_vec().into_boxed_slice();
                    socket.send(packet, peer);
                }
                PeerState::Disconnected => {
                    log::info!("Peer left: {peer}");
                }
            }
        }

        // Accept any messages incoming
        for (peer, packet) in socket.receive() {
            let message = String::from_utf8_lossy(&packet);
            log::info!("Message from {peer}: {message:?}");
        }

        select! {
                // Restart this loop every 100ms
                _ = (&mut timeout).fuse() => {
                    timeout.reset(Duration::from_millis(TIMEOUT_DURATION));
                }

                // Or break if the message loop ends (disconnected, closed, etc.)
                _ = &mut loop_fut => {
                    break;
                }
            }
    }
}
