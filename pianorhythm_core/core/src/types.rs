use pianorhythm_proto::pianorhythm_effects::AppStateEffects;

#[cfg(feature = "use_synth")]
use pianorhythm_synth::PianoRhythmSynthEvent;

/// An api to emit events back to the front end client
pub trait CoreClientApi {
    fn init(&mut self) -> ();

    /// Emits binary data to websocket
    fn ws_emit_binary(&self, _bytes: Vec<u8>) -> ();

    /// Dispatches app effects back to the front end
    fn dispatch_app_effect(&self, _app_state_effect: &AppStateEffects) -> ();

    /// Dispatches midi sequencer related effects back to the front end
    fn dispatch_midi_sequencer_effect(&self, _app_state_effect: &AppStateEffects) -> () {}

    /// Dispatches synth events back to front end
    #[cfg(feature = "use_synth")]
    fn dispatch_midi_synth_event(&self, _event: &PianoRhythmSynthEvent) -> ();
}

pub type CoreClientApiType = Box<dyn CoreClientApi + Send + Sync>;
