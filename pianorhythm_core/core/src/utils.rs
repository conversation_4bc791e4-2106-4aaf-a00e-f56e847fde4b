#![allow(unused_imports)]

use std::fmt::Debug;

use cached::proc_macro::cached;
#[cfg(target_arch = "wasm32")]
use js_sys::Reflect;
use protobuf::Message;
use protobuf::ProtobufEnum;
use serde::{Deserialize, Serialize, Serializer};
use serde::ser::{SerializeMap, SerializeSeq};
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::{JsCast, JsValue};
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::closure::Closure;
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::prelude::wasm_bindgen;
#[cfg(target_arch = "wasm32")]
use web_sys::{CustomEvent, WorkerGlobalScope};

use pianorhythm_proto::client_message::ChatMessageDto;
use pianorhythm_proto::midi_renditions::{AudioChannel, MidiDto, MidiDto_MidiAllSoundOff, MidiDto_MidiNoteOff, MidiDto_MidiNoteOn, MidiDto_MidiNoteSustain, MidiDto_MidiPitchBend, MidiDtoType, MidiNoteSource, SetChannelInstrumentPayload, SetChannelInstrumentType};
use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action, AudioSynthActions};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_proto::server_message::MidiMessageInputDto;

#[cfg(target_arch = "wasm32")]
pub fn set_panic_hook() {
    // When the `console_error_panic_hook` feature is enabled, we can call the
    // `set_panic_hook` function at least once during initialization, and then
    // we will get better error messages if our code ever panics.
    //
    // For more details see
    // https://github.com/rustwasm/console_error_panic_hook#readme
    #[cfg(feature = "console_error_panic_hook")]
    console_error_panic_hook::set_once();
}

#[inline]
#[cfg(target_arch = "wasm32")]
pub fn emit_custom_event(event_name: &str, js_value: &JsValue) -> Result<bool, wasm_bindgen::JsValue> {
    if is_audio_worklet_scope() {
        return Ok(false);
    }

    let event = CustomEvent::new(event_name).expect("expect custom event");
    event.init_custom_event_with_can_bubble_and_cancelable_and_detail(event_name, true, true, &js_value);

    if is_worker_global_scope() {
        let worker_global_scope = js_sys::global().dyn_into::<WorkerGlobalScope>()?;
        return worker_global_scope.dispatch_event(&event);
    } else if is_main_thread_scope() {
        let main_global_scope = js_sys::global().dyn_into::<web_sys::Window>()?;
        return main_global_scope.dispatch_event(&event);
    }

    Ok(false)
}

#[inline]
#[cfg(target_arch = "wasm32")]
pub fn broadcast_app_events(events: &Vec<AppStateEvents>) {
    let mapped: Vec<i32> = events.clone().into_iter().map(|x| x.value()).collect::<_>();
    let value = serde_wasm_bindgen::to_value(&mapped).unwrap();
    _ = emit_custom_event("app_events", &value);
}

macro_rules! generate_type {
    ($name: ident) => {
        #[derive(Deserialize, Serialize, Eq, PartialEq, Debug, Clone, Hash, Ord, PartialOrd)]
        pub struct $name(pub String);

        impl $name {
            pub fn new() -> Self {
                Self::default()
            }

            pub fn is_empty(&self) -> bool {
                self.0.is_empty()
            }

            pub fn get(&self) -> &str {
                &self.0
            }
        }

        impl Default for $name {
            fn default() -> Self {
                Self(uuid::Uuid::new_v4().to_string())
            }
        }

        impl From<String> for $name {
            fn from(data: String) -> Self {
                $name(data)
            }
        }

        impl From<&str> for $name {
            fn from(data: &str) -> Self {
                $name(data.to_string())
            }
        }

        impl From<Option<String>> for $name {
            fn from(data: Option<String>) -> Self {
                $name(data.unwrap_or_default())
            }
        }

        impl std::fmt::Display for $name {
            fn fmt(&self, fmt: &mut std::fmt::Formatter<'_>) -> Result<(), std::fmt::Error> {
                write!(fmt, "{}", self.0)
            }
        }
    };
}

generate_type!(SocketId);

#[derive(Deserialize, Serialize, Eq, PartialEq, Debug, Clone, Hash, Ord, PartialOrd)]
pub struct RoomId(String);

impl RoomId {
    pub fn is_empty(&self) -> bool {
        self.0 == String::default()
    }

    pub fn missing_id() -> Self {
        RoomId::from("missing room id")
    }

    pub fn to_string(&self) -> String {
        self.0.to_string()
    }
}

impl From<&str> for RoomId {
    fn from(data: &str) -> Self {
        Self(data.to_string().replace("\"", ""))
    }
}

pub fn create_system_chat_message(message: &str) -> ChatMessageDto {
    let mut chat_message_dto = ChatMessageDto::new();
    chat_message_dto.set_message(message.to_string());
    chat_message_dto.set_messageID(uuid::Uuid::new_v4().to_string());
    chat_message_dto.set_isSystem(true);
    chat_message_dto.set_isBot(true);
    chat_message_dto.set_username("System".to_string());
    chat_message_dto.set_socketID("bot.system".to_string());
    chat_message_dto.set_userColor("#4cd4f3".to_string());
    chat_message_dto.set_ts(chrono::Utc::now().to_string());
    chat_message_dto
}

macro_rules! set_value_for_option {
    ($source: expr, $target: expr,$source_field_name: ident, $field_name_set: ident) => {{
        if let Some(value) = $source.$source_field_name {
            $target.$field_name_set(value as i32);
        }
    }};
}

/// Creates a new `AppStateActions` message configured for synthesizer actions.
///
/// # Arguments
///
/// * `synth_action` - The `AudioSynthActions` message to be wrapped in the app state action
///
/// # Returns
///
/// Returns an `AppStateActions` message with:
/// - Action type set to `SynthAction`
/// - The provided `AudioSynthActions` message set as the audio synth action
///
/// # Example
///
/// ```rust
/// let mut synth_action = pianorhythm_proto::pianorhythm_actions::AudioSynthActions::new();
/// synth_action.set_action(pianorhythm_proto::pianorhythm_actions::AudioSynthActions_Action::NoteOn);
/// let app_action = pianorhythm_core::utils::create_audio_synth_action(synth_action);
/// ```
pub fn create_audio_synth_action(synth_action: AudioSynthActions) -> AppStateActions {
    let mut action = AppStateActions::new();
    action.set_action(AppStateActions_Action::SynthAction);
    action.set_audioSynthAction(synth_action);
    action
}

#[inline]
pub fn clamp<T>(value: T, min: T, max: T) -> T
    where
        T: Sized,
        T: Ord,
{
    if value < min {
        min
    } else if value > max {
        max
    } else {
        value
    }
}

#[inline]
#[cfg(target_arch = "wasm32")]
pub fn is_worker_global_scope() -> bool {
    js_sys::global().is_instance_of::<web_sys::WorkerGlobalScope>()
}

#[inline]
#[cfg(target_arch = "wasm32")]
pub fn get_worker_global_scope() -> web_sys::WorkerGlobalScope {
    js_sys::global().dyn_into::<web_sys::WorkerGlobalScope>().expect("expect worker scope")
}

#[inline]
#[cfg(target_arch = "wasm32")]
pub fn get_main_global_scope() -> web_sys::Window {
    js_sys::global().dyn_into::<web_sys::Window>().expect("expected main thread scope")
}

#[inline]
#[cfg(target_arch = "wasm32")]
pub fn is_main_thread_scope() -> bool {
    js_sys::global().is_instance_of::<web_sys::Window>()
}

#[inline]
#[cfg(target_arch = "wasm32")]
pub fn is_audio_worklet_scope() -> bool {
    js_sys::global().is_instance_of::<web_sys::AudioWorkletGlobalScope>()
}

/// Creates a new `AppStateEffects` message for synth channel updates.
///
/// # Arguments
///
/// * `audio_channel` - The audio channel information to be included in the effect
///
/// # Returns
///
/// Returns an `AppStateEffects` message configured with:
/// - Action type set to `SynthChannelUpdated`
/// - The provided audio channel data cloned into the effect
///
/// # Example
///
/// ```rust
/// let mut channel = pianorhythm_proto::midi_renditions::AudioChannel::new();
/// channel.set_channel(1);
/// let effect = pianorhythm_core::utils::create_channel_updated_action(&channel);
/// ```
pub fn create_channel_updated_action(audio_channel: &AudioChannel) -> AppStateEffects {
    let mut effect = AppStateEffects::new();
    effect.set_action(AppStateEffects_Action::SynthChannelUpdated);
    effect.set_audioChannel(audio_channel.clone());
    effect
}

pub trait IntoOption
    where
        Self: Sized + std::fmt::Display,
{
    fn to_option(&self) -> Option<String> {
        if self.to_string().trim().is_empty() {
            None
        } else {
            Some(self.to_string())
        }
    }
}

impl<String: std::fmt::Display> IntoOption for String {}

pub fn hash_socket_id(source_socket_id: &str) -> Option<u32> {
    source_socket_id.to_option().map(pianorhythm_shared::util::hash_string_to_u32)
}

pub fn create_simple_channel_instrument_payload(channel: u32, bank: u32, preset: u32) -> SetChannelInstrumentPayload {
    let mut payload = SetChannelInstrumentPayload::new();
    payload.set_bank(bank);
    payload.set_preset(preset);
    payload.set_channel(channel);
    payload.set_setActive(true);
    payload.set_field_type(SetChannelInstrumentType::Add);
    payload
}

#[cfg(feature = "use_synth")]
pub fn piano_rhythm_web_socket_emit_event_to_proto(
    event: &pianorhythm_synth::PianoRhythmWebSocketEmitEvent
) -> MidiDto {
    let mut midi_dto = MidiDto::new();

    // Note On
    if let Some(_note_on) = event.note_on {
        midi_dto.set_messageType(MidiDtoType::NoteOn);
        let mut note_on = MidiDto_MidiNoteOn::new();
        note_on.set_note(_note_on.note as i32);
        note_on.set_channel(_note_on.channel as i32);
        note_on.set_velocity(_note_on.velocity as i32);

        set_value_for_option!(_note_on, note_on, program, set_program);
        set_value_for_option!(_note_on, note_on, volume, set_volume);
        set_value_for_option!(_note_on, note_on, bank, set_bank);
        set_value_for_option!(_note_on, note_on, pan, set_pan);
        set_value_for_option!(_note_on, note_on, expression, set_expression);

        midi_dto.set_noteOn(note_on);
    }

    // Note Off
    if let Some(_note_off) = event.note_off {
        midi_dto.set_messageType(MidiDtoType::NoteOff);
        let mut note_off = MidiDto_MidiNoteOff::new();
        note_off.set_note(_note_off.note as i32);
        note_off.set_channel(_note_off.channel as i32);
        midi_dto.set_noteOff(note_off);
    }

    // Sustain
    if let Some(_sustain) = event.sustain {
        midi_dto.set_messageType(MidiDtoType::Sustain);
        let mut sustain = MidiDto_MidiNoteSustain::new();
        sustain.set_channel(_sustain.channel as i32);
        sustain.set_value(_sustain.value >= 64);
        midi_dto.set_sustain(sustain);
    }

    // All sound/notes off
    if let Some(_sound_off) = event.all_sound_off.or(event.all_notes_off) {
        midi_dto.set_messageType(MidiDtoType::AllSoundOff);
        let mut sound_off = MidiDto_MidiAllSoundOff::new();
        sound_off.set_channel(_sound_off as i32);
        midi_dto.set_allSoundOff(sound_off);
    }

    // Pitch
    if let Some(_pitch) = event.pitch_bend {
        midi_dto.set_messageType(MidiDtoType::PitchBend);
        let mut pitch_bend = MidiDto_MidiPitchBend::new();
        pitch_bend.set_channel(_pitch.channel as u32);
        pitch_bend.set_value(_pitch.value);
        midi_dto.set_pitchBend(pitch_bend);
    }

    let source = event.note_source.unwrap_or(255);
    midi_dto.set_noteSource(MidiNoteSource::from_i32(source as i32).unwrap_or(MidiNoteSource::MIDI));
    // log::debug!("[piano_rhythm_web_socket_emit_event_to_proto] {:?}", &midi_dto);

    midi_dto
}

#[cfg(target_arch = "wasm32")]
pub mod wasm_util {
    use std::cell::RefCell;
    use std::fmt;
    use std::rc::Rc;

    use game_loop::{game_loop, GameLoop, Time};
    use wasm_bindgen::JsCast;
    use wasm_bindgen::prelude::*;
    use web_sys::window;

    #[wasm_bindgen]
    extern {
        fn requestAnimationFrame(func: &::js_sys::Function) -> i32;
        fn cancelAnimationFrame(id: i32);
        fn performanceNow() -> f64;
    }

    pub(crate) struct AnimationLoop {
        num_updates: u32,
        num_renders: u32,
    }

    impl AnimationLoop {
        pub fn new() -> Self {
            Self { num_updates: 0, num_renders: 0 }
        }
    }

    #[derive(Copy, Clone)]
    pub struct AnimationLoopTime(f64);

    impl game_loop::TimeTrait for AnimationLoopTime {
        fn now() -> Self {
            Self(performanceNow() / 1000.)
        }

        fn sub(&self, other: &Self) -> f64 {
            self.0 - other.0
        }

        fn supports_sleep() -> bool {
            false
        }

        fn sleep(_seconds: f64) {
            unimplemented!("Not supported for WASM.");
        }
    }

    pub fn animation_frame<G, U, R>(mut g: GameLoop<G, AnimationLoopTime, ()>, mut update: U, mut render: R)
        where G: 'static,
              U: FnMut(&mut GameLoop<G, AnimationLoopTime, ()>) + 'static,
              R: FnMut(&mut GameLoop<G, AnimationLoopTime, ()>) + 'static,
    {
        if g.next_frame(&mut update, &mut render) {
            let next_frame = move || animation_frame(g, update, render);
            let closure = Closure::once_into_js(next_frame);
            let js_func = closure.as_ref().unchecked_ref();
            requestAnimationFrame(js_func);
        }
    }

    #[derive(Debug)]
    pub struct AnimationFrame {
        render_id: i32,
        _closure: Closure<dyn Fn(JsValue)>,
        callback_wrapper: Rc<RefCell<Option<CallbackWrapper>>>,
    }

    struct CallbackWrapper(Box<dyn FnOnce(f64) + 'static>);

    impl fmt::Debug for CallbackWrapper {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.write_str("CallbackWrapper")
        }
    }

    impl Drop for AnimationFrame {
        fn drop(&mut self) {
            if self.callback_wrapper.borrow_mut().is_some() {
                log::trace!("Animation frame dropped.");
                cancelAnimationFrame(self.render_id);
                // web_sys::window()
                //     .unwrap_throw()
                //     .cancel_animation_frame(self.render_id)
                //     .unwrap_throw()
            }
        }
    }

    pub fn request_animation_frame<F>(callback_once: F) -> AnimationFrame
        where
            F: FnOnce(f64) + 'static,
    {
        let callback_wrapper = Rc::new(RefCell::new(Some(CallbackWrapper(Box::new(callback_once)))));
        let callback: Closure<dyn Fn(JsValue)> = {
            let callback_wrapper = Rc::clone(&callback_wrapper);
            Closure::wrap(Box::new(move |v: JsValue| {
                let time: f64 = v.as_f64().unwrap_or(0.0);
                let callback = callback_wrapper.borrow_mut().take().unwrap().0;
                callback(time);
            }))
        };

        // let render_id = web_sys::window()
        //     .unwrap_throw()
        //     .request_animation_frame(callback.as_ref().unchecked_ref())
        //     .unwrap_throw();

        let render_id = requestAnimationFrame(callback.as_ref().unchecked_ref());

        AnimationFrame {
            render_id,
            _closure: callback,
            callback_wrapper,
        }
    }
}
