use std::ops::Deref;
use std::rc::Rc;

use reactive_state::{Reducer, ReducerResult};
use rustc_hash::FxHashMap;
use serde::Serialize;

use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_proto::room_renditions::BasicRoomDto;

use crate::reducers::app_state::AppState;
use crate::utils::RoomId;

#[derive(<PERSON><PERSON>, Default, Debug, PartialEq, Serialize)]
pub struct RoomsState {
    #[serde(serialize_with = "pianorhythm_shared::util::hashmap_proto_serialize")]
    pub rooms: FxHashMap<RoomId, BasicRoomDto>,
}

#[derive(<PERSON><PERSON>, Default)]
pub struct RoomsStateReducer;

impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for RoomsStateReducer {
    fn reduce(&self, prev_state: &Rc<AppState>, action: &AppStateActions) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
        let events: Vec<AppStateEvents> = Vec::new();
        let mut effects: Vec<AppStateEffects> = Vec::new();

        let mut current_app_state = prev_state.deref().clone();
        let mut new_rooms_state = current_app_state.rooms_list_state;

        match action.action {
            AppStateActions_Action::SetRooms if action.has_roomsList() => {
                new_rooms_state.rooms.clear();
                action.get_roomsList().list.iter().for_each(|room| {
                    let room_id = RoomId::from(room.get_roomID());
                    new_rooms_state.rooms.insert(room_id, room.clone());
                });

                let mut effect = AppStateEffects::new();
                effect.set_action(AppStateEffects_Action::SetRooms);
                effect.set_roomsList(action.get_roomsList().clone());
                effects.push(effect);
            }
            AppStateActions_Action::AddRoom | AppStateActions_Action::UpdateRoom if action.has_basicRoomDto() => {
                let room_dto = action.get_basicRoomDto();
                let room_id = RoomId::from(room_dto.get_roomID());
                new_rooms_state.rooms.insert(room_id, room_dto.clone());

                let mut effect = AppStateEffects::new();

                if action.action == AppStateActions_Action::AddRoom {
                    effect.set_action(AppStateEffects_Action::AddRoom);
                } else {
                    effect.set_action(AppStateEffects_Action::UpdateRoom);
                }

                effect.set_basicRoomDto(room_dto.clone());
                effects.push(effect);
            }
            AppStateActions_Action::DeleteRoom if action.has_stringValue() => {
                let room_id = RoomId::from(action.get_stringValue());
                new_rooms_state.rooms.remove(&room_id);

                let mut effect = AppStateEffects::new();
                effect.set_action(AppStateEffects_Action::DeleteRoom);
                effect.set_roomId(action.get_stringValue().to_string());
                effects.push(effect);
            }
            _ => {}
        }

        current_app_state.rooms_list_state = new_rooms_state;
        ReducerResult {
            state: Rc::new(current_app_state),
            events,
            effects,
        }
    }
}
