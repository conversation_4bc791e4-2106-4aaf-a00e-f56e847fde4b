use std::ops::Deref;
use std::rc::Rc;

use reactive_state::{Reducer, ReducerR<PERSON>ult};
use serde::Serialize;

use pianorhythm_proto::client_message::WelcomeDto;
use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_proto::user_renditions::{UserClientDto, UserDto, UserStatus};

use crate::reducers::app_state::AppState;
use crate::reducers::current_room_state::update_user_from_command;
use crate::utils::SocketId;

/// Represents the state of the client, including the current user and socket ID.
///
/// Fields:
/// - `client`: Optionally holds the current user's client data.
/// - `socket_id`: Optionally holds the client's socket identifier.
#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Serialize)]
pub struct ClientState {
    #[serde(serialize_with = "pianorhythm_shared::util::option_proto_serialize")]
    pub client: Option<UserClientDto>,
    pub socket_id: Option<SocketId>,
}

impl ClientState {
    pub fn is_client(&self, input: &UserClientDto) -> bool {
        match &self.client {
            Some(x) => {
                x.get_userDto().get_socketID().to_lowercase() == input.get_userDto().get_socketID().to_lowercase()
            }
            _ => false
        }
    }
}

#[derive(Clone, Default)]
pub struct ClientStateReducer;

impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for ClientStateReducer {
    fn reduce(&self, prev_state: &Rc<AppState>, action: &AppStateActions) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
        let mut events: Vec<AppStateEvents> = Vec::new();
        let mut effects: Vec<AppStateEffects> = Vec::new();

        let mut current_app_state = prev_state.deref().clone();
        let mut new_client_state = current_app_state.client_state;

        match action.action {
            AppStateActions_Action::TriggerOfflineMode if action.get_boolValue() => {
                log::trace!("Setting user for offline mode");

                let mut effect = AppStateEffects::new();
                effect.set_action(AppStateEffects_Action::OnWelcome);

                let mut welcome_dto = WelcomeDto::new();
                let mut user_client = UserClientDto::new();
                let mut user_dto = UserDto::new();
                user_dto.set_username("offline".to_string());
                user_dto.set_usertag("off#line".to_string());
                user_dto.set_uuid("offline".to_string());
                user_dto.set_color("#001100".to_string());
                user_dto.set_socketID("00000000000000".to_string());
                user_dto.set_status(UserStatus::Unknown);
                user_client.set_userDto(user_dto);
                welcome_dto.set_userClientDto(user_client);
                effect.set_welcomeDto(welcome_dto.clone());
                effects.push(effect);

                let user_client_dto = welcome_dto.get_userClientDto().clone();
                effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ClientUpdated, |effect| {
                    effect.set_userClientDto(user_client_dto.clone());
                }));

                events.push(AppStateEvents::ClientLoaded);

                new_client_state.client = Some(user_client_dto.clone());
                new_client_state.socket_id = Some(SocketId::from(user_client_dto.get_userDto().socketID.as_str()));
            }
            AppStateActions_Action::SetClientLoaded if prev_state.client_state.client.is_none() && action.has_welcomeDto() => {
                let welcome_dto = action.get_welcomeDto();
                let user_client_dto = welcome_dto.get_userClientDto().clone();
                new_client_state.client = Some(user_client_dto.clone());
                new_client_state.socket_id = Some(SocketId::from(user_client_dto.get_userDto().socketID.as_str()));

                let mut effect = AppStateEffects::new();
                effect.set_action(AppStateEffects_Action::OnWelcome);
                effect.set_welcomeDto(welcome_dto.clone());
                effects.push(effect);

                effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ClientUpdated, |effect| {
                    effect.set_userClientDto(user_client_dto.clone());
                }));

                events.push(AppStateEvents::ClientLoaded);
            }
            AppStateActions_Action::UpdateClient if new_client_state.client.is_some() && action.has_userUpdateCommand() => {
                if let Some(mut client) = new_client_state.client.clone() {
                    let initial_user_dto = client.get_userDto().clone();
                    let user_dto = update_user_from_command(client.get_userDto(), &action.get_userUpdateCommand());

                    if initial_user_dto != user_dto {
                        effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ClientUpdated, |effect| {
                            effect.set_userClientDto(client.clone());
                        }));

                        client.set_userDto(user_dto);
                        new_client_state.client = Some(client);
                        events.push(AppStateEvents::ClientUpdated);
                    }
                }
            }
            _ => {}
        }

        current_app_state.client_state = new_client_state;
        ReducerResult {
            state: Rc::new(current_app_state),
            events,
            effects,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn given_trigger_offline_mode_action_when_reducing_then_client_state_is_set() {
        let reducer = ClientStateReducer;
        let prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::default();
        action.set_action(AppStateActions_Action::TriggerOfflineMode);
        action.set_boolValue(true);

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.client_state.client.is_some(), true);
        assert_eq!(result.state.client_state.socket_id.is_some(), true);
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.effects.len(), 2);
    }

    #[test]
    fn given_set_client_loaded_action_when_reducing_then_client_state_is_set() {
        let reducer = ClientStateReducer;
        let prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::default();
        action.set_action(AppStateActions_Action::SetClientLoaded);

        let mut welcome_dto = WelcomeDto::new();
        let mut user_client = UserClientDto::new();
        let mut user_dto = UserDto::new();
        user_dto.set_username("offline".to_string());
        user_dto.set_usertag("off#line".to_string());
        user_dto.set_uuid("offline".to_string());
        user_dto.set_color("#001100".to_string());
        user_dto.set_socketID("00000000000000".to_string());
        user_dto.set_status(UserStatus::Unknown);
        user_client.set_userDto(user_dto);
        welcome_dto.set_userClientDto(user_client);
        action.set_welcomeDto(welcome_dto.clone());

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.client_state.client.is_some(), true);
        assert_eq!(result.state.client_state.socket_id.is_some(), true);
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.effects.len(), 2);
    }

    #[test]
    fn given_update_client_action_when_reducing_then_client_state_is_updated() {
        let reducer = ClientStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::default();

        // Set up initial client
        action.set_action(AppStateActions_Action::SetClientLoaded);
        let mut welcome_dto = WelcomeDto::new();
        let mut user_client = UserClientDto::new();
        let mut user_dto = UserDto::new();
        user_dto.set_username("offline".to_string());
        user_dto.set_usertag("off#line".to_string());
        user_dto.set_uuid("offline".to_string());
        user_dto.set_color("#001100".to_string());
        user_dto.set_socketID("00000000000000".to_string());
        user_dto.set_status(UserStatus::Unknown);
        user_client.set_userDto(user_dto);
        welcome_dto.set_userClientDto(user_client);
        action.set_welcomeDto(welcome_dto.clone());
        prev_state = reducer.reduce(&prev_state, &action).state;

        let mut user_update_command = pianorhythm_proto::user_renditions::UserUpdateCommand::new();
        user_update_command.set_nickname("test_nickname".to_string());
        user_update_command.set_socketID("00000000000000".to_string());
        action.set_action(AppStateActions_Action::UpdateClient);
        action.set_userUpdateCommand(user_update_command.clone());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.client_state.client.clone().is_some(), true);
        assert_eq!(result.state.client_state.socket_id.is_some(), true);
        assert_eq!(result.state.client_state.client.clone().unwrap().get_userDto().get_nickname(), "test_nickname");
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.effects.len(), 1);
    }
}