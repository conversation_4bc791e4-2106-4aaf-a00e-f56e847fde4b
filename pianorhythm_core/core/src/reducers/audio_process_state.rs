use std::ops::Deref;
use std::rc::Rc;

use derivative::Derivative;
use reactive_state::{Reducer, ReducerResult};
use rustc_hash::{FxHashMap, FxHashSet};
use serde::Serialize;

use pianorhythm_proto::midi_renditions::{ActiveChannelsMode, Instrument};
use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action, SocketIdWithInt32};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;

use crate::reducers::app_state::AppState;
use crate::utils::{create_simple_channel_instrument_payload, SocketId};

#[derive(Clone, Debug, PartialEq, Serialize, Derivative)]
#[derivative(Default)]
pub struct AudioProcessState {
    pub muted_users: FxHashSet<String>,
    pub server_time_offset: f32,
    #[derivative(Default(value = "pianorhythm_shared::audio::MAX_VELOCITY_USER_PERCENTAGE as f32"))]
    pub global_velocity_percentage: f32,
    pub muted_everyone_else: bool,
    pub user_velocity_percentages: FxHashMap<SocketId, f32>,
    pub hashed_socket_ids: FxHashMap<SocketId, u32>,
    pub user_volumes: FxHashMap<SocketId, u32>,
    pub client_socket_id_hashed: Option<u32>,
    pub initialized: bool,
    pub use_separate_drum_kit: bool,
    pub listen_to_program_changes: bool,
    pub is_drum_channel_muted: bool,
    pub equalizer_enabled: bool,
    pub reverb_enabled: bool,
    pub apply_velocity_curve: bool,
    pub client_is_muted: bool,
    pub primary_channel: u8,
    pub max_multi_mode_channels: u8,
    #[serde(serialize_with = "pianorhythm_shared::util::vec_proto_serialize")]
    pub instruments: Vec<Instrument>,
    #[serde(serialize_with = "pianorhythm_shared::util::proto_serialize")]
    pub slot_mode: ActiveChannelsMode,
}

#[derive(Clone, Default)]
pub struct AudioProcessStateReducer;

impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for AudioProcessStateReducer {
    fn reduce(&self, prev_state: &Rc<AppState>, action: &AppStateActions) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
        let mut events: Vec<AppStateEvents> = Vec::new();
        let mut effects: Vec<AppStateEffects> = Vec::new();
        let current_state = &prev_state.deref().clone().audio_process_state;
        let mut new_audio_state = current_state.clone();

        let mut set_primary_channel = |primary_channel: u8| {
            if current_state.primary_channel != primary_channel {
                new_audio_state.primary_channel = primary_channel;
            }
        };

        let mut set_slot_mode = |_slot_mode: &ActiveChannelsMode| {
            let slot_mode = _slot_mode.clone();
            if current_state.slot_mode != slot_mode {
                new_audio_state.slot_mode = slot_mode;

                // Primary channel must be one of the enabled channels
                if new_audio_state.slot_mode == ActiveChannelsMode::MULTI {
                    #[cfg(feature = "use_synth")] {
                        let channel = pianorhythm_synth::get_all_audio_channels(None)
                            .iter()
                            .find(|x| x.active && x.channel < 3)
                            .map(|x| x.channel)
                            .unwrap_or_default();
                        set_primary_channel(channel as u8);
                    }
                }
            }
        };

        let mut add_synth_user = |value: &SocketIdWithInt32| {
            let socket_id = value.get_socketId();

            if !socket_id.is_empty() {
                let hash_value = value.get_int32Value();
                new_audio_state.hashed_socket_ids.insert(SocketId::from(socket_id), hash_value);

                // Setting synth socket id for client
                let mut added_event_for_client = false;
                if let Some(client_state) = &prev_state.client_state.socket_id {
                    if new_audio_state.client_socket_id_hashed.is_none()
                        && pianorhythm_shared::util::hash_string_to_u32(client_state.to_string()) == hash_value
                    {
                        log::info!("[add_synth_user] Successfully AddedClientSynthUser");
                        new_audio_state.client_socket_id_hashed = Some(hash_value);
                        events.push(AppStateEvents::AddedClientSynthUser);
                        added_event_for_client = true;
                    }
                } else {
                    log::warn!("[add_synth_user] Socket ID value empty for client state.");
                }

                if !added_event_for_client {
                    events.push(AppStateEvents::AddedSynthUser);
                }
            } else {
                log::warn!("[add_synth_user] Socket ID value empty when adding synth user.");
            }
        };

        match action.action {
            AppStateActions_Action::InitializeAudioState if !current_state.initialized => {
                new_audio_state.instruments = vec![];
                new_audio_state.initialized = true;
                new_audio_state.max_multi_mode_channels = pianorhythm_shared::defaults::AUDIO_MULTIMODE_MAX_CHANNELS;

                set_primary_channel(pianorhythm_shared::audio::DEFAULT_PRIMARY_CHANNEL);
                events.push(AppStateEvents::AudioStateInitialized);
            }
            AppStateActions_Action::AddHashedSynthUser if action.has_socketIdWithIn32() => {
                add_synth_user(action.get_socketIdWithIn32());
            }
            AppStateActions_Action::RemoveHashedSynthUser if action.has_socketId() => {
                if new_audio_state
                    .hashed_socket_ids
                    .remove(&SocketId::from(action.get_socketId().to_string()))
                    .is_some()
                {
                    events.push(AppStateEvents::RemovedSynthUser);
                }
            }
            AppStateActions_Action::SetLoadedInstruments if action.has_instrumentsList() => {
                let instruments_list = action.get_instrumentsList().get_instruments().to_vec();

                if new_audio_state.instruments != instruments_list {
                    events.push(AppStateEvents::SynthSoundfontLoaded);
                }

                new_audio_state.instruments = instruments_list;
                events.push(AppStateEvents::InstrumentsLoaded);
            }
            AppStateActions_Action::SetSlotMode if action.has_slotMode() && current_state.initialized => {
                set_slot_mode(&action.get_slotMode());
            }
            AppStateActions_Action::IncrementSlotMode if current_state.initialized => {
                let slot_mode = current_state.slot_mode.clone();
                set_slot_mode(&pianorhythm_shared::util::get_next_slot_mode(&slot_mode));
            }
            AppStateActions_Action::SetIsDrumChannelMuted if current_state.initialized => {
                new_audio_state.is_drum_channel_muted = action.get_boolValue();
            }
            AppStateActions_Action::SetEqualizerEnabled if current_state.initialized => {
                new_audio_state.equalizer_enabled = action.get_boolValue();
            }
            AppStateActions_Action::SetReverbEnabled if current_state.initialized => {
                new_audio_state.reverb_enabled = action.get_boolValue();
            }
            AppStateActions_Action::AudioSetApplyVelocityCurve if current_state.initialized => {
                new_audio_state.apply_velocity_curve = action.get_boolValue();
            }
            AppStateActions_Action::SetClientIsMuted if current_state.initialized => {
                new_audio_state.client_is_muted = action.get_boolValue();
            }
            AppStateActions_Action::SetListenToProgramChanges if current_state.initialized => {
                new_audio_state.listen_to_program_changes = action.get_boolValue();
            }
            AppStateActions_Action::SetMaxMultiModeChannels if action.has_uint32Value() && current_state.initialized => {
                new_audio_state.max_multi_mode_channels = action.get_uint32Value() as u8;
            }
            AppStateActions_Action::SetPrimaryChannel => {
                set_primary_channel(action.get_uint32Value() as u8);
            }
            AppStateActions_Action::SetServerTimeOffset if action.has_doubleValue() => {
                new_audio_state.server_time_offset = action.get_doubleValue() as f32;
            }
            AppStateActions_Action::JoinedRoom if action.has_joinedRoomData() => {
                new_audio_state.hashed_socket_ids.clear();
                new_audio_state.user_volumes.clear();
                events.push(AppStateEvents::ClearedSynthUsers);
            }
            AppStateActions_Action::MuteEveryoneElse => {
                let value = action.get_boolValue();
                new_audio_state.muted_everyone_else = value;
                events.push(if value { AppStateEvents::RoomMuted } else { AppStateEvents::RoomUnmuted });
            }
            AppStateActions_Action::ResetAudioChannelsToDefault => {
                set_primary_channel(pianorhythm_shared::audio::DEFAULT_PRIMARY_CHANNEL);
            }
            AppStateActions_Action::SetIsPlayingDrumsMode if action.get_boolValue() => {
                let target_channel = pianorhythm_shared::midi::DRUM_CHANNEL;

                if let Some(drum_instrument) = current_state.instruments
                    .iter()
                    .find(|x| x.bank == pianorhythm_shared::midi::DEFAULT_DRUM_BANK_1 as u32 || x.bank == pianorhythm_shared::midi::DEFAULT_DRUM_BANK_2 as u32)
                {
                    set_slot_mode(&ActiveChannelsMode::ALL);
                    set_primary_channel(target_channel);

                    #[cfg(feature = "use_synth")]
                    {
                        #[cfg(debug_assertions)]
                        log::trace!("[SetIsPlayingDrumsMode] Setting drum instrument on channel: {:?}", drum_instrument);

                        pianorhythm_synth::client_set_instrument_on_channel(&create_simple_channel_instrument_payload(
                            target_channel as u32,
                            drum_instrument.bank,
                            drum_instrument.preset,
                        ));
                    }
                } else {
                    log::warn!("[SetIsPlayingDrumsMode] No drum instrument found in soundfont.");
                }
            }
            AppStateActions_Action::SetUsers if action.has_userDtoList() => {
                for user in action.get_userDtoList().get_userDto() {
                    let mut payload = SocketIdWithInt32::new();
                    payload.set_socketId(user.get_socketID().to_string());
                    payload.set_int32Value(pianorhythm_shared::util::hash_string_to_u32(user.get_socketID().to_string()));
                    add_synth_user(&payload);
                }
            }
            AppStateActions_Action::AddUser if action.has_userDto() => {
                let user = action.get_userDto();
                let mut payload = SocketIdWithInt32::new();
                payload.set_socketId(user.get_socketID().to_string());
                payload.set_int32Value(pianorhythm_shared::util::hash_string_to_u32(user.get_socketID().to_string()));
                add_synth_user(&payload);
            }
            AppStateActions_Action::UpdateUser => {}
            AppStateActions_Action::RemoveUser if action.has_sourceSocketID() => {
                let socket_id = action.get_sourceSocketID().to_string();
                new_audio_state.muted_users.remove(&socket_id);
                new_audio_state.hashed_socket_ids.remove(&SocketId::from(socket_id));
            }
            AppStateActions_Action::SetUserMuted if action.has_sourceSocketID() => {
                let socket_id = action.get_sourceSocketID().to_string();
                if action.get_boolValue() {
                    new_audio_state.muted_users.insert(socket_id);
                } else {
                    new_audio_state.muted_users.remove(&socket_id);
                }
            }
            _ => {}
        }

        // Handle effects for changes
        if current_state.slot_mode != new_audio_state.slot_mode {
            effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::SetSlotMode, |effect| effect.set_slotMode(new_audio_state.slot_mode)));
        }

        if current_state.primary_channel != new_audio_state.primary_channel {
            effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::SetPrimaryChannel, |effect| {
                effect.set_uint32Value(new_audio_state.primary_channel as u32)
            }));
        }

        if current_state.is_drum_channel_muted != new_audio_state.is_drum_channel_muted {
            effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::DrumChannelIsMuted, |effect| {
                effect.set_boolValue(new_audio_state.is_drum_channel_muted);
            }));
        }

        if current_state.equalizer_enabled != new_audio_state.equalizer_enabled {
            effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::EqualizerEnabled, |effect| {
                effect.set_boolValue(new_audio_state.equalizer_enabled);
            }));
        }

        if current_state.reverb_enabled != new_audio_state.reverb_enabled {
            effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ReverbEnabled, |effect| {
                effect.set_boolValue(new_audio_state.reverb_enabled);
            }));
        }

        if current_state.apply_velocity_curve != new_audio_state.apply_velocity_curve {
            effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::AudioApplyVelocityCurve, |effect| {
                effect.set_boolValue(new_audio_state.apply_velocity_curve);
            }));
        }

        if current_state.max_multi_mode_channels != new_audio_state.max_multi_mode_channels {
            effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::MaxMultiModeChannels, |effect| {
                effect.set_uint32Value(new_audio_state.max_multi_mode_channels as u32);
            }));
        }

        if current_state.client_is_muted != new_audio_state.client_is_muted {
            effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ClientIsMuted, |effect| {
                effect.set_boolValue(new_audio_state.client_is_muted);
            }));
        }

        if current_state.listen_to_program_changes != new_audio_state.listen_to_program_changes {
            effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ListenToProgramChanges, |effect| {
                effect.set_boolValue(new_audio_state.listen_to_program_changes);
            }));
        }

        if action.has_sourceSocketID() {
            effects.iter_mut().for_each(|effect| {
                effect.set_sourceSocketID(action.get_sourceSocketID().to_string());
            });
        }

        let mut new_state = prev_state.deref().clone();
        new_state.audio_process_state = new_audio_state.clone();

        if prev_state.audio_process_state.instruments != new_state.audio_process_state.instruments {
            events.push(AppStateEvents::AudioInstrumentsUpdated);
        }

        ReducerResult {
            state: Rc::new(new_state),
            events,
            effects,
        }
    }
}

#[cfg(test)]
mod tests {
    use protobuf::RepeatedField;

    use super::*;

    #[test]
    fn given_initialize_audio_state_action_when_reducing_then_audio_state_is_initialized() {
        let reducer = AudioProcessStateReducer;
        let prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.initialized, true);
        assert_eq!(result.events, vec![AppStateEvents::AudioStateInitialized]);
    }

    #[test]
    fn given_add_hashed_synth_user_action_when_reducing_then_synth_user_is_added() {
        let reducer = AudioProcessStateReducer;
        let prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::AddHashedSynthUser);
        action.set_socketIdWithIn32(SocketIdWithInt32 {
            socketId: "test".to_string(),
            int32Value: 123,
            ..Default::default()
        });
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.hashed_socket_ids.len(), 1);
        assert_eq!(result.events, vec![AppStateEvents::AddedSynthUser]);
    }

    #[test]
    fn given_remove_hashed_synth_user_action_when_reducing_then_synth_user_is_removed() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::AddHashedSynthUser);
        action.set_socketIdWithIn32(SocketIdWithInt32 {
            socketId: "test".to_string(),
            int32Value: 123,
            ..Default::default()
        });
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::RemoveHashedSynthUser);
        action.set_socketId("test".to_string());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.hashed_socket_ids.len(), 0);
        assert_eq!(result.events, vec![AppStateEvents::RemovedSynthUser]);
    }

    #[test]
    fn given_set_loaded_instruments_action_when_reducing_then_instruments_are_set() {
        let reducer = AudioProcessStateReducer;
        let prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetLoadedInstruments);
        action.set_instrumentsList(pianorhythm_proto::midi_renditions::InstrumentsList {
            instruments: RepeatedField::from_vec(vec![Instrument {
                bank: 1,
                preset: 1,
                ..Default::default()
            }]),
            ..Default::default()
        });

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.instruments.len(), 1);
        assert_eq!(result.events, vec![AppStateEvents::SynthSoundfontLoaded, AppStateEvents::InstrumentsLoaded, AppStateEvents::AudioInstrumentsUpdated]);
    }

    #[test]
    fn given_set_slot_mode_action_and_state_is_initialized_when_reducing_then_slot_mode_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetSlotMode);
        action.set_slotMode(ActiveChannelsMode::ALL);
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.slot_mode, ActiveChannelsMode::ALL);
        assert_eq!(result.effects.len(), 1);
        assert_eq!(result.effects[0].action, AppStateEffects_Action::SetSlotMode);
    }

    #[test]
    fn given_increment_slot_mode_action_and_state_is_initialized_when_reducing_then_slot_mode_is_incremented() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::IncrementSlotMode);
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.slot_mode, ActiveChannelsMode::MULTI);
        assert_eq!(result.effects.len(), 1);
        assert_eq!(result.effects[0].action, AppStateEffects_Action::SetSlotMode);
    }

    #[test]
    fn given_set_is_drum_channel_muted_action_and_state_is_initialized_when_reducing_then_is_drum_channel_muted_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetIsDrumChannelMuted);
        action.set_boolValue(true);
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.is_drum_channel_muted, true);
        assert_eq!(result.effects.len(), 1);
        assert_eq!(result.effects[0].action, AppStateEffects_Action::DrumChannelIsMuted);
    }

    #[test]
    fn given_set_equalizer_enabled_action_and_state_is_initialized_when_reducing_then_equalizer_enabled_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetEqualizerEnabled);
        action.set_boolValue(true);
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.equalizer_enabled, true);
        assert_eq!(result.effects.len(), 1);
        assert_eq!(result.effects[0].action, AppStateEffects_Action::EqualizerEnabled);
    }

    #[test]
    fn given_set_reverb_enabled_action_and_state_is_initialized_when_reducing_then_reverb_enabled_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetReverbEnabled);
        action.set_boolValue(true);
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.reverb_enabled, true);
        assert_eq!(result.effects.len(), 1);
        assert_eq!(result.effects[0].action, AppStateEffects_Action::ReverbEnabled);
    }

    #[test]
    fn given_set_client_is_muted_action_and_state_is_initialized_when_reducing_then_client_is_muted_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetClientIsMuted);
        action.set_boolValue(true);
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.client_is_muted, true);
        assert_eq!(result.effects.len(), 1);
        assert_eq!(result.effects[0].action, AppStateEffects_Action::ClientIsMuted);
    }

    #[test]
    fn given_set_listen_to_program_changes_action_and_state_is_initialized_when_reducing_then_listen_to_program_changes_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetListenToProgramChanges);
        action.set_boolValue(true);
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.listen_to_program_changes, true);
        assert_eq!(result.effects.len(), 1);
        assert_eq!(result.effects[0].action, AppStateEffects_Action::ListenToProgramChanges);
    }

    #[test]
    fn given_set_max_multi_mode_channels_action_and_state_is_initialized_when_reducing_then_max_multi_mode_channels_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetMaxMultiModeChannels);
        let target_channels = 1;
        action.set_uint32Value(target_channels);
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.audio_process_state.max_multi_mode_channels, target_channels as u8);
        assert_eq!(result.effects.len(), 1);
        assert_eq!(result.effects[0].action, AppStateEffects_Action::MaxMultiModeChannels);
    }

    #[test]
    fn given_set_primary_channel_action_when_reducing_then_primary_channel_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        let target_channel = 1;
        action.set_action(AppStateActions_Action::SetPrimaryChannel);
        action.set_uint32Value(target_channel);
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.primary_channel, target_channel as u8);
    }

    #[test]
    fn given_set_server_time_offset_action_when_reducing_then_server_time_offset_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        let target_offset = 1.0;
        action.set_action(AppStateActions_Action::SetServerTimeOffset);
        action.set_doubleValue(target_offset);
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.server_time_offset, target_offset as f32);
    }

    #[test]
    fn given_joined_room_action_when_reducing_then_synth_users_are_cleared() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::JoinedRoom);
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.hashed_socket_ids.len(), 0);
    }

    #[test]
    fn given_mute_everyone_else_action_when_reducing_then_mute_everyone_else_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::MuteEveryoneElse);
        action.set_boolValue(true);
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.muted_everyone_else, true);
    }

    #[test]
    fn given_reset_audio_channels_to_default_action_when_reducing_then_primary_channel_is_set_to_default() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        let target_channel = 1;
        action.set_action(AppStateActions_Action::SetPrimaryChannel);
        action.set_uint32Value(target_channel);
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::ResetAudioChannelsToDefault);
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.primary_channel, pianorhythm_shared::audio::DEFAULT_PRIMARY_CHANNEL);
    }

    #[test]
    fn given_set_is_playing_drums_mode_action_and_state_is_initialized_when_reducing_then_drum_channel_is_set() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAudioState);
        prev_state = reducer.reduce(&prev_state, &action).state;

        // Load a drum instrument
        action.set_action(AppStateActions_Action::SetLoadedInstruments);
        action.set_instrumentsList(pianorhythm_proto::midi_renditions::InstrumentsList {
            instruments: RepeatedField::from_vec(vec![Instrument {
                bank: pianorhythm_shared::midi::DEFAULT_DRUM_BANK_1 as u32,
                preset: 1,
                ..Default::default()
            }]),
            ..Default::default()
        });
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetIsPlayingDrumsMode);
        action.set_boolValue(true);
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.slot_mode, ActiveChannelsMode::ALL);
        assert_eq!(prev_state.audio_process_state.primary_channel, pianorhythm_shared::midi::DRUM_CHANNEL);
    }

    #[test]
    fn given_set_users_action_when_reducing_then_synth_users_are_added() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        let mut user = pianorhythm_proto::user_renditions::UserDto::new();
        user.set_socketID("test".to_string());
        action.set_action(AppStateActions_Action::AddUser);
        action.set_userDto(user.clone());
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetUsers);
        action.set_userDtoList(pianorhythm_proto::client_message::UserDtoList {
            userDto: RepeatedField::from_vec(vec![user.clone()]),
            ..Default::default()
        });
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.hashed_socket_ids.len(), 1);
    }

    #[test]
    fn given_add_user_action_when_reducing_then_synth_user_is_added() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        let mut user = pianorhythm_proto::user_renditions::UserDto::new();
        user.set_socketID("test".to_string());
        action.set_action(AppStateActions_Action::AddUser);
        action.set_userDto(user.clone());
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.hashed_socket_ids.len(), 1);
    }

    #[test]
    fn given_remove_user_action_when_reducing_then_synth_user_is_removed() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        let mut user = pianorhythm_proto::user_renditions::UserDto::new();
        user.set_socketID("test".to_string());
        action.set_action(AppStateActions_Action::AddUser);
        action.set_userDto(user.clone());
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::RemoveUser);
        action.set_sourceSocketID("test".to_string());
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.hashed_socket_ids.len(), 0);
    }

    #[test]
    fn given_set_user_muted_action_when_reducing_then_user_is_muted() {
        let reducer = AudioProcessStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut action = AppStateActions::new();
        let mut user = pianorhythm_proto::user_renditions::UserDto::new();
        user.set_socketID("test".to_string());
        action.set_action(AppStateActions_Action::AddUser);
        action.set_userDto(user.clone());
        prev_state = reducer.reduce(&prev_state, &action).state;

        action.set_action(AppStateActions_Action::SetUserMuted);
        action.set_sourceSocketID("test".to_string());
        action.set_boolValue(true);
        prev_state = reducer.reduce(&prev_state, &action).state;

        assert_eq!(prev_state.audio_process_state.muted_users.len(), 1);
    }
}