use crate::reducers::app_state::AppState;
use pianorhythm_proto::pianorhythm_actions::AppStateActions;
use pianorhythm_proto::pianorhythm_effects::AppStateEffects;
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use reactive_state::middleware::{Middleware, NotifyFn, ReduceFn, ReduceMiddlewareResult};

pub type EmitNotificationFunc = Box<dyn Fn(Vec<AppStateEvents>) -> ()>;
pub type EmitNotificationFunc2 = Box<dyn Fn(AppStateEffects) -> ()>;

pub struct BroadcastAppStateMiddleware<'c> {
    #[cfg(not(target_arch = "wasm32"))]
    pub on_notify_events: Option<EmitNotificationFunc>,
    pub core_api: &'c crate::types::CoreClientApiType,
}

impl<'c> Middleware<AppState, AppStateActions, AppStateEvents, AppStateEffects> for BroadcastAppStateMiddleware<'c> {
    fn on_reduce(
        &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, action: Option<&AppStateActions>,
        reduce: ReduceFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
        reduce(store, action)
    }

    fn process_effect(
        &self, _store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, effect: AppStateEffects,
    ) -> Option<AppStateEffects> {
        self.core_api.dispatch_app_effect(&effect);
        Some(effect)
    }

    fn on_notify(
        &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, events: Vec<AppStateEvents>,
        notify: NotifyFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> Vec<AppStateEvents> {
        let events = notify(store, events);

        if events.len() > 0 {
            #[cfg(not(target_arch = "wasm32"))]
            if let Some(func) = &self.on_notify_events {
                func(events.clone());
            }

            #[cfg(target_arch = "wasm32")]
            {
                crate::utils::broadcast_app_events(&events);
            }
        }

        events
    }
}
