use std::fmt::Debug;
use std::hash::Hash;

use reactive_state::middleware::{Middleware, NotifyFn, ReduceFn, ReduceMiddlewareResult};
use reactive_state::Store;

pub enum LogLevel {
    Trace,
    Debug,
    Warn,
    Info,
}

impl LogLevel {
    pub fn log<S: AsRef<str>>(&self, message: S) {
        log::info!("{}", message.as_ref())
        // match self {
        //     LogLevel::Trace => log::trace!("{}", message.as_ref()),
        //     LogLevel::Debug => log::debug!("{}", message.as_ref()),
        //     LogLevel::Warn => log::warn!("{}", message.as_ref()),
        //     LogLevel::Info => log::info!("{}", message.as_ref()),
        // }
    }
}

impl Default for LogLevel {
    fn default() -> Self {
        LogLevel::Info
    }
}

/// Logging [Middleware](crate::middleware::Middleware) which uses the
/// [log](log) macros to publish actions/events that occur within the
/// [Store](crate::Store).
///
/// See [simple_logger](super::simple_logger) for more details.
pub struct PianoRhythmSimpleLoggerMiddleware {
    log_level: LogLevel,
}

impl PianoRhythmSimpleLoggerMiddleware {
    pub fn new() -> Self {
        PianoRhythmSimpleLoggerMiddleware {
            log_level: LogLevel::default(),
        }
    }

    pub fn log_level(mut self, log_level: LogLevel) -> Self {
        self.log_level = log_level;
        self
    }
}

impl Default for PianoRhythmSimpleLoggerMiddleware {
    fn default() -> Self {
        PianoRhythmSimpleLoggerMiddleware::new()
    }
}

impl<State, Action, Event, Effect> Middleware<State, Action, Event, Effect> for PianoRhythmSimpleLoggerMiddleware
where
    Event: Clone + Hash + Eq + Debug,
    State: Debug,
    Action: Debug,
    Effect: Debug,
{
    fn on_reduce(
        &self, store: &Store<State, Action, Event, Effect>, action: Option<&Action>, reduce: ReduceFn<State, Action, Event, Effect>,
    ) -> ReduceMiddlewareResult<Event, Effect> {
        let was_action = match &action {
            Some(action) => {
                self.log_level.log(format!("[Reactive Logger] prev state: {:?}", store.state()));
                self.log_level.log(format!("[Reactive Logger] action: {:?}", action));
                true
            }
            None => {
                self.log_level.log("[Reactive Logger] action: None");
                false
            }
        };

        let events = reduce(store, action);

        if was_action {
            self.log_level.log(format!("[Reactive Logger] next state: {:?}", store.state()));
        }

        events
    }

    fn process_effect(&self, _store: &Store<State, Action, Event, Effect>, effect: Effect) -> Option<Effect> {
        self.log_level.log(format!("[Reactive Logger] effect: {:?}", effect));
        Some(effect)
    }

    fn on_notify(
        &self, store: &Store<State, Action, Event, Effect>, events: Vec<Event>, notify: NotifyFn<State, Action, Event, Effect>,
    ) -> Vec<Event> {
        for event in &events {
            self.log_level.log(format!("[Reactive Logger] event: {:?}", event));
        }

        notify(store, events)
    }
}
