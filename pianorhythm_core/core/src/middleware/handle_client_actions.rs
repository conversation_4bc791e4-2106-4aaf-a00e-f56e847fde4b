use protobuf::Message;
use reactive_state::middleware::{Middleware, NotifyFn, ReduceFn, ReduceMiddlewareResult};

use crate::reducers::app_state::AppState;
use crate::utils::create_system_chat_message;
use pianorhythm_proto::client_message::{ClientMessage, ClientMessageType, CommandResponseType};
use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
use pianorhythm_proto::pianorhythm_effects::AppStateEffects;
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_proto::server_message::{
    AvatarCommandDto, AvatarCommandDto_AvatarCommandType, ServerCommandDU, ServerCommandDU_CommandType, ServerMessage, ServerMessageType
};
use pianorhythm_shared::WORLD::AVATAR::AvatarCustomizationValidation;

pub struct HandleAppStateActionsMiddleware<'c> {
    pub core_api: &'c crate::types::CoreClientApiType,
}

impl<'c> Middleware<AppState, AppStateActions, AppStateEvents, AppStateEffects>
    for HandleAppStateActionsMiddleware<'c>
{
    fn on_reduce(
        &self,
        store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
        action: Option<&AppStateActions>,
        reduce: ReduceFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
        if let Some(action) = action {
            match action.action {
                AppStateActions_Action::JoinRoomByName if action.has_joinRoomByNameRequest() => {
                    let mut server_message = ServerMessage::new();
                    server_message.set_messageType(ServerMessageType::JoinRoomByName);
                    server_message.set_joinRoomRequest(action.get_joinRoomByNameRequest().clone());

                    self.core_api
                        .ws_emit_binary(server_message.write_to_bytes().unwrap_or_default());
                }
                AppStateActions_Action::JoinRoomById => {
                    let mut server_message = ServerMessage::new();
                    server_message.set_messageType(ServerMessageType::ServerCommand);

                    let mut server_command = ServerCommandDU::new();
                    if action.has_roomIdWithPassword() {
                        server_command.set_commandType(ServerCommandDU_CommandType::JoinRoomWithPassword);
                        server_command.set_roomIDAndPassword(action.get_roomIdWithPassword().clone());
                    } else if action.has_roomId() {
                        server_command.set_commandType(ServerCommandDU_CommandType::Join);
                        server_command.set_roomIDorName(action.get_roomId().to_string());
                    }

                    server_message.set_serverCommand(server_command);

                    self.core_api
                        .ws_emit_binary(server_message.write_to_bytes().unwrap_or_default());
                }
                AppStateActions_Action::JoinNextAvailableLobby => {
                    let mut server_message = ServerMessage::new();
                    server_message.set_messageType(ServerMessageType::JoinNextAvailableLobby);
                    self.core_api
                        .ws_emit_binary(server_message.write_to_bytes().unwrap_or_default());
                }

                // ---------------------------------------
                // Avatar Related Commands
                // ---------------------------------------
                AppStateActions_Action::SetAvatarPosition if action.has_avatarWorldPosition() => {
                    let mut server_message = ServerMessage::new();
                    server_message.set_messageType(ServerMessageType::AvatarCommand);

                    let mut command = AvatarCommandDto::new();
                    command.set_commandType(AvatarCommandDto_AvatarCommandType::SetPosition);
                    command.set_worldPosition(action.get_avatarWorldPosition().clone());
                    server_message.set_avatarCommand(command);

                    self.core_api
                        .ws_emit_binary(server_message.write_to_bytes().unwrap_or_default());
                }
                AppStateActions_Action::SetAvatarPianoBench if action.has_avatarPianoBench() => {
                    let mut server_message = ServerMessage::new();
                    server_message.set_messageType(ServerMessageType::AvatarCommand);

                    let mut command = AvatarCommandDto::new();
                    command.set_commandType(AvatarCommandDto_AvatarCommandType::SetPianoBenchTarget);
                    command.set_intValue(action.get_avatarPianoBench());
                    server_message.set_avatarCommand(command);

                    self.core_api
                        .ws_emit_binary(server_message.write_to_bytes().unwrap_or_default());
                }
                AppStateActions_Action::UpdateAvatarCustomization if action.has_avatarCustomizationData() => {
                    match action.get_avatarCustomizationData().validate_avatar_customization() {
                        Ok(_) => {
                            #[cfg(debug_assertions)]
                            log::debug!("Validated avatar customization: {:?}", action.get_avatarCustomizationData());

                            let mut server_message = ServerMessage::new();
                            server_message.set_messageType(ServerMessageType::AvatarCommand);

                            let mut command = AvatarCommandDto::new();
                            command.set_commandType(AvatarCommandDto_AvatarCommandType::SetAvatarCustomizationData);
                            command.set_avatarCustomizationData(action.get_avatarCustomizationData().clone());
                            server_message.set_avatarCommand(command);

                            self.core_api
                                .ws_emit_binary(server_message.write_to_bytes().unwrap_or_default());
                        }
                        Err(errors) => {
                            log::error!("Failed to validate avatar customization: {:?}", errors);
                        }
                    }
                }
                _ => {}
            }
        }
        reduce(store, action)
    }

    fn process_effect(
        &self,
        _store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
        effect: AppStateEffects,
    ) -> Option<AppStateEffects> {
        Some(effect)
    }

    fn on_notify(
        &self,
        store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
        events: Vec<AppStateEvents>,
        notify: NotifyFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> Vec<AppStateEvents> {
        let events = notify(store, events);
        events
    }
}

pub fn client_message_to_action(client_message: &ClientMessage) -> AppStateActions {
    let mut action = AppStateActions::new();
    action.set_action(AppStateActions_Action::Unknown);

    match client_message.messageType {
        ClientMessageType::Welcome if client_message.has_welcomeDto() => {
            action.set_action(AppStateActions_Action::SetClientLoaded);
            action.set_welcomeDto(client_message.get_welcomeDto().clone());
        }
        ClientMessageType::ClientUpdated if client_message.has_userUpdateCommand() => {
            action.set_action(AppStateActions_Action::UpdateClient);
            action.set_userUpdateCommand(client_message.get_userUpdateCommand().clone());
        }
        ClientMessageType::UsersInRoomList if client_message.has_userDtoList() => {
            action.set_action(AppStateActions_Action::SetUsers);
            action.set_userDtoList(client_message.get_userDtoList().clone());
        }
        ClientMessageType::UserJoined if client_message.has_userDto() => {
            action.set_action(AppStateActions_Action::AddUser);
            action.set_userDto(client_message.get_userDto().clone())
        }
        ClientMessageType::UserUpdated if client_message.has_userUpdateCommand() => {
            action.set_action(AppStateActions_Action::UpdateUser);
            action.set_userUpdateCommand(client_message.get_userUpdateCommand().clone());
        }
        ClientMessageType::UserLeft if client_message.has_socketId() => {
            action.set_action(AppStateActions_Action::RemoveUser);
            action.set_socketId(client_message.get_socketId().to_string());
        }
        ClientMessageType::JoinedRoomSuccess if client_message.has_joinedRoomData() => {
            action.set_action(AppStateActions_Action::JoinedRoom);
            action.set_joinedRoomData(client_message.get_joinedRoomData().clone());
        }
        ClientMessageType::BasicRoomApiCreated | ClientMessageType::BasicRoomApiUpdated
            if client_message.has_basicRoomDto() =>
        {
            action.set_action(AppStateActions_Action::AddRoom);
            action.set_basicRoomDto(client_message.get_basicRoomDto().clone());
        }
        ClientMessageType::BasicRoomApiDeleted if client_message.has_stringValue() => {
            action.set_action(AppStateActions_Action::DeleteRoom);
            action.set_stringValue(client_message.get_stringValue().to_string());
        }
        ClientMessageType::LoadRoomChatHistory if client_message.has_roomChatHistory() => {
            action.set_action(AppStateActions_Action::SetRoomChatMessages);
            action.set_roomChatHistory(client_message.get_roomChatHistory().clone());
        }
        ClientMessageType::RoomChatMessage if client_message.has_chatMessageDto() => {
            action.set_action(AppStateActions_Action::AddRoomChatMessage);
            action.set_chatMessageDto(client_message.get_chatMessageDto().clone());
        }
        ClientMessageType::UsersTyping if client_message.has_socketIDList() => {
            action.set_action(AppStateActions_Action::SetUsersTyping);
            action.set_socketIdList(client_message.get_socketIDList().clone());
        }
        ClientMessageType::LoadRoomWelcomeMessage if client_message.has_stringValue() => {
            action.set_action(AppStateActions_Action::AddRoomChatMessage);
            action.set_chatMessageDto(create_system_chat_message(client_message.get_stringValue()));
        }
        ClientMessageType::EditMessageByID if client_message.has_chatMessageDto() => {
            action.set_action(AppStateActions_Action::EditRoomChatMessage);
            action.set_chatMessageDto(client_message.get_chatMessageDto().clone());
        }
        ClientMessageType::ClearChatByMessageID if client_message.has_stringValue() => {
            action.set_action(AppStateActions_Action::DeleteRoomChatMessage);
            action.set_stringValue(client_message.get_stringValue().to_string());
        }
        ClientMessageType::ClearChat => {
            action.set_action(AppStateActions_Action::ClearChat);
        }
        ClientMessageType::ClearChatByUsername if client_message.has_usertag() => {
            action.set_action(AppStateActions_Action::ClearChatByUsername);
            action.set_usertag(client_message.get_usertag().to_string());
        }
        ClientMessageType::ClearChatBySocketID if client_message.has_socketId() => {
            action.set_action(AppStateActions_Action::ClearChatBySocketID);
            action.set_socketId(client_message.get_socketId().to_string());
        }
        ClientMessageType::GetRoomFullDetails if client_message.has_roomFullDetails() => {
            action.set_action(AppStateActions_Action::GetRoomFullDetails);
            action.set_roomFullDetails(client_message.get_roomFullDetails().clone());
        }
        ClientMessageType::RoomSettingsUpdated if client_message.has_roomSettings() => {
            action.set_action(AppStateActions_Action::SetRoomSettings);
            action.set_roomSettings(client_message.get_roomSettings().clone());
        }
        ClientMessageType::RoomOwnerUpdated if client_message.has_socketId() => {
            action.set_action(AppStateActions_Action::SetCurrentRoomOwner);
            action.set_socketId(client_message.get_socketId().to_string());
        }
        ClientMessageType::MidiMessage if client_message.has_midiMessageOutputDto() => {
            action.set_action(AppStateActions_Action::HandleMidiMessage);
            action.set_midiMessageOutputDto(client_message.get_midiMessageOutputDto().clone());
        }
        ClientMessageType::CmdResponse if client_message.has_commandResponse() => {
            let command_response = client_message.get_commandResponse().clone();
            match command_response.messageType {
                CommandResponseType::Error => {}
                CommandResponseType::ValidationError if command_response.has_validationErrorList() => {
                    action.set_action(AppStateActions_Action::ServerToClientMessage);
                    action.set_commandResponse(command_response.clone());
                }
                CommandResponseType::JoinRoomFail if command_response.has_joinRoomFailResponse() => {
                    let fail_response = command_response.get_joinRoomFailResponse();
                    action.set_action(AppStateActions_Action::FailToJoinRoom);
                    action.set_joinRoomFailResponse(fail_response.clone());
                }
                CommandResponseType::LeftRoom => {}
                CommandResponseType::RoomsList if command_response.has_roomsList() => {
                    action.set_action(AppStateActions_Action::SetRooms);
                    action.set_roomsList(command_response.get_roomsList().clone());
                }
                CommandResponseType::KickedUsersInRoomList => {}
                CommandResponseType::RoomSettings => {}
                CommandResponseType::RegistrationSuccess => {}
                CommandResponseType::Toast if command_response.has_stringValue() => {
                    action.set_action(AppStateActions_Action::EmitToast);
                    action.set_stringValue(command_response.get_stringValue().to_string());
                }
                CommandResponseType::EnterRoomPassword => {}
                _ => {}
            }
        }
        _ => {}
    }

    action
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::CoreClientApi;
    use pianorhythm_proto::client_message::{
        ClientValidationError, CommandResponse, CommandResponse_ClientValidationErrorList, CommandResponse_JoinRoomFailResponse, CommandResponse_RoomsList, JoinRoomFailType, MidiMessageOutputDto, SocketIdList
    };
    use pianorhythm_proto::pianorhythm_effects::AppStateEffects_Action;
    use pianorhythm_proto::room_renditions::BasicRoomDto;
    use pianorhythm_proto::server_message::ServerMessage_JoinRoomByNameRequest;
    use protobuf::Message;
    use reactive_state::middleware::Middleware;
    use reactive_state::{Reducer, ReducerResult, Store};
    use std::collections::VecDeque;
    use std::rc::Rc;
    use std::sync::{Arc, Mutex};

    // Mock CoreClientApi for testing
    #[derive(Default)]
    struct MockCoreClientApi {
        pub emitted_messages: Arc<Mutex<VecDeque<Vec<u8>>>>,
    }

    impl CoreClientApi for MockCoreClientApi {
        fn init(&mut self) {}

        fn ws_emit_binary(&self, bytes: Vec<u8>) {
            self.emitted_messages.lock().unwrap().push_back(bytes);
        }

        fn dispatch_app_effect(&self, _app_state_effect: &AppStateEffects) {}

        #[cfg(feature = "use_synth")]
        fn dispatch_midi_synth_event(&self, _event: &pianorhythm_synth::PianoRhythmSynthEvent) {}
    }

    // Simple test reducer
    struct TestReducer;
    impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for TestReducer {
        fn reduce(
            &self,
            state: &Rc<AppState>,
            _action: &AppStateActions,
        ) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
            ReducerResult {
                state: state.clone(),
                events: vec![],
                effects: vec![],
            }
        }
    }

    // Helper function to create a test store
    fn create_test_store() -> Store<AppState, AppStateActions, AppStateEvents, AppStateEffects> {
        Store::new(TestReducer, AppState::new())
    }

    // Helper function to create mock middleware
    fn create_mock_middleware() -> (HandleAppStateActionsMiddleware<'static>, Arc<Mutex<VecDeque<Vec<u8>>>>) {
        // Create a shared Arc for the emitted messages
        let emitted_messages = Arc::new(Mutex::new(VecDeque::new()));

        // Create mock API with the shared emitted_messages
        let mock_api = MockCoreClientApi {
            emitted_messages: emitted_messages.clone(),
        };

        let boxed_api: crate::types::CoreClientApiType = Box::new(mock_api);
        let leaked_api: &'static crate::types::CoreClientApiType = Box::leak(Box::new(boxed_api));
        let middleware = HandleAppStateActionsMiddleware { core_api: leaked_api };
        (middleware, emitted_messages)
    }

    #[test]
    fn test_middleware_creation() {
        let (middleware, _) = create_mock_middleware();
        // Test that middleware can be created successfully
        assert!(!std::ptr::eq(middleware.core_api as *const _, std::ptr::null()));
    }

    #[test]
    fn test_join_room_by_name_action() {
        let (middleware, emitted_messages) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        let mut join_room_request = ServerMessage_JoinRoomByNameRequest::new();
        action.set_joinRoomByNameRequest(join_room_request);
        action.set_action(AppStateActions_Action::JoinRoomByName);

        // Mock reduce function
        let reduce_fn = |store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
                         _action: Option<&AppStateActions>| {
            reactive_state::middleware::ReduceMiddlewareResult {
                events: vec![],
                effects: vec![],
            }
        };

        let _result = middleware.on_reduce(&store, Some(&action), reduce_fn);

        // Verify message was emitted
        let messages = emitted_messages.lock().unwrap();
        assert_eq!(messages.len(), 1);
    }

    #[test]
    fn test_join_room_by_id_action() {
        let (middleware, emitted_messages) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::JoinRoomById);
        action.set_roomId("room123".to_string());

        let reduce_fn = |_store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
                         _action: Option<&AppStateActions>| {
            reactive_state::middleware::ReduceMiddlewareResult {
                events: vec![],
                effects: vec![],
            }
        };

        let _result = middleware.on_reduce(&store, Some(&action), reduce_fn);

        let messages = emitted_messages.lock().unwrap();
        assert_eq!(messages.len(), 1);
    }

    #[test]
    fn test_join_next_available_lobby() {
        let (middleware, emitted_messages) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::JoinNextAvailableLobby);

        let reduce_fn = |_store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
                         _action: Option<&AppStateActions>| {
            reactive_state::middleware::ReduceMiddlewareResult {
                events: vec![],
                effects: vec![],
            }
        };

        let _result = middleware.on_reduce(&store, Some(&action), reduce_fn);

        let messages = emitted_messages.lock().unwrap();
        assert_eq!(messages.len(), 1);
    }

    #[test]
    fn test_set_avatar_position() {
        let (middleware, emitted_messages) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAvatarPosition);

        let reduce_fn = |_store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
                         _action: Option<&AppStateActions>| {
            reactive_state::middleware::ReduceMiddlewareResult {
                events: vec![],
                effects: vec![],
            }
        };

        let _result = middleware.on_reduce(&store, Some(&action), reduce_fn);

        // Avatar position without data should not emit a message
        let messages = emitted_messages.lock().unwrap();
        assert_eq!(messages.len(), 0);
    }

    #[test]
    fn test_unknown_action_no_emission() {
        let (middleware, emitted_messages) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::Unknown);

        let reduce_fn = |_store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
                         _action: Option<&AppStateActions>| {
            reactive_state::middleware::ReduceMiddlewareResult {
                events: vec![],
                effects: vec![],
            }
        };

        let _result = middleware.on_reduce(&store, Some(&action), reduce_fn);

        let messages = emitted_messages.lock().unwrap();
        assert_eq!(messages.len(), 0); // No message should be emitted for unknown actions
    }

    #[test]
    fn test_none_action_no_emission() {
        let (middleware, emitted_messages) = create_mock_middleware();
        let store = create_test_store();

        let reduce_fn = |_store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
                         _action: Option<&AppStateActions>| {
            reactive_state::middleware::ReduceMiddlewareResult {
                events: vec![],
                effects: vec![],
            }
        };

        let _result = middleware.on_reduce(&store, None, reduce_fn);

        let messages = emitted_messages.lock().unwrap();
        assert_eq!(messages.len(), 0); // No message should be emitted when action is None
    }

    #[test]
    fn test_process_effect_passthrough() {
        let (middleware, _) = create_mock_middleware();
        let store = create_test_store();

        let mut effect = AppStateEffects::new();
        effect.set_action(AppStateEffects_Action::Unknown);

        let result = middleware.process_effect(&store, effect.clone());
        assert!(result.is_some());
        assert_eq!(result.unwrap(), effect);
    }

    #[test]
    fn test_on_notify_passthrough() {
        let (middleware, _) = create_mock_middleware();
        let store = create_test_store();

        let events = vec![AppStateEvents::AppStateReset];
        let notify_fn = |_store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
                         events: Vec<AppStateEvents>| events;

        let result = middleware.on_notify(&store, events.clone(), notify_fn);
        assert_eq!(result, events);
    }

    // Tests for client_message_to_action function
    #[test]
    fn test_client_message_to_action_welcome() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::Welcome);

        let action = client_message_to_action(&client_message);
        // Should return Unknown action if welcomeDto is not set
        assert_eq!(action.action, AppStateActions_Action::Unknown);
    }

    #[test]
    fn test_client_message_to_action_unknown_message_type() {
        let mut client_message = ClientMessage::new();
        // Don't set any specific message type or data

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::Unknown);
    }

    #[test]
    fn test_client_message_to_action_clear_chat() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::ClearChat);

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::ClearChat);
    }

    #[test]
    fn test_client_message_to_action_load_room_welcome_message() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::LoadRoomWelcomeMessage);
        client_message.set_stringValue("Welcome to the room!".to_string());

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::AddRoomChatMessage);
        assert!(action.has_chatMessageDto());

        let chat_message = action.get_chatMessageDto();
        assert_eq!(chat_message.get_message(), "Welcome to the room!");
        assert_eq!(chat_message.get_username(), "System");
        assert!(chat_message.get_isSystem());
        assert!(chat_message.get_isBot());
    }

    #[test]
    fn test_client_message_to_action_clear_chat_by_username() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::ClearChatByUsername);
        client_message.set_usertag("BadUser".to_string());

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::ClearChatByUsername);
        assert_eq!(action.get_usertag(), "BadUser");
    }

    #[test]
    fn test_client_message_to_action_command_response_validation_error() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::CmdResponse);

        let mut command_response = CommandResponse::new();
        command_response.set_messageType(CommandResponseType::ValidationError);

        let mut validation_error_list = CommandResponse_ClientValidationErrorList::new();
        let mut validation_error = ClientValidationError::new();
        validation_error.set_FieldName("username".to_string());
        validation_error.set_Reason("Username is required".to_string());
        validation_error_list.mut_data().push(validation_error);
        command_response.set_validationErrorList(validation_error_list);

        client_message.set_commandResponse(command_response);

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::ServerToClientMessage);
        assert!(action.has_commandResponse());
        assert!(action.get_commandResponse().has_validationErrorList());
    }

    #[test]
    fn test_client_message_to_action_command_response_join_room_fail() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::CmdResponse);

        let mut command_response = CommandResponse::new();
        command_response.set_messageType(CommandResponseType::JoinRoomFail);

        let mut join_room_fail_response = CommandResponse_JoinRoomFailResponse::new();
        join_room_fail_response.set_reason(JoinRoomFailType::RoomFull);
        command_response.set_joinRoomFailResponse(join_room_fail_response);

        client_message.set_commandResponse(command_response);

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::FailToJoinRoom);
        assert!(action.has_joinRoomFailResponse());
        assert_eq!(action.get_joinRoomFailResponse().get_reason(), JoinRoomFailType::RoomFull);
    }

    #[test]
    fn test_client_message_to_action_command_response_rooms_list() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::CmdResponse);

        let mut command_response = CommandResponse::new();
        command_response.set_messageType(CommandResponseType::RoomsList);

        let mut rooms_list = CommandResponse_RoomsList::new();
        let mut basic_room_dto = BasicRoomDto::new();
        basic_room_dto.set_roomName("TestRoom".to_string());
        rooms_list.mut_list().push(basic_room_dto);
        command_response.set_roomsList(rooms_list);

        client_message.set_commandResponse(command_response);

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::SetRooms);
        assert!(action.has_roomsList());
        assert_eq!(action.get_roomsList().get_list().len(), 1);
    }

    #[test]
    fn test_client_message_to_action_command_response_toast() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::CmdResponse);

        let mut command_response = CommandResponse::new();
        command_response.set_messageType(CommandResponseType::Toast);
        command_response.set_stringValue("Success!".to_string());

        client_message.set_commandResponse(command_response);

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::EmitToast);
        assert_eq!(action.get_stringValue(), "Success!");
    }

    #[test]
    fn test_client_message_to_action_midi_message() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::MidiMessage);

        let mut midi_message_output_dto = MidiMessageOutputDto::new();
        midi_message_output_dto.set_socketID("test_socket".to_string());
        client_message.set_midiMessageOutputDto(midi_message_output_dto);

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::HandleMidiMessage);
        assert!(action.has_midiMessageOutputDto());
        assert_eq!(action.get_midiMessageOutputDto().get_socketID(), "test_socket");
    }

    #[test]
    fn test_client_message_to_action_basic_room_created() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::BasicRoomApiCreated);

        let mut basic_room_dto = BasicRoomDto::new();
        basic_room_dto.set_roomName("NewRoom".to_string());
        basic_room_dto.set_roomID("room123".to_string());
        client_message.set_basicRoomDto(basic_room_dto);

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::AddRoom);
        assert!(action.has_basicRoomDto());
        assert_eq!(action.get_basicRoomDto().get_roomName(), "NewRoom");
        assert_eq!(action.get_basicRoomDto().get_roomID(), "room123");
    }

    #[test]
    fn test_client_message_to_action_basic_room_updated() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::BasicRoomApiUpdated);

        let mut basic_room_dto = BasicRoomDto::new();
        basic_room_dto.set_roomName("UpdatedRoom".to_string());
        client_message.set_basicRoomDto(basic_room_dto);

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::AddRoom);
        assert!(action.has_basicRoomDto());
        assert_eq!(action.get_basicRoomDto().get_roomName(), "UpdatedRoom");
    }

    #[test]
    fn test_client_message_to_action_basic_room_deleted() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::BasicRoomApiDeleted);
        client_message.set_stringValue("room123".to_string());

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::DeleteRoom);
        assert_eq!(action.get_stringValue(), "room123");
    }

    #[test]
    fn test_client_message_to_action_users_typing() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::UsersTyping);

        let mut socket_id_list = SocketIdList::new();
        socket_id_list.mut_socketIDs().push("user1".to_string());
        socket_id_list.mut_socketIDs().push("user2".to_string());
        client_message.set_socketIDList(socket_id_list);

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::SetUsersTyping);
        assert!(action.has_socketIdList());
        assert_eq!(action.get_socketIdList().get_socketIDs().len(), 2);
    }

    #[test]
    fn test_client_message_to_action_missing_required_data() {
        let mut client_message = ClientMessage::new();
        client_message.set_messageType(ClientMessageType::Welcome);
        // Don't set welcomeDto - should result in Unknown action

        let action = client_message_to_action(&client_message);
        assert_eq!(action.action, AppStateActions_Action::Unknown);
    }

    #[test]
    fn test_join_room_by_id_missing_data() {
        let (middleware, emitted_messages) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::JoinRoomById);
        // Don't set roomId or roomIdWithPassword

        let reduce_fn = |_store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
                         _action: Option<&AppStateActions>| {
            reactive_state::middleware::ReduceMiddlewareResult {
                events: vec![],
                effects: vec![],
            }
        };

        let _result = middleware.on_reduce(&store, Some(&action), reduce_fn);

        let messages = emitted_messages.lock().unwrap();
        assert_eq!(messages.len(), 1); // Should still emit a message but with empty command

        let server_message = ServerMessage::parse_from_bytes(&messages[0]).unwrap();
        assert_eq!(server_message.messageType, ServerMessageType::ServerCommand);
    }

    #[test]
    fn test_set_avatar_position_missing_data() {
        let (middleware, emitted_messages) = create_mock_middleware();
        let store = create_test_store();

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAvatarPosition);
        // Don't set avatarWorldPosition

        let reduce_fn = |_store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
                         _action: Option<&AppStateActions>| {
            reactive_state::middleware::ReduceMiddlewareResult {
                events: vec![],
                effects: vec![],
            }
        };

        let _result = middleware.on_reduce(&store, Some(&action), reduce_fn);

        let messages = emitted_messages.lock().unwrap();
        assert_eq!(messages.len(), 0); // No message should be emitted without required data
    }
}
