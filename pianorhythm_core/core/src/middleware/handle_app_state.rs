use std::rc::Rc;
use crate::reducers::app_state::{AppState};
use pianorhythm_proto::pianorhythm_actions::{AppStateActions};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use reactive_state::middleware::{Middleware, NotifyFn};
use reactive_state::{Store};

pub struct CoreLibStateManagementMiddleware {
  pub on_room_state_updated: Option<fn(Rc<AppState>)>,
}

impl Middleware<AppState, AppStateActions, AppStateEvents, AppStateEffects> for CoreLibStateManagementMiddleware {
    fn on_notify(
        &self, store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, events: Vec<AppStateEvents>,
        notify: NotifyFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> Vec<AppStateEvents> {
        let events = notify(store, events);

        if events.len() > 0 {
          let state = store.state();

          if events.contains(&AppStateEvents::RoomStateUpdated) {
            if let Some(on_room_state_updated) = self.on_room_state_updated {
              on_room_state_updated(state.clone());
            }
          }
        }

        events
    }
}
