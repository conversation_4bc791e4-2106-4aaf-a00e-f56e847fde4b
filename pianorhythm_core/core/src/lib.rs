extern crate core;

use std::io::Cursor;
use std::ops::Deref;
use std::sync::{Arc, OnceLock};

#[cfg(target_arch = "wasm32")]
use gloo::utils::window;
#[cfg(target_arch = "wasm32")]
use gloo_timers::callback::Timeout;
#[cfg(target_arch = "wasm32")]
use js_sys::Function;
#[cfg(target_arch = "wasm32")]
use js_sys::Reflect;
use once_cell::sync::{Lazy, OnceCell};
use protobuf::{Message, ProtobufEnum, RepeatedField};
use reactive_state::{CompositeReducer, Store};
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::prelude::*;

use pianorhythm_proto::client_message::*;
use pianorhythm_proto::midi_renditions::{AudioChannel, Instrument, InstrumentsList};
use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
use pianorhythm_proto::pianorhythm_app_renditions::{
    AppMidiSequencerEvent, AppMidiSequencerEventType, AppMidiSequencerProgramChange, AppMidiSequencerTempoChange
};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_proto::server_message::{ServerMessage, ServerMessageType};
#[cfg(feature = "use_synth")]
pub use pianorhythm_synth::*;

use crate::common::note_buffer::NoteBufferEngine;
use crate::midi::HandleWebsocketMidiMessage;
use crate::reducers::app_state::{AppState, AppStateReducer};
use crate::reducers::audio_process_state::AudioProcessStateReducer;
use crate::reducers::client_state::ClientStateReducer;
use crate::reducers::current_room_state::CurrentRoomStateReducer;
use crate::reducers::rooms_state::RoomsStateReducer;
use crate::types::CoreClientApi;
use crate::utils::hash_socket_id;
#[cfg(target_arch = "wasm32")]
use crate::utils::wasm_util::AnimationLoop;
use crate::websocket::webrtc::WebRtcChannelMessages;

pub mod audio;
pub mod common;
pub mod middleware;
pub mod midi;
pub mod reducers;
pub mod types;
pub mod utils;
mod websocket;

#[cfg(feature = "wee_alloc")]
#[global_allocator]
static ALLOC: wee_alloc::WeeAlloc = wee_alloc::WeeAlloc::INIT;

static mut APP_STATE: OnceLock<Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>> = OnceLock::new();
static mut CORE_CLIENT_API: OnceLock<types::CoreClientApiType> = OnceLock::new();
static mut NOTE_BUFFER_ENGINE: OnceLock<NoteBufferEngine> = OnceLock::new();

#[cfg(target_arch = "wasm32")]
static mut ADAPTIVE_FLUSH_TIMEOUT: OnceLock<Option<Timeout>> = OnceLock::new();

pub static mut MIDI_RECORDER_RENDER_START: Lazy<bool> = Lazy::new(|| false);
pub static mut MIDI_RECORDER_ACTIVE_CHANNEL: Lazy<u8> = Lazy::new(|| pianorhythm_shared::midi::MAX_CHANNEL + 1);
pub static mut MIDI_SEQUENCER_RENDER_START: Lazy<bool> = Lazy::new(|| false);

#[cfg(feature = "use_synth")]
pub static mut MIDI_SEQUENCER: OnceCell<MidiFileSequencer> = OnceCell::new();
#[cfg(feature = "use_synth")]
pub static mut MIDI_RECORDER: OnceCell<MidiRecorder> = OnceCell::new();
#[cfg(feature = "use_synth")]
pub static mut VP_SHEET_SEQUENCER: OnceCell<VPSheetMusicSequencer> = OnceCell::new();

#[cfg(not(target_arch = "wasm32"))]
static mut HANDLE_WS_MIDI_MESSAGE: OnceLock<midi::handle_ws_midi::HandleMidiMessage> = OnceLock::new();

#[cfg(target_arch = "wasm32")]
static mut HANDLE_WS_MIDI_MESSAGE: OnceLock<midi::wasm_handle_ws_midi::WasmHandleMidiMessage> = OnceLock::new();

#[cfg(feature = "use_synth")]
pub type SynthEventsEmitterFunc = Box<dyn Fn(PianoRhythmSynthEvent) -> ()>;

#[cfg(not(target_arch = "wasm32"))]
static mut WS_CLIENT: OnceLock<ezsockets::Client<websocket::ws::WebSocketClient>> = OnceLock::new();
#[cfg(not(target_arch = "wasm32"))]
static mut DESKTOP_WS_DATA_EMITTER: OnceLock<Box<dyn Fn(Vec<u8>) -> ()>> = OnceLock::new();
#[cfg(not(target_arch = "wasm32"))]
static mut DESKTOP_APP_STATE_EFFECTS_EMITTER: OnceLock<middleware::broadcast_app_state::EmitNotificationFunc2> =
    OnceLock::new();
#[cfg(feature = "use_synth")]
#[cfg(not(target_arch = "wasm32"))]
static mut DESKTOP_SYNTH_EVENTS_EMITTER: OnceLock<SynthEventsEmitterFunc> = OnceLock::new();
#[cfg(not(target_arch = "wasm32"))]
static mut DESKTOP_MIDI_SEQUENCER_EFFECTS_EMITTER: OnceLock<middleware::broadcast_app_state::EmitNotificationFunc2> =
    OnceLock::new();

/// The broadcast channel for synth events (main thread)
#[cfg(target_arch = "wasm32")]
static mut WASM_SYNTH_EVENTS_BROADCAST_CHANNEL: OnceLock<web_sys::BroadcastChannel> = OnceLock::new();

/// The broadcast channel for synth events (worker thread)
#[cfg(target_arch = "wasm32")]
static mut WASM_SYNTH_EVENTS_BROADCAST_CHANNEL_M: OnceLock<web_sys::BroadcastChannel> = OnceLock::new();

#[cfg(target_arch = "wasm32")]
fn emit_synth_event_to_broadcast_channel(js_output: &JsValue) {
    unsafe {
        // This is not needed in the audio worklet scope
        // and will be handled via SharedArrayBuffer
        if utils::is_audio_worklet_scope() {
            return;
        }

        if utils::is_main_thread_scope() {
            if let Some(channel) = WASM_SYNTH_EVENTS_BROADCAST_CHANNEL_M.get() {
                let _result = channel.post_message(&js_output);
            }
        } else {
            if let Some(channel) = WASM_SYNTH_EVENTS_BROADCAST_CHANNEL.get() {
                let _result = channel.post_message(&js_output);
            }
        }
    }
}

#[cfg(target_arch = "wasm32")]
#[derive(Default)]
struct WasmCoreClientApi {}

#[cfg(target_arch = "wasm32")]
impl WasmCoreClientApi {
    pub fn new() -> Self {
        WasmCoreClientApi {}
    }
}

#[cfg(target_arch = "wasm32")]
impl CoreClientApi for WasmCoreClientApi {
    fn init(&mut self) -> () {}

    fn ws_emit_binary(&self, bytes: Vec<u8>) -> () {
        unsafe {
            websocket_send_binary(bytes);
        }
    }

    fn dispatch_app_effect(&self, app_state_effect: &AppStateEffects) -> () {
        if !app_state_effect.is_initialized() || app_state_effect.action == AppStateEffects_Action::Unknown {
            return;
        }

        let bytes = app_state_effect.write_to_bytes().unwrap_or_default();
        let js_output = &JsValue::from(js_sys::Uint8Array::from(&bytes[..]));
        let _result = crate::utils::emit_custom_event("app_effects", &js_output);
    }

    fn dispatch_midi_sequencer_effect(&self, app_state_effect: &AppStateEffects) -> () {
        if !app_state_effect.is_initialized() || app_state_effect.action == AppStateEffects_Action::Unknown {
            return;
        }

        let bytes = app_state_effect.write_to_bytes().unwrap_or_default();
        let js_output = &JsValue::from(js_sys::Uint8Array::from(&bytes[..]));
        let _result = crate::utils::emit_custom_event("midi_sequencer_effects", &js_output);
    }

    #[cfg(feature = "use_synth")]
    fn dispatch_midi_synth_event(&self, event: &PianoRhythmSynthEvent) -> () {
        if let Ok(js_output) = serde_wasm_bindgen::to_value(&event) {
            //log::debug!("Dispatching synth event: {:?}", &event);
            emit_synth_event_to_broadcast_channel(&js_output);
        } else {
            #[cfg(debug_assertions)]
            log::error!("Failed to serialize synth event: {:?}", &event);
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[derive(Default)]
struct DesktopCoreClientApi;

#[cfg(not(target_arch = "wasm32"))]
impl CoreClientApi for DesktopCoreClientApi {
    fn init(&mut self) -> () {}

    fn ws_emit_binary(&self, bytes: Vec<u8>) -> () {
        websocket_send_binary(bytes);
    }

    fn dispatch_app_effect(&self, app_state_effect: &AppStateEffects) -> () {
        if !app_state_effect.is_initialized() || app_state_effect.action == AppStateEffects_Action::Unknown {
            return;
        }

        unsafe {
            DESKTOP_APP_STATE_EFFECTS_EMITTER.get().inspect(|emitter| {
                emitter(app_state_effect.clone());
            });
        }
    }

    fn dispatch_midi_sequencer_effect(&self, app_state_effect: &AppStateEffects) -> () {
        if !app_state_effect.is_initialized() || app_state_effect.action == AppStateEffects_Action::Unknown {
            return;
        }

        unsafe {
            DESKTOP_MIDI_SEQUENCER_EFFECTS_EMITTER.get().inspect(|emitter| {
                emitter(app_state_effect.clone());
            });
        }
    }

    #[cfg(feature = "use_synth")]
    fn dispatch_midi_synth_event(&self, event: &PianoRhythmSynthEvent) -> () {
        unsafe {
            if let Some(value) = DESKTOP_SYNTH_EVENTS_EMITTER.get() {
                value(event.clone());
            }
        }
    }
}

// ---------------------------------
// INTERNAL RELATED FUNCTIONS
// ---------------------------------
pub fn init(
    #[cfg(not(target_arch = "wasm32"))] on_handle_ws_midi_message: midi::HandleWsMidiMessageOutput,
    #[cfg(feature = "use_synth")]
    #[cfg(not(target_arch = "wasm32"))]
    on_handle_synth_events: SynthEventsEmitterFunc,
    #[cfg(not(target_arch = "wasm32"))] on_notify: Option<middleware::broadcast_app_state::EmitNotificationFunc>,
    #[cfg(not(target_arch = "wasm32"))] on_effects: Option<middleware::broadcast_app_state::EmitNotificationFunc2>,
    #[cfg(not(target_arch = "wasm32"))] on_midi_sequencer_effects: Option<
        middleware::broadcast_app_state::EmitNotificationFunc2,
    >,
) {
    unsafe {
        #[cfg(target_arch = "wasm32")]
        {
            if utils::is_audio_worklet_scope() {
                return;
            }

            let channel_name = "pianorhythm.synth.events";

            if utils::is_main_thread_scope() {
                if WASM_SYNTH_EVENTS_BROADCAST_CHANNEL_M.get().is_none() {
                    _ = WASM_SYNTH_EVENTS_BROADCAST_CHANNEL_M
                        .set(web_sys::BroadcastChannel::new(channel_name).unwrap());
                    log::debug!("Synth events broadcast channel set. (main thread)");
                }
            } else if utils::is_worker_global_scope() {
                if WASM_SYNTH_EVENTS_BROADCAST_CHANNEL.get().is_none() {
                    _ = WASM_SYNTH_EVENTS_BROADCAST_CHANNEL.set(web_sys::BroadcastChannel::new(channel_name).unwrap());
                    log::debug!("Synth events broadcast channel set. (worker thread)");
                }
            }

            if CORE_CLIENT_API.get().is_none() {
                _ = CORE_CLIENT_API.set(Box::new(WasmCoreClientApi::new()));
                log::debug!("Core Client API set.");
            }

            if HANDLE_WS_MIDI_MESSAGE.get().is_none() {
                if utils::is_worker_global_scope() {
                    if let Some(api) = CORE_CLIENT_API.get() {
                        log::debug!("Setting wasm midi handler.");
                        _ = HANDLE_WS_MIDI_MESSAGE
                            .set(midi::wasm_handle_ws_midi::WasmHandleMidiMessage { core_api: api })
                    }
                }
            }
        }

        if APP_STATE.get().is_some() {
            #[cfg(debug_assertions)]
            log::warn!("Init called but app state reducer already initialized...");
            return;
        }

        #[cfg(not(target_arch = "wasm32"))]
        {
            if CORE_CLIENT_API.get().is_none() {
                _ = CORE_CLIENT_API.set(Box::new(DesktopCoreClientApi::default()));
            }

            #[cfg(feature = "use_synth")]
            if DESKTOP_SYNTH_EVENTS_EMITTER.get().is_none() {
                _ = DESKTOP_SYNTH_EVENTS_EMITTER.set(on_handle_synth_events);
            }

            if let Some(func) = on_effects {
                set_app_state_effects_emitter(func);
            }

            if let Some(func) = on_midi_sequencer_effects {
                if DESKTOP_MIDI_SEQUENCER_EFFECTS_EMITTER.get().is_none() {
                    _ = DESKTOP_MIDI_SEQUENCER_EFFECTS_EMITTER.set(func);
                }
            }

            if HANDLE_WS_MIDI_MESSAGE.get().is_none() {
                if let Some(api) = CORE_CLIENT_API.get() {
                    _ = HANDLE_WS_MIDI_MESSAGE.set(midi::handle_ws_midi::HandleMidiMessage {
                        core_api: api,
                        on_handle: on_handle_ws_midi_message,
                    })
                }
            }
        }

        if let Some(api) = CORE_CLIENT_API.get_mut() {
            api.init();
        }

        let initial_app_state = AppState::default();

        let reducer = CompositeReducer::new(vec![
            Box::new(AppStateReducer::default()),
            Box::new(ClientStateReducer::default()),
            Box::new(CurrentRoomStateReducer::default()),
            Box::new(AudioProcessStateReducer::default()),
            Box::new(RoomsStateReducer::default()),
        ]);

        let store = Store::new(reducer, initial_app_state);

        if let Some(api) = CORE_CLIENT_API.get() {
            store.add_middleware(middleware::handle_client_actions::HandleAppStateActionsMiddleware { core_api: api });
            store.add_middleware(middleware::handle_synth_actions::HandleSynthActionsMiddleware { core_api: api });
            store.add_middleware(middleware::handle_server_events::HandleServerEventsMiddleware { core_api: api });
            store.add_middleware(middleware::handle_vp_sequencer_actions::HandleVPSequencerActionsMiddleware {
                core_api: api,
            });
            store.add_middleware(middleware::handle_app_state::CoreLibStateManagementMiddleware {
                on_room_state_updated: Some(|state| {
                    if let Some(nb_engine) = NOTE_BUFFER_ENGINE.get_mut() {
                        nb_engine.stop_emitting_to_ws_when_alone(state.stop_emitting_to_ws_when_alone);
                    }
                }),
            });
            // Broadcast should be the last middleware
            store.add_middleware(middleware::broadcast_app_state::BroadcastAppStateMiddleware {
                #[cfg(not(target_arch = "wasm32"))]
                on_notify_events: on_notify,
                core_api: api,
            });
        } else {
            log::error!("Middleware not added to core client API since it wasn't initialized.");
        }

        _ = APP_STATE.set(store);
        log::trace!("Core init called and app state set.");
    }
}

#[cfg(feature = "use_synth")]
pub fn handle_audio_channel_updates(events: Vec<AudioChannel>) {
    unsafe {
        CORE_CLIENT_API.get().inspect(|api| {
            for channel in events.iter() {
                api.dispatch_app_effect(&utils::create_channel_updated_action(&channel));
            }
        });
    }
}

#[cfg(feature = "use_synth")]
pub fn handle_synth_events(events: Vec<PianoRhythmSynthEvent>) {
    unsafe {
        //log::info!("handle_synth_events: {:?}", &events);

        if let Some(api) = CORE_CLIENT_API.get() {
            for event in events.iter() {
                api.dispatch_midi_synth_event(&event);

                if let Some(recorder) = MIDI_RECORDER.get_mut() {
                    recorder.record(*MIDI_RECORDER_ACTIVE_CHANNEL, &event);
                }
            }
        } else {
            #[cfg(debug_assertions)]
            log::error!("Core client API not found when handling synth events.");
        }
    }
}

#[cfg(feature = "use_synth")]
pub fn on_soundfont_loaded_successfully() -> () {
    unsafe {
        if let Some(store) = APP_STATE.get_mut() {
            if let Some(synth) = SYNTH.get() {
                let presets = synth.get_all_presets_from_sf_raw();
                let instruments: Vec<Instrument> = presets
                    .iter()
                    .map(|preset| {
                        let mut instrument = Instrument::new();

                        instrument.set_name(preset.name.clone());
                        instrument.set_display_name(preset.name.clone());
                        instrument.set_bank(preset.bank.clone());
                        instrument.set_is_drum_kit(false);
                        instrument.set_preset(preset.preset.clone());
                        instrument.set_key_low(preset.key_low.clone());
                        instrument.set_key_high(preset.key_high.clone());
                        instrument
                    })
                    .collect::<_>();

                let mut sync_action = AppStateActions::new();
                sync_action.set_action(AppStateActions_Action::SetLoadedInstruments);

                let mut i_list = InstrumentsList::new();
                i_list.set_instruments(RepeatedField::from_vec(instruments));

                CORE_CLIENT_API.get().inspect(|api| {
                    api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(
                        AppStateEffects_Action::SoundfontPresetsLoaded,
                        |effect| {
                            effect.set_instrumentsList(i_list.clone());
                        },
                    ))
                });

                sync_action.set_instrumentsList(i_list);
                store.dispatch(sync_action);
            }
        }
    }
}

#[cfg(feature = "use_synth")]
pub fn on_soundfont_load_fail(error_message: Option<String>) -> () {
    unsafe {
        log::info!("Fail: {:?}", error_message);

        if let Some(api) = CORE_CLIENT_API.get() {
            let mut effect = AppStateEffects::new();
            effect.set_action(AppStateEffects_Action::SynthSoundfontFailedToLoad);
            effect.set_message(error_message.unwrap_or_default());
            api.dispatch_app_effect(&effect);
        }
    }
}

pub fn synth_get_audio_channels() -> Vec<AudioChannel> {
    #[cfg(feature = "use_synth")]
    return pianorhythm_synth::get_all_audio_channels(None);

    #[cfg(not(feature = "use_synth"))]
    return vec![];
}

// ---------------------------------
// API RELATED FUNCTIONS
// ---------------------------------
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn get_core_version() -> String {
    format!("* PianoRhythm Core v{} *", env!("CARGO_PKG_VERSION"))
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn get_synth_version() -> String {
    #[cfg(feature = "use_synth")]
    return synth_get_version();

    #[cfg(not(feature = "use_synth"))]
    return String::new();
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn get_renderer_version() -> String {
    // #[cfg(feature = "use_renderer")]
    // {
    //     return pianorhythm_renderer::get_lib_version();
    // }

    format!("* [Feature Disabled] PianoRhythm Renderer v{} *", "0.0.0")
}

#[cfg(feature = "use_synth")]
#[cfg(not(target_arch = "wasm32"))]
pub fn create_synth(options: PianoRhythmSynthesizerDescriptor) -> Result<(), String> {
    create_synth_desktop(
        options,
        Some(Box::new(|event| {
            emit_to_note_buffer_engine(&event);
        })),
        Some(Box::new(handle_synth_events)),
        Some(Box::new(handle_audio_channel_updates)),
    )
    .inspect(|_| unsafe {
        if let Some(store) = APP_STATE.get() {
            let mut action = AppStateActions::new();
            action.set_action(AppStateActions_Action::SynthEngineCreated);
            store.dispatch(action);
        }
    })
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn init_note_buffer_engine() {
    unsafe {
        #[cfg(feature = "use_synth")]
        if NOTE_BUFFER_ENGINE.get().is_none() {
            let mut nb_engine = NoteBufferEngine::new(Box::new(move |buffer| {
                let mut server_message = ServerMessage::new();
                server_message.set_messageType(ServerMessageType::MidiMessage);
                server_message.set_midiMessageInputDto(buffer);
                let bytes = server_message.write_to_bytes().unwrap_or_default();
                websocket_send_binary(bytes);
            }));

            nb_engine.initialize();

            // Start adaptive flushing automatically
            #[cfg(target_arch = "wasm32")]
            {
                //nb_engine.set_adaptive_flushing_enabled(false);
                //schedule_adaptive_flush();

                if utils::is_worker_global_scope() {
                    log::debug!("Creating interval for note buffer engine.");
                    let interval = gloo_timers::callback::Interval::new(200, || {
                        flush_note_buffer_engine();
                    });
                    interval.forget();
                }
            }

            _ = NOTE_BUFFER_ENGINE.set(nb_engine);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn flush_note_buffer_engine() {
    unsafe {
        // Stop adaptive flushing first
        #[cfg(target_arch = "wasm32")]
        {
            if let Some(timeout_opt) = ADAPTIVE_FLUSH_TIMEOUT.get_mut() {
                if let Some(timeout) = timeout_opt.take() {
                    timeout.cancel();
                }
            }
        }

        if let Some(nb_engine) = NOTE_BUFFER_ENGINE.get_mut() {
            nb_engine.flush_buffer();
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn note_buffer_engine_set_self_hosted(value: bool) {
    unsafe {
        if let Some(nb_engine) = NOTE_BUFFER_ENGINE.get_mut() {
            nb_engine.set_room_is_self_hosted(value);
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn update_server_timing(ping_time: i64, server_time: i64, local_time: i64) {
    unsafe {
        if let Some(nb_engine) = NOTE_BUFFER_ENGINE.get_mut() {
            //nb_engine.update_server_timing(ping_time, server_time, local_time);
        }
    }
}

#[cfg(target_arch = "wasm32")]
fn schedule_adaptive_flush() {
    unsafe {
        // Cancel any existing timeout. This is for external calls to reset the timer.
        if let Some(timeout_opt) = ADAPTIVE_FLUSH_TIMEOUT.get_mut() {
            if let Some(timeout) = timeout_opt.take() {
                timeout.cancel();
            }
        }

        //if let Some(nb_engine) = NOTE_BUFFER_ENGINE.get_mut() {
        //    let next_interval = nb_engine.get_current_flush_interval();
        //
        //    if next_interval == 0 {
        //        // Flush immediately and reschedule without setting a timeout.
        //        nb_engine.flush_buffer();
        //        schedule_adaptive_flush();
        //    } else {
        //        // Schedule the next flush with a modified, safer closure.
        //        let timeout = Timeout::new(next_interval, || {
        //            // FIX: The timeout has fired. Take its handle from the global
        //            // state so the subsequent recursive call doesn't find it.
        //            // This "disarms" the self-cancellation problem.
        //            if let Some(timeout_opt) = ADAPTIVE_FLUSH_TIMEOUT.get_mut() {
        //                timeout_opt.take();
        //            }
        //
        //            // Now, perform the actual work.
        //            if let Some(nb_engine) = NOTE_BUFFER_ENGINE.get_mut() {
        //                nb_engine.flush_buffer();
        //            }
        //
        //            // Finally, schedule the next one. This call is now safe.
        //            schedule_adaptive_flush();
        //        });
        //
        //        // Store the new timeout handle.
        //        if let Some(timeout_opt) = ADAPTIVE_FLUSH_TIMEOUT.get_mut() {
        //            *timeout_opt = Some(timeout);
        //        } else {
        //            let _ = ADAPTIVE_FLUSH_TIMEOUT.set(Some(timeout));
        //        }
        //    }
        //}
    }
}

#[cfg(feature = "use_synth")]
fn emit_to_note_buffer_engine(event: &pianorhythm_synth::PianoRhythmWebSocketEmitEvent) {
    // Ignore previews
    if NoteSourceType::from_u8_option(event.note_source).is_preview() {
        return;
    }

    unsafe {
        let payload = utils::piano_rhythm_web_socket_emit_event_to_proto(&event);

        if let Some(nb_engine) = NOTE_BUFFER_ENGINE.get_mut() {
            nb_engine.process_message(payload.clone());
        }

        if let Ok(channel) = websocket::webrtc::WEBRTC_CHANNEL.lock() {
            if let Some(sender) = channel.deref() {
                _ = sender.send(WebRtcChannelMessages::Message(payload.write_to_bytes().unwrap_or_default()));
            }
        }
    }
}

/// Describes server messages that don't need to be
/// emitted to the front end and should only be
/// handled by the app state reducer.
const FILTERED: [ClientMessageType; 24] = [
    ClientMessageType::ClientUpdated,
    ClientMessageType::UsersInRoomList,
    ClientMessageType::UserJoined,
    ClientMessageType::UserLeft,
    ClientMessageType::UsersTyping,
    ClientMessageType::UserUpdated,
    ClientMessageType::JoinedRoomSuccess,
    ClientMessageType::JoinedRoomFail,
    ClientMessageType::CmdResponse,
    ClientMessageType::BasicRoomApiCreated,
    ClientMessageType::BasicRoomApiUpdated,
    ClientMessageType::BasicRoomApiDeleted,
    ClientMessageType::RoomChatMessage,
    ClientMessageType::LoadRoomChatHistory,
    ClientMessageType::Welcome,
    ClientMessageType::LoadRoomWelcomeMessage,
    ClientMessageType::EditMessageByID,
    ClientMessageType::ClearChatByMessageID,
    ClientMessageType::ClearChatByUsername,
    ClientMessageType::ClearChat,
    ClientMessageType::ClearChatByAmount,
    ClientMessageType::ClearChatBySocketID,
    ClientMessageType::RoomSettingsUpdated,
    ClientMessageType::MidiMessage,
];

/// Describe server messages that don't need to be handled
/// by the state reducer.
const NO_REDUCER_MESSAGES: [ClientMessageType; 1] = [ClientMessageType::MidiMessage];

/// Parses messages from the server
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn handle_client_message(bytes: &[u8]) -> bool {
    let mut emit = true;

    unsafe {
        if let Ok(client_message) = ClientMessage::parse_from_bytes(&bytes) {
            // log::info!("[handle_client_message]: {:?}", &client_message);

            if FILTERED.contains(&client_message.messageType) {
                emit = false;
            }

            if NO_REDUCER_MESSAGES.contains(&client_message.messageType) {
                match client_message.messageType {
                    ClientMessageType::MidiMessage if client_message.has_midiMessageOutputDto() => {
                        if let Some(handler) = HANDLE_WS_MIDI_MESSAGE.get() {
                            if let Some(store) = APP_STATE.get() {
                                handler.handle(client_message.get_midiMessageOutputDto(), store.state());
                            }
                        }
                    }
                    _ => {}
                }

                return false;
            }

            let action = middleware::handle_client_actions::client_message_to_action(&client_message);
            // log::info!("action output: {:?}", &action);

            if action.action != AppStateActions_Action::Unknown {
                if let Some(store) = APP_STATE.get_mut() {
                    store.dispatch(action);
                }
            }
        }

        #[cfg(not(target_arch = "wasm32"))]
        if emit {
            if let Some(emitter) = DESKTOP_WS_DATA_EMITTER.get() {
                emitter(Vec::from(bytes))
            } else {
                log::warn!("Desktop ws data emitter not found.");
            }
        }
    }

    emit
}

pub fn handle_connection_retry() {
    unsafe {
        CORE_CLIENT_API.get().inspect(|api| {
            api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(
                AppStateEffects_Action::Toast,
                |effect| {
                    effect.set_message("Connection interrupted. Attempting to reconnect. Please wait...".to_string());
                },
            ))
        });
    }
}

pub fn handle_connection_failed() {
    unsafe {
        CORE_CLIENT_API.get().inspect(|api| {
            api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(
                AppStateEffects_Action::WebsocketDisconnected,
                |_| {},
            ));
        });

        if let Some(store) = APP_STATE.get_mut() {
            let mut action = AppStateActions::new();
            action.set_boolValue(true);
            action.set_action(AppStateActions_Action::TriggerOfflineMode);
            store.dispatch(action.clone());
        }
    }
}

/// Handles actions from the front-end client
pub fn handle_core_app_actions(bytes: &[u8]) -> Option<AppStateActions> {
    if let Ok(action) = AppStateActions::parse_from_bytes(bytes) {
        if !action.is_initialized() {
            return None;
        }

        // log::info!("got_app_action: {:?}", &action);

        match action.action {
            AppStateActions_Action::SetAppSettings if action.has_appSettings() => unsafe {
                if let Some(nb) = NOTE_BUFFER_ENGINE.get_mut() {
                    nb.debug_mode = action.get_appSettings().ENABLE_DEBUG_MODE;
                }
            },
            // Don't emit from buffer if client is muted
            AppStateActions_Action::SetServerTimeOffset if action.has_doubleValue() => unsafe {
                if let Some(nb) = NOTE_BUFFER_ENGINE.get_mut() {
                    nb.server_time_offset = action.get_doubleValue() as i64;
                }
            },
            AppStateActions_Action::Logout => {
                #[cfg(feature = "use_synth")]
                pianorhythm_synth::disconnect();
                reset_app_state();
            }
            #[cfg(feature = "use_synth")]
            x if format!("{:?}", x).starts_with("MidiSequencer") => unsafe {
                handle_midi_sequencer_action(action.clone());
            },
            #[cfg(feature = "use_synth")]
            x if format!("{:?}", x).starts_with("AppMidiLooper") => unsafe {
                handle_midi_recorder_action(action.clone());
            },
            #[cfg(feature = "use_synth")]
            x if format!("{:?}", x).starts_with("VPSequencer") => unsafe {
                if handle_vp_sequencer_action(action.clone()).is_none() {
                    return None;
                }
            },
            _ => {}
        }

        unsafe {
            if let Some(store) = APP_STATE.get_mut() {
                store.dispatch(action.clone());
            }
        }

        return Some(action);
    }

    None
}

//TODO
fn on_logout() {
    let mut action = AppStateActions::new();
    action.set_action(AppStateActions_Action::Logout);
    handle_core_app_actions(action.write_to_bytes().unwrap_or_default().as_slice());
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn emit_core_app_actions(bytes: &[u8]) {
    handle_core_app_actions(bytes);
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn reset_app_state() {
    unsafe {
        if let Some(store) = APP_STATE.get_mut() {
            let mut action = AppStateActions::new();
            action.set_action(AppStateActions_Action::ResetState);
            store.dispatch(action);
        }

        if let Some(nb) = NOTE_BUFFER_ENGINE.get_mut() {
            nb.clean_up();
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn get_hash_socket_id(socket_id: &str) -> Option<u32> {
    hash_socket_id(socket_id)
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_sustain(value: u32, socket_id: Option<String>) {
    let bytes = [pianorhythm_shared::midi::CONTROLLER_BYTE, 64, value as u8];

    #[cfg(feature = "use_synth")]
    {
        _ = pianorhythm_synth::parse_midi_data(
            &bytes,
            socket_id.map(|x| hash_socket_id(&x)).flatten(),
            Some(pianorhythm_synth::NoteSourceType::Midi.to_u8()),
            None,
        );
    }
}

#[allow(unreachable_code)]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn synth_load_soundfont(buf: &[u8]) -> Result<(), String> {
    #[cfg(feature = "use_synth")]
    {
        log::info!("Loading soundfont!");

        let results = load_soundfont(buf);

        if let Err(ref err) = results {
            log::error!("ERROR: {:?}", err);
            on_soundfont_load_fail(Some(err.to_string()));
        } else {
            on_soundfont_loaded_successfully();
        }

        return results;
    }

    return Ok(());
}

#[cfg(not(target_arch = "wasm32"))]
pub fn set_ws_event_emitter(func: Box<dyn Fn(Vec<u8>) -> ()>) {
    unsafe {
        if DESKTOP_WS_DATA_EMITTER.get().is_none() {
            _ = DESKTOP_WS_DATA_EMITTER.set(func);
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
pub fn set_app_state_effects_emitter(func: middleware::broadcast_app_state::EmitNotificationFunc2) {
    unsafe {
        if DESKTOP_APP_STATE_EFFECTS_EMITTER.get().is_none() {
            _ = DESKTOP_APP_STATE_EFFECTS_EMITTER.set(func);
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
pub async fn websocket_connect(url: String) -> Result<(), ezsockets::Error> {
    unsafe {
        if let Some(api) = CORE_CLIENT_API.get() {
            let config = ezsockets::ClientConfig::new(url.as_str())
                .socket_config(ezsockets::SocketConfig { ..Default::default() });
            let (_client, handle) = ezsockets::connect(
                |handle| websocket::ws::WebSocketClient {
                    core_api: api,
                    handle,
                    connect_retry: 0,
                },
                config,
            )
            .await;

            log::trace!("Ezsocket client created.");

            if WS_CLIENT.get().is_none() {
                _ = WS_CLIENT.set(_client);
            } else {
                log::warn!("Websocket client already set.");
            }

            handle.await
        } else {
            log::error!("Failed to load the websocket client since the core API was not initialized.");
            panic!("Failed to load the websocket client since the core API was not initialized.");
        }
    }
}

#[cfg(not(target_arch = "wasm32"))]
pub fn websocket_disconnect() {
    unsafe {
        if let Some(client) = WS_CLIENT.get() {
            _ = client.close(None);
        }

        WS_CLIENT = OnceLock::new();
    }
}

#[cfg(not(target_arch = "wasm32"))]
pub fn websocket_send_binary(bytes: Vec<u8>) {
    unsafe {
        if let Some(client) = WS_CLIENT.get() {
            _ = client.binary(bytes);
        }
    }
}

// ---------------------------------
// WASM RELATED FUNCTIONS
// ---------------------------------
#[cfg(feature = "use_synth")]
#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
/// Initialize the audio scheduler with this node
pub fn init_audio_scheduler(sample_rate: f32, node: &web_sys::AudioWorkletNode) {
    use midi::audio_scheduler::{ensure_main_scheduler, set_audio_worklet_node};
    ensure_main_scheduler(sample_rate);
    set_audio_worklet_node(node.clone());
}

#[cfg(target_arch = "wasm32")]
static mut WS_CLIENT: OnceLock<web_sys::WebSocket> = OnceLock::new();

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn init_wasm() -> Result<(), JsValue> {
    init();
    Ok(())
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen(start)]
pub fn main_js() -> Result<(), JsValue> {
    use log::Level;
    console_log::init_with_level(Level::Trace).expect("error initializing log");

    // This provides better error messages in debug mode.
    // It's disabled in release mode so it doesn't bloat up the file size.
    #[cfg(debug_assertions)]
    utils::set_panic_hook();

    #[cfg(feature = "use_synth")]
    pianorhythm_synth::synth_main_js()?;

    log::debug!("Core main called.");
    init();

    Ok(())
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn create_synth(options: PianoRhythmSynthesizerDescriptor) -> Result<(), String> {
    unsafe {
        #[cfg(debug_assertions)]
        log::trace!("[create_synth] {:?}", &options);

        _ = SYNTH.set(PianoRhythmSynthesizer::new(
            options,
            Some(Box::new(|event| {
                #[cfg(feature = "use_synth")]
                emit_to_note_buffer_engine(&event);
            })),
            Some(Box::new(handle_synth_events)),
            Some(Box::new(handle_audio_channel_updates)),
        ));

        if let Some(store) = APP_STATE.get() {
            let mut action = AppStateActions::new();
            action.set_action(AppStateActions_Action::SynthEngineCreated);
            store.dispatch(action);
        } else {
            log::error!("App state not initialized when creating synth.");
        }
    }

    Ok(())
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn get_synth_audio_channels() -> js_sys::Array {
    return synth_get_audio_channels()
        .iter()
        .map(|x| x.write_to_bytes().unwrap_or_default())
        .map(|bytes| js_sys::Uint8Array::from(&bytes[..]))
        .map(JsValue::from)
        .collect();
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub async unsafe fn websocket_connect(
    url: String,
    on_connect: Function,
    on_error: Function,
    on_close: Function,
) -> Result<(), JsValue> {
    let ws = websocket::wasm_ws::create_websocket(url, on_connect, on_error, on_close).await?;

    if WS_CLIENT.get().is_none() {
        _ = WS_CLIENT.set(ws);
        log::debug!("Websocket client set.");
    } else {
        log::warn!("Websocket client already set.");
    }

    Ok(())
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub unsafe fn websocket_disconnect() {
    if let Some(ws_client) = WS_CLIENT.get() {
        let result = ws_client.close();
        log::trace!("Websocket close result: {} | {:?}", utils::is_main_thread_scope(), &result);
    }

    WS_CLIENT = OnceLock::new();
}

#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub unsafe fn websocket_send_binary(data: Vec<u8>) {
    if let Some(ws_client) = WS_CLIENT.get() {
        let buffer = js_sys::Uint8Array::from(&data[..]);
        _ = ws_client.send_with_array_buffer(&buffer.buffer());
    }
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn get_wasm_module() -> JsValue {
    wasm_bindgen::module()
}

#[cfg(target_arch = "wasm32")]
#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn get_wasm_memory() -> JsValue {
    wasm_bindgen::memory()
}

#[cfg(target_arch = "wasm32")]
pub fn get_window() -> web_sys::Window {
    if js_sys::global().is_instance_of::<web_sys::WorkerGlobalScope>() {
        let worker_global_scope = js_sys::global()
            .dyn_into::<web_sys::WorkerGlobalScope>()
            .expect("expect worker scope");
        Reflect::get(&worker_global_scope, &JsValue::from_str("window"))
            .unwrap()
            .unchecked_into::<web_sys::Window>()
    } else {
        window()
    }
}

// ---------------------
// MIDI File Sequencer
// ---------------------
#[cfg(feature = "use_synth")]
pub fn on_stop_midi_sequencer() {
    unsafe {
        *MIDI_SEQUENCER_RENDER_START = false;

        if let Some(store) = APP_STATE.get_mut() {
            let mut action = AppStateActions::new();
            action.set_action(AppStateActions_Action::ResetAudioChannelsToDefault);
            store.dispatch(action);
        }

        #[cfg(feature = "use_synth")]
        pianorhythm_synth::remove_socket(pianorhythm_shared::SYNTH_CONSTS::ID::MIDI_SYNTH_SOCKET_ID);
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
pub fn load_midi_file(
    buf: &[u8],
    file_name: Option<String>,
    preview_only: Option<bool>,
    is_vp: Option<bool>,
) -> Result<(), String> {
    unsafe {
        #[cfg(feature = "use_synth")]
        {
            log::trace!("Loading midi file: {:?}...", &file_name);

            let mut c = Cursor::new(buf.to_vec());
            match MidiFile::new(file_name.clone().unwrap_or("N/A".to_string()), &mut c) {
                Ok(file) => {
                    log::trace!("Midi file loaded: {:?}", &file_name);
                    let file = Arc::new(file);

                    if MIDI_SEQUENCER.get().is_none() {
                        let mut x = MidiFileSequencer::new(
                            Some(Box::new(move |data, preview_only| {
                                let source = if preview_only {
                                    NoteSourceType::MidiPlayerPreview
                                } else {
                                    NoteSourceType::MidiPlayer
                                };

                                #[cfg(feature = "use_synth")]
                                pianorhythm_synth::parse_midi_data(
                                    &vec![data[0] + data[1], data[2], data[3]],
                                    Some(pianorhythm_shared::SYNTH_CONSTS::ID::MIDI_SYNTH_SOCKET_ID),
                                    Some(source.to_u8()),
                                    None,
                                );
                            })),
                            Some(Box::new(move |data| {
                                CORE_CLIENT_API.get().inspect(|api| {
                                    if data.message_type == MidiMessageType::FINISHED {
                                        log::trace!("finished");
                                        on_stop_midi_sequencer();
                                    }

                                    api.dispatch_midi_sequencer_effect(&pianorhythm_shared::util::create_effect_with(
                                        AppStateEffects_Action::MidiSequencerEvent,
                                        move |effect| {
                                            let mut sequencer_event = AppMidiSequencerEvent::new();
                                            sequencer_event.set_eventType(
                                                AppMidiSequencerEventType::from_i32(data.message_type as u8 as i32)
                                                    .unwrap_or_default(),
                                            );

                                            if let Some(x) = data.data_str.as_ref() {
                                                sequencer_event.set_dataStr(x.clone());
                                            }
                                            if let Some(x) = data.data_float {
                                                sequencer_event.set_dataFloat(x);
                                            }

                                            sequencer_event.set_channel(data.channel.into());
                                            sequencer_event.set_command(data.command.into());
                                            sequencer_event.set_tick(data.tick.into());
                                            sequencer_event.set_time(data.time.into());
                                            sequencer_event.set_index(data.index.into());
                                            sequencer_event.set_isVPSheet(is_vp.unwrap_or_default());

                                            if data.message_type == MidiMessageType::FILE_OUTPUT {
                                                // log::info!("SEQUENCER EVENT: {:?}", &data);

                                                if let Some(output) = data.file_output.as_ref() {
                                                    sequencer_event
                                                        .set_lyrics(RepeatedField::from_vec(output.lyrics.clone()));
                                                    sequencer_event.set_trackNames(RepeatedField::from_vec(
                                                        output.track_names.clone(),
                                                    ));
                                                    sequencer_event.set_copyrightNotice(RepeatedField::from_vec(
                                                        output.copyright.clone(),
                                                    ));
                                                    sequencer_event.set_markerTexts(RepeatedField::from_vec(
                                                        output.marker_texts.clone(),
                                                    ));
                                                    sequencer_event
                                                        .set_texts(RepeatedField::from_vec(output.texts.clone()));
                                                    sequencer_event.set_fileName(output.file_name.clone());
                                                    sequencer_event.set_totalTime(output.total_time);
                                                    sequencer_event.set_currentBPM(output.current_bpm);
                                                    sequencer_event.set_ppq(output.ppq.into());

                                                    if !output.tempo_changes.is_empty() {
                                                        let input = output
                                                            .tempo_changes
                                                            .clone()
                                                            .iter()
                                                            .map(|x| {
                                                                let mut change = AppMidiSequencerTempoChange::default();
                                                                change.set_time(x.0.into());
                                                                change.set_tempo(x.1.into());
                                                                return change;
                                                            })
                                                            .collect();
                                                        sequencer_event
                                                            .set_tempoChanges(RepeatedField::from_vec(input));
                                                    }

                                                    if !output.program_changes.is_empty() {
                                                        let input = output
                                                            .program_changes
                                                            .clone()
                                                            .iter()
                                                            .map(|x| {
                                                                let mut change =
                                                                    AppMidiSequencerProgramChange::default();
                                                                change.set_channel(x.0.into());
                                                                change.set_program(x.1.into());
                                                                change.set_bank(x.2.into());
                                                                change.set_time(x.3.into());
                                                                return change;
                                                            })
                                                            .collect();
                                                        sequencer_event
                                                            .set_programChanges(RepeatedField::from_vec(input));
                                                    }
                                                }
                                            }

                                            effect.set_midiSequencerEvent(sequencer_event);
                                        },
                                    ))
                                });
                            })),
                        );

                        if let Some(synth) = SYNTH.get() {
                            x.sample_rate = synth.synth_get_sample_rate().clone() as f64;
                        }

                        _ = MIDI_SEQUENCER.set(x);
                        log::trace!("Created midi sequencer.");
                    }

                    if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                        let result = pianorhythm_synth::add_socket(
                            pianorhythm_shared::SYNTH_CONSTS::ID::MIDI_SYNTH_SOCKET_ID,
                            true,
                        );
                        log::trace!("Added midi synth socket: {:?}", &result);

                        pianorhythm_synth::reset_all_controllers(
                            0,
                            Some(pianorhythm_shared::SYNTH_CONSTS::ID::MIDI_SYNTH_SOCKET_ID),
                        );
                        sequencer.is_vp_sheet = is_vp.unwrap_or_default();
                        sequencer.pause();
                        sequencer.play(&file, false, preview_only.unwrap_or_default());
                    }

                    // Desktop version handles looping outside
                    #[cfg(target_arch = "wasm32")]
                    {
                        if !*MIDI_SEQUENCER_RENDER_START {
                            *MIDI_SEQUENCER_RENDER_START = true;

                            let game_loop = game_loop::GameLoop::new(AnimationLoop::new(), 240, 0.1, ());
                            crate::utils::wasm_util::animation_frame(
                                game_loop,
                                move |g| {
                                    if let Some(seq) = MIDI_SEQUENCER.get_mut() {
                                        seq.run();
                                    }
                                    if *MIDI_SEQUENCER_RENDER_START == false {
                                        g.exit();
                                    }
                                },
                                |_| {},
                            );
                        }
                    }
                }
                Err(err) => {
                    log::error!("Failed to load the midi file: {:?}", &err);
                    CORE_CLIENT_API.get().inspect(|api| {
                        api.dispatch_midi_sequencer_effect(&pianorhythm_shared::util::create_effect_with(
                            AppStateEffects_Action::MidiSequencerFailedToLoadMidi,
                            move |effect| {
                                effect.set_message(err.to_string());
                            },
                        ));
                    });
                }
            }
        }

        Ok(())
    }
}

#[cfg(feature = "use_synth")]
fn load_vp_sheet(input: VPSheetMusicFile, download_only: bool) -> Option<Vec<u8>> {
    #[cfg(feature = "use_synth")]
    {
        unsafe {
            log::trace!("Loading vp sheet {:?}...", &input.file_name);

            if VP_SHEET_SEQUENCER.get().is_none() {
                let sequencer = VPSheetMusicSequencer::new();
                _ = VP_SHEET_SEQUENCER.set(sequencer);
                log::trace!("Created vp sheet sequencer.");
            }

            if let Some(sequencer) = VP_SHEET_SEQUENCER.get_mut() {
                let file_name = input.file_name.clone();
                let data = sequencer.play(&Arc::new(input));

                if download_only {
                    return Some(data);
                }

                _ = load_midi_file(&data, file_name, None, Some(true));
            }

            return None;
        }
    }

    #[cfg(not(feature = "use_synth"))]
    None
}

#[cfg(feature = "use_synth")]
unsafe fn handle_vp_sequencer_action(action: AppStateActions) -> Option<AppStateActions> {
    match action.action {
        AppStateActions_Action::VPSequencerDownloadAsMidi | AppStateActions_Action::VPSequencerLoadData
            if action.has_vpFileLoad() =>
        {
            let file = action.get_vpFileLoad();
            if file.get_tracks().is_empty() {
                return None;
            }
        }
        AppStateActions_Action::VPSequencerSetBPM if action.has_uint32Value() => {
            if let Some(vp_sequencer) = VP_SHEET_SEQUENCER.get() {
                if vp_sequencer.file.as_ref().is_none() {
                    return None;
                }
            }
        }
        _ => {
            return None;
        }
    }

    return Some(action);
}

#[cfg(feature = "use_synth")]
unsafe fn handle_midi_sequencer_action(action: AppStateActions) {
    match action.action {
        AppStateActions_Action::MidiSequencerResume => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.resume();
            }
        }
        AppStateActions_Action::MidiSequencerStop => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.stop();
            }

            on_stop_midi_sequencer();
        }
        AppStateActions_Action::MidiSequencerSeekPosition if action.has_doubleValue() => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.set_position(action.get_doubleValue());
            }
        }
        AppStateActions_Action::MidiSequencerSetSpeed if action.has_doubleValue() => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.set_speed(action.get_doubleValue());
            }
        }
        AppStateActions_Action::MidiSequencerPause => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.pause();
            }
        }
        AppStateActions_Action::MidiSequencerRewind => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.rewind();
            }
        }
        AppStateActions_Action::MidiSequencerEnablePreviewOnly => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.set_preview_only(true);
            }
        }
        AppStateActions_Action::MidiSequencerDisablePreviewOnly => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.set_preview_only(false);
            }
        }
        AppStateActions_Action::MidiSequencerForward => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.forward();
            }
        }
        AppStateActions_Action::MidiSequencerMuteTrack if action.has_channelWithBool() => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                let input = action.get_channelWithBool();
                sequencer.set_channel_muted(input.channel as u8, input.get_boolValue());
            }
        }
        AppStateActions_Action::MidiSequencerSoloTrack if action.has_channelWithBool() => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                let input = action.get_channelWithBool();
                sequencer.set_channel_solo(input.channel as u8, input.get_boolValue());
            }
        }
        AppStateActions_Action::MidiSequencerEnableLoop => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.set_loop(true);
            }
        }
        AppStateActions_Action::MidiSequencerDisableLoop => {
            if let Some(sequencer) = MIDI_SEQUENCER.get_mut() {
                sequencer.set_loop(false);
            }
        }
        _ => {}
    }
}

// ---------------------
// App Midi Looper
// ---------------------
#[cfg(feature = "use_synth")]
unsafe fn try_create_recorder() {
    if MIDI_RECORDER.get().is_none() {
        let recorder = MidiRecorder::new(
            Some(Box::new(move |event, _preview| {
                #[cfg(feature = "use_synth")]
                pianorhythm_synth::parse_midi_data(
                    event.raw_bytes.as_slice(),
                    None,
                    Some(event.get_note_source().to_u8()),
                    None,
                );
            })),
            Some(Box::new(move |track| {
                CORE_CLIENT_API.get().inspect(|api| {
                    api.dispatch_app_effect(&pianorhythm_shared::util::create_effect_with(
                        AppStateEffects_Action::AppMidiLooperTrackUpdated,
                        |effect| {
                            effect.set_appMidiTrack(track.clone());
                        },
                    ))
                });
            })),
            None,
        );
        _ = MIDI_RECORDER.set(recorder);
    }
}

#[cfg(feature = "use_synth")]
unsafe fn start_recording(active_channel: u8) {
    try_create_recorder();

    #[cfg(target_arch = "wasm32")]
    {
        if !*MIDI_RECORDER_RENDER_START {
            *MIDI_RECORDER_RENDER_START = true;

            let game_loop = game_loop::GameLoop::new(AnimationLoop::new(), 240, 0.1, ());
            crate::utils::wasm_util::animation_frame(
                game_loop,
                move |g| {
                    if let Some(recorder) = MIDI_RECORDER.get_mut() {
                        recorder.run();
                    }
                    if *MIDI_RECORDER_RENDER_START == false {
                        g.exit();
                    }
                },
                |_| {},
            );
        }
    }

    *MIDI_RECORDER_ACTIVE_CHANNEL = active_channel;
    if let Some(recorder) = MIDI_RECORDER.get_mut() {
        recorder.start(active_channel);
    }
}

#[cfg(feature = "use_synth")]
unsafe fn dispose_recording() {
    *MIDI_RECORDER_RENDER_START = false;

    if let Some(recorder) = MIDI_RECORDER.get_mut() {
        recorder.dispose();
    }

    *MIDI_RECORDER_ACTIVE_CHANNEL = pianorhythm_shared::midi::MAX_CHANNEL + 1;
}

#[cfg(feature = "use_synth")]
unsafe fn handle_midi_recorder_action(action: AppStateActions) {
    // log::trace!("handle_midi_recorder_action: {:?}", &action);
    match action.action {
        AppStateActions_Action::AppMidiLooperRecord if action.has_uint32Value() => {
            start_recording(action.get_uint32Value() as u8)
        }
        AppStateActions_Action::AppMidiLooperStopRecord => {
            if let Some(recorder) = MIDI_RECORDER.get_mut() {
                let mut channel = None;
                if action.has_uint32Value() {
                    channel = Some(action.get_uint32Value() as u8)
                }
                recorder.stop_recording(channel);
            }
        }
        AppStateActions_Action::AppMidiLooperDispose => {
            dispose_recording();
        }
        AppStateActions_Action::AppMidiLooperPlay => {
            try_create_recorder();

            if let Some(recorder) = MIDI_RECORDER.get_mut() {
                recorder.play_all();
            }
        }
        AppStateActions_Action::AppMidiLooperStop => {
            if let Some(recorder) = MIDI_RECORDER.get_mut() {
                recorder.stop_all();
            }
        }
        AppStateActions_Action::AppMidiLooperEnableTrim => {
            if let Some(recorder) = MIDI_RECORDER.get_mut() {
                recorder.auto_trim = true;
            }
        }
        AppStateActions_Action::AppMidiLooperDisableTrim => {
            if let Some(recorder) = MIDI_RECORDER.get_mut() {
                recorder.auto_trim = false;
            }
        }
        AppStateActions_Action::AppMidiLooperSetTrack if action.has_appMidiTrack() => {
            try_create_recorder();

            if let Some(recorder) = MIDI_RECORDER.get_mut() {
                let track = action.get_appMidiTrack();
                recorder.update_track(&track);
            }
        }
        AppStateActions_Action::AppMidiLooperGetTracks => {
            try_create_recorder();

            if let Some(recorder) = MIDI_RECORDER.get_mut() {
                recorder.dispatch_current_tracks();
            }
        }
        AppStateActions_Action::AppMidiLooperClearTrack if action.has_uint32Value() => {
            if let Some(recorder) = MIDI_RECORDER.get_mut() {
                recorder.clear_track(action.get_uint32Value() as u8);
            }
        }
        AppStateActions_Action::AppMidiLooperToggleTrackPlaying if action.has_channelWithBool() => {
            if let Some(recorder) = MIDI_RECORDER.get_mut() {
                let input = action.get_channelWithBool();
                recorder.toggle_track_playing(input.get_channel() as u8, input.get_boolValue());
            }
        }
        _ => {}
    }
}

#[cfg(test)]
mod lib_tests {
    use super::*;

    #[test]
    fn test_hash_socket_id() {
        let id = "1234567890";
        let hash = hash_socket_id(id);
        assert_eq!(hash, Some(594476337));
    }

    #[test]
    fn test_get_hash_socket_id() {
        let id = "1234567890";
        let hash = get_hash_socket_id(id);
        assert_eq!(hash, Some(594476337));
    }

    #[test]
    fn test_handle_core_app_actions() {
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::Logout);

        let bytes = action.write_to_bytes().unwrap();
        let result = handle_core_app_actions(&bytes);
        assert_eq!(result.is_some(), true);
    }
}
