use std::sync::{Arc, Mutex};

use rustc_hash::FxHashMap;
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::JsCast;

use crate::common::keyboard_service_constants::keyboard_helper;

#[derive(Clone)]
struct ActiveKey {
    key_code: u8,
    note: u8,
}

pub struct ServiceKeyboardInputEvent {
    key_code: u8,
    shift_key: bool,
    alt_key: bool,
    caps_lock_down: bool,
}

pub struct ServiceKeyboardOutputEvent {
    pub note: u8,
    pub velocity: Option<u8>,
}

pub type KeyboardServiceEventCallback = Box<dyn Fn(ServiceKeyboardOutputEvent) + Send + 'static>;

pub struct KeyMaps {
    mpp_key_map: FxHashMap<u8, u8>,
    vp_key_map: FxHashMap<u8, u8>,
}

pub struct KeyboardService {
    keys_down: Arc<Mutex<FxHashMap<u8, ActiveKey>>>,
    caps_lock_down: Arc<Mutex<bool>>,
    can_play_keys: Arc<Mutex<bool>>,
    keyboard_layout: Arc<Mutex<String>>,
    key_maps: Arc<KeyMaps>,
    mouse_pos_velocity: Arc<Mutex<Option<u32>>>,
    #[cfg(target_arch = "wasm32")]
    mouse_pos_event_listener: Option<gloo_events::EventListener>,
}

impl KeyboardService {
    pub fn new() -> Self {
        Self {
            #[cfg(target_arch = "wasm32")]
            mouse_pos_event_listener: None,
            keys_down: Arc::new(Mutex::new(FxHashMap::default())),
            caps_lock_down: Arc::new(Mutex::new(false)),
            can_play_keys: Arc::new(Mutex::new(false)),
            mouse_pos_velocity: Arc::new(Mutex::new(None)),
            keyboard_layout: Arc::new(Mutex::new(String::from("VP"))),
            key_maps: Arc::new(KeyMaps {
                mpp_key_map: keyboard_helper::mpp_key_map(),
                vp_key_map: keyboard_helper::vp_key_map(),
            }),
        }
    }

    #[cfg(target_arch = "wasm32")]
    pub fn init(&mut self, keydown_cb: KeyboardServiceEventCallback, keyup_cb: KeyboardServiceEventCallback) -> bool {
        if js_sys::global().is_instance_of::<web_sys::Window>() {
            let window = web_sys::window().expect("global window does not exists");
            let document = window.document().expect("expecting a document on window");

            let keys_down = Arc::clone(&self.keys_down);
            let keys_down2 = Arc::clone(&self.keys_down);
            let keyboard_layout = Arc::clone(&self.keyboard_layout);
            let key_maps = Arc::clone(&self.key_maps);
            let caps_lock_down = Arc::clone(&self.caps_lock_down);
            let caps_lock_down2 = Arc::clone(&self.caps_lock_down);
            let mouse_pos_velocity = Arc::clone(&self.mouse_pos_velocity);
            let can_play_keys = Arc::clone(&self.can_play_keys);
            let can_play_keys2 = Arc::clone(&self.can_play_keys);

            let on_keydown = gloo_events::EventListener::new(&document, "keydown", move |event| {
                let keyboard_event = event.dyn_ref::<web_sys::KeyboardEvent>().unwrap();
                if keyboard_event.repeat() {
                    return;
                }

                let capslock_down = keyboard_event.key() == "CapsLock";
                if let Ok(mut cd) = caps_lock_down.lock() {
                    *cd = capslock_down;
                }

                if let Ok(can_lock) = can_play_keys.lock() {
                    if *can_lock == false { return; }
                }

                let key_code = keyboard_event.key_code() as u8;
                if key_code < 39 || key_code > 95 {
                    return;
                }

                if let Ok(mut keys_down) = keys_down.lock() {
                    if keys_down.contains_key(&key_code) {
                        return;
                    }

                    if let Some(note) = get_note(&keyboard_layout.lock().unwrap(), &key_maps, ServiceKeyboardInputEvent {
                        key_code: key_code,
                        shift_key: keyboard_event.shift_key(),
                        alt_key: keyboard_event.alt_key(),
                        caps_lock_down: keyboard_event.key() == "CapsLock",
                    }) {
                        keydown_cb(ServiceKeyboardOutputEvent {
                            note,
                            velocity: mouse_pos_velocity.lock().ok().map(|x| (*x).map(|y| y as u8)).unwrap_or_default(),
                        });

                        keys_down.insert(key_code, ActiveKey {
                            key_code,
                            note,
                        });
                    }
                }
            });
            on_keydown.forget();

            let on_keyup = gloo_events::EventListener::new(&document, "keyup", move |event| {
                let keyboard_event = event.dyn_ref::<web_sys::KeyboardEvent>().unwrap();

                if keyboard_event.repeat() {
                    return;
                }

                let capslock_down = keyboard_event.key() == "CapsLock";
                if let Ok(mut cd) = caps_lock_down2.lock() {
                    *cd = capslock_down;
                }

                if let Ok(can_lock) = can_play_keys2.lock() {
                    if *can_lock == false { return; }
                }

                if let Ok(mut keys_down) = keys_down2.lock() {
                    let key_code = keyboard_event.key_code();

                    if let Some(active_key) = keys_down.get(&(key_code as u8)) {
                        let note = active_key.note;

                        keyup_cb(ServiceKeyboardOutputEvent {
                            note,
                            velocity: None,
                        });

                        _ = keys_down.remove(&(key_code as u8));
                    }
                }
            });
            on_keyup.forget();

            return true;
        }

        false
    }

    #[cfg(target_arch = "wasm32")]
    pub fn set_capture_mouse_movement(&mut self, value: bool) {
        if !value && self.mouse_pos_event_listener.is_some() {
            if let Some(_) = &self.mouse_pos_event_listener {
                self.mouse_pos_event_listener = None;
            }

            if let Ok(mut vel) = self.mouse_pos_velocity.lock() {
                *vel = None;
            }
        } else if value && self.mouse_pos_event_listener.is_none() {
            let can_play_keys = Arc::clone(&self.can_play_keys);

            if js_sys::global().is_instance_of::<web_sys::Window>() {
                let window = web_sys::window().expect("global window does not exists");
                let document = window.document().expect("expecting a document on window");
                let mouse_pos_velocity = Arc::clone(&self.mouse_pos_velocity);
                let event = gloo_events::EventListener::new(&document, "mousemove", move |event| {
                    if let Ok(can_lock) = can_play_keys.lock() {
                        if *can_lock == false { return; }
                    }

                    let mouse_event = event.dyn_ref::<web_sys::MouseEvent>().unwrap();
                    if let Some(window_height) = window.inner_height().unwrap().as_f64().map(|x| x as f32) {
                        let client_y = mouse_event.client_y();
                        let velocity = map_mouse_pos_to_velocity(client_y as f32, window_height);
                        // crate::console_log!("Mouse move: {}", velocity);

                        if let Ok(mut vel) = mouse_pos_velocity.lock() {
                            *vel = Some(velocity as u32);
                        }
                    }
                });
                self.mouse_pos_event_listener = Some(event);
            }
        }
    }

    #[cfg(not(target_arch = "wasm32"))]
    pub fn init(&self, keydown_cb: KeyboardServiceEventCallback, keyup_cb: KeyboardServiceEventCallback) -> bool {
        true
    }

    pub fn set_can_play_keys(&mut self, value: bool) {
        if let Ok(mut can_lock) = self.can_play_keys.lock() {
            *can_lock = value;
        }
    }
}

fn map_mouse_pos_to_velocity(pos_y: f32, inner_height: f32) -> f32 {
    let vel = (pos_y.max(0.0)) / inner_height;
    map(vel, 0.0, 1.0, 10.0, 127.0)
}

fn map(value: f32, in_min: f32, in_max: f32, out_min: f32, out_max: f32) -> f32 {
    (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min
}

fn get_note_from_layout(layout: &str, keycode: u8, key_maps: &KeyMaps) -> Option<u8> {
    match layout {
        "VP" => {
            key_maps.vp_key_map.get(&keycode).cloned()
        }
        "MPP" => {
            key_maps.mpp_key_map.get(&keycode).cloned()
        }
        _ => None
    }
}

fn get_note(keyboard_layout: &str, key_maps: &KeyMaps, evt: ServiceKeyboardInputEvent) -> Option<u8> {
    let keycode = evt.key_code;

    if let Some(mut note) = get_note_from_layout(&keyboard_layout, keycode, key_maps) {
        let is_vp = keyboard_layout == "VP";
        let is_custom = keyboard_layout == "CUSTOM";

        if is_custom {
            if evt.shift_key {
                note += 12;
            }
            if evt.alt_key {
                note += 24;
            }
            if evt.caps_lock_down {
                note -= 12;
            }
            return Some(note);
        }

        let octave = if is_vp { 12 * 2 } else { 12 * 3 };

        if evt.shift_key {
            note += if is_vp { 1 } else { 12 };
        }

        if evt.alt_key {
            note = if is_vp { note - 1 } else { note + 24 };
        }

        if !is_vp && evt.caps_lock_down {
            note -= 12;
        }

        return Some(note + octave);
    }

    None
}
