pub mod keyboard_helper {
    use rustc_hash::FxHashMap;

    pub fn mpp_key_map() -> FxHashMap<u8, u8> {
        let mut map = FxHashMap::default();
        map.insert(65, 8);
        map.insert(90, 9);
        map.insert(83, 10);
        map.insert(88, 11);
        map.insert(67, 12);
        map.insert(70, 13);
        map.insert(86, 14);
        map.insert(71, 15);
        map.insert(66, 16);
        map.insert(78, 17);
        map.insert(74, 18);
        map.insert(77, 19);
        map.insert(75, 20);
        map.insert(188, 21);
        map.insert(76, 22);
        map.insert(190, 23);
        map.insert(191, 24);
        map.insert(222, 25);
        map.insert(49, 20);
        map.insert(81, 21);
        map.insert(50, 22);
        map.insert(87, 23);
        map.insert(69, 24);
        map.insert(52, 25);
        map.insert(82, 26);
        map.insert(53, 27);
        map.insert(84, 28);
        map.insert(89, 29);
        map.insert(55, 30);
        map.insert(85, 31);
        map.insert(56, 32);
        map.insert(57, 34);
        map.insert(189, 37);
        map.insert(187, 39);
        map.insert(73, 33);
        map.insert(79, 35);
        map.insert(80, 36);
        map.insert(219, 38);
        map.insert(221, 40);
        map
    }

    pub fn vp_key_map() -> FxHashMap<u8, u8> {
        let mut map = FxHashMap::default();
        map.insert(49, 12);
        map.insert(50, 14);
        map.insert(51, 16);
        map.insert(52, 17);
        map.insert(53, 19);
        map.insert(54, 21);
        map.insert(55, 23);
        map.insert(56, 24);
        map.insert(57, 26);
        map.insert(48, 28);
        map.insert(81, 29);
        map.insert(87, 31);
        map.insert(69, 33);
        map.insert(82, 35);
        map.insert(84, 36);
        map.insert(89, 38);
        map.insert(85, 40);
        map.insert(73, 41);
        map.insert(79, 43);
        map.insert(80, 45);
        map.insert(65, 47);
        map.insert(83, 48);
        map.insert(68, 50);
        map.insert(70, 52);
        map.insert(71, 53);
        map.insert(72, 55);
        map.insert(74, 57);
        map.insert(75, 59);
        map.insert(76, 60);
        map.insert(90, 62);
        map.insert(88, 64);
        map.insert(67, 65);
        map.insert(86, 67);
        map.insert(66, 69);
        map.insert(78, 71);
        map.insert(77, 72);
        map
    }
}