use protobuf::RepeatedField;

use pianorhythm_proto::midi_renditions::{MidiDto, MidiDtoType};
use pianorhythm_proto::server_message::{MidiMessageInputDto, MidiMessageInputDto_MidiMessageInputBuffer};

pub type NoteBufferEngineOnFlushedBuffer = Box<dyn Fn(MidiMessageInputDto) + Send + 'static>;

pub struct NoteBufferEngine {
    note_buffer: Vec<MidiMessageInputDto_MidiMessageInputBuffer>,
    note_buffer_time: Option<i64>,
    pub server_time_offset: i64,
    max_note_buffer_size: usize,
    room_is_self_hosted: bool,
    client_is_self_muted: bool,
    stop_emitting_to_ws_when_alone: bool,
    pub initialized: bool,
    pub debug_mode: bool,
    on_handle: NoteBufferEngineOnFlushedBuffer,
}

impl NoteBufferEngine {
    pub fn new(on_handle: NoteBufferEngineOnFlushedBuffer) -> Self {
        NoteBufferEngine {
            note_buffer: vec![],
            note_buffer_time: None,
            server_time_offset: 0,
            max_note_buffer_size: 300,
            room_is_self_hosted: false,
            initialized: false,
            client_is_self_muted: false,
            debug_mode: false,
            stop_emitting_to_ws_when_alone: false,
            on_handle,
        }
    }

    pub fn initialize(&self) {}

    pub fn dispose(&mut self) {
        self.clean_up();
    }

    /// Resets the buffer and its start time.
    pub fn clean_up(&mut self) {
        self.note_buffer_time = None;
        self.note_buffer = vec![];
    }

    /// Flushes the current buffer of notes via the on_handle callback.
    pub fn flush_buffer(&mut self) {
        if let Some(nbft) = self.note_buffer_time {
            if self.note_buffer.is_empty() {
                return;
            }

            let mut output = MidiMessageInputDto::new();
            output.set_time(format!("{}", nbft + self.server_time_offset));
            output.set_data(RepeatedField::from_vec(self.note_buffer.clone()));

            if self.debug_mode {
                // log::info!("Note Buffer Output {:?} | {}", &output, self.server_time_offset);
            }

            (self.on_handle)(output);

            self.note_buffer_time = None;
            self.note_buffer = vec![];
        }
    }

    /// Toggles whether to process messages when the user is alone.
    /// Clears the buffer if set to true.
    pub fn stop_emitting_to_ws_when_alone(&mut self, is_alone: bool) {
        self.stop_emitting_to_ws_when_alone = is_alone;

        if is_alone {
            self.clean_up();
        }
    }

    /// Sets if the room is hosted by the current user. If so, MIDI messages
    /// from this client are typically ignored to prevent echo.
    pub fn set_room_is_self_hosted(&mut self, value: bool) {
        self.room_is_self_hosted = value;
    }

    /// Toggles the client's mute status.
    /// Clears the buffer if muted.
    pub fn set_client_is_muted(&mut self, is_muted: bool) {
        self.client_is_self_muted = is_muted;

        if is_muted {
            self.clean_up();
        }
    }

    /// Processes an incoming MIDI message, adding it to the buffer if conditions are met.
    pub fn process_message(&mut self, dto: MidiDto) {
        if self.client_is_self_muted || self.room_is_self_hosted || self.stop_emitting_to_ws_when_alone {
            return;
        }

        match self.note_buffer_time {
            None => {
                self.note_buffer_time = Some(chrono::Utc::now().timestamp_millis());
                let mut delay = 0;

                // Minimum delay
                if dto.messageType == MidiDtoType::NoteOff {
                    delay = 40;
                }

                self.push_to_note_buffer(dto, delay);
            }
            Some(nbft) => {
                let delay = chrono::Utc::now().timestamp_millis() - nbft;
                self.push_to_note_buffer(dto, delay);
            }
        }
    }

    // Helper function to create and push a message to the note buffer.
    fn push_to_note_buffer(&mut self, dto: MidiDto, delay: i64) {
        // The condition must be strictly less than (<) the max size to avoid an
        // off-by-one error where the buffer could hold one more item than the max.
        if self.note_buffer.len() < self.max_note_buffer_size {
            let mut buffer = MidiMessageInputDto_MidiMessageInputBuffer::new();
            buffer.set_delay(delay as f64);
            buffer.set_data(dto);
            self.note_buffer.push(buffer);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;
    use std::time::Duration;
    use std::sync::{Arc, Mutex};
    use pianorhythm_proto::midi_renditions::{MidiDto_MidiNoteOff, MidiDto_MidiNoteOn};

    // Helper to create a NoteOn MidiDto for testing.
    fn create_note_on_dto(note: i32, velocity: i32) -> MidiDto {
        let mut midi_dto = MidiDto::new();
        midi_dto.set_messageType(MidiDtoType::NoteOn);
        let mut note_on = MidiDto_MidiNoteOn::new();
        note_on.set_channel(0);
        note_on.set_note(note);
        note_on.set_velocity(velocity);
        midi_dto.set_noteOn(note_on);
        midi_dto
    }

    // Helper to create a NoteOff MidiDto for testing.
    fn create_note_off_dto(note: i32) -> MidiDto {
        let mut midi_dto = MidiDto::new();
        midi_dto.set_messageType(MidiDtoType::NoteOff);
        let mut note_off = MidiDto_MidiNoteOff::new();
        note_off.set_channel(0);
        note_off.set_note(note);
        midi_dto.set_noteOff(note_off);
        midi_dto
    }

    #[test]
    fn test_process_and_flush_happy_path() {
        // Use Arc<Mutex<...>> to capture the flushed data from the callback in a thread-safe way.
        let flushed_data = Arc::new(Mutex::new(None));
        let flushed_data_clone = Arc::clone(&flushed_data);

        let on_handle = Box::new(move |dto: MidiMessageInputDto| {
            let mut data = flushed_data_clone.lock().unwrap();
            *data = Some(dto);
        });

        let mut engine = NoteBufferEngine::new(on_handle);
        engine.server_time_offset = 1000; // Set a known offset for predictable time calculations.

        // Process two messages
        let note_on_1 = create_note_on_dto(60, 100);
        engine.process_message(note_on_1.clone());
        assert_eq!(engine.note_buffer.len(), 1);
        assert!(engine.note_buffer_time.is_some());

        // Introduce a small delay to test the delay calculation.
        thread::sleep(Duration::from_millis(50));

        let note_on_2 = create_note_on_dto(62, 110);
        engine.process_message(note_on_2.clone());
        assert_eq!(engine.note_buffer.len(), 2);

        // Flush the buffer
        let buffer_time_before_flush = engine.note_buffer_time.unwrap();
        engine.flush_buffer();

        // Assert buffer is cleared after flush
        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());

        // Assert that the on_handle callback was called with the correct data
        let locked_data = flushed_data.lock().unwrap();
        let flushed_dto = locked_data.as_ref().expect("on_handle was not called");

        assert_eq!(flushed_dto.get_data().len(), 2);
        assert_eq!(
            flushed_dto.get_time(),
            format!("{}", buffer_time_before_flush + engine.server_time_offset)
        );

        // Check first message in buffer
        let msg1 = &flushed_dto.get_data()[0];
        assert_eq!(msg1.get_delay(), 0.0); // First message has 0 delay.
        assert_eq!(msg1.get_data(), &note_on_1);

        // Check second message in buffer
        let msg2 = &flushed_dto.get_data()[1];
        assert!(msg2.get_delay() >= 50.0); // Delay should be at least 50ms.
        assert!(msg2.get_delay() < 100.0); // And reasonably less than a much larger value.
        assert_eq!(msg2.get_data(), &note_on_2);
    }

    #[test]
    fn test_flush_empty_buffer_does_nothing() {
        // Use a flag to check if the handler was called.
        let handler_called = Arc::new(Mutex::new(false));
        let handler_called_clone = Arc::clone(&handler_called);

        let on_handle = Box::new(move |_| {
            *handler_called_clone.lock().unwrap() = true;
        });

        let mut engine = NoteBufferEngine::new(on_handle);
        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());

        engine.flush_buffer();

        // The handler should NOT have been called.
        assert_eq!(*handler_called.lock().unwrap(), false);
    }

    #[test]
    fn test_messages_are_ignored_when_muted() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.set_client_is_muted(true);
        assert!(engine.client_is_self_muted);

        engine.process_message(create_note_on_dto(60, 100));

        assert!(engine.note_buffer.is_empty());
    }

    #[test]
    fn test_setting_muted_clears_buffer() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.process_message(create_note_on_dto(60, 100));
        assert_eq!(engine.note_buffer.len(), 1);

        // Muting should clear any pending notes.
        engine.set_client_is_muted(true);
        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());
    }

    #[test]
    fn test_messages_are_ignored_when_self_hosted() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.set_room_is_self_hosted(true);
        assert!(engine.room_is_self_hosted);

        engine.process_message(create_note_on_dto(60, 100));

        assert!(engine.note_buffer.is_empty());
    }

    #[test]
    fn test_messages_are_ignored_when_alone() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.stop_emitting_to_ws_when_alone(true);
        assert!(engine.stop_emitting_to_ws_when_alone);

        engine.process_message(create_note_on_dto(60, 100));

        assert!(engine.note_buffer.is_empty());
    }

    #[test]
    fn test_setting_alone_clears_buffer() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.process_message(create_note_on_dto(60, 100));
        assert_eq!(engine.note_buffer.len(), 1);

        // Setting alone status should clear pending notes.
        engine.stop_emitting_to_ws_when_alone(true);
        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());
    }

    #[test]
    fn test_note_buffer_max_size() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.max_note_buffer_size = 5;

        for i in 0..10 {
            engine.process_message(create_note_on_dto(60 + i, 100));
        }

        // The buffer should not grow past the max size.
        assert_eq!(engine.note_buffer.len(), 5);
    }

    #[test]
    fn test_initial_note_off_delay() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));

        // The first message is a NoteOff.
        engine.process_message(create_note_off_dto(60));

        assert_eq!(engine.note_buffer.len(), 1);
        let buffered_message = &engine.note_buffer[0];

        // It should have the special hardcoded delay.
        assert_eq!(buffered_message.get_delay(), 40.0);
    }

    #[test]
    fn test_dispose_clears_buffer() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.process_message(create_note_on_dto(60, 100));
        assert_eq!(engine.note_buffer.len(), 1);
        assert!(engine.note_buffer_time.is_some());

        engine.dispose();

        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());
    }
}