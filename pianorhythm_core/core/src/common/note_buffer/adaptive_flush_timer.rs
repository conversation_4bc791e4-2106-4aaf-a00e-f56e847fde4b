use instant::Instant;
use std::time::Duration;

/// Configuration for the adaptive flush timer, controlling how often
/// the buffer should be flushed based on activity and buffer utilization.
#[derive(Debug, <PERSON>lone)]
pub struct AdaptiveFlushConfig {
    /// Minimum flush interval in milliseconds (used during high activity).
    pub min_interval_ms: u32, // 25ms for high activity
    /// Maximum flush interval in milliseconds (used during low activity).
    pub max_interval_ms: u32, // 200ms for low activity
    /// Time window in milliseconds for detecting note activity.
    pub activity_window_ms: u32, // 100ms window for activity detection
    /// Buffer utilization threshold (0.7 = flush when 70% full).
    pub buffer_threshold: f32, // 0.7 = flush when 70% full
    /// Number of notes in the activity window to be considered high activity.
    pub high_activity_note_count: u32, // 5 notes in window = high activity
}

impl Default for AdaptiveFlushConfig {
    /// Returns the default configuration for the adaptive flush timer.
    fn default() -> Self {
        Self {
            min_interval_ms: 25,
            max_interval_ms: 200,
            activity_window_ms: 100,
            buffer_threshold: 0.7,
            high_activity_note_count: 5,
        }
    }
}

/// Adaptive flush timer that dynamically adjusts the flush interval
/// based on recent note activity and buffer utilization.
pub struct AdaptiveFlushTimer {
    config: AdaptiveFlushConfig,
    last_note_time: Option<Instant>,
    note_timestamps: Vec<Instant>,
    current_interval_ms: u32,
}

impl AdaptiveFlushTimer {
    /// Creates a new `AdaptiveFlushTimer` with the given configuration.
    ///
    /// # Arguments
    ///
    /// * `config` - The configuration for the adaptive flush timer.
    pub fn new(config: AdaptiveFlushConfig) -> Self {
        Self {
            current_interval_ms: config.max_interval_ms,
            config,
            last_note_time: None,
            note_timestamps: Vec::new(),
        }
    }

    /// Records a note activity at the current time and updates the activity window.
    pub fn record_note_activity(&mut self) {
        let now = Instant::now();
        self.last_note_time = Some(now);
        self.note_timestamps.push(now);

        // Clean old timestamps outside activity window
        let cutoff = Duration::from_millis(self.config.activity_window_ms as u64);
        self.note_timestamps
            .retain(|timestamp| now.duration_since(*timestamp) <= cutoff);
    }

    /// Calculates the next flush interval based on buffer utilization and recent activity.
    ///
    /// # Arguments
    ///
    /// * `buffer_utilization` - The current buffer utilization (0.0 to 1.0).
    ///
    /// # Returns
    ///
    /// The next flush interval in milliseconds. Returns 0 for immediate flush.
    pub fn calculate_next_interval(&mut self, buffer_utilization: f32) -> u32 {
        // Check for immediate flush conditions
        if self.should_flush_immediately(buffer_utilization) {
            return 0; // Flush immediately
        }

        // Determine activity level
        let is_high_activity = self.note_timestamps.len() >= self.config.high_activity_note_count as usize;
        let time_since_last_note_ms = self
            .last_note_time
            .map(|last| Instant::now().duration_since(last).as_millis() as i64)
            .unwrap_or(i64::MAX);

        // Calculate interval based on activity and buffer state
        self.current_interval_ms = if is_high_activity || time_since_last_note_ms < self.config.activity_window_ms as i64 {
            self.config.min_interval_ms
        } else {
            // Gradually increase interval as activity decreases
            let activity_factor = (time_since_last_note_ms as f32 / self.config.activity_window_ms as f32).min(1.0);
            let interval_range = self.config.max_interval_ms - self.config.min_interval_ms;
            self.config.min_interval_ms + (interval_range as f32 * activity_factor) as u32
        };

        self.current_interval_ms
    }

    /// Determines if the buffer should be flushed immediately based on utilization and activity.
    ///
    /// # Arguments
    ///
    /// * `buffer_utilization` - The current buffer utilization (0.0 to 1.0).
    ///
    /// # Returns
    ///
    /// `true` if the buffer should be flushed immediately, `false` otherwise.
    pub fn should_flush_immediately(&self, buffer_utilization: f32) -> bool {
        buffer_utilization > 0.9 || // 90% full
            (buffer_utilization > self.config.buffer_threshold &&
                self.note_timestamps.len() >= self.config.high_activity_note_count as usize)
    }

    /// Returns the current flush interval in milliseconds.
    pub fn get_current_interval(&self) -> u32 {
        self.current_interval_ms
    }
}
