[package]
name = "pianorhythm_core"
version = "0.1.0"
authors = ["Oak <<EMAIL>>"]
edition = "2021"
rust-version.workspace = true

[lib]
crate-type = ["cdylib", "rlib"]

[features]
default = [
    "console_error_panic_hook",
    "use_synth",
]
use_synth = ["pianorhythm_synth"]
desktop_lib = []

[dependencies]
pianorhythm_proto = { path = "../proto" }
pianorhythm_shared = { path = "../shared" }
serde = { workspace = true }
log = { workspace = true }
protobuf = { workspace = true }
chrono = { workspace = true }
once_cell = { workspace = true }
derivative = { workspace = true }
rustc-hash = { workspace = true }
cfg-if = { workspace = true }
uuid = { workspace = true }
matchbox_socket = "0.10.0"
lazy_static = "1.5.0"
crossbeam-channel = { workspace = true }
js-sys = { workspace = true }

[target.'cfg(target_arch = "x86_64")'.dependencies]
cached = { workspace = true }
reactive-state = { workspace = true, features = ["simple_logger"] }
ezsockets = { version = "0.6.1", features = ["rustls"] }
async-trait = "0.1.77"
pianorhythm_synth = { path = "../synth", features = ["desktop_lib"], optional = true }
futures = { version = "0.3.30" }
futures-timer = { version = "3" }
instant = { version = "0.1.13" }

[target.'cfg(all(target_arch = "wasm32", target_os = "unknown"))'.dependencies]
pianorhythm_synth = { path = "../synth", optional = true }
wasm-bindgen = { workspace = true }
wasm-bindgen-futures = { workspace = true }
web-sys = { workspace = true }
js-sys = { workspace = true }
gloo = { workspace = true }
gloo-timers = { workspace = true }
gloo-events = { workspace = true }
gloo-storage = { workspace = true }
cached = { workspace = true, features = ["wasm"] }
console_log = { workspace = true }
serde-wasm-bindgen = { workspace = true }
console_error_panic_hook = { version = "0.1.7", optional = true }
reactive-state = { workspace = true, features = ["wasm-bindgen"] }
wasm_thread = "0.3.0"
futures = { version = "0.3.30", default-features = false }
futures-timer = { version = "3", features = ["wasm-bindgen"] }
async_wasm_task = "0.2.3"
game-loop = { version = "1.3.0" }
instant = { version = "0.1.13", features = ["wasm-bindgen", "inaccurate"] }

# `wee_alloc` is a tiny allocator for wasm that is only ~1K in code size
# compared to the default allocator's ~10K. It is slower than the default
# allocator, however.
#
# Unfortunately, `wee_alloc` requires nightly Rust when targeting wasm for now.
wee_alloc = { version = "0.4.5", optional = true }

[profile.dev.package."*"]
opt-level = 3

