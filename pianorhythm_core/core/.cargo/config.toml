[unstable]
build-std = ['std', 'panic_abort']

[target.wasm32-unknown-unknown]
rustflags = ["-C", "target-feature=+atomics,+bulk-memory,+mutable-globals", "-C",
    "link-args=--shared-memory --import-memory --max-memory=4294967296"]

[target.release]
rustflags = ["-C", "target-feature=+atomics,+bulk-memory,+mutable-globals", "-C",
    "link-args=--shared-memory --import-memory --max-memory=4294967296"]