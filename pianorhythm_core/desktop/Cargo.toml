[package]
name = "pianorhythm_desktop"
version = "0.2.0"
description = "PianoRhythm Desktop Application"
authors = ["Oak"]
rust-version.workspace = true
edition = "2021"

[features]
# by default <PERSON><PERSON> runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = [
    "custom-protocol",
    "use_bevy_renderer"
    #    "use_bevy_ecs"
]
# this feature is used for production builds where `devPath` points to the filesystem
# DO NOT remove this
custom-protocol = ["tauri/custom-protocol"]
use_bevy_ecs = []
use_bevy_renderer = [
#    "winit"
]

[build-dependencies]
tauri-build = { version = "2.0.1", features = [] }

[dependencies]
pianorhythm_core = { path = "../core", default-features = false, features = ["desktop_lib", "use_synth"] }
pianorhythm_proto = { path = "../proto" }
pianorhythm_bevy_renderer = { path = "../bevy_renderer", features = ["desktop"] }

serde = { workspace = true }
serde_json = { workspace = true }
protobuf = { workspace = true }
midir = { workspace = true }
midly = { workspace = true }
lazy_static = { workspace = true }
log = { workspace = true }
wasm-msgpack = { workspace = true }
crossbeam-channel = { workspace = true }
log-panics = { workspace = true }
raw-window-handle = { version = "0.6" }
atomic_float = "1.1.0"
cpal = { version = "0.15.3", features = [] }
reqwest = "0.12"
nodi = "1.0.0"
wgpu = "23.0.1"
kira = "0.10.4"
rtrb = "0.3.2"

tauri = { version = "2.0.3", features = ["devtools", "unstable", "config-json5"] }
tauri-plugin-updater = "2.0.2"
tauri-plugin-log = { version = "2.0.1", features = ["colored"] }
tauri-plugin-fs = "2.0.1"
tauri-plugin-dialog = "2.0.1"
tauri-plugin-http = { version = "2.0.1", features = ["json", "cookies", "multipart", "stream", "unsafe-headers", "gzip"] }
tauri-plugin-single-instance = "2.0.1"

[profile.dev.package."*"]
opt-level = 3
