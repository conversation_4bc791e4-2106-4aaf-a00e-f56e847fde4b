#![feature(option_result_contains)]

mod backend_desktop;
mod renderer_cpu_usage;

use std::collections::HashMap;
use std::error::Error;
use std::fmt::{Debug, Display};
use std::ops::Add;
use std::sync::atomic::{AtomicBool, AtomicU32};
use std::sync::mpsc::{channel, Sender};
use std::sync::{mpsc, Arc, Mutex, OnceLock};
use std::time::{Duration, Instant};

use crate::audio_engine::backend_desktop::{CpalSynthBackend, CustomCpalBackendSettings};
use cpal::traits::{DeviceTrait, HostTrait, StreamTrait};
use cpal::{FromSample, Sample, StreamConfig, StreamInstant};
use kira::backend::mock::{MockBackend, MockBackendSettings};
use kira::backend::{mock, Backend, Renderer};
use kira::sound::static_sound::StaticSoundData;
use kira::sound::PlaybackState;
use kira::{AudioManager, AudioManagerSettings};
use lazy_static::lazy_static;
use midir::{MidiInputConnection, MidiOutputConnection};
use nodi::Connection;
use pianorhythm_bevy_renderer::bevy_desktop::{default, ManiaAudioEvent};
use pianorhythm_bevy_renderer::plugins::games::mania::events::ManiaAudioSongState;
use serde::Serialize;
use tauri::async_runtime::JoinHandle;
use tauri::{Emitter, Manager, WebviewWindow, Window, Wry};
use wasm_msgpack::encode::SerializeIntoSlice;

struct MidiHandle(String, JoinHandle<()>, Arc<AtomicBool>);

lazy_static! {
    static ref STREAM_LOOP: AtomicBool = AtomicBool::new(false);
    static ref CLIENT_SOCKET_ID: AtomicU32 = AtomicU32::new(0);
    static ref AUDIO_SYNTH_HANDLE: Mutex<Option<JoinHandle<()>>> = Mutex::new(None);
}

static mut SYNTH_BACKEND: OnceLock<SynthBackend> = OnceLock::new();
static mut AUDIO_MANAGER: OnceLock<AudioManager<CpalSynthBackend>> = OnceLock::new();

#[derive(Default)]
pub struct MidiState {
    pub inputs: Mutex<HashMap<String, MidiInputConnection<()>>>,
    pub outputs: Arc<Mutex<HashMap<String, MidiOutputConnection>>>,
}

pub struct SynthBackendTask {
    func: Box<dyn Fn() + Send>,
    due: Instant,
    id: u128,
}

#[derive(Clone, Serialize)]
struct PianoRhythmMidiMessage {
    message: Vec<u8>,
}

fn emit_error_message(window: &Window<Wry>, message: &str) {
    window
        .emit("error_message", message)
        .inspect_err(|e| {
            log::error!("Error sending error message: {} | Hah...", e);
        })
        .ok();
}

pub struct SynthBackend {
    _host: cpal::Host,
    device: cpal::Device,
    stream_config: cpal::StreamConfig,
    sample_format: cpal::SampleFormat,
    sender: Option<Sender<SynthBackendTask>>,
    current_task_id: u128,
}

impl SynthBackend {
    pub fn new(
        options: &pianorhythm_core::PianoRhythmSynthesizerDescriptor,
        device_id: Option<String>,
    ) -> Result<Self, Box<dyn Error>> {
        let available_hosts = cpal::available_hosts();
        log::info!("Available hosts:\n  {:?}", available_hosts);
        log::info!("Supported hosts:\n  {:?}", cpal::ALL_HOSTS);
        let host = cpal::default_host();

        let target_device = if let Ok(mut output_devices) = host.output_devices() {
            output_devices
                .find(|device| {
                    if let Some(name) = &device_id {
                        if let Ok(ref name2) = device.name() {
                            return name.eq(name2) && device.supported_output_configs().is_ok();
                        }
                    }
                    return false;
                })
                .or(host.default_output_device())
        } else {
            None
        };

        let device = target_device.ok_or("failed to find a default output device")?;
        log::info!("Selected audio device {:?}", device.name());

        let base_config = device.default_output_config()?;
        let stream_config: StreamConfig = StreamConfig {
            buffer_size: options
                .buffer_size
                .map_or(cpal::BufferSize::Default, |v| cpal::BufferSize::Fixed(v as u32)),
            sample_rate: options
                .sample_rate
                .map_or(base_config.sample_rate(), |v| cpal::SampleRate(v as u32)),
            channels: options.audio_channels.into(),
            ..base_config.config()
        };

        let sample_format = base_config.sample_format();
        log::info!(
            "Device Name: {:?} | BufferSize: {:?} | Sample Rate: {}",
            device.name(),
            stream_config.buffer_size,
            stream_config.sample_rate.0
        );

        let mut manager = AudioManager::<CpalSynthBackend>::new(AudioManagerSettings {
            backend_settings: CustomCpalBackendSettings {
                sample_rate: stream_config.sample_rate.0,
                num_channels: stream_config.channels as u32,
                ..default()
            },
            ..default()
        })
        .unwrap();

        unsafe {
            _ = AUDIO_MANAGER.set(manager);
        }

        Ok(Self {
            _host: host,
            device,
            stream_config,
            sample_format,
            sender: None,
            current_task_id: 0,
        })
    }

    fn run<T: cpal::Sample>(&mut self, window: Arc<Window>) -> Result<(cpal::Stream), Box<dyn Error>> {
        let err_fn = move |err| {
            let message = format!(
                "[Audio Stream] An error occurred when trying to create stream: {} | Please try reloading the app.",
                err
            );
            log::error!("{}", message);
            emit_error_message(window.as_ref(), &message);
        };

        let num_of_channels = self.stream_config.channels as usize;
        log::info!("Number of Device Audio Channels: {num_of_channels:?}");

        let mut since: u128 = 0;
        let ts = Instant::now();
        let (tx, mut rx) = channel::<SynthBackendTask>();
        self.sender = Some(tx.clone());

        let mut buffer_tasks: Arc<Mutex<Vec<SynthBackendTask>>> = Arc::new(Mutex::new(vec![]));

        #[cfg(debug_assertions)]
        log::debug!("Spawning thread for audio buffer.");

        tauri::async_runtime::spawn(async move {
            log::debug!("Audio buffer tasks loop started.");

            loop {
                if let Ok(mut current_buffer) = buffer_tasks.lock() {
                    let current_time = Instant::now();

                    for task in current_buffer.iter() {
                        if task.due <= current_time {
                            (task.func)();
                        }
                    }

                    if current_buffer.len() > 0 {
                        current_buffer.retain(|x| x.due >= current_time);
                    }

                    // Execute tasks
                    if let Ok(task) = rx.try_recv() {
                        current_buffer.push(task);
                    }
                }
            }
        });

        #[cfg(debug_assertions)]
        log::debug!("Spawned thread for audio buffer.");

        #[cfg(feature = "use_bevy_renderer")]
        let mut audio_time_start: Option<StreamInstant> = None;

        match self.device.build_output_stream(
            &self.stream_config,
            move |output: &mut [f32], info: &cpal::OutputCallbackInfo| {
                let playback_ts = info.timestamp().playback;
                let callback_ts = info.timestamp().callback;

                if let Some(current_latency) = playback_ts.duration_since(&callback_ts) {
                    // This gives you the current latency between when the callback runs
                    // and when the audio will actually be played by the hardware
                    pianorhythm_bevy_renderer::bevy_desktop::ManiaAudioEvent::send_current_time(
                        current_latency.as_millis(),
                    );
                }

                unsafe {
                    if let Some(manager) = AUDIO_MANAGER.get_mut() {
                        let mut renderer = manager.backend_mut();
                        renderer.on_start_processing();
                        renderer.process(output);
                    }
                }

                #[cfg(feature = "use_bevy_renderer")]
                {
                    if audio_time_start.is_none() {
                        audio_time_start = Some(info.timestamp().callback);
                    }

                    if let Some(time_start) = audio_time_start {
                        let time_now = info.timestamp().callback;
                        if let Some(time_since) = time_now.duration_since(&time_start) {
                            // log::info!("Callback set to {:?}", time_since.as_millis());
                            unsafe {
                                pianorhythm_bevy_renderer::bevy_desktop::ManiaAudioEvent::send_current_time(
                                    time_since.as_millis(),
                                );
                            }
                        }
                    }
                }

                for frame in output.chunks_mut(num_of_channels) {
                    let (l, r) = pianorhythm_core::read_next_rust();
                    let l: f32 = f32::from_sample(l);
                    let r: f32 = f32::from_sample(r);

                    let mut left = l;
                    let mut right = r;
                    pianorhythm_core::equalize(&mut left);
                    pianorhythm_core::equalize(&mut right);

                    // Mix the samples from renderer.process and pianorhythm_core::read_next_rust
                    match num_of_channels {
                        1 => {
                            // Mono output: average the left and right samples
                            let sample = (frame[0] + (l + r) / 2.0) / 2.0;
                            frame[0] = sample;
                        }
                        2 => {
                            // Stereo output: assign left and right samples
                            let left_sample = (frame[0] + l) / 2.0;
                            let right_sample = (frame[1] + r) / 2.0;
                            frame[0] = left_sample;
                            frame[1] = right_sample;
                        }
                        _ => {
                            // Multi-channel output: distribute samples across channels
                            // Store original values before modifying
                            let original_values: Vec<f32> = frame.iter().copied().collect();

                            for (i, sample_out) in frame.iter_mut().enumerate() {
                                let sample = if i % 2 == 0 {
                                    (original_values[i] + l) / 2.0
                                } else {
                                    (original_values[i] + r) / 2.0
                                };
                                *sample_out = sample;
                            }
                        }
                    }
                }

                since = since.saturating_add(10);
                let elapsed = ts.elapsed();

                // Note buffer flush
                if since % 200 == 0 {
                    pianorhythm_core::flush_note_buffer_engine();
                    since = 0;
                }
            },
            err_fn,
            None,
        ) {
            Ok(stream) => match stream.play() {
                Ok(_) => {
                    log::debug!("Audio stream build success.");
                    Ok(stream)
                }
                Err(err) => Err(format!("Failed to play stream. PlayStreamError: {err:?}")
                    .to_string()
                    .into()),
            },
            Err(err) => Err(format!("Failed to build stream. BuildStreamError: {err:?}")
                .to_string()
                .into()),
        }
    }

    pub fn new_output_connection(&mut self, window: Arc<Window>) -> Result<(cpal::Stream), Box<dyn Error>> {
        match self.sample_format {
            cpal::SampleFormat::I8 => self.run::<i8>(window),
            cpal::SampleFormat::I16 => self.run::<i16>(window),
            cpal::SampleFormat::I32 => self.run::<i32>(window),
            cpal::SampleFormat::I64 => self.run::<i64>(window),
            cpal::SampleFormat::U8 => self.run::<u8>(window),
            cpal::SampleFormat::U16 => self.run::<u16>(window),
            cpal::SampleFormat::U32 => self.run::<u32>(window),
            cpal::SampleFormat::U64 => self.run::<u64>(window),
            cpal::SampleFormat::F32 => self.run::<f32>(window),
            cpal::SampleFormat::F64 => self.run::<f64>(window),

            sample_format => Err(format!("Unsupported sample format {}", sample_format))?,
        }
    }

    pub fn add_task(&mut self, ms: u64, func: Box<dyn Fn() + Send>) {
        let current_task_id = self.current_task_id.saturating_add(1);
        if let Some(sender) = &self.sender {
            sender.send(SynthBackendTask {
                func,
                id: current_task_id,
                due: Instant::now().add(Duration::from_millis(ms)),
            });
        }
        self.current_task_id = current_task_id;
    }

    fn playback_state_to_mania_audio_state(playback_state: PlaybackState) -> ManiaAudioSongState {
        match playback_state {
            PlaybackState::Playing => ManiaAudioSongState::Playing,
            PlaybackState::Paused => ManiaAudioSongState::Paused,
            PlaybackState::Stopping => ManiaAudioSongState::Stopped,
            _ => ManiaAudioSongState::Stopped,
        }
    }

    pub fn play_song_from_file(path: impl AsRef<std::path::Path> + Send + 'static + Display + Debug) {
        log::debug!("Playing song from file {path:?}");
        tauri::async_runtime::spawn(async move {
            unsafe {
                if let Some(manager) = AUDIO_MANAGER.get_mut() {
                    match StaticSoundData::from_file(path) {
                        Ok(sound_data) => {
                            let mut sound = manager.play(sound_data).unwrap();
                            let sound_state = sound.state();

                            let mut song_state = Self::playback_state_to_mania_audio_state(sound_state);
                            ManiaAudioEvent::send_song_state(song_state);

                            loop {
                                if sound.state() == PlaybackState::Stopped {
                                    ManiaAudioEvent::send_song_state(ManiaAudioSongState::Stopped);
                                    break;
                                }

                                let current_song_state = Self::playback_state_to_mania_audio_state(sound.state());
                                if song_state != current_song_state {
                                    song_state = current_song_state;
                                    ManiaAudioEvent::send_song_state(song_state);
                                }

                                ManiaAudioEvent::send_current_song_position(sound.position());
                                std::thread::sleep(Duration::from_millis(1));
                            }
                        }
                        Err(err) => {
                            panic!("Failed to load static audio file: {:?}", err);
                        }
                    }
                } else {
                    panic!("Failed to load audio manager");
                }
            }
        });
        log::debug!("Done");
    }
}

pub async fn start(
    window: Arc<Window>,
    options: &pianorhythm_core::PianoRhythmSynthesizerDescriptor,
    device_id: Option<String>,
) -> Result<cpal::Stream, Box<dyn Error>> {
    let mut synth_backend = SynthBackend::new(options, device_id)?;
    let result = synth_backend.new_output_connection(window.clone());

    unsafe {
        _ = SYNTH_BACKEND.set(synth_backend);
    }

    result
}

// Used as a way to simulate setTimeout in js for Rust functions
pub fn add_task_to_audio_task(ms: f64, task: Box<dyn Fn() + Send>) {
    unsafe {
        if let Some(synth) = SYNTH_BACKEND.get_mut() {
            synth.add_task(ms as u64, task);
        };
    }
}

pub mod audio_engine_commands {
    use std::collections::HashMap;
    use std::fs::write;
    use std::io::{Cursor, Write};
    use std::path::Path;
    use std::sync::atomic::{AtomicBool, Ordering};
    use std::sync::Arc;
    use std::time::Duration;

    use cpal::traits::{DeviceTrait, HostTrait};
    use lazy_static::lazy_static;
    use midir::{MidiInput, MidiOutput};
    use pianorhythm_bevy_renderer::bevy_desktop::ManiaAudioEvent;
    use pianorhythm_core::{PianoRhythmCurrentProgram, PianoRhythmSynthEvent};
    use protobuf::Message;
    use reqwest::header::{ACCEPT, USER_AGENT};
    use tauri::{command, Emitter, Manager, Window, Wry};

    use super::{emit_error_message, start, MidiState, AUDIO_SYNTH_HANDLE, CLIENT_SOCKET_ID, STREAM_LOOP};

    lazy_static! {
        static ref SOUNDFONT_LOADING: AtomicBool = AtomicBool::new(false);
    }

    #[derive(Clone, serde::Serialize)]
    struct SoundfontPresetsLoadedPayload {
        presets: Vec<Vec<u8>>,
        file: String,
    }

    #[derive(Clone, serde::Serialize)]
    struct MidiConnectionError {
        midi_id: String,
        error_message: String,
    }

    #[command]
    pub fn init_soundfont(
        window: Window<Wry>,
        sample_rate: f32,
        backend_synth: i8,
        device_id: Option<String>,
    ) -> Result<(), String> {
        if let Ok(o) = AUDIO_SYNTH_HANDLE.try_lock() {
            if (o.is_some()) {
                return Ok(());
            }
        }

        if let Some(device) = device_id.clone() {
            log::debug!("Initializing synthesizer. Device ID: {device:?}");
        }

        let arc_window = Arc::new(window).clone();
        let handle_for_error = arc_window.clone();

        let handle = tauri::async_runtime::spawn(async move {
            let options = pianorhythm_core::PianoRhythmSynthesizerDescriptor {
                audio_channels: 2,
                sample_rate: Some(sample_rate),
                backend_synth: pianorhythm_core::PianoRhythmSynthesizerBackendSynth::from_i8(backend_synth),
                ..pianorhythm_core::PianoRhythmSynthesizerDescriptor::default()
            };
            log::debug!("Synthesizer options: {options:?}");
            let handle_emit = arc_window.clone();

            pianorhythm_core::create_synth(options);
            log::info!("Synthesizer initialized.");

            match start(arc_window, &options, device_id.clone()).await {
                Ok(_) => {
                    ManiaAudioEvent::send_audio_engine_ready();

                    log::debug!("Stream loop started.");
                    STREAM_LOOP.store(true, Ordering::Relaxed);
                    loop {
                        if STREAM_LOOP.load(Ordering::Relaxed) == false {
                            return;
                        }
                    }
                }
                Err(err) => {
                    let id = device_id.clone();
                    let message = format!("Failed to start audio stream for device: {id:?} | {err:?}");
                    log::error!("{}", message);
                    emit_error_message(handle_for_error.as_ref(), &message);
                }
            }
        });

        tauri::async_runtime::spawn(async move {
            pianorhythm_core::init_note_buffer_engine();
        });

        let mut state = AUDIO_SYNTH_HANDLE.lock().expect("Could not lock mutex");
        *state = Some(handle);

        log::info!("Audio synth handle created.");
        Ok(())
    }

    #[command]
    pub fn dispose(midi_state: tauri::State<'_, MidiState>) {
        let mut state = AUDIO_SYNTH_HANDLE.lock().expect("Could not lock mutex");
        if let Some(mut handle) = state.as_mut() {
            log::info!("Disposing synthesizer...");
            pianorhythm_core::dispose();

            for (_, conn) in midi_state.inputs.lock().unwrap().drain().into_iter() {
                conn.close();
            }

            for (_, conn) in midi_state.outputs.lock().unwrap().drain().into_iter() {
                conn.close();
            }

            STREAM_LOOP.store(false, Ordering::Relaxed);
            handle.abort();
            *state = None;
            log::info!("Synthesizer disposed.");
        }
    }

    #[command]
    pub fn disconnect() {
        pianorhythm_core::disconnect();
    }

    #[tauri::command]
    pub fn get_default_output_device() -> Option<String> {
        let host = cpal::default_host();
        host.default_output_device()
            .map(|e| e.name().ok().unwrap_or("Unknown Output Device Name".to_string()))
    }

    #[tauri::command]
    pub async fn list_available_devices() -> Result<HashMap<usize, String>, String> {
        let mut hash_devices = HashMap::new();
        let available_hosts = cpal::available_hosts();

        for host_id in available_hosts {
            log::debug!("{}", host_id.name());

            if let Ok(host) = cpal::host_from_id(host_id) {
                let default_out = host
                    .default_output_device()
                    .map(|e| e.name().ok().unwrap_or("Unknown Output Device Name".to_string()));
                log::debug!("Default Output Device: {:?}", default_out);

                if let Ok(devices) = std::panic::catch_unwind(|| host.output_devices().unwrap()) {
                    for (device_index, device) in devices.enumerate() {
                        if let Ok(device_name) = device.name() {
                            log::debug!("  {}. \"{}\"", device_index + 1, device_name);
                            hash_devices.insert(device_index, device_name);
                        }
                    }
                };
            }
        }

        Ok(hash_devices)
    }

    #[tauri::command]
    pub fn list_midi_input_connections() -> HashMap<usize, String> {
        pianorhythm_core::list_midi_input_connections()
    }

    #[tauri::command]
    pub fn list_midi_output_connections() -> HashMap<usize, String> {
        pianorhythm_core::list_midi_output_connections()
    }

    #[tauri::command]
    pub fn open_midi_input_connection(midi_state: tauri::State<MidiState>, window: Window<Wry>, midi_id: String) {
        if let Ok(state) = midi_state.inputs.try_lock() {
            if state.contains_key(&midi_id) {
                return;
            }
        }

        let handle = Arc::new(window).clone();
        let handle_for_error = handle.clone();
        let midi_in = MidiInput::new(&("pianorhythm-input-".to_owned() + &midi_id));

        match (pianorhythm_core::on_handle_midi_input(&midi_id, midi_in)) {
            Ok(midi_in_conn) => {
                midi_state.inputs.lock().unwrap().insert(midi_id, midi_in_conn);
            }
            Err(error) => {
                log::error!("Midi Input Connection Error: {} | {}", error, midi_id);

                handle_for_error
                    .as_ref()
                    .emit(
                        "midi_input_fail",
                        MidiConnectionError {
                            midi_id,
                            error_message: error.to_string(),
                        },
                    )
                    .map_err(|e| {
                        log::error!("Error sending Midi Input Fail message: {}", e);
                    })
                    .ok();
            }
        }
    }

    #[tauri::command]
    pub fn close_midi_input_connection(midi_state: tauri::State<'_, MidiState>, midi_id: String) {
        if let Some(input) = midi_state.inputs.lock().unwrap().remove(&midi_id) {
            input.close();
        }
    }

    #[tauri::command]
    pub fn open_midi_output_connection(midi_state: tauri::State<'_, MidiState>, window: Window<Wry>, midi_id: String) {
        if let Ok(state) = midi_state.outputs.try_lock() {
            if state.contains_key(&midi_id) {
                return;
            }
        }

        let mut output_id = "pianorhythm-output-".to_owned();
        output_id.push_str(midi_id.as_str());

        let midi_out = MidiOutput::new(output_id.as_str());
        let handle = Arc::new(window).clone();
        let handle_for_error = handle.clone();
        match midi_out {
            Ok(midi_out) => {
                let midi_out_ports = midi_out.ports();
                let port = midi_out_ports.into_iter().find(|x| {
                    if let Ok(port_name) = midi_out.port_name(x) {
                        return port_name == midi_id;
                    }
                    return false;
                });

                match port {
                    Some(port) => {
                        let midi_out_conn = midi_out.connect(&port, "midir-output");
                        match midi_out_conn {
                            Ok(midi_out_conn) => {
                                log::debug!("midi output opened: {:?}", &midi_id);
                                midi_state.outputs.lock().unwrap().insert(midi_id, midi_out_conn);
                            }
                            Err(error) => {
                                log::error!("Midi Output Connection Error: {} | {}", error, midi_id);
                                handle_for_error
                                    .as_ref()
                                    .emit(
                                        "midi_output_fail",
                                        MidiConnectionError {
                                            midi_id,
                                            error_message: error.to_string(),
                                        },
                                    )
                                    .map_err(|e| {
                                        log::error!("Error sending Midi Output Fail message: {}", e);
                                    })
                                    .ok();
                            }
                        }
                    }
                    None => {
                        log::error!("No port found at index {}", midi_id);

                        handle_for_error
                            .as_ref()
                            .emit(
                                "midi_output_fail",
                                MidiConnectionError {
                                    midi_id,
                                    error_message: "No port found at index.".to_string(),
                                },
                            )
                            .map_err(|e| {
                                log::error!("Error sending Midi Output Fail message: {}", e);
                            })
                            .ok();
                    }
                }
            }
            Err(e) => {
                let message = format!("Midi Output Error: {} | {}", e, midi_id);
                log::error!("{}", message);
                emit_error_message(handle_for_error.as_ref(), &message);
            }
        }
    }

    #[tauri::command]
    pub fn close_midi_output_connection(midi_state: tauri::State<'_, MidiState>, midi_id: String) {
        if let Some(output) = midi_state.outputs.lock().unwrap().remove(&midi_id) {
            output.close();
        }
    }

    #[tauri::command]
    pub fn emit_to_midi_output(midi_state: tauri::State<'_, MidiState>, midi_id: String, data: Vec<u8>) {
        // Prevent loop feedback
        // if let Ok(state) = midi_state.inputs.try_lock() {
        //     log::debug!("[emit to output] {} | {}", &midi_id, state.contains_key(&midi_id));
        //
        //     // let hashed_id = pianorhythm_core::hash_device_id(midi_id);
        //     if state.contains_key(&midi_id) {
        //         return;
        //     }
        // }

        if let Ok(mut output) = midi_state.outputs.try_lock() {
            if let Some(mut mut_output) = output.get_mut(&midi_id) {
                mut_output
                    .send(data.as_slice())
                    .unwrap_or_else(|_| log::error!("Error when forwarding message ..."));
            }
        }
    }

    #[command]
    pub fn all_sounds_off(channel: u8, socket_id: Option<u32>) {
        pianorhythm_core::all_sounds_off(channel, socket_id)
    }

    #[command]
    pub fn all_notes_off(channel: u8, socket_id: Option<u32>) {
        pianorhythm_core::all_notes_off(channel, socket_id)
    }

    #[command]
    pub fn reset_all_controllers(channel: u8, socket_id: Option<u32>) {
        pianorhythm_core::reset_all_controllers(channel, socket_id)
    }

    #[command]
    pub fn add_socket(socket_id: u32, is_client: bool) -> bool {
        pianorhythm_core::add_socket(socket_id, is_client)
    }

    #[command]
    pub fn reset() {
        pianorhythm_core::reset();
        CLIENT_SOCKET_ID.store(0, Ordering::Relaxed);
    }

    #[command]
    pub fn remove_socket(socket_id: u32) {
        pianorhythm_core::remove_socket(socket_id);
    }

    #[command]
    pub fn set_client_socket_id(socket_id: u32) {
        pianorhythm_core::set_client_socket_id(socket_id);
        CLIENT_SOCKET_ID.store(socket_id, Ordering::Relaxed)
    }

    #[command]
    pub fn set_interpolation_method(method: u32) {
        pianorhythm_core::set_interpolation_method(method);
    }

    #[command]
    pub fn set_octave_offset(value: i8) {
        pianorhythm_core::set_octave_offset(value)
    }

    #[command]
    pub fn set_transpose_offset(value: i8) {
        pianorhythm_core::set_transpose_offset(value)
    }

    #[command]
    pub fn set_max_multi_mode_channels(value: u8) {
        pianorhythm_core::set_max_multi_mode_channels(value)
    }

    #[command]
    pub fn set_slot_mode(value: i8) {
        pianorhythm_core::set_slot_mode(value)
    }

    #[command]
    pub fn set_primary_channel(value: u8) {
        pianorhythm_core::set_primary_channel(value)
    }

    #[command]
    pub fn set_disable_velocity_for_client(value: bool) {
        pianorhythm_core::set_disable_velocity_for_client(value)
    }

    #[command]
    pub fn synth_set_gain(value: f32) {
        pianorhythm_core::synth_set_gain(value)
    }

    #[command]
    pub fn synth_set_user_gain(value: f32, socket_id: Option<u32>) {
        pianorhythm_core::synth_set_user_gain(value, socket_id)
    }

    #[command]
    pub fn synth_set_polyphony(value: u16) {
        pianorhythm_core::synth_set_polyphony(value)
    }

    #[command]
    pub fn synth_set_reverb(value: bool) {
        pianorhythm_core::synth_set_reverb(value)
    }

    #[command]
    pub fn synth_set_auto_fill_channels_with_default(value: bool) {
        pianorhythm_core::synth_set_auto_fill_channels_with_default(value)
    }

    #[command]
    pub fn program_select(channel: u8, preset_id: u8, socket_id: Option<u32>) {
        pianorhythm_core::program_select(channel, preset_id, socket_id)
    }

    #[command]
    pub fn bank_select(channel: u8, bank_id: u32, socket_id: Option<u32>) {
        pianorhythm_core::bank_select(channel, bank_id, socket_id)
    }

    #[command]
    pub fn note_on(
        channel: u8,
        key: u8,
        vel: u8,
        socket_id: Option<u32>,
        source: u8,
    ) -> Option<Vec<PianoRhythmSynthEvent>> {
        return pianorhythm_core::note_on_desktop(channel, key, vel, socket_id, source);
    }

    #[command]
    pub fn note_off(channel: u8, key: u8, socket_id: Option<u32>, source: u8) -> Option<Vec<PianoRhythmSynthEvent>> {
        return pianorhythm_core::note_off_desktop(channel, key, socket_id, source);
    }

    #[command]
    pub fn from_socket_note_off(channel: u8, key: u8, socket_id: Option<u32>) {
        return pianorhythm_core::from_socket_note_off(channel, key, socket_id);
    }

    #[command]
    pub fn from_socket_note_on(data: pianorhythm_core::PianoRhythmWebSocketMidiNoteOn, socket_id: Option<u32>) {
        pianorhythm_core::from_socket_note_on(data, socket_id);
    }

    #[command]
    pub fn clear_program_on_channel(channel: u8, socket_id: Option<u32>) {
        pianorhythm_core::clear_program_on_channel(channel, socket_id);
    }

    #[command]
    pub fn mute_user(socket_id: Option<u32>, value: bool) {
        pianorhythm_core::mute_user(socket_id, value);
    }

    #[command]
    pub fn set_channel_active(channel: u8, value: bool, socket_id: Option<u32>) {
        pianorhythm_core::set_channel_active(channel, value, socket_id)
    }

    #[command]
    pub fn instrument_exists(banknum: u32, prognum: u8) -> bool {
        pianorhythm_core::instrument_exists(banknum, prognum)
    }

    #[command]
    pub fn get_program(_channel: u8, socket_id: Option<u32>) -> Option<PianoRhythmCurrentProgram> {
        pianorhythm_core::get_program(_channel, socket_id)
    }

    #[command]
    pub fn volume_change(channel: u8, value: u8, socket_id: Option<u32>) -> Option<Vec<PianoRhythmSynthEvent>> {
        pianorhythm_core::volume_change(channel, value, socket_id)
    }

    #[command]
    pub fn pan_change(channel: u8, value: u8, socket_id: Option<u32>) -> Option<Vec<PianoRhythmSynthEvent>> {
        pianorhythm_core::pan_change(channel, value, socket_id)
    }

    #[command]
    pub fn damper_pedal(channel: u8, value: u8, socket_id: Option<u32>) {
        pianorhythm_core::damper_pedal(channel, value, socket_id);
    }

    #[command]
    pub fn parse_midi_data(
        window: Window,
        data: Vec<u8>,
        socket_id: Option<u32>,
        source: Option<u8>,
        device_id: Option<u32>,
    ) -> Option<Vec<PianoRhythmSynthEvent>> {
        pianorhythm_core::parse_midi_data(&data.as_slice(), socket_id, source, device_id)
    }

    #[command]
    pub fn get_synth_users() -> Vec<u32> {
        return pianorhythm_core::get_synth_users();
    }

    #[command]
    pub fn from_socket_pitch(data: pianorhythm_core::PianoRhythmWebSocketMidiPitchBend, socket_id: Option<u32>) {
        pianorhythm_core::from_socket_pitch(data, socket_id);
    }

    #[command]
    pub fn synth_get_reverb_level() -> f32 {
        return pianorhythm_core::synth_get_reverb_level();
    }

    #[command]
    pub fn synth_get_reverb_damp() -> f32 {
        return pianorhythm_core::synth_get_reverb_damp();
    }

    #[command]
    pub fn synth_get_reverb_room_size() -> f32 {
        return pianorhythm_core::synth_get_reverb_room_size();
    }

    #[command]
    pub fn synth_get_reverb_width() -> f32 {
        return pianorhythm_core::synth_get_reverb_width();
    }

    #[command]
    pub fn synth_set_reverb_width(value: f32) {
        pianorhythm_core::synth_set_reverb_width(value);
    }

    #[command]
    pub fn synth_set_reverb_damp(value: f32) {
        pianorhythm_core::synth_set_reverb_damp(value);
    }

    #[command]
    pub fn synth_set_reverb_room_size(value: f32) {
        pianorhythm_core::synth_set_reverb_room_size(value);
    }

    #[command]
    pub fn synth_set_reverb_level(value: f32) {
        pianorhythm_core::synth_set_reverb_level(value);
    }

    #[command]
    pub fn get_all_presets_from_sf() -> Vec<Vec<u8>> {
        let presets = pianorhythm_core::get_all_presets_from_sf();
        presets
            .iter()
            .map(|x| x.write_to_bytes().unwrap_or_default())
            .collect::<_>()
    }

    fn load_soundfont_synth(window: &Window, file_name: &str, data: &[u8]) -> Result<(), String> {
        if (SOUNDFONT_LOADING.load(Ordering::SeqCst)) {
            return Err(format!("Soundfont already loading. File: {}", file_name));
        }

        SOUNDFONT_LOADING.store(true, Ordering::SeqCst);
        log::debug!("Loading soundfont: {}", &file_name);
        window.emit("soundfont_load_start", ());

        return match pianorhythm_core::load_soundfont(data) {
            Ok(()) => {
                pianorhythm_core::on_soundfont_loaded_successfully();
                log::info!("Soundfont loaded: {}", &file_name);
                SOUNDFONT_LOADING.store(false, Ordering::SeqCst);
                window.emit("soundfont_load_finish", ());
                Ok(())
            }
            Err(err) => {
                pianorhythm_core::on_soundfont_load_fail(Some(err.to_string()));
                SOUNDFONT_LOADING.store(false, Ordering::SeqCst);
                window.emit("soundfont_load_fail", ());
                Err(err)
            }
        };
    }

    #[command(async)]
    pub async fn load_soundfont_by_url(
        app_handle: tauri::AppHandle,
        window: Window<Wry>,
        resource_path: String,
        save_locally: Option<bool>,
    ) -> Result<(), String> {
        log::info!("Loading Soundfont by url: {:?} | Save locally: {save_locally:?}", resource_path);
        window.emit("soundfont_download_start", ());

        let total_size = {
            let resp = reqwest::get(&resource_path)
                .await
                .or(Err(format!("Failed to get resource size from '{}'", &resource_path)))?;
            log::info!("Content length: {:?}", resp.content_length());

            resp.content_length().unwrap_or(0)
        };

        if total_size > 0 {
            window.emit("soundfont_download_progress", 0 / total_size);
        } else {
            window.emit("soundfont_download_progress", -1);
        }

        log::info!("Soundfont total size: {}", &total_size);

        let client = reqwest::Client::new();

        let request = client
            .get(&resource_path)
            .header(USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0")
            .header(ACCEPT, "application/octet-stream")
            .header("Range", format!("bytes={}-", 0))
            .timeout(Duration::new(60 * 10, 0));

        let mut res = request
            .send()
            .await
            .or(Err(format!("Failed to GET from '{}'", &resource_path)))?;

        let mut downloaded: u64 = 0;
        let mut data = Cursor::new(Vec::new());
        while let Some(chunk) = res
            .chunk()
            .await
            .or(Err(format!("Failed to download chunk from '{}'", &resource_path)))?
        {
            downloaded += chunk.len() as u64;
            data.write_all(&chunk)
                .or(Err(format!("Failed to write chunk for '{}'", &resource_path)))?;

            if total_size > 0 {
                _ = window.emit("soundfont_download_progress", (downloaded as f64 / total_size as f64) * 100.0);

                #[cfg(debug_assertions)]
                log::info!(
                    "Downloaded: {} of {} | {}",
                    downloaded,
                    (downloaded as f64 / total_size as f64) * 100.0,
                    total_size
                );
            } else {
                _ = window.emit("soundfont_download_progress", downloaded);
            }
        }

        if (!res.status().is_success()) {
            return Err(format!(
                "[StatusCode: {}] Failed to get soundfont resource: '{}'",
                res.status(),
                &resource_path
            ));
        }

        window.emit("soundfont_download_complete", ());
        let prefix_err_string = "Failed to save Soundfont locally:";
        let data = data.into_inner();

        if save_locally == Some(true) {
            window.emit("soundfont_save_cache_start", ());

            if let Some(file_name) = Path::new(&resource_path).file_name() {
                if let Ok(data_dir) = app_handle.path().app_data_dir() {
                    let destination = data_dir.as_path().join("soundfonts").join(file_name);
                    if (!destination.exists()) {
                        if let Ok(()) = write(data_dir.as_path().join("soundfonts").join(file_name), &data) {
                            log::info!("Saved Soundfont locally: {:?}", file_name);
                            window.emit("soundfont_save_cache_finish", ());
                        } else {
                            let error_message =
                                format!("{prefix_err_string:?} {:?}. Writing data to location failed.", file_name);
                            log::error!("{}", error_message);
                            window.emit("soundfont_save_cache_fail", error_message);
                        }
                    }
                } else {
                    let error_message =
                        format!("{prefix_err_string:?}  {:?}. Unable to resolve app data directory.", file_name);
                    log::error!("{}", error_message);
                    window.emit("soundfont_save_cache_fail", error_message);
                }
            }
        }

        load_soundfont_synth(&window, &resource_path, &data.as_slice())
    }

    #[command]
    pub async fn load_soundfont_by_path(window: Window, resource_path: String) -> Result<(), String> {
        let path = Path::new(resource_path.as_str());
        log::debug!("Attempting to load Soundfont by path: {:?}", path);

        match std::fs::read(&path) {
            Ok(data) => {
                let data: &[u8] = &data;
                load_soundfont_synth(&window, &resource_path, data)
            }
            Err(err) => {
                SOUNDFONT_LOADING.store(false, Ordering::SeqCst);
                log::error!("Error: {:?}", &err);
                Err(format!("Failed to read soundfont from '{}' | Error: {:?}", &resource_path, &err))
            }
        }
    }

    #[command]
    pub fn set_equalizer_band(idx: usize, curve: i32, frequency: f32, resonance: f32, gain: f32) {
        pianorhythm_core::set_equalizer_band(idx, curve, frequency, resonance, gain)
    }

    #[command]
    pub fn set_equalizer_gain(idx: usize, value: f32) {
        pianorhythm_core::set_equalizer_gain(idx, value)
    }

    #[command]
    pub fn set_equalizer_freq(idx: usize, value: f32) {
        pianorhythm_core::set_equalizer_freq(idx, value)
    }

    #[command]
    pub fn set_equalizer_bypass(idx: usize, value: bool) {
        pianorhythm_core::set_equalizer_bypass(idx, value)
    }

    #[command]
    pub fn set_equalizer_resonance(idx: usize, value: f32) {
        pianorhythm_core::set_equalizer_resonance(idx, value)
    }

    #[command]
    pub fn reset_equalizer() {
        pianorhythm_core::reset_equalizer()
    }

    #[command]
    pub fn note_buffer_engine_set_self_hosted(value: bool) {
        pianorhythm_core::note_buffer_engine_set_self_hosted(value);
    }

    #[command]
    pub fn set_use_default_instrument_when_missing_for_other_users(value: bool) {
        pianorhythm_core::set_use_default_instrument_when_missing_for_other_users(value)
    }

    #[command]
    pub fn bypassall_equalizer(value: bool) {
        pianorhythm_core::bypassall_equalizer(value)
    }

    #[command]
    pub fn set_drum_channel_muted(value: bool) {
        pianorhythm_core::set_drum_channel_muted(value)
    }

    #[command]
    pub fn set_equalizer_enabled(value: bool) {
        pianorhythm_core::set_equalizer_enabled(value)
    }

    #[command]
    pub fn set_midi_output_only(value: bool) {
        pianorhythm_core::set_midi_output_only(value)
    }

    #[command]
    pub fn set_max_note_on_time(value: Option<f64>) {
        pianorhythm_core::set_max_note_on_time(value)
    }

    #[command]
    pub fn set_max_velocity(value: Option<u8>) {
        pianorhythm_core::set_max_velocity(value)
    }

    #[command]
    pub fn set_min_velocity(value: Option<u8>) {
        pianorhythm_core::set_min_velocity(value)
    }

    #[command]
    pub fn get_synth_audio_channels() -> Vec<Vec<u8>> {
        pianorhythm_core::synth_get_audio_channels()
            .iter()
            .map(|x| x.write_to_bytes().unwrap_or_default())
            .collect()
    }

    #[command]
    pub fn get_core_version() -> String {
        pianorhythm_core::get_core_version()
    }

    #[command]
    pub fn get_synth_version() -> String {
        pianorhythm_core::get_synth_version()
    }

    #[command]
    pub fn get_renderer_version() -> String {
        pianorhythm_core::get_renderer_version()
    }

    #[command]
    pub fn set_apply_velocity_curve(value: bool) {
        pianorhythm_core::set_apply_velocity_curve(value);
    }
}
