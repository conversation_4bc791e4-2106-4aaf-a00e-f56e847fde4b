use crate::audio_engine::renderer_cpu_usage::RendererWithCpuUsage;
use cpal::BufferSize;
use kira::backend::{Backend, Renderer};
use rtrb::Consumer;
use std::sync::Mutex;

#[derive(Default)]
enum CustomCpalBackendState {
    #[default]
    Uninitialized,
    Initialized {
        renderer: Mutex<RendererWithCpuUsage>,
    },
}

pub(super) struct CustomCpalBackendSettings {
    pub sample_rate: u32,
    pub num_channels: u32,
    pub buffer_size: BufferSize,
}

impl Default for CustomCpalBackendSettings {
    fn default() -> Self {
        Self {
            sample_rate: 0,
            num_channels: 2,
            buffer_size: BufferSize::Default,
        }
    }
}

pub(super) struct CpalSynthBackend {
    state: CustomCpalBackendState,
    buffer_size: BufferSize,
    sample_rate: u32,
    num_channels: u32,
    cpu_usage_consumer: Option<Mutex<Consumer<f32>>>,
}

impl CpalSynthBackend {
    /**
    Returns the oldest reported CPU usage in the queue.

    The formula for the CPU usage is time elapsed / time allotted, where
    - time elapsed is the amount of time it took to fill the audio buffer
      requested by the OS
    - time allotted is the maximum amount of time Kira could take to process
      audio and still finish in time to avoid audio stuttering (num frames / sample
      rate)
    */
    pub fn pop_cpu_usage(&mut self) -> Option<f32> {
        self.cpu_usage_consumer.as_mut().unwrap().get_mut().unwrap().pop().ok()
    }
}

impl Backend for CpalSynthBackend {
    type Settings = CustomCpalBackendSettings;
    type Error = ();

    fn setup(settings: Self::Settings, internal_buffer_size: usize) -> Result<(Self, u32), Self::Error> {
        log::info!("Using custom cpal backend settings");

        Ok((
            Self {
                state: Default::default(),
                buffer_size: settings.buffer_size,
                sample_rate: settings.sample_rate,
                num_channels: settings.num_channels,
                cpu_usage_consumer: None,
            },
            settings.sample_rate,
        ))
    }

    fn start(&mut self, renderer: Renderer) -> Result<(), Self::Error> {
        if let CustomCpalBackendState::Uninitialized = self.state {
            let (renderer, cpu_usage_consumer) = RendererWithCpuUsage::new(renderer);
            self.state = CustomCpalBackendState::Initialized {
                renderer: Mutex::new(renderer),
            };
            self.cpu_usage_consumer = Some(Mutex::new(cpu_usage_consumer));
        }

        Ok(())
    }
}

impl CpalSynthBackend {
    pub fn on_start_processing(&mut self) {
        if let CustomCpalBackendState::Initialized { renderer } = &mut self.state {
            renderer.get_mut().expect("mutex poisoned").on_start_processing();
        } else {
            panic!("backend is not initialized")
        }
    }

    pub fn process(&mut self, output: &mut [f32]) {
        if let CustomCpalBackendState::Initialized { renderer } = &mut self.state {
            renderer
                .get_mut()
                .expect("mutex poisoned")
                .process(output, self.num_channels as u16, self.sample_rate)
        } else {
            panic!("backend is not initialized")
        }
    }
}
