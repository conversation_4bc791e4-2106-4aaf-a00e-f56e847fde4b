use std::sync::Arc;

use protobuf::{Message, ProtobufEnum};
use tauri::{Emitter, Manager, Window, Wry};

use pianorhythm_core::PianoRhythmSynthEvent;
use pianorhythm_proto::pianorhythm_effects::AppStateEffects;
use pianorhythm_proto::pianorhythm_events::AppStateEvents;

pub fn encode_synth_event(synth_event: &PianoRhythmSynthEvent) -> Vec<u8> {
    let mut buf = [0u8; 300];
    if let Ok(len) = wasm_msgpack::encode::serde::to_array(synth_event, &mut buf) {
        buf[..len].to_vec()
    } else {
        vec![]
    }
}

pub fn emit_app_effect(window: &Arc<Window<Wry>>, effect: AppStateEffects) {
    window
        .emit("app_effects", effect.write_to_bytes().unwrap_or_default())
        .inspect_err(|e| {
            log::error!("[emit_app_effect] app_effects: {}", e);
        })
        .ok();
}

pub fn emit_app_events(window: &Arc<Window<Wry>>, events: Vec<AppStateEvents>) {
    let mapped: Vec<i32> = events.into_iter().map(|x| x.value()).collect::<_>();
    window
        .emit("app_events", mapped)
        .inspect_err(|e| {
            log::error!("[emit_app_event] Error app_events: {}", e);
        })
        .ok();
}

pub fn emit_midi_sequencer_effect(window: &Arc<Window<Wry>>, effect: AppStateEffects) {
    window
        .emit("midi_sequencer_effects", effect.write_to_bytes().unwrap_or_default())
        .inspect_err(|e| {
            log::error!("Error midi_sequencer_effects: {}", e);
        })
        .ok();
}

pub fn emit_midi_message(window: &Window<Wry>, synth_event: &PianoRhythmSynthEvent) {
    window
        .emit("midi_message", encode_synth_event(&synth_event))
        .inspect_err(|e| {
            log::error!("[emit_midi_message] Error sending midi message: {}", e);
        })
        .ok();
}
