use atomic_float::AtomicF64;
use pianorhythm_bevy_renderer::bevy_desktop as bevy;
use pianorhythm_bevy_renderer::bevy_desktop::app::PluginsState;
use pianorhythm_bevy_renderer::bevy_desktop::ecs::system::SystemState;
use pianorhythm_bevy_renderer::bevy_desktop::input::mouse::*;
use pianorhythm_bevy_renderer::bevy_desktop::input::*;
use pianorhythm_bevy_renderer::bevy_desktop::math::DVec2;
use pianorhythm_bevy_renderer::bevy_desktop::renderer::{initialize_renderer, RenderInstance, WgpuWrapper};
use pianorhythm_bevy_renderer::bevy_desktop::settings::{RenderCreation, WgpuSettings};
use pianorhythm_bevy_renderer::bevy_desktop::tasks::tick_global_task_pools_on_main_thread;
use pianorhythm_bevy_renderer::bevy_desktop::*;
use pianorhythm_bevy_renderer::core::events::{AppExtensions, ChannelSender, ECSSynthEventsAction, ECSWorldAction, ECSWorldActions};
use pianorhythm_bevy_renderer::resources::CoreSystems;
use pianorhythm_bevy_renderer::utils::desktop_ffi::{CustomWindowEvents, RendererEvent};
use pianorhythm_proto::pianorhythm_actions::AppStateActions;
use protobuf::Message;
use std::cell::{RefCell, RefMut};
use std::rc::Rc;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use std::thread;
use std::time::{Duration, Instant};
use tauri::async_runtime::block_on;
use tauri::{Manager, RunEvent, WebviewWindow};
use wgpu::{Backends, RequestAdapterOptions};

fn create_window_handle(
    mut commands: Commands, query: Query<(Entity, Option<&'static RawHandleWrapperHolder>)>, tauri_app: NonSend<tauri::AppHandle>,
) {
    let tauri_window = tauri_app.get_webview_window("main").unwrap();
    let window_wrapper = WindowWrapper::new(tauri_window);

    for (entity, handle_holder) in query.iter() {
        if let Ok(handle_wrapper) = RawHandleWrapper::new(&window_wrapper) {
            commands.entity(entity).insert(handle_wrapper.clone());

            if let Some(handle_holder) = handle_holder {
                *handle_holder.0.lock().unwrap() = Some(handle_wrapper);
            }
        }
    }
}

// TODO: Fix issue with mouse position being incorrect on load and after resizing
// TODO: Fix 2d camera not showing transparent background to show 3d scene
pub struct TauriPlugin {
    setup: Box<dyn Fn() -> tauri::App + Send + Sync>,
}

impl TauriPlugin {
    pub fn new<F>(setup: F) -> Self
    where
        F: Fn() -> tauri::App + Send + Sync + 'static,
    {
        Self { setup: Box::new(setup) }
    }
}

impl Plugin for TauriPlugin {
    fn build(&self, app: &mut App) {
        let tauri_app = (self.setup)();

        app.add_systems(Startup, create_window_handle);
        app.insert_non_send_resource(tauri_app.handle().clone());
        app.insert_non_send_resource(tauri_app);

        unsafe {
            if let Some((sender, receiver)) = pianorhythm_bevy_renderer::utils::desktop_ffi::UNIFIED_EVENT_CHANNEL.get() {
                app.add_event_channel::<RendererEvent>(sender.clone(), receiver.clone());
                app.add_systems(First, receiving_event);
                // app.add_systems(Update, handle_click);
            } else {
                ::log::error!("Events channel not found for renderer");
            }
        }

        app.set_runner(run_tauri_app);
    }

    fn finish(&self, _app: &mut App) {
        ::log::info!("{:?} finished.", &self.name());
    }
}

pub static APP_WINDOW_SCALE_FACTOR: AtomicF64 = AtomicF64::new(1.0);

fn receiving_event(
    mut commands: Commands, query: Single<(Entity, &mut Window), With<PrimaryWindow>>, mut event_reader: EventReader<RendererEvent>,
    mut window_event_writer: EventWriter<bevy::window::WindowEvent>, mut window_resized: EventWriter<bevy::window::WindowResized>,
    mut scale_factor_event_writer: EventWriter<bevy::window::WindowScaleFactorChanged>,
    mut backend_scale_factor_event_writer: EventWriter<bevy::window::WindowBackendScaleFactorChanged>,
    mut motion_event_writer: EventWriter<MouseMotion>, mut mouse_button_event_writer: EventWriter<MouseButtonInput>,
    mut mouse_wheel_event_writer: EventWriter<MouseWheel>, mut actions_event_writer: EventWriter<ECSWorldActions>,
    mut synth_actions_event_writer: EventWriter<ECSSynthEventsAction>,
) {
    let (window_entity, mut window) = query.into_inner();

    let mut set_window_scale_factor = |window: &mut Window, scale_factor: f64| {
        window.resolution.set_scale_factor(scale_factor as f32);

        let prior_factor = window.resolution.scale_factor();
        let scale_factor_override = window.resolution.scale_factor_override();

        backend_scale_factor_event_writer.send(WindowBackendScaleFactorChanged {
            window: window_entity.clone(),
            scale_factor: scale_factor as f64,
        });

        if scale_factor_override.is_none() && (scale_factor as f32 != prior_factor) {
            scale_factor_event_writer.send(WindowScaleFactorChanged {
                window: window_entity,
                scale_factor: scale_factor,
            });
        }
    };

    for event in event_reader.read() {
        match event {
            RendererEvent::AppEffect(effect) => {
                actions_event_writer.send(ECSWorldAction::AppEffect(effect.clone()).into());
            }
            RendererEvent::AppAction(action) => {
                actions_event_writer.send(ECSWorldAction::AppAction(action.clone()).into());
            }
            RendererEvent::AppActionRaw(data) => {
                if let Ok(action) = AppStateActions::parse_from_bytes(&data) {
                    actions_event_writer.send(ECSWorldAction::AppAction(action.clone()).into());
                }
            }
            RendererEvent::AppEvent(events) => {
                for event in events {
                    actions_event_writer.send(ECSWorldAction::AppEvent(event.clone()).into());
                }
            }
            RendererEvent::SynthEvent(event) => {
                let raw_bytes = event.raw_bytes.clone();
                if let Some(output) = pianorhythm_bevy_renderer::core::process_synth_event::process(event, &raw_bytes) {
                    synth_actions_event_writer.send(output);
                }
            }
            RendererEvent::WindowEvent(window_event) => {
                match window_event {
                    CustomWindowEvents::MouseMoved(input_position, delta) => {
                        let physical_position = DVec2::new(input_position.x.into(), input_position.y.into());
                        window.set_physical_cursor_position(Some(physical_position));

                        let scale_factor = window.resolution.scale_factor();
                        let position = (physical_position / scale_factor as f64).as_vec2();
                        // ::log::info!("Mouse moved: {:?} | {:?} | {}", window.size(), window.physical_size(), scale_factor);

                        let delta = Vec2::new(delta.0 as f32, delta.1 as f32);
                        window_event_writer.send(bevy::window::WindowEvent::CursorMoved(CursorMoved {
                            window: window_entity,
                            position: position,
                            delta: Some(delta),
                        }));

                        motion_event_writer.send(MouseMotion { delta });
                    }
                    CustomWindowEvents::MouseWheel((x, y)) => {
                        mouse_wheel_event_writer.send(MouseWheel {
                            unit: MouseScrollUnit::Pixel,
                            x: *x as f32,
                            y: *y as f32,
                            window: window_entity,
                        });
                    }
                    CustomWindowEvents::MouseDown((btn, x, y)) => {
                        let physical_position = DVec2::new((*x).into(), (*y).into());
                        //::log::debug!("Mouse down: {:?}", physical_position);
                        //::log::debug!("Window Info: {:?} | {:?} | {}", window.size(), window.physical_size(), window.resolution.scale_factor());
                        // window.set_physical_cursor_position(Some(physical_position));

                        let mouse_event = MouseButtonInput {
                            state: ButtonState::Pressed,
                            button: get_button_from_index(*btn),
                            window: window_entity,
                        };
                        mouse_button_event_writer.send(mouse_event.clone());
                        window_event_writer.send(bevy::window::WindowEvent::MouseButtonInput(mouse_event));
                    }
                    CustomWindowEvents::MouseUp(btn) => {
                        let mouse_event = MouseButtonInput {
                            state: ButtonState::Released,
                            button: get_button_from_index(*btn),
                            window: window_entity,
                        };
                        mouse_button_event_writer.send(mouse_event.clone());
                        window_event_writer.send(bevy::window::WindowEvent::MouseButtonInput(mouse_event));
                    }
                    CustomWindowEvents::ScaleFactorChange(scale_factor) => {
                        APP_WINDOW_SCALE_FACTOR.store(*scale_factor, Ordering::Relaxed);
                        set_window_scale_factor(&mut window, *scale_factor);
                    }
                    CustomWindowEvents::WindowResized((physical_width, physical_height)) => {
                        // ::log::info!("Resizing window {} | {} | {}", physical_width, physical_height, window.scale_factor());
                        // window.resolution.set(*physical_width as f32, *physical_height as f32);
                        // window_resized.send(WindowResized {
                        //     window: window_entity,
                        //     width: *physical_width as f32,
                        //     height: *physical_height as f32,
                        // });
                        // set_window_scale_factor(&mut window, APP_WINDOW_SCALE_FACTOR.load(Ordering::Relaxed));
                    }
                    _ => {}
                }
            }
            _ => {}
        }
    }
}

fn handle_click(
    mouse_button_input: Res<ButtonInput<MouseButton>>, camera: Single<(&Camera, &GlobalTransform)>, windows: Single<&Window>, mut commands: Commands,
) {
    let (camera, camera_transform) = *camera;
    let scale_factor = windows.scale_factor();

    if let Some(pos) = windows
        .cursor_position()
        .and_then(|cursor| camera.viewport_to_world(camera_transform, cursor).ok())
        .map(|ray| ray.origin.truncate())
    {
        if mouse_button_input.just_pressed(MouseButton::Left) {
            ::log::info!("Cursor position: {:?} | {:?}", pos, scale_factor);
        }
    }
}

fn run_tauri_app(app: App) -> AppExit {
    let app = Rc::new(RefCell::new(app));
    let mut tauri_app = app.borrow_mut().world_mut().remove_non_send_resource::<tauri::App>().unwrap();

    let target_frame_duration = Duration::from_secs_f64(1.0 / 60.0); // 60Hz
    let mut frame_count = 0;
    let mut last_second = Instant::now();

    loop {
        let frame_start = Instant::now();

        let app_clone = app.clone();
        tauri_app.run_iteration(move |app_handle, event: RunEvent| {
            handle_tauri_events(app_handle, event, app_clone.borrow_mut());
        });

        if tauri_app.webview_windows().is_empty() {
            ::log::info!("Executing cleanup_before_exit");
            tauri_app.cleanup_before_exit();
            ::log::info!("Executed cleanup_after_exit");
            break;
        }

        let app_clone = app.clone();
        app.borrow_mut().update();

        // let frame_duration = frame_start.elapsed();
        // if frame_duration < target_frame_duration {
        //     std::thread::sleep(target_frame_duration - frame_duration);
        // }

        frame_count += 1;

        if last_second.elapsed() >= Duration::from_secs(1) {
            frame_count = 0;
            last_second = Instant::now();
        }
    }

    AppExit::Success
}

fn handle_tauri_events(app_handle: &tauri::AppHandle, event: RunEvent, mut app: RefMut<'_, App>) {
    if app.plugins_state() != PluginsState::Cleaned {
        if app.plugins_state() != PluginsState::Ready {
            tick_global_task_pools_on_main_thread();
        }
    }

    match event {
        tauri::RunEvent::Ready => handle_ready_event(app_handle, app),
        tauri::RunEvent::ExitRequested { api, .. } => {}
        tauri::RunEvent::WindowEvent { label, event, .. } => handle_window_event(event, app),
        tauri::RunEvent::MainEventsCleared => {}
        _ => (),
    }
}

fn handle_ready_event(app_handle: &tauri::AppHandle, mut app: RefMut<'_, App>) {
    if app.plugins_state() != PluginsState::Cleaned {
        while app.plugins_state() != PluginsState::Ready {
            tick_global_task_pools_on_main_thread();
        }

        app.finish();
        app.cleanup();
    }
}

fn handle_window_event(event: tauri::WindowEvent, app: RefMut<'_, App>) {
    // ::log::info!("handle_window_event {:?}", event);

    match event {
        tauri::WindowEvent::Moved(position) => handle_window_moved(position, app),
        tauri::WindowEvent::Resized(size) => handle_window_resize(size, app),
        tauri::WindowEvent::ScaleFactorChanged {
            scale_factor,
            new_inner_size,
            ..
        } => handle_window_factor_change(scale_factor, new_inner_size, app),
        _ => (),
    }
}

fn handle_window_moved(position: tauri::PhysicalPosition<i32>, mut app: RefMut<'_, App>) {
    // ::log::info!("moved window {:?}", position);

    let mut event_writer_system_state: pianorhythm_bevy_renderer::bevy_desktop::ecs::system::SystemState<(
        EventWriter<WindowMoved>,
        Query<(Entity, &mut Window)>,
    )> = SystemState::new(app.world_mut());

    let (mut window_moved, mut window_query) = event_writer_system_state.get_mut(app.world_mut());

    for (entity, mut window) in window_query.iter_mut() {
        let position = IVec2::new(position.x, position.y);
        window.position = WindowPosition::new(position);
        window_moved.send(WindowMoved { window: entity, position });
    }
}

fn handle_window_resize(size: tauri::PhysicalSize<u32>, mut app: RefMut<'_, App>) {
    // ::log::info!("resized window {:?}", size);

    let mut event_writer_system_state: pianorhythm_bevy_renderer::bevy_desktop::ecs::system::SystemState<(
        EventWriter<WindowResized>,
        Query<(Entity, &mut Window)>,
    )> = SystemState::new(app.world_mut());

    let (mut window_resized, mut window_query) = event_writer_system_state.get_mut(app.world_mut());

    for (entity, mut window) in window_query.iter_mut() {
        window.resolution = WindowResolution::new(size.width as f32, size.height as f32);
        window_resized.send(WindowResized {
            window: entity,
            width: size.width as f32,
            height: size.height as f32,
        });
    }
}

fn handle_window_factor_change(scale_factor: f64, new_inner_size: tauri::PhysicalSize<u32>, mut app: RefMut<'_, App>) {
    // ::log::info!("scale_factor: {} : {:?}", scale_factor, new_inner_size);

    let mut event_writer_system_state: SystemState<(
        EventWriter<WindowResized>,
        EventWriter<WindowScaleFactorChanged>,
        Query<(Entity, &mut Window)>,
    )> = SystemState::new(app.world_mut());

    let (mut window_resized, mut window_scale_factor_changed, mut window_query) = event_writer_system_state.get_mut(app.world_mut());

    for (entity, mut window) in window_query.iter_mut() {
        window.resolution = WindowResolution::new(new_inner_size.width as f32, new_inner_size.height as f32);
        window_scale_factor_changed.send(WindowScaleFactorChanged {
            window: entity,
            scale_factor,
        });
        window_resized.send(WindowResized {
            window: entity,
            width: new_inner_size.width as f32,
            height: new_inner_size.height as f32,
        });
    }
}

fn get_button_from_index(index: u32) -> MouseButton {
    match index {
        0 => MouseButton::Left,
        1 => MouseButton::Middle,
        2 => MouseButton::Right,
        _ => MouseButton::Other(index as u16),
    }
}
