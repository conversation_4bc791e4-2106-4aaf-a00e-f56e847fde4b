{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "main-capability", "description": "Capability for the main window", "windows": ["main"], "remote": {"urls": ["http://**", "https://**", "ws://**", "wss://**"]}, "permissions": ["core:path:default", "core:event:default", "core:event:allow-listen", "core:event:allow-emit", "core:event:allow-unlisten", "core:window:default", "core:app:default", "core:resources:default", "core:menu:default", "core:tray:default", "updater:default", "log:default", "log:allow-log", "http:allow-fetch", "http:allow-fetch-cancel", "http:allow-fetch-read-body", "http:allow-fetch-send", "dialog:allow-open", "dialog:allow-save", "dialog:allow-confirm", "dialog:allow-ask", "fs:default", "fs:read-all", "fs:write-all", "fs:scope-app", "fs:scope-app-index", "fs:scope-app-recursive", "fs:scope-appdata", "fs:scope-applog", "fs:scope-data", "fs:allow-app-read", "fs:allow-app-write", "fs:allow-appdata-read", "fs:allow-appdata-write", "fs:allow-data-read", "fs:allow-data-read-recursive", "fs:allow-data-write", "fs:allow-data-write-recursive", "core:webview:default", "core:webview:allow-create-webview", "core:webview:allow-create-webview-window", "core:webview:allow-internal-toggle-devtools", "updater:default", "updater:allow-check", "updater:allow-download-and-install", {"identifier": "http:default", "allow": [{"url": "http://localhost/*"}, {"url": "http://localhost:7000/*"}, {"url": "http://localhost:8080/*"}, {"url": " https://assets.pianorhythm.io/*"}, {"url": " https://staging.pianorhythm.io/*"}, {"url": " https://staging-app.pianorhythm.io/*"}, {"url": " https://pianorhythm.io/*"}, {"url": " https://app.pianorhythm.io/*"}]}, {"identifier": "fs:scope", "allow": [{"path": "$APPDATA"}, {"path": "$APPDATA/**"}, {"path": "$APPLOCALDATA/logs/**"}, {"path": "$APPDATA/com.pianorhythm.v3/soundfonts/**"}, {"path": "$APPDATA/com.pianorhythm-staging.v3/soundfonts/**"}, {"path": "$APPDATA/com.pianorhythm.v3/logs/**"}, {"path": "$APPDATA/com.pianorhythm-staging.v3/logs/**"}, {"path": "$APPDATA/com.pianorhythm.v3/plugins/**"}, {"path": "$APPDATA/com.pianorhythm-staging.v3/plugins/**"}, {"path": "$APPDATA/com.pianorhythm.v3"}, {"path": "$APPDATA/com.pianorhythm-staging.v3"}, {"path": "$RESOURCE"}, {"path": "$RESOURCE/**"}, {"path": "$APP"}, {"path": "$APP/**"}]}]}