use crate::dependent_module;
use wasm_bindgen::prelude::*;
use wasm_bindgen::JsValue;
use wasm_bindgen_futures::JsFuture;
use web_sys::{AudioContext, AudioWorkletNode, AudioWorkletNodeOptions};
use std::collections::VecDeque;

pub const BUFFER_SIZE: usize = 64;

#[wasm_bindgen]
pub struct WasmAudioProcessor(
    Box<dyn FnMut(&mut [f32], &mut [f32]) -> ()>,
    Box<dyn Fn(&[u8], Option<u32>, Option<u8>, Option<u32>) -> ()>,
    Box<dyn Fn(String, bool) -> ()>,
    Box<dyn Fn(String, bool) -> ()>,
    Box<dyn Fn(&JsValue, Option<u32>) -> ()>, // from_socket_note_on
    Box<dyn Fn(u8, u8, Option<u32>) -> ()>,   // from_socket_note_off
    Box<dyn Fn(&JsValue, Option<u32>) -> ()>, // from_socket_pitch
    Box<dyn Fn(u8, Option<u32>) -> ()>,       // all_sounds_off
);

#[wasm_bindgen]
impl WasmAudioProcessor {
    pub fn process(&mut self, buf: &mut [f32]) -> bool {
        let mut empty_buf: Vec<f32> = vec![];
        self.0(buf, &mut empty_buf[..]);
        true
    }
    
    pub fn parse_midi_data(&mut self, data: &[u8], socket_id: Option<u32>, source: Option<u8>, device_id: Option<u32>) {
        self.1(data, socket_id, source, device_id);
    }

    pub fn process_stereo(&mut self, left_buf: &mut [f32], right_buf: &mut [f32]) -> bool {
        self.0(left_buf, right_buf);
        true
    }

    pub fn handle_midi_input_connection(&mut self, midi_id: String, active: bool) {
        self.2(midi_id, active);
    }

    pub fn handle_midi_output_connection(&mut self, midi_id: String, active: bool) {
        self.3(midi_id, active);
    }

    pub fn from_socket_note_on(&mut self, event: &JsValue, socket_id: Option<u32>) {
        self.4(event, socket_id);
    }

    pub fn from_socket_note_off(&mut self, channel: u8, key: u8, socket_id: Option<u32>) {
        self.5(channel, key, socket_id);
    }

    pub fn from_socket_pitch(&mut self, event: &JsValue, socket_id: Option<u32>) {
        self.6(event, socket_id);
    }

    pub fn all_sounds_off(&mut self, channel: u8, socket_id: Option<u32>) {
        self.7(channel, socket_id);
    }

    pub fn pack(self) -> usize {
        Box::into_raw(Box::new(self)) as usize
    }

    pub fn unpack(val: usize) -> Self {
        unsafe { *Box::from_raw(val as *mut _) }
    }
}

pub fn wasm_audio_node(
    ctx: &AudioContext,
    process: Box<dyn FnMut(&mut [f32], &mut [f32]) -> ()>,
    parse_midi_data: Box<dyn Fn(&[u8], Option<u32>, Option<u8>, Option<u32>) -> ()>,
    handle_midi_input_connection: Box<dyn Fn(String, bool) -> ()>,
    handle_midi_output_connection: Box<dyn Fn(String, bool) -> ()>,
    from_socket_note_on: Box<dyn Fn(&JsValue, Option<u32>) -> ()>,
    from_socket_note_off: Box<dyn Fn(u8, u8, Option<u32>) -> ()>,
    from_socket_pitch: Box<dyn Fn(&JsValue, Option<u32>) -> ()>,
    all_sounds_off: Box<dyn Fn(u8, Option<u32>) -> ()>,
    number_of_channels: Option<u32>, buffer_size: Option<i32>,
) -> Result<AudioWorkletNode, JsValue> {
    // TODO: Uncomment once midi I/O is available in audio worklets
    nop();

    AudioWorkletNode::new_with_options(
        ctx,
        "WasmProcessor",
        &AudioWorkletNodeOptions::new()
            .channel_count(number_of_channels.unwrap_or(2).into())
            .output_channel_count(&JsValue::from(js_sys::Array::of1(&JsValue::from(number_of_channels.unwrap_or(2)))))
            .processor_options(Some(&js_sys::Array::of4(
                &wasm_bindgen::module(),
                &wasm_bindgen::memory(),
                &WasmAudioProcessor(
                    process,
                    parse_midi_data,
                    handle_midi_input_connection,
                    handle_midi_output_connection,
                    from_socket_note_on,
                    from_socket_note_off,
                    from_socket_pitch,
                    all_sounds_off
                ).pack().into(),
                &buffer_size.unwrap_or(128).into(),
            ))),
    )
}

// TextEncoder and TextDecoder are not available in Audio Worklets, but there
// is a dirty workaround: Import polyfill.js to install stub implementations
// of these classes in globalThis.
#[cfg(target_arch = "wasm32")]
#[wasm_bindgen(module = "/src/polyfill.js")]
extern "C" {
    fn nop();
}