use std::sync::Arc;

use convert_case::{Case, Casing};
use midly::{<PERSON><PERSON>, MetaMessage, Track};
use midly::{Format, MidiMessage, Smf, Timing, TrackEvent, TrackEventKind};
use midly::live::LiveEvent;
use rustc_hash::FxHashMap;

use {
    once_cell::sync::Lazy,
    regex::Regex,
};

use crate::{MidiFile, MidiMessageType};

const TICKS_PER_QN: u16 = 96;
const TICKS_PER_QN_32: u32 = TICKS_PER_QN as u32;
const WHOLE_BEAT: u32 = TICKS_PER_QN_32;
const HALF_BEAT: u32 = TICKS_PER_QN_32 / 2;
const QUARTER_TICK: u32 = TICKS_PER_QN_32 / 4;

#[derive(Clone, Debug)]
pub enum DataMessageType {
    Chord(String),
    Sequence(Vec<String>),
    Note(String),
    EmptySpacePause,
    PipePause(usize),
    Bpm(usize),
    Discarded(String),
    TrackName((usize, String)),
    LineEnd,
}

impl DataMessageType {
    pub fn is_discarded(&self) -> bool {
        match self {
            DataMessageType::Discarded(_) => true,
            _ => false
        }
    }

    pub fn is_pause(&self) -> bool {
        match self {
            DataMessageType::PipePause(_) => true,
            DataMessageType::LineEnd => true,
            _ => false
        }
    }
}

#[derive(Default)]
pub struct VPSheetMusicFile {
    pub tracks: Arc<Vec<String>>,
    pub play_loop: bool,
    pub preview_only: bool,
    pub bpm: Option<usize>,
    pub file_name: Option<String>,
}

#[derive(Default)]
pub struct VPSheetMusicSequencer {
    pub file: Option<Arc<VPSheetMusicFile>>,
    key_map: FxHashMap<u8, u8>,
}

impl VPSheetMusicSequencer {
    pub fn new() -> Self {
        Self {
            file: None,
            key_map: vp_key_map(),
            ..Default::default()
        }
    }

    fn process_chunk(input: &str) -> Vec<(Option<String>, DataMessageType)> {
        let mut track: Vec<(Option<String>, DataMessageType)> = vec![];

        match input {
            x if x == " " => {
                track.push((Some(x.to_string()), DataMessageType::EmptySpacePause));
            }
            // Pause for "|" and n number of spaces
            x if x.contains("|") => {
                for char in x.chars(){
                    match char.to_string().as_str() {
                        x if x == "|" => track.push((Some(x.to_string()), DataMessageType::PipePause(1))),
                        x => track.extend(VPSheetMusicSequencer::process_chunk(x)),
                        _ => {}
                    }
                }
            }
            // Chords
            x if x.starts_with('[') && x.ends_with(']') => {
                let body = (x[1..x.len() - 1]).to_string();

                // [a s d f] |  Play the sequence at fastest possible speed
                if body.contains(" ") {
                    for note in body
                        .split("")
                        .map(|x| x.trim().to_string())
                        .filter(|x| !x.is_empty()) {
                        track.push((Some(note.clone()), DataMessageType::Note(note)));
                    }
                } else
                // [asdf] | Play notes together simultaneously
                {
                    track.push((Some(x.to_string()), DataMessageType::Chord(body)));
                }
            }
            // Notes
            x => {
                // Single Notes
                for n in x.split("").filter(|x| !x.is_empty()) {
                    if n == " " {
                        track.push((Some(n.to_string()), DataMessageType::EmptySpacePause));
                    } else {
                        track.push((Some(n.to_string()), DataMessageType::Note(n.trim().to_string())));
                    }
                }
            }
            // Discarded
            x => track.push((None, DataMessageType::Discarded(x.to_string())))
        }

        track
    }

    pub fn parse(input: &VPSheetMusicFile) -> Vec<Vec<(Option<String>, DataMessageType)>> {
        let mut messages = vec![];

        for data in input.tracks.iter() {
            let mut track: Vec<(Option<String>, DataMessageType)> = vec![
                (None, DataMessageType::Bpm(input.bpm.unwrap_or(pianorhythm_shared::midi::DEFAULT_TEMPO as usize)))
            ];

            let Ok(re) = Regex::new(r"(\[.*?])") else { continue; };

            // Source: https://virtualpiano.net/how-to-play/
            for line in data.split("\n") {
                let chunks = split_keep(&re, line);

                for &chunk in chunks.iter() {
                    // println!("Chunk: {}", chunk);
                    track.extend(VPSheetMusicSequencer::process_chunk(chunk));
                }

                track.push((Some("/".to_string()), DataMessageType::LineEnd));
            }

            messages.push(track);
        }

        messages
    }

    fn create_track_events<'a>(messages: &'a Vec<(Option<String>, DataMessageType)>, key_map: &FxHashMap<u8, u8>) -> Vec<TrackEvent<'a>> {
        let mut track_events: Vec<TrackEvent> = vec![];

        let create_track_event = |note: u8, velocity: u8, delta: u32| {
            let kind = if velocity > 0 {
                TrackEventKind::Midi {
                    channel: 0.into(),
                    message: MidiMessage::NoteOn {
                        key: note.into(),
                        vel: velocity.into(),
                    },
                }
            } else {
                TrackEventKind::Midi {
                    channel: 0.into(),
                    message: MidiMessage::NoteOff {
                        key: note.into(),
                        vel: 0.into(),
                    },
                }
            };

            TrackEvent {
                delta: delta.into(),
                kind,
            }
        };

        let mut iter = messages.iter().peekable();

        let mut tick: u32 = 0;

        let mut handle_note_on = |key: &str, tick: u32| {
            if let Some(note) = map_key_to_js(key).and_then(|x| key_map.get(&x).cloned()) {
                return Some(create_track_event(note + (12 * 2), 100, tick));
            }

            None
        };

        let mut handle_note_off = |key: &str, tick: u32| {
            if let Some(note) = map_key_to_js(key).and_then(|x| key_map.get(&x).cloned()) {
                return Some(create_track_event(note + (12 * 2), 0, tick));
            }

            None
        };

        let last_is_note_off = |events: &Vec<TrackEvent>| {
            events.last().map(|x| {
                match x.kind {
                    TrackEventKind::Midi { message, .. } => {
                        match message {
                            MidiMessage::NoteOff { .. } => return true,
                            _ => {}
                        }
                    }
                    _ => {}
                }

                false
            }).unwrap_or_default()
        };

        let mut previous_message_type: Option<&DataMessageType> = None;

        while let Some((raw, message)) = iter.next() {
            let save_lyric = |delta: Option<u32>| {
                if let Some(value) = raw.as_ref() {
                    return Some(TrackEvent {
                        delta: delta.unwrap_or_default().into(),
                        kind: TrackEventKind::Meta(MetaMessage::Lyric(value.as_bytes())),
                    });
                }

                None
            };

            let note_off_tick = |events: &Vec<TrackEvent>| {
                if last_is_note_off(&events) { 0 } else { HALF_BEAT }
            };

            let last_message_was_pause = || {
              previous_message_type.map(|x| x.is_pause()).unwrap_or_default()
            };

            match message {
                DataMessageType::Bpm(bpm) => {
                    let mut target_bpm = bpm.clone();
                    if target_bpm <= 0 { target_bpm = pianorhythm_shared::midi::DEFAULT_TEMPO as usize; }

                    let value = 60_000_000 / target_bpm;
                    track_events.push(TrackEvent {
                        delta: 0.into(),
                        kind: TrackEventKind::Meta(MetaMessage::Tempo((value.clone() as u32).into())),
                    })
                }
                DataMessageType::Chord(value) => {
                    if last_message_was_pause() { tick += QUARTER_TICK; }

                    for key in value.chars() {
                        handle_note_on(key.to_string().as_str(), tick).inspect(|x| track_events.push(*x));
                        tick = 0;
                    }
                    save_lyric(None).inspect(|x| track_events.push(*x));

                    for key in value.chars() {
                        handle_note_off(
                            key.to_string().as_str(),
                            note_off_tick(&track_events),
                        ).inspect(|x| track_events.push(*x));
                    }
                }
                DataMessageType::Note(value) => {
                    if last_message_was_pause() { tick += QUARTER_TICK; }
                    handle_note_on(value, tick).inspect(|x| track_events.push(*x));
                    save_lyric(None).inspect(|x| track_events.push(*x));

                    tick = 0;

                    handle_note_off(
                        value,
                        note_off_tick(&track_events),
                    ).inspect(|x| track_events.push(*x));
                }
                DataMessageType::EmptySpacePause => {
                    tick += QUARTER_TICK;
                    save_lyric(Some(tick)).inspect(|x| track_events.push(*x));
                }
                DataMessageType::PipePause(value) => {
                    let mut pipe_tick = 0;
                    for _ in 0..value.clone() { pipe_tick += QUARTER_TICK; }
                    save_lyric(Some(pipe_tick)).inspect(|x| track_events.push(*x));
                }
                DataMessageType::LineEnd => {
                    save_lyric(None).inspect(|x| track_events.push(*x));
                }
                _ => {}
            }

            previous_message_type = Some(message);
        }

        track_events.push(TrackEvent {
            delta: TICKS_PER_QN_32.into(),
            kind: TrackEventKind::Meta(MetaMessage::EndOfTrack),
        });

        track_events
    }

    fn create_midi(messages: Vec<Vec<(Option<String>, DataMessageType)>>, key_map: &FxHashMap<u8, u8>) -> Vec<u8> {
        let mut smf = Smf {
            header: Header {
                format: Format::Parallel,
                timing: Timing::Metrical(TICKS_PER_QN.into()),
            },
            tracks: messages.iter().map(|track|
                VPSheetMusicSequencer::create_track_events(track, &key_map)
            ).collect(),
        };

        let mut buf: Vec<u8> = Vec::new();
        smf.write(&mut buf).unwrap();
        // smf.save("./vp_seq_test.mid").unwrap();

        buf
    }

    pub fn to_midi(&self, input: &Arc<VPSheetMusicFile>) -> Vec<u8> {
        VPSheetMusicSequencer::create_midi(
            VPSheetMusicSequencer::parse(&input),
            &self.key_map,
        )
    }

    pub fn play(&mut self, input: &Arc<VPSheetMusicFile>) -> Vec<u8> {
        self.file = Some(Arc::clone(&input));
        self.to_midi(input)
    }

    pub fn update_bpm(&mut self, bpm: usize) {
        let mut vp_file = match self.file.as_mut() {
            Some(value) => value,
            None => return,
        };

        if let Some(modified) = Arc::get_mut(&mut vp_file) {
            modified.bpm = Some(bpm);
        } else {
            log::error!("could not get mut");
        };
    }
}

fn vp_key_map() -> FxHashMap<u8, u8> {
    let mut map = FxHashMap::default();
    map.insert(49, 12);
    map.insert(50, 14);
    map.insert(51, 16);
    map.insert(52, 17);
    map.insert(53, 19);
    map.insert(54, 21);
    map.insert(55, 23);
    map.insert(56, 24);
    map.insert(57, 26);
    map.insert(48, 28);
    map.insert(81, 29);
    map.insert(87, 31);
    map.insert(69, 33);
    map.insert(82, 35);
    map.insert(84, 36);
    map.insert(89, 38);
    map.insert(85, 40);
    map.insert(73, 41);
    map.insert(79, 43);
    map.insert(80, 45);
    map.insert(65, 47);
    map.insert(83, 48);
    map.insert(68, 50);
    map.insert(70, 52);
    map.insert(71, 53);
    map.insert(72, 55);
    map.insert(74, 57);
    map.insert(75, 59);
    map.insert(76, 60);
    map.insert(90, 62);
    map.insert(88, 64);
    map.insert(67, 65);
    map.insert(86, 67);
    map.insert(66, 69);
    map.insert(78, 71);
    map.insert(77, 72);
    map
}

fn map_key_to_js(key: &str) -> Option<u8> {
    match key.to_case(Case::Pascal).as_str() {
        "Backspace" => Some(8),
        "Tab" => Some(9),
        "Enter" => Some(13),
        "Shift" => Some(16),
        "Ctrl" => Some(17),
        "Alt" => Some(18),
        "PauseBreak" => Some(19),
        "CapsLock" => Some(20),
        "Escape" => Some(27),
        "Space" | "Spacebar" => Some(32),
        "PageUp" => Some(33),
        "PageDown" => Some(34),
        "End" => Some(35),
        "Home" => Some(36),
        "ArrowLeft" => Some(37),
        "ArrowUp" => Some(38),
        "ArrowRight" => Some(39),
        "ArrowDown" => Some(40),
        "PrintScreen" => Some(44),
        "Insert" => Some(45),
        "Delete" => Some(46),
        "0" | ")" => Some(48),
        "1" | "!" => Some(49),
        "2" | "@" => Some(50),
        "3" | "#" => Some(51),
        "4" | "$" => Some(52),
        "5" | "%" => Some(53),
        "6" | "^" => Some(54),
        "7" | "&" => Some(55),
        "8" | "*" => Some(56),
        "9" | " (" => Some(57),
        "a" | "A" => Some(65),
        "b" | "B" => Some(66),
        "c" | "C" => Some(67),
        "d" | "D" => Some(68),
        "e" | "E" => Some(69),
        "f" | "F" => Some(70),
        "g" | "G" => Some(71),
        "h" | "H" => Some(72),
        "i" | "I" => Some(73),
        "j" | "J" => Some(74),
        "k" | "K" => Some(75),
        "l" | "L" => Some(76),
        "m" | "M" => Some(77),
        "n" | "N" => Some(78),
        "o" | "O" => Some(79),
        "p" | "P" => Some(80),
        "q" | "Q" => Some(81),
        "r" | "R" => Some(82),
        "s" | "S" => Some(83),
        "t" | "T" => Some(84),
        "u" | "U" => Some(85),
        "v" | "V" => Some(86),
        "w" | "W" => Some(87),
        "x" | "X" => Some(88),
        "y" | "Y" => Some(89),
        "z" | "Z" => Some(90),
        "LeftWindowKey" => Some(91),
        "RightWindowKey" => Some(92),
        "SelectKey" => Some(93),
        "Numpad0" => Some(96),
        "Numpad1" => Some(97),
        "Numpad2" => Some(98),
        "Numpad3" => Some(99),
        "Numpad4" => Some(100),
        "Numpad5" => Some(101),
        "Numpad6" => Some(102),
        "Numpad7" => Some(103),
        "Numpad8" => Some(104),
        "Numpad9" => Some(105),
        "Multiply" => Some(106),
        "Add" => Some(107),
        "Subtract" => Some(109),
        "DecimalPoint" => Some(110),
        "Divide" => Some(111),
        "F1" => Some(112),
        "F2" => Some(113),
        "F3" => Some(114),
        "F4" => Some(115),
        "F5" => Some(116),
        "F6" => Some(117),
        "F7" => Some(118),
        "F8" => Some(119),
        "F9" => Some(120),
        "F10" => Some(121),
        "F11" => Some(122),
        "F12" => Some(123),
        "NumLock" => Some(144),
        "ScrollLock" => Some(145),
        "MyComputer" => Some(182),
        "MyCalculator" => Some(183),
        "SemiColon" | ";" => Some(186),
        "EqualSign" | "=" => Some(187),
        "Comma" | "," => Some(188),
        "Dash" | "-" | "Underscore" | "minus" => Some(189),
        "Period" | "." => Some(190),
        "ForwardSlash" | "/" => Some(191),
        "OpenBracket" | "[" | "{" => Some(219),
        "BackSlash" | "/" => Some(220),
        "CloseBracket" | "]" | "}" => Some(221),
        "SingleQuote" | "'" => Some(222),
        _ => None,
    }
}

fn split_keep<'a>(r: &Regex, text: &'a str) -> Vec<&'a str> {
    let mut result = Vec::new();
    let mut last = 0;

    for (index, matched) in text.match_indices(r) {
        if last != index {
            result.push(&text[last..index]);
        }
        result.push(matched);
        last = index + matched.len();
    }

    if last < text.len() {
        result.push(&text[last..]);
    }

    result
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use super::*;

    #[test]
    fn test_parse() {
        let mut sequencer = VPSheetMusicSequencer::new();

        // asgore's theme
        let file = Arc::new(vec![String::from("[6s] [6s] [8f] [8f] [9d]|||
[7a] [7a] [9d] [9d] [8s]|||
[6p] [6p] [8s] [8s] [9a] 8 7 6
[5o] [4o] [5p] [6p] [3u]|||
[6s] [6s] [8f] [8f] [9d]|||
[wh] [wh] [9d] [9d] [0f]| w|
[0f] [9d] [8f] [0h] [0j]| [9l] l
[0k] [9j] [7h] 0 [6j]|||
[2s] s [3f] f [6d]676[6s][8f][6p]9[6p]6[7a]668[6s]9
[6f]6766869[6s]686[7f]797
[8d]898[8s][0f][8p]w[8a]8[9h]880[8d]w
[8f]898808w[8f]8f [7g]7g
[6h]67686[7j]0[6s]676[8p]6[7d]0
[6h]67686[7j]0[6s]676[8l]6[7j]0
[6f]677886600669966
[8g]fd| [2s] s [3f] f
[6d]676[6s][8f][6p]9[6p]6[7a]668[6s]9
[6f]6766869[6s]686[7f]797
[8d]898[8s][0f][8p]w[8a]8[9h]880[8d]w
[8f]898808w[8f]8f [7g]7g
[6h]67686[7j]0[6s]676[8p]6[7d]0
[6h]67686[7j]0[6s]676[8l]6[7j]0
[6f]677886600669966
[8g]fd| [6s]| [5a]|
[6p]sp[5f][6p]|5[6p]a[5s][6h]f7 5
[6p]dp[5f][6p]|[5o][6p]a[5s][6h]f7 5
[7p]dp[8f][7p]|[8o][7p]a[8s][9h]f0 7
[6o] [5f]6 5[6d] [7s] [7a] [7o][5a]|
[6p]sp[5f][6p]|5[6p]a[5s][6h]f7 5
[6p]dp[5f][6p]|[5o][6p]a[5s][6h]f7 5
[7p]dp[8f][7p]|[8o][7p]a[8s][9h]f0 7
[6o] [5f]6 5[6d] s a po|
[6p][6s][5p][5f][6p]655[6p][6a][5s][5h][6f] 7
[6p][6d][5p][5f][6p]65[5o][6p][6a][5s][5h][6f] 7
[8p][8d][7p][7f][8p]87[7o][8p][8a][7s][7h][8f]06
o6p6f|d[7s]7a7po5
[6f][8a][6p][0f][6p]|[5o][6p][7a][8s][wh][0f]|
[6f][9a][6p][0f][6p]|[5o][6p][7a][8s][wh][0f]|
[6f][9a][6p][0f][6p]|[5o][6p][7a][8s][wh][0f]|
[5o] [6p] [0f]|[9d][8s] [7a] [6p][5o]|
[6z]8606|5678w0|
69606|5[6x]78w0|
[6l]9606|5678w0|
5 6 0|98 7 65|
[6p][6s][5p][5f][6p]65[5o][6p][6a][5s][5h][6f] 7
[6p][6d][5p][5f][6p]65[5o][6p][6a][5s][5h][6f] 7
[8p][8d][7p][7f][8p]87[7o][8p][8a][7s][7h][8f]06
o6p6f|d[7s]7a7po5
[6d]6 666 666 6[6p] [8s]
[7d]7 777f7[7h]7g7[7f] [9g]
[8d]8 888 8[9p] [6p][9d] d[8g]
[6f]|6 6|[3d]|[6f]|[8g] [48j]|||
d f [4g] [8j] [60] h g hf|
s [5d] 9 [6q]|
f g j 0 h [8g] hf|l [48j]|||
d f [4g] [8j] [60] h g hf|
s [5d] 9 [6q]|||
p d [0s]|||
[9c]xzlz j [7k]|h|df
[^g]|l8jl [9j]|||
[9c]xzlz j [7k]|h|df
[^g]|f8pf [9d]|||
[9c]xzlz j [7k]|h|df
[^g]|l8jl [9j]|||
[9c]xzlz j [7k]|h|df
[^g]|f8pf [9d]|||
[2c][6x][2z][4l]z[3k][1j][3l][2j]|[6d]|[1l]z
[2c][6x][2z][4l]d[3c][1x][3z][2z]4[3x]5[6c] [6b][7n]
[8m][7n][6b][5v][7b][6v][5b][7n][6b]|[2z]|[2z][3x]
[4c]|[5v]|[^B] [6b] [4c] [2z] [6j]
[2c][6x][2z][4l]z[3k][1j][3l][2j]|[6d]|[1l]z
[2c][6x][2z][4l]d[3c][1x][3z][2z]4[3x]5[6c] [6b][7n]
[8m][7n][6b][5v][7b][6v][5b][7n][6b]|[2z]|[2l][3z]
[2c]34[6x] 4[3l]2[2z]346 4[3l][2z]
[3c]45[7x] 5[4l]3[3z]457 5[4l][3z]
[2c]34[6x] 4[3l]2[2z]346 4[3l][2z]
[3c]45[7x] 5[4l]3[3z]457l5[4z]3
9|6|8 7|5|6
9|6|8 7|5|6 9"
        )]);

        _ = sequencer.to_midi(&Arc::new(VPSheetMusicFile {
            tracks: file,
            bpm: Some(115),
            ..Default::default()
        }));
    }
}

