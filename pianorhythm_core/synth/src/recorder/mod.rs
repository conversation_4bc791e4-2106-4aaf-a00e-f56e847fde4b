use std::cell::Cell;
use std::sync::{Arc, Mutex};
use std::time::Instant;

use midly::{Format, Header, MetaMessage, MidiMessage, Smf, Timing, Track, TrackEvent, TrackEventKind};
use midly::live::LiveEvent;
use midly::num::{u28, u4};
use rustc_hash::FxHashMap;
use oxisynth::MidiEvent;

use pianorhythm_proto::pianorhythm_app_renditions::AppMidiTrack;

use crate::{PianoRhythmSynthEvent, PianoRhythmSynthEventCallback, VPSheetMusicSequencer};

const TICKS_PER_QN: u16 = 960;

pub type MidiRecorderMessageListen = Box<dyn Fn(PianoRhythmSynthEvent, bool) + Send + 'static>;
pub type MidiRecorderTrackUpdateDispatch = Box<dyn Fn(AppMidiTrack) + Send + 'static>;

struct RecordedMessage {
    event: PianoRhythmSynthEvent,
    time_stamp: f64,
}

struct TrackTickCounter {
    #[cfg(not(target_arch = "wasm32"))]
    start_time: Instant,
    #[cfg(target_arch = "wasm32")]
    start_time: instant::Instant,
}

impl TrackTickCounter {
    pub fn new() -> Self {
        #[cfg(not(target_arch = "wasm32"))]
            let start_time = Instant::now();

        #[cfg(target_arch = "wasm32")]
            let start_time = instant::Instant::now();

        Self {
            start_time
        }
    }

    pub fn tick_count(&self) -> usize {
        self.start_time.elapsed().as_millis() as usize
    }

    pub fn reset_start_time(&mut self) {
        #[cfg(not(target_arch = "wasm32"))]
            let start_time = Instant::now();

        #[cfg(target_arch = "wasm32")]
            let start_time = instant::Instant::now();

        self.start_time = start_time;
    }
}

#[derive(Default)]
pub struct MidiRecorder {
    callback: Option<MidiRecorderMessageListen>,
    track_update_dispatch: Option<MidiRecorderTrackUpdateDispatch>,
    tracks: Vec<AppMidiTrack>,
    messages: FxHashMap<u8, Vec<RecordedMessage>>,
    msg_index: FxHashMap<u8, usize>,
    track_tick_counters: FxHashMap<u8, TrackTickCounter>,
    recording_tracks: FxHashMap<u8, bool>,
    playing_tracks: FxHashMap<u8, bool>,
    playing: bool,
    pub auto_trim: bool,
    pub running: bool,
    pub only_record_client: bool
}

impl MidiRecorder {
    pub fn new(
        callback: Option<MidiRecorderMessageListen>,
        track_update_dispatch: Option<MidiRecorderTrackUpdateDispatch>,
        frame_rate: Option<usize>,
    ) -> Self {
        let fr = frame_rate.unwrap_or(60);

        // Create vec of 16 tracks
        let mut tracks = vec![];
        for index in 0..pianorhythm_shared::midi::MAX_CHANNEL + 1 {
            let mut track = AppMidiTrack::default();
            track.set_index(index as u32);
            if index <= 2 { track.set_active(true); }
            track.set_name(format!("Track {}", index));

            if let Some(cb) = &track_update_dispatch {
                cb(track.clone());
            }

            tracks.push(track);
        }

        Self {
            callback,
            track_update_dispatch,
            tracks,
            ..Default::default()
        }
    }

    pub fn run(&mut self) {
        self.running = true;

        for (_, is_playing) in &self.playing_tracks {
            if is_playing.eq(&false) {
                continue;
            }

            for (channel, messages) in &self.messages {
                if self.recording_tracks.get(&channel).copied().unwrap_or_default() {
                    continue;
                }

                if let Some(tick_counter) = self.track_tick_counters.get_mut(&channel) {
                    if let Some(msg_index) = self.msg_index.get_mut(&channel) {
                        while *msg_index < messages.len() {
                            let msg = &messages[*msg_index];
                            if msg.time_stamp <= tick_counter.tick_count() as f64 {
                                if let Some(cb) = &self.callback {
                                    cb(msg.event.clone(), true);
                                }
                                *msg_index += 1;
                            } else {
                                break;
                            }
                        }

                        if *msg_index >= messages.len() {
                            *msg_index = 0;
                            tick_counter.reset_start_time();
                        }
                    }
                }
            }
        }
    }

    pub fn add_synth_message(&mut self, target_channel: u8, synth_event: &PianoRhythmSynthEvent) {
        if let Some(messages) = self.messages.get_mut(&target_channel) {
            if let Some(counter) = self.track_tick_counters.get_mut(&target_channel) {
                let current_tick = counter.tick_count() as f64;
                log::trace!("event: {} | {:?}", current_tick, &synth_event.raw_bytes);
                messages.push(RecordedMessage {
                    event: synth_event.clone(),
                    time_stamp: current_tick,
                })
            }
        }
    }

    pub fn record(&mut self, target_channel: u8, synth_event: &PianoRhythmSynthEvent) {
        if self.only_record_client && !synth_event.is_client {
            return;
        }

        if target_channel > pianorhythm_shared::midi::MAX_CHANNEL {
            return;
        }

        if !self.recording_tracks.get(&target_channel).copied().unwrap_or_default() {
            return;
        }

        if let Some(event) = synth_event.message {
            match event {
                MidiEvent::NoteOn { channel, key, vel } => {
                    self.add_synth_message(target_channel, &synth_event);
                }
                MidiEvent::NoteOff { channel, key } => {
                    self.add_synth_message(target_channel, &synth_event);
                }
                MidiEvent::ControlChange { channel, ctrl, .. }
                if ctrl == pianorhythm_shared::MIDI_CONTROL_BYTES::DAMPER_PEDAL => {
                    self.add_synth_message(target_channel, &synth_event);
                }
                _ => {}
            }
        }
    }

    pub fn start(&mut self, channel: u8) {
        self.messages.insert(channel, vec![]);
        self.msg_index.insert(channel, 0);
        self.track_tick_counters.insert(channel, TrackTickCounter::new());
        self.recording_tracks.insert(channel, true);
        self.playing_tracks.insert(channel, false);

        if let Some(track) = self.tracks.get_mut(channel as usize) {
            track.set_recording(true);
            track.set_playing(false);

            if let Some(cb) = &self.track_update_dispatch {
                cb(track.clone());
            }
        }
    }

    pub fn clear(&mut self) {
        self.messages.clear();
        self.msg_index.clear();
        self.track_tick_counters.clear();
        self.recording_tracks.clear();
        self.playing_tracks.clear();

        for track in self.tracks.iter_mut() {
            track.set_hasData(false);
            track.set_active(track.index <= 2);
        }

        self.dispatch_current_tracks();
    }

    pub fn stop_recording(&mut self, channel: Option<u8>) {
        self.running = false;

        if let Some(ch) = channel {
            self.recording_tracks.insert(ch, false);

            if self.auto_trim {
                self.trim(ch);
            }

            if let Some(messages) = self.messages.get(&ch) {
                if let Some(track) = self.tracks.get_mut(ch as usize) {
                    track.set_hasData(!messages.is_empty());
                    track.set_active(true);
                    track.set_recording(false);

                    if let Some(cb) = &self.track_update_dispatch {
                        cb(track.clone());
                    }
                }
            }
        }
    }

    pub fn dispose(&mut self) {
        self.clear();
    }

    pub fn play_all(&mut self) {
        for track in self.tracks.iter_mut() {
            track.set_playing(track.hasData);
            let channel = track.get_index() as u8;

            if track.hasData {
                self.msg_index.insert(channel, 0);
                self.playing_tracks.insert(channel, true);
                self.track_tick_counters.insert(channel, TrackTickCounter::new());
            } else {
                self.msg_index.remove(&channel);
                self.track_tick_counters.remove(&channel);
                self.playing_tracks.remove(&channel);
            }
        }

        self.dispatch_current_tracks();
    }

    pub fn stop_all(&mut self) {
        self.playing_tracks.clear();
        self.msg_index.clear();
        self.track_tick_counters.clear();

        for track in self.tracks.iter_mut() { track.set_playing(false); }
        self.dispatch_current_tracks();
    }

    pub fn update_track(&mut self, track: &AppMidiTrack) {
        let index = track.get_index() as u8;
        if index > pianorhythm_shared::midi::MAX_CHANNEL {
            return;
        }

        self.tracks[index as usize] = track.clone();

        if !track.active {
            self.clear_track(index);
            return;
        }

        if let Some(cb) = &self.track_update_dispatch {
            cb(track.clone());
        }
    }

    pub fn trim(&mut self, target_channel: u8) {
        // Trim messages so that the first message's time stamp start from 0 and the last message's time stamp ends at the last tick
        // Messages following the first message will be shifted to the left by the first message's time stamp
        for (channel, messages) in &mut self.messages {
            if messages.is_empty() || !channel.eq(&target_channel) {
                continue;
            }

            let first_msg = messages.first().unwrap();
            let first_ts = first_msg.time_stamp;
            let delta = first_ts;

            for msg in messages.iter_mut() {
                msg.time_stamp -= delta;
            }

            self.msg_index.insert(*channel, 0);
        }
    }

    pub fn toggle_track_playing(&mut self, index: u8, is_playing: bool) {
        if let Some(track) = self.tracks.get_mut(index as usize) {
            if !track.active || !track.hasData {
                return;
            }

            track.set_playing(is_playing);
            track.set_recording(false);
        } else {
            return;
        }

        if (is_playing) {
            self.msg_index.insert(index, 0);
            self.track_tick_counters.insert(index, TrackTickCounter::new());
        } else {
            self.msg_index.remove(&index);
            self.track_tick_counters.remove(&index);
        }

        self.playing_tracks.insert(index, is_playing);

        self.dispatch_current_tracks();
    }

    pub fn clear_track(&mut self, index: u8) {
        if let Some(track) = self.tracks.get_mut(index as usize) {
            self.messages.remove(&index);
            self.msg_index.remove(&index);
            self.track_tick_counters.remove(&index);
            self.recording_tracks.remove(&index);
            self.playing_tracks.remove(&index);
            track.set_hasData(false);

            if let Some(cb) = &self.track_update_dispatch {
                cb(track.clone());
            }
        }
    }

    pub fn dispatch_track(&self, track: &AppMidiTrack) {
        if let Some(cb) = &self.track_update_dispatch {
            cb(track.clone());
        }
    }

    pub fn dispatch_current_tracks(&self) {
        for track in self.tracks.iter() {
            self.dispatch_track(&track);
        }
    }
}