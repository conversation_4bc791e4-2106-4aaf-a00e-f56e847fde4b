use std::collections::HashMap;
use std::convert::TryInto;
use std::io::{<PERSON><PERSON><PERSON>, Write};
use std::ops::Add;
use std::panic::{self, UnwindSafe};
use std::sync::Mutex;

use midir::{Ignore, MidiInput, MidiInputConnection, MidiOutput, MidiOutputConnection};
use midly::live::LiveEvent;
use midly::MidiMessage;
use num_traits::ToPrimitive;
use serde::{Deserialize, Serialize};
use oxisynth::{InterpolationMethod, MidiEvent, OxiError, ReverbParams};
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::{
    JsObject, JsValue, prelude::{Closure, wasm_bindgen},
};

use pianorhythm_proto::midi_renditions::MidiNoteSource;

#[cfg(target_arch = "wasm32")]
use crate::wasm_bindgen::*;

#[derive(Clone)]
pub struct CoreSoundfontPreset {
    pub name: String,
    pub bank: u32,
    pub preset: u32,
    pub key_low: u8,
    pub key_high: u8,
}

pub trait ISynthesizer {
    fn instrument_exists(&self, banknum: u32, prognum: u8) -> bool;
    fn set_sample_rate(&mut self, sample_rate: f32);
    fn send_event(&mut self, event: MidiEvent, socket_id: &u32) -> Result<(), OxiError>;
    fn get_reverb_level(&self) -> f32;
    fn get_reverb_room_size(&self) -> f32;
    fn get_reverb_damp(&self) -> f32;
    fn get_reverb_width(&self) -> f32;
    fn set_reverb_params(&mut self, params: &ReverbParams);
    fn set_reverb_room_size(&mut self, roomsize: f32);
    fn set_reverb_level(&mut self, level: f32);
    fn set_reverb_damp(&mut self, damp: f32);
    fn set_reverb_width(&mut self, width: f32);
    fn set_reverb_active(&mut self, active: bool);
    fn set_chorus_active(&mut self, active: bool);
    fn set_max_note_on_time(&mut self, value: Option<f64>);
    fn read_next(&mut self) -> (f32, f32);
    fn load_soundfont(&mut self, buf: &[u8]) -> Result<(), String>;
    fn get_all_presets_from_sf_raw(&self) -> Vec<CoreSoundfontPreset>;
    fn is_channel_active(&self, channel: u8, socket_id: &u32) -> bool;
    fn synth_set_polyphony(&mut self, value: u16);
    fn synth_set_gain(&mut self, value: f32);
    fn clear_program_on_channel(&mut self, channel: u8, socket_id: &u32);
    fn get_cc_value(&self, channel: u8, ctrl: u16, socket_id: &u32) -> Option<u8>;
    fn bank_select_with_channel(&mut self, channel: u8, bank: u32, id: &u32) -> Result<(), String>;
    fn get_program(&self, channel: u8, socket_id: &u32) -> Option<(u32, u8)>;
    fn has_soundfont_loaded(&self) -> bool;
    fn get_default_instrument(&self) -> Option<&CoreSoundfontPreset>;
    /// Returns true if the user was successfully added.
    fn add_socket(&mut self, id: &u32, is_client: bool) -> bool;
    fn has_socket_id(&self, id: &u32) -> bool;
    fn remove_socket(&mut self, id: &u32);
    fn mute_socket(&mut self, id: &u32, value: bool);
    fn reset_synth_players(&mut self);
    fn set_socket_user_gain(&mut self, socket_id: &u32, value: f32);
    fn clear_sockets(&mut self);
    fn get_user_ids(&self) -> Vec<u32>;
    fn set_channel_active(&mut self, channel: u8, value: bool, socket_id: &u32);
    fn is_socket_muted(&self, socket_id: &u32) -> bool;
    fn channel_has_preset_loaded(&self, channel: u8, socket_id: &u32) -> bool;
    fn dispose(&mut self);
    fn reset_voices(&mut self);
    fn set_user_interp_method(&mut self, chan: Option<usize>, socket_id: &u32, interp_method: InterpolationMethod);
    fn update_synth_players_interpolation_method(&mut self, interp_method: InterpolationMethod);
    fn get_audio_channel(&self, channel: u8, socket_id: &u32) -> Option<pianorhythm_proto::midi_renditions::AudioChannel>;
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
#[derive(Default, Debug, Copy, Clone, Serialize, Deserialize)]
pub struct PianoRhythmWebSocketMidiNoteOn {
    pub channel: u8,
    pub note: u8,
    pub velocity: u8,
    pub program: Option<u8>,
    pub volume: Option<u8>,
    pub bank: Option<u32>,
    pub expression: Option<u8>,
    pub pan: Option<u8>,
    pub source: Option<u8>,
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
impl PianoRhythmWebSocketMidiNoteOn {
    #[cfg_attr(target_arch = "wasm32", wasm_bindgen(constructor))]
    pub fn new(
        channel: u8,
        note: u8,
        velocity: u8,
        program: Option<u8>,
        volume: Option<u8>,
        bank: Option<u32>,
        expression: Option<u8>,
        pan: Option<u8>,
        source: Option<u8>,
    ) -> PianoRhythmWebSocketMidiNoteOn {
        PianoRhythmWebSocketMidiNoteOn {
            channel,
            note,
            velocity,
            program,
            volume,
            bank,
            expression,
            pan,
            source,
        }
    }
}

#[derive(Debug, Copy, Clone, Serialize, Deserialize)]
pub struct PianoRhythmWebSocketMidiNoteOff {
    pub channel: u8,
    pub note: u8,
}

#[derive(Debug, Copy, Clone, Serialize, Deserialize)]
pub struct PianoRhythmWebSocketMidiSustain {
    pub channel: u8,
    pub value: u8,
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
#[derive(Debug, Copy, Clone, Serialize, Deserialize)]
pub struct PianoRhythmWebSocketMidiPitchBend {
    pub channel: u8,
    pub value: u32,
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
impl PianoRhythmWebSocketMidiPitchBend {
    #[cfg_attr(target_arch = "wasm32", wasm_bindgen(constructor))]
    pub fn new(channel: u8, value: u32) -> Self {
        Self { channel, value }
    }
}


#[derive(Debug, Default, Clone, Copy, Serialize, Deserialize)]
pub struct PianoRhythmWebSocketEmitEvent {
    pub note_on: Option<PianoRhythmWebSocketMidiNoteOn>,
    pub note_off: Option<PianoRhythmWebSocketMidiNoteOff>,
    pub sustain: Option<PianoRhythmWebSocketMidiSustain>,
    pub pitch_bend: Option<PianoRhythmWebSocketMidiPitchBend>,
    pub all_sound_off: Option<u8>,
    pub all_notes_off: Option<u8>,
    pub system_reset: Option<bool>,
    pub note_source: Option<u8>,
}

#[derive(Default)]
pub struct MidiState {
    pub inputs: Mutex<HashMap<String, MidiInputConnection<()>>>,
    pub outputs: Mutex<HashMap<String, MidiOutputConnection>>,
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
#[derive(Copy, Clone, Debug, Serialize, Deserialize, PartialEq)]
pub enum PianoRhythmSynthesizerBackendSynth {
    OXISYNTH = 1,
    RUSTYSYNTH = 2,
    XSYNTH = 3,
}

impl PianoRhythmSynthesizerBackendSynth {
    pub fn from_i8(value: i8) -> PianoRhythmSynthesizerBackendSynth {
        match value {
            1 => PianoRhythmSynthesizerBackendSynth::OXISYNTH,
            2 => PianoRhythmSynthesizerBackendSynth::RUSTYSYNTH,
            3 => PianoRhythmSynthesizerBackendSynth::XSYNTH,
            _ => PianoRhythmSynthesizerBackendSynth::OXISYNTH,
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
#[derive(Copy, Clone, Debug, Serialize, Deserialize)]
pub struct PianoRhythmSynthesizerDescriptor {
    pub sample_rate: Option<f32>,
    pub buffer_size: Option<f32>,
    pub audio_channels: u8,
    pub backend_synth: PianoRhythmSynthesizerBackendSynth,
}

impl PianoRhythmSynthesizerDescriptor {
    pub const DEFAULT_SAMPLE_RATE: f32 = 44100.0;

    pub fn sanitize(&mut self) -> Self {
        let mut sample_rate = self.sample_rate.unwrap_or(PianoRhythmSynthesizerDescriptor::DEFAULT_SAMPLE_RATE);
        if (sample_rate > 96_000.0) {
            sample_rate = 96_000.0;
        }

        if (sample_rate < 8000.0) {
            sample_rate = 8000.0;
        }

        self.sample_rate = Some(sample_rate);

        self.clone()
    }
}

impl Default for PianoRhythmSynthesizerDescriptor {
    fn default() -> Self {
        Self {
            sample_rate: Some(PianoRhythmSynthesizerDescriptor::DEFAULT_SAMPLE_RATE),
            buffer_size: None,
            audio_channels: 2,
            backend_synth: PianoRhythmSynthesizerBackendSynth::OXISYNTH,
        }
    }
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
impl PianoRhythmSynthesizerDescriptor {
    #[cfg_attr(target_arch = "wasm32", wasm_bindgen(constructor))]
    pub fn new(backend_synth: i8, sample_rate: Option<f32>) -> PianoRhythmSynthesizerDescriptor {
        PianoRhythmSynthesizerDescriptor {
            backend_synth: PianoRhythmSynthesizerBackendSynth::from_i8(backend_synth),
            sample_rate,
            ..PianoRhythmSynthesizerDescriptor::default()
        }
    }
}

#[derive(Clone)]
pub struct SynthInstrumentChannel {
    pub instrument: Option<u8>,
}

impl Default for SynthInstrumentChannel {
    fn default() -> Self {
        Self { instrument: None }
    }
}

#[repr(u8)]
#[derive(Copy, Clone, PartialEq, Debug, Default)]
pub enum NoteSourceType {
    Ignored = 255,
    Keyboard = 1,
    Mouse = 2,
    #[default]
    Midi = 3,
    MidiPlayerPreview = 4,
    MidiPlayer = 5,
}

impl NoteSourceType {
    pub fn from_u8(value: u8) -> Self {
        match value {
            255 => NoteSourceType::Ignored,
            1 => NoteSourceType::Keyboard,
            2 => NoteSourceType::Mouse,
            3 => NoteSourceType::Midi,
            4 => NoteSourceType::MidiPlayerPreview,
            5 => NoteSourceType::MidiPlayer,
            _ => Default::default(),
        }
    }

    pub fn to_u8(&self) -> u8 {
        match self {
            NoteSourceType::Ignored => 255,
            NoteSourceType::Keyboard => 1,
            NoteSourceType::Mouse => 2,
            NoteSourceType::Midi => 3,
            NoteSourceType::MidiPlayerPreview => 4,
            NoteSourceType::MidiPlayer => 5,
        }
    }

    pub fn from_u8_option(value: Option<u8>) -> Self {
        match value {
            Some(v) => NoteSourceType::from_u8(v),
            None => Default::default(),
        }
    }

    pub fn is_mouse(&self) -> bool {
        match self {
            NoteSourceType::Mouse => true,
            _ => false
        }
    }

    pub fn is_keyboard_or_mouse(&self) -> bool {
        match self {
            NoteSourceType::Mouse | NoteSourceType::Keyboard => true,
            _ => false
        }
    }

    pub fn is_preview(&self) -> bool {
        match self {
            NoteSourceType::MidiPlayerPreview => true,
            _ => false
        }
    }

    pub fn from_proto_source(source: MidiNoteSource) -> Self {
        match source {
            MidiNoteSource::KEYBOARD => NoteSourceType::Keyboard,
            MidiNoteSource::MOUSE => NoteSourceType::Mouse,
            MidiNoteSource::MIDI => NoteSourceType::Midi,
            MidiNoteSource::MIDI_PLAYER_PREVIEW => NoteSourceType::MidiPlayerPreview,
            MidiNoteSource::MIDI_PLAYER => NoteSourceType::MidiPlayer,
            _ => NoteSourceType::Ignored,
        }
    }
}

#[derive(Default, Clone, PartialEq, Debug)]
pub struct WebMidiPayload {
    event_name: String,
    channel: u8,
    bytes: Option<[u8; 3]>,
    socket_id: Option<String>,
    device_id: Option<String>,
    note1: Option<u8>,
    note2: Option<u8>,
    program: Option<u8>,
    volume: Option<u8>,
    pitch: Option<u32>,
    express: Option<u8>,
    pan: Option<u8>,
    source: Option<NoteSourceType>,
}

#[cfg_attr(target_arch = "wasm32", wasm_bindgen)]
#[derive(Serialize, Deserialize, Clone, Default, Debug)]
pub struct PianoRhythmCurrentProgram {
    pub channel: u8,
    pub bank: u32,
    pub program: u8,
}

#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct PianoRhythmSynthEvent {
    #[serde(skip_serializing)]
    pub message: Option<MidiEvent>,
    /// Reference to MidiEvent::to_message_type_u8()
    pub message_type: Option<u8>,
    pub channel: u8,
    pub raw_bytes: Vec<u8>,
    pub current_program: Option<u8>,
    pub current_bank: Option<i32>,
    pub current_volume: Option<u8>,
    pub current_pitch: Option<u32>,
    pub current_expression: Option<u8>,
    pub current_pan: Option<u8>,
    pub source: Option<u8>,
    pub device_id: Option<u32>,
    pub socket_id: Option<u32>,
    pub is_client: bool,
    #[serde(skip)]
    pub audio_channel: Option<pianorhythm_proto::midi_renditions::AudioChannel>,
}

impl PianoRhythmSynthEvent {
    pub fn get_note_source(&self) -> NoteSourceType {
        NoteSourceType::from_u8_option(self.source)
    }
}

#[cfg_attr(target_arch = "wasm32", derive(Serialize, Deserialize))]
#[derive(Clone, Default, Debug)]
pub struct PianoRhythmSynthOutputEvent {
    pub message: Option<String>,
    pub synth_event_bytes: Option<Vec<u8>>,
    pub midi_event_bytes: Option<Vec<u8>>,
}

pub trait SplitChecked: Sized {
    fn split_checked(&mut self, at: usize) -> Option<Self>;
}

impl<'a> SplitChecked for &'a [u8] {
    #[inline]
    fn split_checked(&mut self, at: usize) -> Option<&'a [u8]> {
        if at > self.len() {
            None
        } else {
            let (extracted, remainder) = self.split_at(at);
            *self = remainder;
            Some(extracted)
        }
    }
}

#[warn(non_snake_case)]
pub mod piano_rhythm_midi_control_change {
    const LSB_MASK: u8 = 0x20;

    /// Bank select: most significant byte.
    pub const BANK_SELECT_MSB: u8 = 0x00;

    /// Bank select: least significant byte.
    pub const BANK_SELECT_LSB: u8 = BANK_SELECT_MSB | LSB_MASK;

    /// Modulation: most significant byte.
    pub const MODULATION_MSB: u8 = 0x01;

    /// Modulation: least significant byte.
    pub const MODULATION_LSB: u8 = MODULATION_MSB | LSB_MASK;

    /// Breath controller: most significant byte.
    pub const BREATH_CONTROLLER_MSB: u8 = 0x02;

    /// Breach controller: least significant byte.
    pub const BREATH_CONTROLLER_LSB: u8 = BREATH_CONTROLLER_MSB | LSB_MASK;

    /// Foot controller: most significant byte.
    pub const FOOT_CONTROLLER_MSB: u8 = 0x04;

    /// Foot controller: least significant byte.
    pub const FOOT_CONTROLLER_LSB: u8 = FOOT_CONTROLLER_MSB | LSB_MASK;

    /// Portamento: most significant byte.
    pub const PORTAMENTO_TIME_MSB: u8 = 0x05;

    /// Portamento: least significant byte.
    pub const PORTAMENTO_TIME_LSB: u8 = PORTAMENTO_TIME_MSB | LSB_MASK;

    /// Data entry: most significant byte.
    pub const DATA_ENTRY_MSB: u8 = 0x06;

    /// Data entry: least significant byte.
    pub const DATA_ENTRY_LSB: u8 = DATA_ENTRY_MSB | LSB_MASK;

    /// Main volume: most significant byte.
    pub const MAIN_VOLUME_MSB: u8 = 0x07;

    /// Main volume: least significant byte.
    pub const MAIN_VOLUME_LSB: u8 = MAIN_VOLUME_MSB | LSB_MASK;

    /// Balance: most significant byte.
    pub const BALANCE_MSB: u8 = 0x08;

    /// Balance: least significant byte.
    pub const BALANCE_LSB: u8 = BALANCE_MSB | LSB_MASK;

    /// Pan: most significant byte.
    pub const PAN_MSB: u8 = 0x0A;

    /// Pan: least significant byte.
    pub const PAN_LSB: u8 = PAN_MSB | LSB_MASK;

    /// Expression controller: most significant byte.
    pub const EXPRESSION_CONTROLLER_MSB: u8 = 0x0B;

    /// Expression controller: least significant byte.
    pub const EXPRESSION_CONTROLLER_LSB: u8 = EXPRESSION_CONTROLLER_MSB | LSB_MASK;

    /// Effect control 1: most significant byte.
    pub const EFFECT_CONTROL_1_MSB: u8 = 0x0C;

    /// Effect control 1: least significant byte.
    pub const EFFECT_CONTROL_1_LSB: u8 = EFFECT_CONTROL_1_MSB | LSB_MASK;

    /// Effect control 2: most significant byte.
    pub const EFFECT_CONTROL_2_MSB: u8 = 0x0D;

    /// Effect control 2: least significant byte.
    pub const EFFECT_CONTROL_2_LSB: u8 = EFFECT_CONTROL_2_MSB | LSB_MASK;

    /// General purpose controller 1: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_1_MSB: u8 = 0x10;

    /// General purpose controller 1: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_1_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_1_MSB | LSB_MASK;

    /// General purpose controller 2: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_2_MSB: u8 = 0x11;

    /// General purpose controller 2: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_2_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_2_MSB | LSB_MASK;

    /// General purpose controller 3: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_3_MSB: u8 = 0x12;

    /// General purpose controller 3: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_3_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_3_MSB | LSB_MASK;

    /// General purpose controller 4: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_4_MSB: u8 = 0x13;

    /// General purpose controller 4: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_4_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_4_MSB | LSB_MASK;

    /// Damper pedal.
    pub const DAMPER_PEDAL: u8 = 0x40;

    /// Portamento.
    pub const PORTAMENTO: u8 = 0x41;

    /// Sustenuto.
    pub const SUSTENUTO: u8 = 0x42;

    /// Soft pedal.
    pub const SOFT_PEDAL: u8 = 0x43;

    /// Legato footswitch.
    pub const LEGATO_FOOTSWITCH: u8 = 0x44;

    /// Hold 2.
    pub const HOLD_2: u8 = 0x45;

    /// Sound controller 1. Default: Timber variation
    pub const SOUND_CONTROLLER_1: u8 = 0x46;

    /// Sound controller 2. Default: Timber/harmonic content
    pub const SOUND_CONTROLLER_2: u8 = 0x47;

    /// Sound controller 3. Default: Release time
    pub const SOUND_CONTROLLER_3: u8 = 0x48;

    /// Sound controller 4. Default: Attack time
    pub const SOUND_CONTROLLER_4: u8 = 0x49;

    /// Sound controller 5.
    pub const SOUND_CONTROLLER_5: u8 = 0x4A;

    /// Sound controller 6.
    pub const SOUND_CONTROLLER_6: u8 = 0x4B;

    /// Sound controller 7.
    pub const SOUND_CONTROLLER_7: u8 = 0x4C;

    /// Sound controller 8.
    pub const SOUND_CONTROLLER_8: u8 = 0x4D;

    /// Sound controller 9.
    pub const SOUND_CONTROLLER_9: u8 = 0x4E;

    /// Sound controller 10.
    pub const SOUND_CONTROLLER_10: u8 = 0x4F;

    /// General purpose controller 5: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_5_MSB: u8 = 0x50;

    /// General purpose controller 6: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_6_MSB: u8 = 0x51;

    /// General purpose controller 7: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_7_MSB: u8 = 0x52;

    /// General purpose controller 8: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_8_MSB: u8 = 0x53;

    /// Portamento.
    pub const PORTAMENTO_CONTROL: u8 = 0x54;

    /// Effects depth 1. Formerly "External Effects Depth"
    pub const EFFECTS_1_DEPTH: u8 = 0x5B;

    /// Effects depth 2. Formerly "Tremolo Depth"
    pub const EFFECTS_2_DEPTH: u8 = 0x5C;

    /// Effects depth 3. Formerly "Chorus Depth"
    pub const EFFECTS_3_DEPTH: u8 = 0x5D;

    /// Effects depth 4. Formerly "Celeste Detune"
    pub const EFFECTS_4_DEPTH: u8 = 0x5E;

    /// Effects depth 5. Formerly "Phaser Depth"
    pub const EFFECTS_5_DEPTH: u8 = 0x5F;

    /// Non-registered parameter number: least significant byte.
    pub const NON_REGISTERED_PARAMETER_NUMBER_LSB: u8 = 0x62;

    /// Non-registered parameter number: most significant byte.
    pub const NON_REGISTERED_PARAMETER_NUMBER_MSB: u8 = 0x63;

    /// Registered parameter number: least significant byte.
    pub const REGISTERED_PARAMETER_NUMBER_LSB: u8 = 0x64;

    /// Registered parameter number: most significant byte.
    pub const REGISTERED_PARAMETER_NUMBER_MSB: u8 = 0x65;

    /// Mode message: all sound off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`
    pub const ALL_SOUND_OFF: u8 = 0x78;

    /// Mode message: reset all controllers.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`
    pub const RESET_ALL_CONTROLLERS: u8 = 0x79;

    /// Mode message: local control.
    ///
    /// When local control is on (default), the device responds to its local controls.
    /// When local control is off, it only responds to data recieved over MIDI.
    ///
    /// See the module [`local_control`] for possible values of the data byte
    /// (the third byte of the event).
    ///
    /// [`local_control`]: ./local_control/index.html
    pub const LOCAL_CONTROL: u8 = 0x7A;

    /// Constants for the data byte (3rd byte) of a local control control change event.
    pub mod local_control {
        /// Local control off: the device only responds to data recieved over MIDI.
        pub const LOCAL_CONTROL_OFF: u8 = 0;
        /// Local control on: the device also responds to local events (keys played, ...).
        pub const LOCAL_CONTROL_ON: u8 = 127;
    }

    /// Mode message: all notes off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    pub const ALL_NOTES_OFF: u8 = 0x7B;

    /// Mode message: omni mode off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    /// # Remark
    /// This message also causes all notes off.
    pub const OMNI_MODE_OFF: u8 = 0x7C;

    /// Mode message: omni mode on.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    /// # Remark
    /// This message also causes all notes off.
    pub const OMNI_MODE_ON: u8 = 0x7D;

    /// Mode message: mono mode on
    ///
    /// For this event, the data byte (the third byte of the event)
    /// indicates the number of channels (omni off) or `0` (omni on).
    /// # Remark
    /// This message also causes all notes off.
    pub const MONO_MODE_ON: u8 = 0x7E;

    /// Poly mode on
    ///
    /// # Remark
    /// This message also causes all notes off.
    pub const POLY_MODE_ON: u8 = 0x7F;
}

pub type WebsocketEmitEventCallback = Box<dyn Fn(PianoRhythmWebSocketEmitEvent) + Send + 'static>;
pub type PianoRhythmSynthEventCallback = Box<dyn Fn(Vec<PianoRhythmSynthEvent>) + Send + 'static>;
pub type PianoRhythmAudioChannelUpdateCallback = Box<dyn Fn(Vec<pianorhythm_proto::midi_renditions::AudioChannel>) + Send + 'static>;
