use std::rc::Rc;
use derivative::Derivative;
use reactive_state::{Reducer, ReducerResult};
use rustc_hash::{FxHashMap, FxHashSet};
use serde::Serialize;
use pianorhythm_proto::midi_renditions::ActiveChannelsMode;
use pianorhythm_proto::pianorhythm_actions::AppStateActions;
use pianorhythm_proto::pianorhythm_effects::AppStateEffects;
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use simple_eq::Equalizer;
use crate::utils::Range;

const TRANSPOSE_OFFSET_RANGE: Range<i8> = Range { min: -14, max: 14 };
const MULTI_MODE_MAX_CHANNEL_RANGE: Range<u8> = Range { min: 1, max: 16 };
const OCTAVE_OFFSET_RANGE: Range<i8> = Range { min: -7, max: 7 };
const MIN_MIDI_NOTE: u8 = 20;
const MAX_MIDI_NOTE: u8 = 108;
const MIN_MIDI_VELOCITY: u8 = 0;
const MAX_MIDI_VELOCITY: u8 = 127;
const SLOT_MODE_SPLIT2_MAX_CHANNEL: u8 = 2;
const SLOT_MODE_SPLIT4_MAX_CHANNEL: u8 = 4;
const SLOT_MODE_SPLIT8_MAX_CHANNEL: u8 = 8;
const SLOT_MODE_MULTI_MAX_CHANNEL: u8 = 3;
const MIDI_CONTROL_CHANGE: u8 = 176;

#[derive(Clone, Debug, PartialEq, Derivative)]
#[derivative(Default)]
pub struct ClientSynthState {
    octave_offset: i8,
    transpose_offset: i8,
    disable_velocity_for_client: bool,
    #[derivative(Default(value = "true"))]
    use_default_instrument_when_missing_for_other_users: bool,
    midi_auto_fill_empty_channels_with_default_instrument: bool,
    equalizer_enabled: bool,
    #[derivative(Default(value = "ActiveChannelsMode::ALL"))]
    slot_mode: ActiveChannelsMode,
    split_keys: Vec<Vec<u8>>,
    #[derivative(Default(value = "0"))]
    primary_channel: u8,
    max_velocity: Option<u8>,
    min_velocity: Option<u8>,
    #[derivative(Default(value = "pianorhythm_shared::midi::SLOT_MODE_MULTI_MAX_CHANNEL"))]
    max_multi_mode_channels: u8,
    #[derivative(Default(value = "pianorhythm_shared::audio::MAX_VELOCITY_USER_PERCENTAGE as u32"))]
    global_user_velocity: u32,
    user_velocity_percentages: FxHashMap<u32, u32>,
    user_volumes: FxHashMap<u32, u32>,
    loaded_banks: Vec<u32>,
    loaded_presets: Vec<u32>,
    drum_channel_muted: bool,
    midi_output_only: bool,
    pub muted_users: FxHashSet<String>,
    pub server_time_offset: f32,
    #[derivative(Default(value = "pianorhythm_shared::audio::MAX_VELOCITY_USER_PERCENTAGE as f32"))]
    pub global_velocity_percentage: f32,
    pub muted_everyone_else: bool,
    pub client_socket_id_hashed: Option<u32>,
    pub initialized: bool,
    pub use_separate_drum_kit: bool,
    pub listen_to_program_changes: bool,
    pub output_own_notes_to_output: bool,
    pub use_default_bank_when_missing: bool,
    pub is_drum_channel_muted: bool,
    pub reverb_enabled: bool,
    pub room_is_self_hosted: bool,
    pub client_is_muted: bool,
}

#[derive(Clone, Default)]
pub struct ClientSynthStateStateReducer;

#[derive(Clone)]
pub enum ClientSynthStateAction {
    SetLoadedBanks(Vec<u32>),
    SetLoadedPresets(Vec<u32>),
    SetUserVolume((u32, u32)),
    SetUserVelocityPercentage((u32, u32)),
    ClearUserVolume,
    ClearUserVelocityPercentage,
}

impl Reducer<ClientSynthState, ClientSynthStateAction, AppStateEvents, AppStateEffects> for ClientSynthStateStateReducer {
    fn reduce(&self, prev_state: &Rc<ClientSynthState>, action: &ClientSynthStateAction) -> ReducerResult<ClientSynthState, AppStateEvents, AppStateEffects> {
        let mut events: Vec<AppStateEvents> = Vec::new();
        let mut effects: Vec<AppStateEffects> = Vec::new();
        let mut new_state = prev_state.clone();

        match action {
            ClientSynthStateAction::SetLoadedBanks(value) => {
                // new_state.loaded_banks = value.to_owned();
            }
            ClientSynthStateAction::SetLoadedPresets(value) => {
                // new_state.loaded_presets = value.to_owned();
            }
            _ => {}
        }
        ReducerResult {
            state: new_state,
            events,
            effects,
        }
    }
}