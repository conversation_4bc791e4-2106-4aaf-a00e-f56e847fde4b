pub fn set_panic_hook() {
    // When the `console_error_panic_hook` feature is enabled, we can call the
    // `set_panic_hook` function at least once during initialization, and then
    // we will get better error messages if our code ever panics.
    //
    // For more details see
    // https://github.com/rustwasm/console_error_panic_hook#readme
    #[cfg(target_arch = "wasm32")]
    #[cfg(feature = "console_error_panic_hook")]
    console_error_panic_hook::set_once();
}

pub struct Range<T> {
  pub min: T,
  pub max: T,
}

#[derive(Debug)]
pub enum RangeError<T> {
  ToBig { got: T, max: T },
  ToSmall { got: T, min: T },
}

impl<T: PartialOrd + Copy> Range<T> {
  pub fn check(&self, v: T) -> Result<T, RangeError<T>> {
      if v < self.min {
          Err(RangeError::ToSmall {
              got: v,
              min: self.min,
          })
      } else if v > self.max {
          Err(RangeError::ToBig {
              got: v,
              max: self.max,
          })
      } else {
          Ok(v)
      }
  }
}

#[inline]
pub fn map_f32 (value: f32, low1: f32, high1: f32, low2: f32, high2: f32) -> f32 {
  low2 + (value - low1) * (high2 - low2) / (high1 - low1)
}