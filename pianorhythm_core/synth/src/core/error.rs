use super::{SoundFont, TypedIndex};

#[derive(thiserror::<PERSON><PERSON><PERSON>, Debug)]
pub enum OxiError {
    #[error("Generic Error")]
    GenericError,
    #[error("Key out of range (0-127)")]
    KeyOutOfRange,
    #[error("Velocity out of range (0-127)")]
    VelocityOutOfRange,
    #[error("Channel out of range")]
    ChannelOutOfRange,
    #[error("Ctrl out of range (0-127)")]
    CtrlOutOfRange,
    #[error("CC Value out of range (0-127)")]
    CCValueOutOfRange,
    #[error("Program out of range")]
    ProgramOutOfRange,
    #[error("Key pressure out of range (0-127)")]
    KeyPressureOutOfRange,
    #[error("Channel pressure out of range (0-127)")]
    ChannelPressureOutOfRange,
    #[error("PithBend ({value}) out of range")]
    PithBendOutOfRange { value: u32 },
    #[error("Channel ({channel}) has no preset")]
    ChannelHasNoPreset { channel: u8 },
    #[error("Channel ({channel}) is not active")]
    ChannelNotActive { channel: u8 },
    #[error(
        "There is no preset with bank number {bank_id} and preset number {preset_id} in SoundFont {sfont_id}"
    )]
    PresetNotFound {
        bank_id: u32,
        preset_id: u8,
        sfont_id: TypedIndex<SoundFont>,
    },
    #[error("Socket User not found")]
    SocketUserNotFound,
    #[error("Channel ({channel}) not found for user {socket_id}")]
    NoChannelFoundForUser { channel: u8, socket_id: u32 },
    #[error("Channel ({channel}) not found")]
    ChannelNotFound { channel: u8 },
    #[error("Note On Error. Channel: ({channel})")]
    NoteOnError { channel: u8 },
    #[error("Midi event was unknown")]
    UnknownEvent,
}
