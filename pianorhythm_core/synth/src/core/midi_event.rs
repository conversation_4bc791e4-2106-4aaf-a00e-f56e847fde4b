use serde::{Deserialize, Serialize};

use pianorhythm_shared::MIDI_CONTROL_BYTES;

use super::{OxiError, utils::RangeCheck};

pub type U7 = u8;
pub type U14 = u16;

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, Serialize, Deserialize, PartialEq)]
pub enum MidiEvent {
    /// Send a noteon message.
    NoteOn {
        channel: u8,
        key: U7,
        vel: U7,
    },
    /// Send a noteoff message.
    NoteOff {
        channel: u8,
        key: U7,
    },
    /// Send a control change message.
    ControlChange {
        channel: u8,
        ctrl: U7,
        value: U7,
    },
    AllNotesOff {
        channel: u8,
    },
    AllSoundOff {
        channel: u8,
    },
    /// Send a pitch bend message.
    PitchBend {
        channel: u8,
        value: U14,
    },
    /// Send a program change message.
    ProgramChange {
        channel: u8,
        program_id: U7,
    },
    /// Set channel pressure
    ChannelPressure {
        channel: u8,
        value: U7,
    },
    /// Set key pressure (aftertouch)
    PolyphonicKeyPressure {
        channel: u8,
        key: U7,
        value: U7,
    },
    SocketUserGainChange {
        value: f32,
    },
    /// Send a reset.
    ///
    /// A reset turns all the notes off and resets the controller values.
    ///
    /// Purpose:
    /// Respond to the MIDI command 'system reset' (0xFF, big red 'panic' button)
    SystemReset,
    SystemResetWithChannel {
        channel: u8,
    },
    Unknown,
}

impl Default for MidiEvent {
    fn default() -> Self {
        MidiEvent::Unknown
    }
}

impl MidiEvent {
    pub fn to_message_type_u8(self) -> u8 {
        match &self {
            MidiEvent::NoteOn { .. } => 1,
            MidiEvent::NoteOff { .. } => 2,
            MidiEvent::ControlChange { .. } => 3,
            MidiEvent::AllNotesOff { .. } => 4,
            MidiEvent::AllSoundOff { .. } => 5,
            MidiEvent::PitchBend { .. } => 6,
            MidiEvent::ProgramChange { .. } => 7,
            MidiEvent::ChannelPressure { .. } => 8,
            MidiEvent::PolyphonicKeyPressure { .. } => 9,
            MidiEvent::SocketUserGainChange { .. } => 10,
            MidiEvent::SystemReset => 11,
            MidiEvent::SystemResetWithChannel { .. } => 12,
            MidiEvent::Unknown => 0,
        }
    }

    pub fn to_message_string(self) -> String {
        match &self {
            MidiEvent::NoteOn { .. } => "NOTE_ON".to_owned(),
            MidiEvent::NoteOff { .. } => "NOTE_OFF".to_owned(),
            MidiEvent::ControlChange {
                channel,
                ctrl,
                value,
            } => {
                let mut control_type = {
                    match *ctrl {
                        MIDI_CONTROL_BYTES::ALL_NOTES_OFF => "ALL_NOTES_OFF",
                        MIDI_CONTROL_BYTES::ALL_SOUND_OFF => "ALL_SOUND_OFF",
                        MIDI_CONTROL_BYTES::OMNI_MODE_OFF => "OMNI_MODE_OFF",
                        MIDI_CONTROL_BYTES::MONO_MODE_ON => "MONO_MODE_ON",
                        MIDI_CONTROL_BYTES::POLY_MODE_ON => "POLY_MODE_ON",
                        MIDI_CONTROL_BYTES::OMNI_MODE_ON => "OMNI_MODE_ON",
                        MIDI_CONTROL_BYTES::MAIN_VOLUME_MSB => "MAIN_VOLUME_MSB",
                        MIDI_CONTROL_BYTES::MAIN_VOLUME_LSB => "MAIN_VOLUME_LSB",
                        MIDI_CONTROL_BYTES::FOOT_CONTROLLER_MSB => "FOOT_CONTROLLER_MSB",
                        MIDI_CONTROL_BYTES::MODULATION_MSB => "MODULATION_MSB",
                        MIDI_CONTROL_BYTES::MODULATION_LSB => "MODULATION_LSB",
                        MIDI_CONTROL_BYTES::BANK_SELECT_MSB => "BANK_SELECT_MSB",
                        MIDI_CONTROL_BYTES::BANK_SELECT_LSB => "BANK_SELECT_LSB",
                        MIDI_CONTROL_BYTES::FOOT_CONTROLLER_LSB => "FOOT_CONTROLLER_LSB",
                        MIDI_CONTROL_BYTES::RESET_ALL_CONTROLLERS => "RESET_ALL_CONTROLLERS",
                        MIDI_CONTROL_BYTES::PAN_MSB => "PAN_MSB",
                        MIDI_CONTROL_BYTES::PAN_LSB => "PAN_LSB",
                        MIDI_CONTROL_BYTES::DAMPER_PEDAL => "DAMPER_PEDAL",
                        MIDI_CONTROL_BYTES::PORTAMENTO => "PORTAMENTO",
                        MIDI_CONTROL_BYTES::SUSTENUTO => "SUSTENUTO",
                        MIDI_CONTROL_BYTES::SOFT_PEDAL => "SOFT_PEDAL",
                        MIDI_CONTROL_BYTES::LEGATO_FOOTSWITCH => "LEGATO_FOOTSWITCH",
                        MIDI_CONTROL_BYTES::HOLD_2 => "HOLD_2",
                        MIDI_CONTROL_BYTES::EFFECTS_1_DEPTH => "EFFECTS_1_DEPTH",
                        MIDI_CONTROL_BYTES::EFFECTS_2_DEPTH => "TREMELO_EFFECT",
                        MIDI_CONTROL_BYTES::EFFECTS_3_DEPTH => "CHORUS_EFFECT",
                        MIDI_CONTROL_BYTES::EFFECTS_4_DEPTH => "CELESTE_EFFECT",
                        MIDI_CONTROL_BYTES::EFFECTS_5_DEPTH => "PHASER_EFFECT",
                        _ => "CONTROL_CHANGE",
                    }
                };
                // return format!("ControlChange - {}", control_type).to_owned();
                return control_type.to_owned();
            }
            MidiEvent::AllNotesOff { .. } => "ALL_NOTES_OFF".to_owned(),
            MidiEvent::AllSoundOff { .. } => "ALL_SOUND_OFF".to_owned(),
            MidiEvent::PitchBend { .. } => "PITCH_BEND".to_owned(),
            MidiEvent::ProgramChange { .. } => "PROGRAM_CHANGE".to_owned(),
            MidiEvent::ChannelPressure { .. } => "CHANNEL_PRESSURE".to_owned(),
            MidiEvent::PolyphonicKeyPressure { .. } => "POLYPHONIC_KEY_PRESSURE".to_owned(),
            MidiEvent::SocketUserGainChange { .. } => "SOCKET_USER_GAIN_CHANGE".to_owned(),
            MidiEvent::SystemReset => "SYSTEM_RESET".to_owned(),
            MidiEvent::SystemResetWithChannel { .. } => "SYSTEM_RESET".to_owned(),
            _ => "UNKNOWN_EVENT".to_owned()
        }
    }

    pub fn check(self) -> Result<Self, OxiError> {
        match &self {
            MidiEvent::NoteOn { key, vel, .. } => {
                RangeCheck::check(0..=127, &key, OxiError::KeyOutOfRange)?;
                RangeCheck::check(0..=127, &vel, OxiError::VelocityOutOfRange)?;
            }
            MidiEvent::NoteOff { key, .. } => {
                RangeCheck::check(0..=127, &key, OxiError::KeyOutOfRange)?;
            }
            MidiEvent::ControlChange { ctrl, value, .. } => {
                RangeCheck::check(0..=127, &ctrl, OxiError::CtrlOutOfRange)?;
                RangeCheck::check(0..=127, &value, OxiError::CCValueOutOfRange)?;
            }
            MidiEvent::AllNotesOff { .. } => {}
            MidiEvent::AllSoundOff { .. } => {}
            MidiEvent::PitchBend { value, .. } => {
                // RangeCheck::check(0..=16383, &value, OxiError::PithBendOutOfRange { value: value.clone() as u32 })?;
                RangeCheck::check(0..=65535, &value, OxiError::PithBendOutOfRange { value: value.clone() as u32 })?;
            }
            MidiEvent::ProgramChange { program_id, .. } => {
                RangeCheck::check(0..=127, &program_id, OxiError::ProgramOutOfRange)?;
            }
            MidiEvent::ChannelPressure { value, .. } => {
                RangeCheck::check(0..=127, &value, OxiError::ChannelPressureOutOfRange)?;
            }
            MidiEvent::PolyphonicKeyPressure { key, value, .. } => {
                RangeCheck::check(0..=127, &key, OxiError::KeyOutOfRange)?;
                RangeCheck::check(0..=127, &value, OxiError::KeyPressureOutOfRange)?;
            }
            _ => {}
        };

        Ok(self)
    }

    pub fn is_program_change(&self) -> bool {
        match self {
            MidiEvent::ProgramChange { .. } => true,
            _ => false
        }
    }

    pub fn is_channel_update_message(&self) -> bool {
        match self {
            MidiEvent::ControlChange { ctrl, .. }
            if *ctrl == MIDI_CONTROL_BYTES::BANK_SELECT_MSB || *ctrl == MIDI_CONTROL_BYTES::BANK_SELECT_LSB
            => true,
            MidiEvent::ControlChange { ctrl, .. }
            if *ctrl == MIDI_CONTROL_BYTES::MAIN_VOLUME_MSB || *ctrl == MIDI_CONTROL_BYTES::MAIN_VOLUME_LSB
            => true,
            MidiEvent::ControlChange { ctrl, .. }
            if *ctrl == MIDI_CONTROL_BYTES::PAN_MSB || *ctrl == MIDI_CONTROL_BYTES::PAN_LSB
            => true,
            MidiEvent::ControlChange { ctrl, .. } if *ctrl == MIDI_CONTROL_BYTES::VOLUME_FINE => true,
            MidiEvent::ControlChange { ctrl, .. } if *ctrl == MIDI_CONTROL_BYTES::EXPRESSION_FINE => true,
            MidiEvent::ControlChange { ctrl, .. } if *ctrl == MIDI_CONTROL_BYTES::REVERB_LEVEL => true,
            MidiEvent::ControlChange { ctrl, .. } if *ctrl == MIDI_CONTROL_BYTES::CHORUS_LEVEL => true,
            MidiEvent::ProgramChange { .. } => true,
            _ => false
        }
    }
}
