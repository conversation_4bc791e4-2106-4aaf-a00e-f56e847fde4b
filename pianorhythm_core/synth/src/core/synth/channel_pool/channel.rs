use std::sync::Arc;
use pianorhythm_proto::midi_renditions::AudioChannel;
use uuid::Uuid;

use super::super::soundfont::{Preset, SoundFont};

use crate::core::settings::DRUM_CHANNEL;
use crate::core::tuning::Tuning;
use crate::core::utils::TypedIndex;
use crate::{GeneratorType, piano_rhythm_midi_control_change};

type MidiControlChange = u32;
const ALL_SOUND_OFF: MidiControlChange = 120;
const RPN_MSB: MidiControlChange = 101;
const RPN_LSB: MidiControlChange = 100;
const NRPN_MSB: MidiControlChange = 99;
const NRPN_LSB: MidiControlChange = 98;
const EFFECTS_DEPTH5: MidiControlChange = 95;
const EFFECTS_DEPTH1: MidiControlChange = 91;
const SOUND_CTRL10: MidiControlChange = 79;
const SOUND_CTRL1: MidiControlChange = 70;
const EXPRESSION_LSB: MidiControlChange = 43;
const PAN_LSB: MidiControlChange = 42;
const VOLUME_LSB: MidiControlChange = 39;
const BANK_SELECT_LSB: MidiControlChange = 32;
const EXPRESSION_MSB: MidiControlChange = 11;
const PAN_MSB: MidiControlChange = 10;
const VOLUME_MSB: MidiControlChange = 7;
const BANK_SELECT_MSB: MidiControlChange = 0;

/* Flags to choose the interpolation method */
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum InterpolationMethod {
    /// No interpolation: Fastest, but questionable audio quality
    None = 0,
    /// Straight-line interpolation: A bit slower, reasonable audio quality
    Linear = 1,
    /// Fourth-order interpolation: Requires 50% of the whole DSP processing time, good quality (default)
    FourthOrder = 4,
    /// Seventh-order interpolation
    SeventhOrder = 7,
}

impl Default for InterpolationMethod {
    fn default() -> Self {
        Self::SeventhOrder
    }
}

#[derive(Clone, Debug)]
pub struct Channel {
    id: usize,
    pub id_guid: String,
    pub user_socket_id: Option<u32>,
    active: bool,
    sfontnum: Option<TypedIndex<SoundFont>>,

    banknum: u32,
    prognum: u8,

    preset: Option<Arc<Preset>>,

    key_pressure: [i8; 128],
    channel_pressure: u8,

    pitch_bend: u16,
    pitch_wheel_sensitivity: u8,

    cc: [u8; 128],
    bank_msb: u8,

    interp_method: InterpolationMethod,
    tuning: Option<Tuning>,

    nrpn_select: i16,
    nrpn_active: i16,

    gen: [f32; 60],
    gen_abs: [i8; 60],

    is_percussion_channel: bool,

    audio_channel: AudioChannel
}

impl Channel {
    pub fn new(id: usize, user_socket_id: Option<u32>, preset: Option<Arc<Preset>>) -> Self {
        let is_percussion_channel = id == DRUM_CHANNEL;
        let mut chan = Self {
            id,
            id_guid: Uuid::new_v4().to_string(),
            user_socket_id,
            is_percussion_channel,
            audio_channel: AudioChannel::new(),
            active: false,
            sfontnum: None,
            banknum: if is_percussion_channel { 128 } else { 0 },
            prognum: 0,

            preset,

            key_pressure: [0; 128],
            channel_pressure: 0,

            pitch_bend: 0,
            pitch_wheel_sensitivity: 0,

            cc: [0; 128],
            bank_msb: 0,

            interp_method: Default::default(),
            tuning: None,

            nrpn_select: 0,
            nrpn_active: 0,

            gen: [0f32; 60],
            gen_abs: [0; 60],
        };
        chan.init_ctrl(0);
        chan
    }

    pub fn init(&mut self, preset: Option<Arc<Preset>>) {
        self.prognum = 0;
        self.banknum = if self.is_percussion_channel { 128 } else { 0 };
        self.sfontnum = None;

        self.preset = preset;
        self.interp_method = Default::default();
        self.tuning = None;
        self.nrpn_select = 0;
        self.nrpn_active = 0;
    }

    pub fn init_ctrl(&mut self, is_all_ctrl_off: i32) {
        self.channel_pressure = 0;
        self.pitch_bend = 0x2000;

        for i in 0..60 {
            self.gen[i as usize] = 0.0;
            self.gen_abs[i as usize] = 0;
        }

        if is_all_ctrl_off != 0 {
            for i in 0..ALL_SOUND_OFF {
                if !(i >= EFFECTS_DEPTH1 && i <= EFFECTS_DEPTH5) {
                    if !(i >= SOUND_CTRL1 && i <= SOUND_CTRL10) {
                        if !(i == BANK_SELECT_MSB
                            || i == BANK_SELECT_LSB
                            || i == VOLUME_MSB
                            || i == VOLUME_LSB
                            || i == PAN_MSB
                            || i == PAN_LSB)
                        {
                            self.cc[i as usize] = 0;
                        }
                    }
                }
            }
        } else {
            for i in 0..128 {
                self.cc[i] = 0;
            }
        }

        for i in 0..128 {
            self.key_pressure[i] = 0;
        }

        self.cc[RPN_LSB as usize] = 127;
        self.cc[RPN_MSB as usize] = 127;
        self.cc[NRPN_LSB as usize] = 127;
        self.cc[NRPN_MSB as usize] = 127;
        self.cc[EXPRESSION_MSB as usize] = 127;
        self.cc[EXPRESSION_LSB as usize] = 127;

        if is_all_ctrl_off == 0 {
            self.pitch_wheel_sensitivity = 2;

            let mut i = SOUND_CTRL1;
            while i <= SOUND_CTRL10 {
                self.cc[i as usize] = 64;
                i += 1
            }

            self.cc[VOLUME_MSB as usize] = pianorhythm_shared::audio::DEFAULT_CHANNEL_VOLUME;
            self.cc[VOLUME_LSB as usize] = 0;
            self.cc[PAN_MSB as usize] = pianorhythm_shared::audio::DEFAULT_PAN;
            self.cc[PAN_LSB as usize] = 0;
        };

        self.update_audio_channel();
    }

    pub fn is_for_user(&self, socket_id: u32) -> bool {
        self.user_socket_id == Some(socket_id)
    }

    fn update_audio_channel_instrument(&mut self) {
        match self.preset() {
            None => self.audio_channel.clear_instrument(),
            Some(preset) => {
                let mut instrument =
                    if self.audio_channel.has_instrument() {
                        self.audio_channel.get_instrument().to_owned()
                    } else {
                        pianorhythm_proto::midi_renditions::Instrument::new()
                    };

                let bank_num = preset.banknum();
                instrument.set_bank(bank_num);
                instrument.set_preset(preset.num());
                let preset_name = preset.name().to_string();
                instrument.set_name(preset_name.clone());
                instrument.set_display_name(preset_name);
                instrument.set_is_drum_kit(bank_num == 120 || bank_num == 128);
                self.audio_channel.set_instrument(instrument);
            }
        }
    }

    pub fn update_audio_channel_after_cc(&mut self) {
        self.audio_channel.set_pan(self.cc(piano_rhythm_midi_control_change::PAN_MSB.into()).into());
        self.audio_channel.set_volume(self.cc(piano_rhythm_midi_control_change::MAIN_VOLUME_MSB.into()).into());
        self.audio_channel.set_expression(self.cc(piano_rhythm_midi_control_change::EXPRESSION_CONTROLLER_MSB.into()).into());
    }

    fn update_audio_channel(&mut self) {
        self.audio_channel.set_channel(self.id() as u32);
        self.update_audio_channel_instrument();
        self.update_audio_channel_after_cc();

        self.audio_channel.set_active(self.active);
        self.audio_channel.set_bank(self.banknum);
        self.audio_channel.set_bank_msb(self.bank_msb.into());
        self.audio_channel.set_preset(self.prognum.into());
        self.audio_channel.set_pitch_bend(self.pitch_bend.into());
        self.audio_channel.set_channel_pressure(self.channel_pressure.into());
        self.audio_channel.set_pitch_wheel_sensitivity(self.pitch_wheel_sensitivity.into());

        // #[cfg(target_arch = "wasm32")]
        // console_log!("Audio Channel Update: {:?}", self.audio_channel);
    }
}

impl Channel {
    pub fn id(&self) -> usize {
        self.id
    }

    pub fn get_audio_channel(&self) -> &AudioChannel {
        &self.audio_channel
    }

    //

    pub fn sfontnum(&self) -> Option<TypedIndex<SoundFont>> {
        self.sfontnum
    }

    pub fn set_sfontnum(&mut self, sfontnum: Option<TypedIndex<SoundFont>>) {
        self.sfontnum = sfontnum;
    }

    //

    pub fn banknum(&self) -> u32 {
        self.banknum
    }

    pub fn set_banknum(&mut self, banknum: u32) {
        self.banknum = banknum;
        self.audio_channel.set_bank(self.banknum);
    }

    pub fn prognum(&self) -> u8 {
        self.prognum
    }

    pub fn set_prognum(&mut self, prognum: u8) {
        self.prognum = prognum;
        self.audio_channel.set_preset(self.prognum.into());
    }

    pub fn preset(&self) -> Option<&Arc<Preset>> {
        self.preset.as_ref()
    }

    pub fn set_preset(&mut self, preset: Option<Arc<Preset>>) {
        self.preset = preset;
        if self.preset.is_none() {
            self.set_active_raw(false);
        }
        self.update_audio_channel_instrument();
    }

    pub fn key_pressure(&self, id: usize) -> i8 {
        self.key_pressure[id]
    }

    pub fn set_key_pressure(&mut self, id: usize, val: i8) {
        self.key_pressure[id] = val;
    }

    //

    pub fn channel_pressure(&self) -> u8 {
        self.channel_pressure
    }

    pub fn set_channel_pressure(&mut self, val: u8) {
        self.channel_pressure = val;
        self.audio_channel.set_channel_pressure(self.channel_pressure.into());
    }

    //

    pub fn pitch_bend(&self) -> u16 {
        self.pitch_bend
    }

    pub fn set_pitch_bend(&mut self, val: u16) {
        self.pitch_bend = val;
        self.audio_channel.set_pitch_bend(self.pitch_bend.into());
    }

    //

    pub fn active(&self) -> bool {
        self.active
    }

    fn set_active_raw(&mut self, val: bool) {
        self.active = val;
        self.audio_channel.set_active(self.active);
    }

    pub fn set_active(&mut self, val: bool) {
        if self.preset.is_none() && val {
            return;
        }

        self.set_active_raw(val);
    }

    //

    pub fn pitch_wheel_sensitivity(&self) -> u8 {
        self.pitch_wheel_sensitivity
    }

    pub fn set_pitch_wheel_sensitivity(&mut self, val: u8) {
        self.pitch_wheel_sensitivity = val;
        self.audio_channel.set_pitch_wheel_sensitivity(self.pitch_wheel_sensitivity.into());
    }

    //

    pub fn cc(&self, id: usize) -> u8 {
        if id < 128 {
            self.cc[id]
        } else {
            0
        }
    }

    pub fn cc_mut(&mut self, id: usize) -> &mut u8 {
        // log::trace!("cc_mut: {}", id);
        &mut self.cc[id]
    }

    //

    pub fn bank_msb(&self) -> u8 {
        self.bank_msb
    }

    pub fn set_bank_msb(&mut self, val: u8) {
        self.bank_msb = val;
        self.audio_channel.set_bank_msb(self.bank_msb.into());
    }

    //

    pub fn interp_method(&self) -> InterpolationMethod {
        self.interp_method
    }

    pub fn set_interp_method(&mut self, new_method: InterpolationMethod) {
        self.interp_method = new_method;
    }

    //

    pub fn tuning(&self) -> Option<&Tuning> {
        self.tuning.as_ref()
    }

    pub fn set_tuning(&mut self, val: Option<Tuning>) {
        self.tuning = val;
    }

    //

    pub fn nrpn_select(&self) -> i16 {
        self.nrpn_select
    }

    pub fn set_nrpn_select(&mut self, value: i16) {
        self.nrpn_select = value;
    }

    //

    pub fn nrpn_active(&self) -> i16 {
        self.nrpn_active
    }

    pub fn set_nrpn_active(&mut self, value: i16) {
        self.nrpn_active = value;
    }

    /// Retrieve the value of a generator. This function returns the value
    /// set by a previous call 'set_gen()' or by an NRPN message.
    ///
    /// Returns the value of the generator.
    pub fn gen(&self, id: GeneratorType) -> f32 {
        self.gen[id as usize]
    }

    pub fn set_gen(&mut self, id: GeneratorType, val: f32) {
        self.gen[id as usize] = val;
    }

    pub fn gen_abs(&self, id: GeneratorType) -> i8 {
        self.gen_abs[id as usize]
    }

    pub fn set_gen_abs(&mut self, id: GeneratorType, val: i8) {
        self.gen_abs[id as usize] = val;
    }
}
