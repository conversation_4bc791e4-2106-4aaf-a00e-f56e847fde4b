use std::collections::HashMap;

use oxisynth::{Chorus, FontBank, InterpolationMethod, MidiEvent, OxiError, PianoRhythmSocketUser, Reverb, ReverbParams, SoundFontId, SynthDescriptor};

use crate::ISynthesizer;

pub struct Synth {
    pub synth_wrapper: oxisynth::Synth,
    pub socket_players: HashMap<u32, oxisynth::PianoRhythmSocketUser>,
}

impl Default for Synth {
    fn default() -> Self {
        Self {
            synth_wrapper: oxisynth::Synth::default(),
            socket_players: HashMap::new(),
        }
    }
}

impl Synth {
    pub fn new(desc: SynthDescriptor) -> Self {
        Synth {
            socket_players: HashMap::new(),
            synth_wrapper: oxisynth::Synth::new(desc).unwrap(),
        }
    }

    /// Returns the number of MIDI channels that the synthesizer uses internally
    pub fn count_midi_channels(&self) -> usize {
        self.synth_wrapper.core.channels.len()
    }

    /// Returns the number of effects channels that the synthesizer uses internally
    pub fn count_effects_channels(&self) -> u8 {
        2
    }

    pub fn count_audio_channels(&self) -> u8 {
        self.synth_wrapper.core.settings.audio_channels
    }

    // Chorus
    pub fn chorus(&self) -> &Chorus {
        &self.synth_wrapper.core.chorus
    }

    pub fn chorus_mut(&mut self) -> &mut Chorus {
        &mut self.synth_wrapper.core.chorus
    }

    pub fn set_chorus_active(&mut self, active: bool) {
        self.synth_wrapper.core.settings.chorus_active = active;
    }
}

// Reverb
impl Synth {
    pub fn get_reverb(&self) -> *const Reverb {
        &self.synth_wrapper.core.reverb
    }

    pub fn get_reverb_mut(&mut self) -> *const Reverb {
        &mut self.synth_wrapper.core.reverb
    }

    pub fn set_reverb_params(&mut self, params: &ReverbParams) {
        let reverb = &mut self.synth_wrapper.core.reverb;
        reverb.set_params(params);
    }

    pub fn set_reverb_room_size(&mut self, roomsize: f32) {
        self.synth_wrapper.core.reverb.set_room_size(roomsize.clamp(0., pianorhythm_shared::audio::MAX_REVERB_ROOMSIZE));
        self.synth_wrapper.core.reverb.update();
    }

    pub fn set_reverb_level(&mut self, level: f32) {
        self.synth_wrapper.core.reverb.set_level(level.clamp(0., pianorhythm_shared::audio::MAX_REVERB_LEVEL));
        self.synth_wrapper.core.reverb.update();
    }

    pub fn set_reverb_damp(&mut self, damp: f32) {
        self.synth_wrapper.core.reverb.set_damp(damp.clamp(0., pianorhythm_shared::audio::MAX_REVERB_DAMP));
        self.synth_wrapper.core.reverb.update();
    }

    pub fn set_reverb_width(&mut self, width: f32) {
        self.synth_wrapper.core.reverb.set_width(width.clamp(0., pianorhythm_shared::audio::MAX_REVERB_WIDTH));
        self.synth_wrapper.core.reverb.update();
    }

    pub fn set_reverb_active(&mut self, active: bool) {
        self.synth_wrapper.core.settings.reverb_active = active;
    }
}

fn reset_synth_players(socket_players: &mut HashMap<u32, PianoRhythmSocketUser>, synth: &mut oxisynth::Synth) {
    for (_socket_id, target) in socket_players.iter_mut() {
        synth.send_event(MidiEvent::SystemReset, target).unwrap();
    }
}

fn update_synth_players_interpolation_method(
    socket_players: &mut HashMap<u32, PianoRhythmSocketUser>, synth: &mut oxisynth::Synth, interp_method: InterpolationMethod,
) {
    for (_socket_id, target) in socket_players.iter_mut() {
        for ch in synth.core.channels.iter_mut() {
            ch.set_interp_method(interp_method);
        }
    }
}

impl Synth {
    pub fn has_socket_id(&self, id: &u32) -> bool {
        return self.socket_players.contains_key(id);
    }

    pub fn add_socket(&mut self, id: &u32, is_client: bool, interp_method: InterpolationMethod) -> bool {
        #[cfg(debug_assertions)]
        log::info!("add_socket: {} | {} | {}", id, is_client, self.has_socket_id(id));

        if self.has_socket_id(id) {
            return false;
        }

        let mut user = PianoRhythmSocketUser {
            is_client,
            socket_id: *id,
            gain: 1.0,
            muted: false,
        };

        self.synth_wrapper.core.channels.add_channels_by_user(
            self.synth_wrapper.core.settings.midi_channels as usize,
            user.socket_id.clone(),
            None,
        );

        // Update interop in channels
        for ch in self.synth_wrapper.core.channels.iter_mut() {
            if !ch.is_for_user(user.socket_id) {
                continue;
            }

            ch.set_interp_method(interp_method);
        }

        self.socket_players.insert(user.socket_id, user);

        // Set the default program
        self.send_event(MidiEvent::ProgramChange { channel: 0, program_id: 0 }, &id).unwrap();

        return true;
    }

    pub fn remove_socket(&mut self, id: &u32) {
        if let Ok(_) = self.send_event(MidiEvent::SystemReset, id) {
            let synth = self.socket_players.remove(id);
            self.synth_wrapper.core.channels.remove_channels_by_user(*id);

            if let Some(target) = synth {
                target.dispose();
            }
        }
    }

    pub fn reset_synth_players(&mut self) {
        reset_synth_players(&mut self.socket_players, &mut self.synth_wrapper);
    }

    pub fn update_synth_players_interpolation_method(&mut self, interp_method: InterpolationMethod) {
        update_synth_players_interpolation_method(&mut self.socket_players, &mut self.synth_wrapper, interp_method);
    }

    pub fn set_socket_user_gain(&mut self, socket_id: &u32, value: f32) {
        if let Some(target) = self.socket_players.get_mut(socket_id) {
            target.set_gain(value)
        }
    }

    pub fn clear_sockets(&mut self) {
        self.socket_players.clear();
    }

    /**
    Set synth sample rate
     */
    pub fn set_sample_rate(&mut self, sample_rate: f32) {
        self.synth_wrapper.set_sample_rate(sample_rate);
    }

    fn send_event(&mut self, event: MidiEvent, socket_id: &u32) -> Result<(), OxiError> {
        if let Some(target_user) = self.socket_players.get_mut(socket_id) {
            return self.synth_wrapper.send_event(event, target_user);
        }

        return Err(OxiError::SocketUserNotFound);
    }

    pub fn font_bank(&self) -> &FontBank {
        &self.synth_wrapper.core.font_bank
    }

    pub fn set_user_interp_method(
        &mut self,
        chan: Option<usize>,
        socket_id: &u32,
        interp_method: InterpolationMethod,
    ) {
        let synth = self.socket_players.get_mut(socket_id);
        if let Some(target) = synth {
            if let Some(chan) = chan {
                let ch = self.synth_wrapper.core.channels.iter_mut().find(|ch| ch.id() == chan && ch.is_for_user(*socket_id));

                if let Some(ch) = ch {
                    ch.set_interp_method(interp_method);
                }
            } else {
                for ch in self.synth_wrapper.core.channels.iter_mut().filter(|ch| ch.is_for_user(*socket_id)) {
                    ch.set_interp_method(interp_method);
                }
            }
        }
    }

    pub fn channel_has_any_preset(&self, chan: u8, socket_id: &u32) -> bool {
        let synth = self.socket_players.get(socket_id);
        if let Some(target) = synth {
            if let Ok(channel) = self.synth_wrapper.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize) {
                // println!("channel_has_any_preset: {:?}", channel.preset().map(|x| x.name()));
                return channel.preset().is_some();
            }
        }

        return false;
    }

    pub fn bank_select_with_channel(
        &mut self,
        chan: u8,
        bank: u32,
        socket_id: &u32,
    ) -> Result<(), OxiError> {
        let user = self.socket_players.get_mut(socket_id);

        if let Some(target) = user {
            let channel = self.synth_wrapper.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize);

            let mut input_channel = if let Ok(channel) = channel {
                channel.clone()
            } else {
                return Err(OxiError::NoChannelFoundForUser {
                    channel: chan,
                    socket_id: socket_id.clone(),
                });
            };

            _ = self.synth_wrapper.bank_select_with_channel(&mut input_channel, bank)?;

            if let Ok(channel) = self.synth_wrapper.core.channels.get_mut_by_user_id_and_channel_id(*socket_id, chan as usize) {
                *channel = input_channel;
            }

            return Ok(());
        }

        return Err(OxiError::SocketUserNotFound);
    }

    /**
    Returns the program, bank, and SoundFont number of the preset on a given channel for a particular user.
     */
    pub fn get_program(
        &self,
        chan: u8,
        socket_id: &u32,
    ) -> Result<(Option<SoundFontId>, u32, u8), OxiError> {
        let synth = self.socket_players.get(socket_id);
        if let Some(target) = synth {
            if let Ok(channel) = self.synth_wrapper.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize) {
                return Ok(oxisynth::core::midi::get_program(channel));
            } else {
                return Err(OxiError::NoChannelFoundForUser {
                    channel: chan,
                    socket_id: socket_id.clone(),
                });
            }
        }

        return Err(OxiError::SocketUserNotFound);
    }

    pub fn clear_program_on_channel(&mut self, chan: u8, socket_id: &u32) -> Result<(), OxiError> {
        let synth = self.socket_players.get_mut(socket_id);
        if let Some(target) = synth {
            return if let Ok(channel) = self.synth_wrapper.core.channels.get_mut_by_user_id_and_channel_id(*socket_id, chan as usize) {
                channel.set_banknum(0);
                channel.set_prognum(0);
                channel.set_preset(None);
                Ok(())
            } else {
                Err(OxiError::NoChannelFoundForUser {
                    channel: chan,
                    socket_id: socket_id.clone(),
                })
            };
        }

        return Err(OxiError::SocketUserNotFound);
    }

    /**
    Returns the program, bank, and SoundFont number of the preset on a given channel.
     */
    pub fn get_cc_by_socket_id(
        &self,
        chan: u8,
        ctrl: u16,
        socket_id: &u32,
    ) -> Result<u8, OxiError> {
        let synth = self.socket_players.get(socket_id);

        if let Some(target) = synth {
            let channel = self.synth_wrapper.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize)?;
            return Ok(oxisynth::core::midi::get_cc(channel, ctrl));
        }

        return Err(OxiError::SocketUserNotFound);
    }

    pub fn is_channel_active(&self, chan: u8, socket_id: &u32) -> bool {
        let synth = self.socket_players.get(socket_id);

        if let Some(target) = synth {
            if let Ok(channel) = self.synth_wrapper.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize) {
                return channel.active();
            }
        }

        return false;
    }
}
