#![allow(dead_code)]

use std::cmp;
use std::ops::Sub;
use std::sync::Arc;

use midly::MetaMessage;

use crate::MidiFileOutput;
use crate::sequencer::MidiFile;
use crate::sequencer::midifile::{Message, MidiMessageType};

pub type MidiFileSequencerMessageListen = Box<dyn Fn(Vec<u8>, bool) + Send + 'static>;
pub type MidiFileSequencerMessageTypeListen = Box<dyn Fn(Message) + Send + 'static>;

const DEFAULT_BLOCK_SIZE: usize = 64;

/// An instance of the MIDI file sequencer.
#[non_exhaustive]
#[derive(Default)]
pub struct MidiFileSequencer {
    callback: Option<MidiFileSequencerMessageListen>,
    callback2: Option<MidiFileSequencerMessageTypeListen>,
    speed: f64,

    muted_channels: Vec<u8>,
    solo_channels: Vec<u8>,

    midi_file: Option<Arc<MidiFile>>,
    play_loop: bool,
    preview_only: bool,
    is_paused: bool,
    pub is_vp_sheet: bool,

    block_wrote: usize,
    buffer_length: usize,

    current_time: f64,
    sustain_active: bool,
    last_tick: f64,
    pub sample_rate: f64,
    msg_index: usize,
    loop_index: usize,
    current_tempo: f64,
    ppq: f64,
    tick_loop: Option<pianorhythm_shared::GameLoop>,
}

impl MidiFileSequencer {
    /// Initializes a new instance of the sequencer.
    ///
    /// # Arguments
    ///
    /// * `synthesizer` - The synthesizer to be handled by the sequencer.
    pub fn new(callback: Option<MidiFileSequencerMessageListen>, callback2: Option<MidiFileSequencerMessageTypeListen>) -> Self {
        Self {
            callback,
            callback2,
            speed: 1.0,
            ..Default::default()
        }
    }

    pub fn update_midi_file(&mut self, midi_file: &Arc<MidiFile>) {
        self.pause();
        self.midi_file = Some(Arc::clone(midi_file));
        self.set_position(self.current_time);
        self.resume();
        log::trace!("Sequencer midi file updated.");
    }

    /// Plays the MIDI file.
    ///
    /// # Arguments
    ///
    /// * `midi_file` - The MIDI file to be played.
    /// * `play_loop` - If `true`, the MIDI file loops after reaching the end.
    pub fn play(&mut self, midi_file: &Arc<MidiFile>, play_loop: bool, preview_only: bool) {
        self.muted_channels = vec![];
        self.solo_channels = vec![];
        self.midi_file = Some(Arc::clone(midi_file));
        self.block_wrote = DEFAULT_BLOCK_SIZE;
        self.play_loop = play_loop;
        self.preview_only = preview_only;
        self.current_time = -1.0;
        self.msg_index = 0;
        self.loop_index = 0;
        self.speed = 1.0;
        self.last_tick = 0.0;
        self.is_paused = false;
        self.sustain_active = false;
        self.buffer_length = (self.sample_rate / DEFAULT_BLOCK_SIZE as f64) as usize;
        self.ppq = midi_file.ppq as f64;

        if let Some(cb) = &self.callback2 {
            let mut file_output = Message::default();
            let mut body = MidiFileOutput::default();

            body.file_name = midi_file.file_name.clone();

            if midi_file.times.len() > 1 {
                body.total_time = self.get_total_time();
            }

            body.lyrics = midi_file.messages.iter()
                .filter(|x| x.message_type == MidiMessageType::LYRIC)
                // .filter_map(|x| x.dataStr.clone().map(|y| (y, x.dataFloat.unwrap_or_default())))
                .filter_map(|x| x.data_str.clone().map(|y| (y, x.index)))
                .enumerate()
                .map(|(idx, x)| format!("pr_{}_idx_{}", x.1, x.0))
                .collect();

            body.track_names = midi_file.messages.iter()
                .filter(|x| x.message_type == MidiMessageType::TRACK_NAME)
                .filter_map(|x| x.data_str.clone())
                .collect();

            body.copyright = midi_file.messages.iter()
                .filter(|x| x.message_type == MidiMessageType::COPYRIGHT_NOTICE)
                .filter_map(|x| x.data_str.clone())
                .collect();

            body.marker_texts = midi_file.messages.iter()
                .filter(|x| x.message_type == MidiMessageType::MARKER_TEXT)
                .filter_map(|x| x.data_str.clone())
                .collect();

            body.texts = midi_file.messages.iter()
                .filter(|x| x.message_type == MidiMessageType::TEXT)
                .filter_map(|x| x.data_str.clone())
                .collect();

            body.program_changes = midi_file.messages.iter()
                .filter(|x| x.is_program_change_command())
                .map(|x| (x.channel, x.data1, x.data2, x.time))
                .collect();

            body.tempo_changes = midi_file.file_messages.iter()
                .filter(|x| x.message_type == MidiMessageType::FILE_TEMPO_CHANGE)
                .map(|x| (x.time, x.get_tempo()))
                .collect();

            body.current_bpm = body.tempo_changes.iter()
                .map(|x| x.1 as i32)
                .collect::<Vec<i32>>()
                .first()
                .cloned()
                .unwrap_or(pianorhythm_shared::midi::DEFAULT_TEMPO.into());

            body.ppq = midi_file.ppq;
            self.current_tempo = body.current_bpm as f64;

            file_output.message_type = MidiMessageType::FILE_OUTPUT;
            file_output.file_output = Some(body);
            cb(file_output);
        }

        self.tick_loop = Some(pianorhythm_shared::GameLoop::new(midi_file.frame_rate as usize, 1).unwrap());
        // self.tick_loop = Some(pianorhythm_shared::GameLoop::new(60 as usize, 1).unwrap());
        log::trace!("Midi sequencer play start. Sample rate: {}", &self.sample_rate);
    }

    /// Stops playing.
    pub fn stop(&mut self) {
        if self.midi_file.is_none() {
            return;
        }

        self.buffer_length = 0;
        self.midi_file = None;
        self.note_off_all();
        if let Some(cb) = &self.callback2 { cb(Message::MessageType(MidiMessageType::STOPPED)) }
        log::trace!("Sequencer stopped.");
    }

    pub fn pause(&mut self) {
        if self.midi_file.is_none() {
            return;
        }

        self.is_paused = true;
        self.note_off_all();
        log::trace!("Sequencer paused.");
    }

    pub fn resume(&mut self) {
        if self.midi_file.is_none() {
            return;
        }

        self.is_paused = false;
        if self.tick_loop.is_some() {
            self.last_tick = self.tick_loop.as_ref().unwrap().tick_count() as f64;
        }
        log::trace!("Sequencer resumed.");
    }

    fn finished(&mut self) {
        self.midi_file = None;
        self.buffer_length = 0;
        self.note_off_all();
        if let Some(cb) = &self.callback2 { cb(Message::MessageType(MidiMessageType::FINISHED)) }
        log::trace!("Sequencer finished.");
    }

    pub fn run(&mut self) {
        if self.is_paused || self.tick_loop.is_none() {
            return;
        }

        let total_time = self.get_total_time();
        let game_loop = self.tick_loop.as_mut().unwrap();

        let mut loop_midi = false;
        let mut midi_finished = false;

        let current_tick = game_loop.tick_count() as f64;
        let elapsed_time = (current_tick - self.last_tick) as f64 / 1_000.0;

        self.current_time += elapsed_time;
        self.last_tick = current_tick;

        for action in game_loop.actions() {
            match action {
                pianorhythm_shared::FrameAction::Tick => {
                    let midi_file = match self.midi_file.as_ref() {
                        Some(value) => value,
                        None => return,
                    };

                    if let Some(cb) = &self.callback2 {
                        self.midi_file.as_ref().map(|_| cb(Message::CurrentTime(self.current_time, 0)));
                    }

                    while self.msg_index < midi_file.messages.len() {
                        if self.is_paused {
                            break;
                        }

                        let msg = &midi_file.messages[self.msg_index];

                        if msg.time <= self.current_time {
                            let message_type = msg.get_message_type();

                            match message_type {
                                MidiMessageType::DATA => 'label: {
                                    if let Some(cb) = &self.callback {
                                        // Check for muted
                                        if self.muted_channels.contains(&msg.channel) {
                                            break 'label;
                                        }

                                        if !self.solo_channels.is_empty() && !self.solo_channels.contains(&msg.channel) {
                                            break 'label;
                                        }

                                        cb(vec![msg.channel, msg.command, msg.data1, msg.data2], self.preview_only);
                                    }
                                }
                                MidiMessageType::TEMPO_CHANGE => {
                                    self.current_tempo = msg.get_tempo();
                                    if let Some(cb) = &self.callback2 { cb(msg.clone()) }
                                }
                                MidiMessageType::MARKER_TEXT |
                                MidiMessageType::TEXT |
                                MidiMessageType::LYRIC if msg.data_str.is_some() => {
                                    if let Some(cb) = &self.callback2 { cb(msg.clone()) }
                                }
                                MidiMessageType::LOOP_START if self.play_loop => {
                                    self.loop_index = self.msg_index;
                                }
                                MidiMessageType::LOOP_END if self.play_loop => {
                                    self.msg_index = self.loop_index;
                                    midi_finished = true;
                                    loop_midi = true;
                                }
                                _ => {}
                            }

                            self.msg_index += 1;
                        } else {
                            break;
                        }
                    }

                    if self.msg_index >= midi_file.messages.len() || self.current_time >= total_time {
                        if self.play_loop {
                            self.msg_index = self.loop_index;
                            loop_midi = true;
                        } else {
                            self.midi_file = None;
                            if let Some(cb) = &self.callback2 { cb(Message::MessageType(MidiMessageType::FINISHED)) }
                            log::trace!("Sequencer finished.");
                        }

                        midi_finished = true;
                    }
                }
                _ => {}
            }
        }

        if loop_midi { game_loop.reset_start_time(); }
        if midi_finished { self.note_off_all(); }
    }

    /// Gets the currently playing MIDI file.
    pub fn get_midi_file(&self) -> Option<&MidiFile> {
        match &self.midi_file {
            None => None,
            Some(value) => Some(value),
        }
    }

    /// Gets the current playback position in seconds.
    pub fn get_position(&self) -> f64 {
        self.current_time
    }

    /// Sets the current playback position in seconds.
    pub fn set_position(&mut self, target: f64) {
        if let Some(midi_file) = self.midi_file.as_ref() {
            if let Some(closest) = midi_file.times.iter().min_by(|x, y| {
                let diff_x = (target - **x).abs();
                let diff_y = (target - **y).abs();
                diff_x.partial_cmp(&diff_y).unwrap()
            }) {
                if let Some(index) = midi_file.times.iter().position(|x| x == closest) {
                    self.note_off_all();
                    self.msg_index = index;
                    self.current_time = closest.clone();
                    if let Some(cb) = &self.callback2 {
                        let mut message = Message::MessageType(MidiMessageType::SEEK_POSITION_CHANGED);
                        message.data_float = Some(self.current_time);
                        cb(message)
                    }
                }
            };
        }
    }

    /// Gets a value that indicates whether the current playback position is at the end of the sequence.
    ///
    /// # Remarks
    ///
    /// If the `play` method has not yet been called, this value will be `true`.
    /// This value will never be `true` if loop playback is enabled.
    pub fn end_of_sequence(&self) -> bool {
        match &self.midi_file {
            None => true,
            Some(value) if !self.play_loop => self.msg_index >= value.messages.len(),
            _ => false,
        }
    }

    /// Gets the current playback speed.
    ///
    /// # Remarks
    ///
    /// The default value is 1.
    /// The tempo will be multiplied by this value during playback.
    pub fn get_speed(&self) -> f64 {
        self.speed
    }

    /// Sets the playback speed.
    ///
    /// # Remarks
    ///
    /// The value must be non-negative.
    pub fn set_speed(&mut self, value: f64) {
        if value < 0.0 {
            panic!("The playback speed must be a non-negative value.");
        }

        self.speed = value;
    }

    pub fn get_total_time(&self) -> f64 {
        match &self.midi_file {
            Some(midi_file) => midi_file.times[midi_file.times.len() - 1],
            _ => 0.,
        }
    }

    pub fn set_channel_muted(&mut self, channel: u8, value: bool) {
        if value {
            self.muted_channels.push(channel);
        } else {
            self.muted_channels.retain(|x| !x.eq(&channel));
        }

        self.muted_channels.sort();
        self.muted_channels.dedup();
        self.note_off_all();
    }

    pub fn set_channel_solo(&mut self, channel: u8, value: bool) {
        if value {
            self.solo_channels.push(channel);
        } else {
            self.solo_channels.retain(|x| !x.eq(&channel));
        }

        self.solo_channels.sort();
        self.solo_channels.dedup();
        self.note_off_all();
    }

    pub fn set_loop(&mut self, value: bool) {
        self.play_loop = value;
    }

    pub fn set_preview_only(&mut self, value: bool) {
        self.preview_only = value;
    }

    pub fn set_sustain_active(&mut self, value: bool) {
        self.sustain_active = value;

        if let Some(cb) = &self.callback {
            cb(vec![0,
                    pianorhythm_shared::MIDI_STATUS_BYTES::CONTROLLER,
                    pianorhythm_shared::MIDI_CONTROL_BYTES::DAMPER_PEDAL,
                    if value { 127 } else { 0 },
            ], self.preview_only);
        }
    }

    pub fn rewind(&mut self) {
        if self.midi_file.is_none() {
            return;
        }

        self.set_position((self.current_time - 10.).max(0.0));
    }

    pub fn forward(&mut self) {
        if self.midi_file.is_none() {
            return;
        }

        self.set_position((self.current_time + 10.).min(self.get_total_time()));
    }

    fn note_off_all(&self) {
        for channel in 0..=pianorhythm_shared::midi::MAX_CHANNEL {
            if let Some(cb) = &self.callback {
                cb(vec![channel, pianorhythm_shared::MIDI_STATUS_BYTES::CONTROLLER, pianorhythm_shared::MIDI_CONTROL_BYTES::ALL_NOTES_OFF, 0], self.preview_only);
            }
        }
    }
}