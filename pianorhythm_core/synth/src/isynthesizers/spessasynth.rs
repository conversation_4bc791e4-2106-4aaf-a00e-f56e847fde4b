use pianorhythm_proto::midi_renditions::AudioChannel;
use crate::{CoreSoundfontPreset, ISynthesizer};

pub struct SpessaSynth;

impl SpessaSynth {
    pub fn new(desc: SynthDescriptor) -> Self {
        let mut settings = rustysynth::SynthesizerSettings::new(desc.sample_rate as i32);
        settings.maximum_polyphony = desc.polyphony as usize;
        settings.enable_reverb_and_chorus = desc.reverb_active;

        SpessaSynth
    }
}

impl ISynthesizer for SpessaSynth {
    fn instrument_exists(&self, banknum: u32, prognum: u8) -> bool {
        todo!()
    }

    fn set_sample_rate(&mut self, sample_rate: f32) {
        todo!()
    }

    fn send_event(&mut self, event: MidiEvent, socket_id: &u32) -> Result<(), OxiError> {
        todo!()
    }

    fn get_reverb_level(&self) -> f32 {
        todo!()
    }

    fn get_reverb_room_size(&self) -> f32 {
        todo!()
    }

    fn get_reverb_damp(&self) -> f32 {
        todo!()
    }

    fn get_reverb_width(&self) -> f32 {
        todo!()
    }

    fn set_reverb_params(&mut self, params: &ReverbParams) {
        todo!()
    }

    fn set_reverb_room_size(&mut self, roomsize: f32) {
        todo!()
    }

    fn set_reverb_level(&mut self, level: f32) {
        todo!()
    }

    fn set_reverb_damp(&mut self, damp: f32) {
        todo!()
    }

    fn set_reverb_width(&mut self, width: f32) {
        todo!()
    }

    fn set_reverb_active(&mut self, active: bool) {
        todo!()
    }

    fn set_chorus_active(&mut self, active: bool) {
        todo!()
    }

    fn set_max_note_on_time(&mut self, value: Option<f64>) {
        todo!()
    }

    fn read_next(&mut self) -> (f32, f32) {
        todo!()
    }

    fn load_soundfont(&mut self, buf: &[u8]) -> Result<(), String> {
        todo!()
    }

    fn get_all_presets_from_sf_raw(&self) -> Vec<CoreSoundfontPreset> {
        todo!()
    }

    fn is_channel_active(&self, channel: u8, socket_id: &u32) -> bool {
        todo!()
    }

    fn synth_set_polyphony(&mut self, value: u16) {
        todo!()
    }

    fn synth_set_gain(&mut self, value: f32) {
        todo!()
    }

    fn clear_program_on_channel(&mut self, channel: u8, socket_id: &u32) {
        todo!()
    }

    fn get_cc_value(&self, channel: u8, ctrl: u16, socket_id: &u32) -> Option<u8> {
        todo!()
    }

    fn bank_select_with_channel(&mut self, channel: u8, bank: u32, id: &u32) -> Result<(), String> {
        todo!()
    }

    fn get_program(&self, channel: u8, socket_id: &u32) -> Option<(u32, u8)> {
        todo!()
    }

    fn has_soundfont_loaded(&self) -> bool {
        todo!()
    }

    fn get_default_instrument(&self) -> Option<&CoreSoundfontPreset> {
        todo!()
    }

    fn add_socket(&mut self, id: &u32, is_client: bool) -> bool {
        todo!()
    }

    fn has_socket_id(&self, id: &u32) -> bool {
        todo!()
    }

    fn remove_socket(&mut self, id: &u32) {
        todo!()
    }

    fn mute_socket(&mut self, id: &u32, value: bool) {
        todo!()
    }

    fn reset_synth_players(&mut self) {
        todo!()
    }

    fn set_socket_user_gain(&mut self, socket_id: &u32, value: f32) {
        todo!()
    }

    fn clear_sockets(&mut self) {
        todo!()
    }

    fn get_user_ids(&self) -> Vec<u32> {
        todo!()
    }

    fn set_channel_active(&mut self, channel: u8, value: bool, socket_id: &u32) {
        todo!()
    }

    fn is_socket_muted(&self, socket_id: &u32) -> bool {
        todo!()
    }

    fn channel_has_preset_loaded(&self, channel: u8, socket_id: &u32) -> bool {
        todo!()
    }

    fn dispose(&mut self) {
        todo!()
    }

    fn reset_voices(&mut self) {
        todo!()
    }

    fn set_user_interp_method(&mut self, chan: Option<usize>, socket_id: &u32, interp_method: InterpolationMethod) {
        todo!()
    }

    fn update_synth_players_interpolation_method(&mut self, interp_method: InterpolationMethod) {
        todo!()
    }

    fn get_audio_channel(&self, channel: u8, socket_id: &u32) -> Option<AudioChannel> {
        todo!()
    }
}