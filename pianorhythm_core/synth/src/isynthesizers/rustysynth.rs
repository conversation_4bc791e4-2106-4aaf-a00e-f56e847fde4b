use std::{collections::HashMap, io::<PERSON>ursor, rc::Rc, sync::Arc, vec};

use pianorhythm_proto::midi_renditions::AudioChannel;
use rustysynth::{SoundFont, Synthesizer};

use crate::{
    CoreSoundfontPreset,
    ISynthesizer, MidiEvent, OxiError, piano_rhythm_midi_control_change, SynthDescriptor,
};

struct PianoRhythmRustySynthUser {
    pub is_client: bool,
    pub socket_id: u32,
    pub muted: bool,
    pub gain: f32,
    pub channel_active: HashMap<u8, bool>,
}

impl PianoRhythmRustySynthUser {
    pub fn new(socket_id: &u32) -> Self {
        let mut channel_active = HashMap::new();

        for i in 0..Synthesizer::CHANNEL_COUNT {
            channel_active.insert(i as u8, false);
        }

        return PianoRhythmRustySynthUser {
            is_client: false,
            socket_id: socket_id.clone(),
            muted: false,
            gain: 1.0,
            channel_active,
        };
    }

    pub fn set_muted(&mut self, muted: bool) {
        self.muted = muted;
    }

    pub fn set_gain(&mut self, gain: f32) {
        self.gain = if gain < 0.01 {
            0.0
        } else if gain > 10.0 {
            10.0
        } else {
            gain
        };
    }
}

pub struct RustySynth {
    core: Option<rustysynth::Synthesizer>,
    global_channels: Vec<rustysynth::Channel>,
    soundfont_loaded: bool,
    soundfont: Option<Arc<SoundFont>>,
    default_instrument: Option<CoreSoundfontPreset>,
    settings: rustysynth::SynthesizerSettings,
    pub drums_channel_active: bool,
    socket_players: HashMap<u32, PianoRhythmRustySynthUser>,
    player_channels: HashMap<u32, Vec<rustysynth::Channel>>,
}

impl RustySynth {
    pub fn new(desc: SynthDescriptor) -> RustySynth {
        let mut settings = rustysynth::SynthesizerSettings::new(desc.sample_rate as i32);
        settings.maximum_polyphony = desc.polyphony as usize;
        settings.enable_reverb_and_chorus = desc.reverb_active;

        return RustySynth {
            core: None,
            soundfont: None,
            settings,
            soundfont_loaded: false,
            default_instrument: None,
            global_channels: vec![],
            socket_players: HashMap::new(),
            player_channels: HashMap::new(),
            drums_channel_active: desc.drums_channel_active,
        };
    }
}

impl ISynthesizer for RustySynth {
    fn instrument_exists(&self, banknum: u32, prognum: u8) -> bool {
        false
    }

    fn set_sample_rate(&mut self, sample_rate: f32) {}

    fn send_event(&mut self, event: MidiEvent, socket_id: &u32) -> Result<(), OxiError> {
        if let Some(target) = self.player_channels.get_mut(socket_id) {
            if let Some(ref mut core) = self.core {
                let channels = target;
                let _socket_id = Some(socket_id.clone());

                match event {
                    MidiEvent::NoteOn { channel, key, vel } => {
                        core.note_on(_socket_id, channels, channel.into(), key.into(), vel.into());
                    }
                    MidiEvent::NoteOff { channel, key } => {
                        core.note_off(_socket_id, channels, channel.into(), key.into());
                    }
                    MidiEvent::ControlChange {
                        channel,
                        ctrl,
                        value,
                    } => core.process_midi_message(
                        _socket_id,
                        channels,
                        channel.into(),
                        0xB0,
                        ctrl.into(),
                        value.into(),
                    ),
                    MidiEvent::AllNotesOff { channel } => core.process_midi_message(
                        _socket_id,
                        channels,
                        channel.into(),
                        0xB0,
                        piano_rhythm_midi_control_change::ALL_NOTES_OFF.into(),
                        0x0,
                    ),
                    MidiEvent::AllSoundOff { channel } => core.process_midi_message(
                        _socket_id,
                        channels,
                        channel.into(),
                        0xB0,
                        piano_rhythm_midi_control_change::ALL_SOUND_OFF.into(),
                        0x0,
                    ),
                    MidiEvent::PitchBend { channel, value } => {
                        // self.core
                        //     .process_midi_message(socket_id, channel.into(), 0xE0, value.into(), 0)
                    }
                    MidiEvent::ProgramChange {
                        channel,
                        program_id,
                    } => core.process_midi_message(
                        _socket_id,
                        channels,
                        channel.into(),
                        0xC0,
                        program_id.into(),
                        0,
                    ),
                    MidiEvent::ChannelPressure { channel, value } => {}
                    MidiEvent::PolyphonicKeyPressure {
                        channel,
                        key,
                        value,
                    } => {}
                    MidiEvent::SocketUserGainChange { value } => {}
                    MidiEvent::SystemReset => core.process_midi_message(
                        _socket_id,
                        channels,
                        0x0,
                        0xB0,
                        piano_rhythm_midi_control_change::RESET_ALL_CONTROLLERS.into(),
                        0x0,
                    ),
                    MidiEvent::SystemResetWithChannel { channel } => core.process_midi_message(
                        _socket_id,
                        channels,
                        channel.into(),
                        0xB0,
                        piano_rhythm_midi_control_change::RESET_ALL_CONTROLLERS.into(),
                        0x0,
                    ),
                    _ => {
                        return Err(OxiError::UnknownEvent);
                    }
                }

                return Ok(());
            }
        }

        return Err(OxiError::SocketUserNotFound);
    }

    fn get_reverb_level(&self) -> f32 {
        0.0
    }

    fn get_reverb_room_size(&self) -> f32 {
        0.0
    }

    fn get_reverb_damp(&self) -> f32 {
        0.0
    }

    fn get_reverb_width(&self) -> f32 {
        0.0
    }

    fn set_reverb_params(&mut self, params: &crate::core::reverb::ReverbParams) {}

    fn set_reverb_room_size(&mut self, roomsize: f32) {}

    fn set_reverb_level(&mut self, level: f32) {}

    fn set_reverb_damp(&mut self, damp: f32) {}

    fn set_reverb_width(&mut self, width: f32) {}

    fn set_reverb_active(&mut self, active: bool) {
        // self.core.set_enable_reverb_and_chorus(active)
    }

    fn set_chorus_active(&mut self, active: bool) {}

    fn set_max_note_on_time(&mut self, value: Option<f64>) {}

    fn read_next(&mut self) -> (f32, f32) {
        if let Some(ref mut core) = self.core {
            let mut left: Vec<f32> = vec![0_f32; 1];
            let mut right: Vec<f32> = vec![0_f32; 1];

            let channels: Vec<&rustysynth::Channel> =
                self.player_channels.values().flatten().collect();

            core.render(&mut left, &mut right, channels);

            let mut out = (0.0f32, 0.0f32);
            for it in left.iter().zip(right.iter()) {
                let (l, r) = it;
                out = (*l, *r);
            }
            out
        } else {
            (0., 0.)
        }
    }

    fn load_soundfont(&mut self, buf: &[u8]) -> Result<(), String> {
        let mut c = Cursor::new(buf.to_vec());
        match rustysynth::SoundFont::new(&mut c) {
            Ok(soundfont) => {
                let sf = Arc::new(soundfont);
                let mut rusty: Synthesizer =
                    rustysynth::Synthesizer::new(&sf, &self.settings).unwrap();
                self.core = Some(rusty);

                self.soundfont_loaded = true;
                self.soundfont = Some(sf);
                return Ok(());
            }
            Err(err) => {
                log::error!("load_soundfont ERROR: {err:?} | {0:?}", buf.len());
                self.soundfont_loaded = false;
                return Err(err.to_string());
            }
        }
    }

    fn get_all_presets_from_sf_raw(&self) -> Vec<crate::CoreSoundfontPreset> {
        let mut output = Vec::new();

        if let Some(sf) = self.core.as_ref().map(|c| c.get_sound_font()) {
            output = sf
                .get_presets()
                .into_iter()
                .map(|preset| crate::CoreSoundfontPreset {
                    name: preset.get_name().into(),
                    bank: preset.get_bank_number() as u32,
                    preset: preset.get_patch_number() as u32,
                    key_low: 0,    //preset.get_key_range_start() as u8,
                    key_high: 127, //preset.get_key_range_end() as u8,
                })
                .collect();
        }

        output.sort_by_key(|k| (k.bank, k.preset));
        return output;
    }

    fn is_channel_active(&self, chan: u8, socket_id: &u32) -> bool {
        if (chan > Synthesizer::CHANNEL_COUNT as u8) {
            return false;
        }

        if let Some(target) = self.socket_players.get(socket_id) {
            return target.channel_active[&chan];
        }

        return false;
    }

    fn synth_set_polyphony(&mut self, value: u16) {
        // self.core.set_maximum_polyphony(value.into());
    }

    fn synth_set_gain(&mut self, value: f32) {
        if let Some(ref mut core) = self.core {
            core.set_master_volume(value)
        }
    }

    fn clear_program_on_channel(&mut self, channel: u8, socket_id: &u32) {
        self.send_event(
            MidiEvent::ProgramChange {
                channel,
                program_id: 0,
            },
            socket_id,
        );
    }

    fn get_cc_value(&self, channel: u8, ctrl: u16, socket_id: &u32) -> Option<u8> {
        if (channel > Synthesizer::CHANNEL_COUNT as u8) {
            return None;
        }

        if let Some(target) = self.player_channels.get(socket_id) {
            let _channel = &target[channel as usize];
            return match ctrl as u8 {
                piano_rhythm_midi_control_change::PAN_MSB => {
                    Some((_channel.pan >> 7) as u8)
                }
                piano_rhythm_midi_control_change::MAIN_VOLUME_MSB => {
                    Some((_channel.volume >> 7) as u8)
                }
                piano_rhythm_midi_control_change::EXPRESSION_CONTROLLER_MSB => {
                    Some((_channel.expression >> 7) as u8)
                }
                _ => None,
            };
        } else {
            return None;
        }
    }

    fn bank_select_with_channel(
        &mut self,
        channel: u8,
        bank: u32,
        socket_id: &u32,
    ) -> Result<(), String> {
        self.send_event(
            MidiEvent::ControlChange {
                channel,
                ctrl: 0x00,
                value: bank as u8,
            },
            socket_id,
        );
        Ok(())
    }

    fn get_program(&self, channel: u8, socket_id: &u32) -> Option<(u32, u8)> {
        if let Some(target) = self.player_channels.get(socket_id) {
            let _channel = &target[channel as usize];
            return Some((_channel.bank_number as u32, _channel.patch_number as u8));
        }

        return None;
    }

    fn has_soundfont_loaded(&self) -> bool {
        self.soundfont_loaded
    }

    fn get_default_instrument(&self) -> Option<&CoreSoundfontPreset> {
        self.default_instrument.as_ref()
    }

    fn add_socket(&mut self, socket_id: &u32, is_client: bool) -> bool {
        if self.has_socket_id(socket_id) {
            return false;
        }

        let mut user = PianoRhythmRustySynthUser::new(socket_id);
        user.is_client = is_client;
        self.socket_players.insert(*socket_id, user);

        let mut channels: Vec<rustysynth::Channel> = Vec::new();

        for i in 0..Synthesizer::CHANNEL_COUNT {
            let mut channel = rustysynth::Channel::new(i == Synthesizer::PERCUSSION_CHANNEL);
            channel.channel_id = i;
            channel.socket_id = Some(socket_id.clone());
            channels.push(channel);
        }

        self.player_channels.insert(socket_id.clone(), channels);

        self.send_event(
            MidiEvent::ProgramChange {
                channel: 0,
                program_id: 0,
            },
            &socket_id,
        );

        return true;
    }

    fn has_socket_id(&self, socket_id: &u32) -> bool {
        return self.socket_players.contains_key(socket_id);
    }

    fn remove_socket(&mut self, socket_id: &u32) {
        self.socket_players.remove(socket_id);
        self.player_channels.remove(socket_id);
    }

    fn mute_socket(&mut self, socket_id: &u32, value: bool) {
        let synth = self.socket_players.get_mut(socket_id);
        if let Some(target) = synth {
            target.set_muted(value);
        }
    }

    fn reset_synth_players(&mut self) {
        for _socket_id in self.get_user_ids() {
            self.send_event(MidiEvent::SystemReset, &_socket_id);
        }
    }

    fn set_socket_user_gain(&mut self, socket_id: &u32, value: f32) {
        if let Some(target) = self.socket_players.get_mut(socket_id) {
            target.set_gain(value);
        }
    }

    fn clear_sockets(&mut self) {
        self.socket_players.clear();
        // self.core.clear_all_channels();
    }

    fn get_user_ids(&self) -> Vec<u32> {
        return self
            .socket_players
            .values()
            .into_iter()
            .map(|x| x.socket_id)
            .collect();
    }

    fn set_channel_active(&mut self, channel: u8, value: bool, socket_id: &u32) {
        let synth = self.socket_players.get_mut(socket_id);
        if let Some(target) = synth {
            target.channel_active.insert(channel, value);
        }
    }

    fn is_socket_muted(&self, socket_id: &u32) -> bool {
        let synth = self.socket_players.get(socket_id);
        if let Some(target) = synth {
            return target.muted;
        }

        return false;
    }

    fn channel_has_preset_loaded(&self, channel: u8, socket_id: &u32) -> bool {
        // let synth = self.socket_players.get_mut(socket_id);
        // if let Some(target) = synth {
        //     return target.channels[channel as usize].patch_number >= 0;
        // }

        return true;
    }

    fn dispose(&mut self) {
        self.soundfont_loaded = false;
        self.clear_sockets();
    }

    fn reset_voices(&mut self) {
        if let Some(ref mut core) = self.core {
            core.reset(self.global_channels.as_mut());
        }
    }

    fn set_user_interp_method(
        &mut self,
        chan: Option<usize>,
        socket_id: &u32,
        interp_method: crate::core::InterpolationMethod,
    ) {}

    fn update_synth_players_interpolation_method(
        &mut self,
        interp_method: crate::core::InterpolationMethod,
    ) {}

    fn get_audio_channel(&self, channel: u8, socket_id: &u32) -> Option<AudioChannel> {
        if let Some(target) = self.player_channels.get(socket_id) {
            let _channel = &target[channel as usize];
            let mut audio_channel = AudioChannel::new();
            audio_channel.set_channel(channel.into());
            audio_channel.set_expression(_channel.expression as u32);
            audio_channel.set_bank(_channel.bank_number as u32);
            audio_channel.set_preset(_channel.patch_number as u32);
            audio_channel.set_pan(_channel.pan as u32);
            audio_channel.set_volume(_channel.volume as u32);
            return None;
        }

        None
    }
}
