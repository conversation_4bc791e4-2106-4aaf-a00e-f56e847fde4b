use std::io::Cursor;

use oxisynth::{InterpolationMethod, MidiEvent, OxiError, ReverbParams, SoundFont, SoundFontId, SynthDescriptor};
use pianorhythm_proto::midi_renditions::AudioChannel;

use crate::{CoreSoundfontPreset, ISynthesizer, Synth};

pub struct OxiSynth {
    pub synth: Synth,
    loaded_soundfont_id: Option<SoundFontId>,
    default_instrument: Option<CoreSoundfontPreset>,
}

impl Default for OxiSynth {
    fn default() -> Self {
        Self {
            loaded_soundfont_id: None,
            default_instrument: None,
            synth: Synth::default(),
        }
    }
}

impl OxiSynth {
    pub fn new(desc: SynthDescriptor) -> Self {
        OxiSynth {
            loaded_soundfont_id: None,
            default_instrument: None,
            synth: Synth::new(desc),
        }
    }
}

impl ISynthesizer for OxiSynth {
    fn instrument_exists(&self, banknum: u32, prognum: u8) -> bool {
        if !self.has_soundfont_loaded() {
            return false;
        }

        let bank = self.synth.font_bank();
        bank.find_preset(banknum, prognum).is_some()
    }

    fn set_sample_rate(&mut self, sample_rate: f32) {
        self.synth.set_sample_rate(sample_rate)
    }

    fn send_event(&mut self, event: MidiEvent, socket_id: &u32) -> Result<(), OxiError> {
        if let Some(target) = self.synth.socket_players.get_mut(socket_id) {
            return self.synth.synth_wrapper.send_event(event, target);
        }

        return Err(OxiError::SocketUserNotFound);
    }

    fn get_reverb_level(&self) -> f32 {
        unsafe {
            let reverb = &*self.synth.get_reverb();
            return reverb.level();
        }
    }

    fn get_reverb_room_size(&self) -> f32 {
        unsafe {
            let reverb = &*self.synth.get_reverb();
            return reverb.room_size();
        }
    }

    fn get_reverb_damp(&self) -> f32 {
        unsafe {
            let reverb = &*self.synth.get_reverb();
            return reverb.damp();
        }
    }

    fn get_reverb_width(&self) -> f32 {
        unsafe {
            let reverb = &*self.synth.get_reverb();
            return reverb.width();
        }
    }

    fn set_reverb_params(&mut self, params: &ReverbParams) {
        self.synth.set_reverb_params(params)
    }

    fn set_reverb_room_size(&mut self, roomsize: f32) {
        self.synth.set_reverb_room_size(roomsize)
    }

    fn set_reverb_level(&mut self, level: f32) {
        self.synth.set_reverb_level(level)
    }

    fn set_reverb_damp(&mut self, damp: f32) {
        self.synth.set_reverb_damp(damp)
    }

    fn set_reverb_width(&mut self, width: f32) {
        self.synth.set_reverb_width(width)
    }

    fn set_reverb_active(&mut self, active: bool) {
        self.synth.set_reverb_active(active)
    }

    fn set_chorus_active(&mut self, active: bool) {
        self.synth.set_chorus_active(active)
    }

    fn set_max_note_on_time(&mut self, value: Option<f64>) {
        self.synth.synth_wrapper.set_max_note_on_time(value)
    }

    fn read_next(&mut self) -> (f32, f32) {
        self.synth.synth_wrapper.core.read_next()
    }

    fn load_soundfont(&mut self, buf: &[u8]) -> Result<(), String> {
        let mut c = Cursor::new(buf.to_vec());
        let font = SoundFont::load(&mut c).map_err(|err| format!("Failed to load soundfont data: {0:?}", err))?;

        // Remove previous font
        if let Some(loaded_sf_id) = self.loaded_soundfont_id {
            self.synth
                .synth_wrapper
                .remove_font(loaded_sf_id, true);
        }

        let id = self.synth.synth_wrapper.add_font(font, true);
        self.loaded_soundfont_id = Some(id);
        self.default_instrument = self.get_all_presets_from_sf_raw().first().clone().map(|x| x.to_owned());

        // Reset everyone else
        self.reset_synth_players();
        Ok(())
    }

    fn get_all_presets_from_sf_raw(&self) -> Vec<CoreSoundfontPreset> {
        return match self.loaded_soundfont_id {
            None => Vec::new(),
            Some(sf_id) => {
                if let Some(sf) = self.synth.synth_wrapper.sound_font(sf_id) {
                    let mapped = sf.presets
                        .iter()
                        .map(|p| {
                            let zones = p.zones();

                            return CoreSoundfontPreset {
                                name: p.name().into(),
                                bank: p.banknum(),
                                preset: p.num(),
                                key_low: zones
                                    .iter()
                                    .map(|z| {
                                        z.inst
                                            .as_ref()
                                            .map(|f| f.zones().iter().map(|k| k.key_low).collect::<Vec<u8>>())
                                            .unwrap_or_default()
                                    })
                                    .flatten()
                                    .min()
                                    .unwrap_or(0),
                                key_high: zones
                                    .iter()
                                    .map(|z| {
                                        z.inst
                                            .as_ref()
                                            .map(|f| f.zones().iter().map(|k| k.key_high).collect::<Vec<u8>>())
                                            .unwrap_or_default()
                                    })
                                    .flatten()
                                    .max()
                                    .unwrap_or(128),
                            };
                        })
                        .collect();
                    mapped
                } else {
                    println!("Failed to get presets since soundfont id does not exist.");
                    log::error!("Failed to get presets since soundfont id does not exist.");
                    Vec::new()
                }
            }
        };
    }

    fn is_channel_active(&self, channel: u8, socket_id: &u32) -> bool {
        self.synth.is_channel_active(channel, socket_id)
    }

    fn synth_set_polyphony(&mut self, value: u16) {
        self.synth.synth_wrapper.set_polyphony(value);
    }

    fn synth_set_gain(&mut self, value: f32) {
        self.synth.synth_wrapper.set_gain(value)
    }

    fn clear_program_on_channel(&mut self, channel: u8, socket_id: &u32) {
        self.synth.clear_program_on_channel(channel, socket_id);
    }

    fn get_cc_value(&self, channel: u8, ctrl: u16, socket_id: &u32) -> Option<u8> {
        self.synth.get_cc_by_socket_id(channel, ctrl, socket_id).ok()
    }

    fn bank_select_with_channel(&mut self, channel: u8, bank: u32, id: &u32) -> Result<(), String> {
        self.synth.bank_select_with_channel(channel, bank, id).map_err(|x| x.to_string())
    }

    fn get_program(&self, channel: u8, socket_id: &u32) -> Option<(u32, u8)> {
        match self.synth.get_program(channel, socket_id) {
            Err(_) => None,
            Ok(e) => Some((e.1, e.2)),
        }
    }

    fn has_soundfont_loaded(&self) -> bool {
        return self.loaded_soundfont_id.is_some();
    }

    fn get_default_instrument(&self) -> Option<&CoreSoundfontPreset> {
        self.default_instrument.as_ref()
    }

    fn add_socket(&mut self, id: &u32, is_client: bool) -> bool {
        self.synth.add_socket(id, is_client, InterpolationMethod::default())
    }

    fn has_socket_id(&self, id: &u32) -> bool {
        self.synth.has_socket_id(id)
    }

    fn remove_socket(&mut self, id: &u32) {
        self.synth.remove_socket(id)
    }

    fn mute_socket(&mut self, id: &u32, value: bool) {
        let synth = self.synth.socket_players.get_mut(id);
        if let Some(target) = synth {
            target.set_muted(value);
        }
    }

    fn reset_synth_players(&mut self) {
        self.synth.reset_synth_players()
    }

    fn set_socket_user_gain(&mut self, socket_id: &u32, value: f32) {
        self.synth.set_socket_user_gain(socket_id, value)
    }

    fn clear_sockets(&mut self) {
        self.synth.clear_sockets()
    }

    fn get_user_ids(&self) -> Vec<u32> {
        return self.synth.socket_players.values().into_iter().map(|x| x.socket_id).collect();
    }

    fn set_channel_active(&mut self, channel: u8, value: bool, socket_id: &u32) {
        let synth = self.synth.socket_players.get_mut(socket_id);
        if let Some(target) = synth {
            if let Ok(channel) = self.synth.synth_wrapper.core.channels.get_mut_by_user_id_and_channel_id(*socket_id, channel as usize) {
                channel.set_active(value);
            }
        }
    }

    fn is_socket_muted(&self, socket_id: &u32) -> bool {
        let synth = self.synth.socket_players.get(socket_id);
        if let Some(target) = synth {
            return target.muted;
        }

        return false;
    }

    fn channel_has_preset_loaded(&self, channel: u8, socket_id: &u32) -> bool {
        return self.synth.channel_has_any_preset(channel, socket_id);
    }

    fn dispose(&mut self) {}

    fn reset_voices(&mut self) {
        self.synth.synth_wrapper.core.voices.reset();
    }

    fn set_user_interp_method(&mut self, chan: Option<usize>, socket_id: &u32, interp_method: InterpolationMethod) {
        self.synth.set_user_interp_method(chan, socket_id, interp_method);
    }

    fn update_synth_players_interpolation_method(&mut self, interp_method: InterpolationMethod) {
        self.synth.update_synth_players_interpolation_method(interp_method);
    }

    fn get_audio_channel(&self, channel: u8, socket_id: &u32) -> Option<AudioChannel> {
        if let Some(target) = self.synth.socket_players.get(socket_id) {
            return self.synth.synth_wrapper.core.channels.get_by_user_id_and_channel_id(*socket_id, channel as usize)
                .ok()
                .map(|x| x.get_audio_channel()).cloned();
        }

        None
    }
}
