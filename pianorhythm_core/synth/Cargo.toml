[package]
name = "pianorhythm_synth"
version = "0.1.2"
authors = ["Oak <<EMAIL>>"]
edition = "2021"
rust-version.workspace = true

[lib]
crate-type = ["lib", "cdylib"]

[features]
default = ["console_error_panic_hook"]
sf3 = ["lewton"]
desktop_lib = []
stand_alone = []

[dependencies]
oxisynth = { path = "./oxisynth" }
soundfont = { path = "./soundfont-rs" }
simple-eq = { path = "./simple-eq-master" }
rustysynth = { path = "./rustysynth/rustysynth" }
pianorhythm_proto = { path = "../proto" }
pianorhythm_shared = { path = "../shared" }
midir = { workspace = true }
midly = { workspace = true }
lazy_static = { workspace = true }
protobuf = { workspace = true }
once_cell = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
cached = { workspace = true }
rustc-hash = { workspace = true }
bitflags = "2.4.1"
byte-slice-cast = "1.0.0"
byteorder = "1.4.3"
generational-arena = "0.2.8"
log = { workspace = true }
num-traits = { version = "0.2", features = ["std"] }
num-derive = "0.4.1"
lewton = { version = "0.10.2", optional = true }
thiserror = "1.0.25"
url = "2.2.2"
realfft = { version = "3.3.0" }
derivative = { workspace = true }
regex = { version = "=1.10.6", features = ["pattern"] }
convert_case = "0.6.0"
uuid = { version = "1.7.0", features = ["v4"] }
lowpass-filter = "0.3.2"

[target.'cfg(target_arch = "x86_64")'.dependencies]
reactive-state = { workspace = true, features = ["simple_logger"] }
instant = { version = "0.1.12" }

[target.'cfg(all(target_arch = "wasm32", target_os = "unknown"))'.dependencies]
console_error_panic_hook = { version = "0.1.7", optional = true }
wasm-bindgen = { workspace = true }
wasm-bindgen-futures = { workspace = true }
serde-wasm-bindgen = { workspace = true }
web-sys = { workspace = true }
wasm-msgpack = { workspace = true }
js-sys = { workspace = true }
reactive-state = { workspace = true, features = ["wasm-bindgen"] }
gloo = { workspace = true }
gloo-timers = { workspace = true }
gloo-events = { workspace = true }
instant = { version = "0.1.12", features = ["wasm-bindgen", "inaccurate"] }
cpal = { version = "0.15.3", features = ["wasm-bindgen"] }
wasm-bindgen-rayon = { version = "1.2.1", features = ["nightly"] }

[dev-dependencies]
byte-slice-cast = "1.2.2"

[profile.dev.package."*"]
opt-level = 3