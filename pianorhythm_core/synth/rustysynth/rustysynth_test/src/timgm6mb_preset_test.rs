#![allow(unused_imports)]

use rustysynth::SoundFont;
use std::fs::File;
use std::path::PathBuf;

use crate::preset_util;

#[test]
fn regions() {
    let mut path = PathBuf::from(env!("CARGO_MANIFEST_DIR"));
    path.pop();
    path.push("TimGM6mb.sf2");
    let mut file = File::open(&path).unwrap();
    let sf = SoundFont::new(&mut file).unwrap();

    // ============================================================
    //  Flute TB
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[0].get_regions()[0], &values);

    // ============================================================
    //  Orchestra
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[1].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[1].get_regions()[1], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[1].get_regions()[2], &values);

    // ============================================================
    //  Brush
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[2].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[2].get_regions()[1], &values);

    // ============================================================
    //  Jazz
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[3].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[3].get_regions()[1], &values);

    // ============================================================
    //  TR 808
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[4].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[4].get_regions()[1], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[4].get_regions()[2], &values);

    // ============================================================
    //  Electronic
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[5].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[5].get_regions()[1], &values);

    // ============================================================
    //  Power
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[6].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[6].get_regions()[1], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[6].get_regions()[2], &values);

    // ============================================================
    //  Room
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[7].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[7].get_regions()[1], &values);

    // ============================================================
    //  Standard
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[8].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[8].get_regions()[1], &values);

    // ============================================================
    //  Gun Shot
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[9].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[9].get_regions()[1], &values);

    // ============================================================
    //  Applause
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[10].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[10].get_regions()[1], &values);

    // ============================================================
    //  Helicopter
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[11].get_regions()[0], &values);

    // ============================================================
    //  Telephone
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[12].get_regions()[0], &values);

    // ============================================================
    //  Bird
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[13].get_regions()[0], &values);

    // ============================================================
    //  Seashore
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[14].get_regions()[0], &values);

    // ============================================================
    //  Breath Noise
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[15].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[15].get_regions()[1], &values);

    // ============================================================
    //  Fret Noise
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[16].get_regions()[0], &values);

    // ============================================================
    //  Reverse Cymbal
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[17].get_regions()[0], &values);

    // ============================================================
    //  Synth Drum
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[18].get_regions()[0], &values);

    // ============================================================
    //  Melodic Tom
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[19].get_regions()[0], &values);

    // ============================================================
    //  Taiko Drum
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[20].get_regions()[0], &values);

    // ============================================================
    //  Wood Block
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[21].get_regions()[0], &values);

    // ============================================================
    //  Steel Drum
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[22].get_regions()[0], &values);

    // ============================================================
    //  Agogo
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[23].get_regions()[0], &values);

    // ============================================================
    //  Tinker Bell
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[24].get_regions()[0], &values);

    // ============================================================
    //  Shenai
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[25].get_regions()[0], &values);

    // ============================================================
    //  Fiddle
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[26].get_regions()[0], &values);

    // ============================================================
    //  Bagpipe
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[27].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[27].get_regions()[1], &values);

    // ============================================================
    //  Kalimba
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[28].get_regions()[0], &values);

    // ============================================================
    //  Koto
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[29].get_regions()[0], &values);

    // ============================================================
    //  Shamisen
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[30].get_regions()[0], &values);

    // ============================================================
    //  Banjo
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[31].get_regions()[0], &values);

    // ============================================================
    //  Sitar
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[32].get_regions()[0], &values);

    // ============================================================
    //  Star Theme
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[33].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[33].get_regions()[1], &values);

    // ============================================================
    //  Echo Drops
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[34].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[34].get_regions()[1], &values);

    // ============================================================
    //  Goblin
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[35].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[35].get_regions()[1], &values);

    // ============================================================
    //  Brightness
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[36].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[36].get_regions()[1], &values);

    // ============================================================
    //  Atmosphere
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[37].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[37].get_regions()[1], &values);

    // ============================================================
    //  Crystal
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[38].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[38].get_regions()[1], &values);

    // ============================================================
    //  Soundtrack
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[39].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[39].get_regions()[1], &values);

    // ============================================================
    //  IceRain
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[40].get_regions()[0], &values);

    // ============================================================
    //  Sweep Pad
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[41].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[41].get_regions()[1], &values);

    // ============================================================
    //  Halo Pad
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[42].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[42].get_regions()[1], &values);

    // ============================================================
    //  Metal Pad
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[43].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[43].get_regions()[1], &values);

    // ============================================================
    //  Bowed Glass
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[44].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[44].get_regions()[1], &values);

    // ============================================================
    //  Space Voice
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[45].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[45].get_regions()[1], &values);

    // ============================================================
    //  Poly Synth
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[46].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[46].get_regions()[1], &values);

    // ============================================================
    //  Warm Pad
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[47].get_regions()[0], &values);

    // ============================================================
    //  Fantasia
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[48].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[48].get_regions()[1], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[48].get_regions()[2], &values);

    // ============================================================
    //  Bass & Lead
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[49].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[49].get_regions()[1], &values);

    // ============================================================
    //  5th Saw Wave
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[50].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[50].get_regions()[1], &values);

    // ============================================================
    //  Solo Vox
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[51].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[51].get_regions()[1], &values);

    // ============================================================
    //  Charang
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[52].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[52].get_regions()[1], &values);

    // ============================================================
    //  Chiffer Lead
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[53].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[53].get_regions()[1], &values);

    // ============================================================
    //  Synth Calliope
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[54].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[54].get_regions()[1], &values);

    // ============================================================
    //  Saw Wave
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[55].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[55].get_regions()[1], &values);

    // ============================================================
    //  Square Wave
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[56].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[56].get_regions()[1], &values);

    // ============================================================
    //  Ocarina
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[57].get_regions()[0], &values);

    // ============================================================
    //  Whistle
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[58].get_regions()[0], &values);

    // ============================================================
    //  Shakuhachi
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[59].get_regions()[0], &values);

    // ============================================================
    //  Bottle Chiff
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[60].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[60].get_regions()[1], &values);

    // ============================================================
    //  Pan Flute
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[61].get_regions()[0], &values);

    // ============================================================
    //  Recorder
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[62].get_regions()[0], &values);

    // ============================================================
    //  Piccolo
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[63].get_regions()[0], &values);

    // ============================================================
    //  Clarinet
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[64].get_regions()[0], &values);

    // ============================================================
    //  Bassoon
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[65].get_regions()[0], &values);

    // ============================================================
    //  English Horn
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[66].get_regions()[0], &values);

    // ============================================================
    //  Oboe (Orch)
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[67].get_regions()[0], &values);

    // ============================================================
    //  French Horns
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[68].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[68].get_regions()[1], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[68].get_regions()[2], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[68].get_regions()[3], &values);

    // ============================================================
    //  Synth Brass 2
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[69].get_regions()[0], &values);

    // ============================================================
    //  Synth Brass 1
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[70].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[70].get_regions()[1], &values);

    // ============================================================
    //  Brass
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[71].get_regions()[0], &values);

    // ============================================================
    //  Mute Trumpet
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[72].get_regions()[0], &values);

    // ============================================================
    //  Tuba
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[73].get_regions()[0], &values);

    // ============================================================
    //  Trombone
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[74].get_regions()[0], &values);

    // ============================================================
    //  SoloTrumpet
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[75].get_regions()[0], &values);

    // ============================================================
    //  Orchestra Hit
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[76].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[76].get_regions()[1], &values);

    // ============================================================
    //  Synth Vox
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[77].get_regions()[0], &values);

    // ============================================================
    //  Voice Oohs
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[78].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[78].get_regions()[1], &values);

    // ============================================================
    //  Synth Strings 2
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[79].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[79].get_regions()[1], &values);

    // ============================================================
    //  Timpani
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[80].get_regions()[0], &values);

    // ============================================================
    //  Harp LP
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[81].get_regions()[0], &values);

    // ============================================================
    //  Pizzicato
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[82].get_regions()[0], &values);

    // ============================================================
    //  Contrabass
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[83].get_regions()[0], &values);

    // ============================================================
    //  Cello
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[84].get_regions()[0], &values);

    // ============================================================
    //  Viola
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[85].get_regions()[0], &values);

    // ============================================================
    //  Violin
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[86].get_regions()[0], &values);

    // ============================================================
    //  Synth Bass 2
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[87].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[87].get_regions()[1], &values);

    // ============================================================
    //  Synth Bass 1
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[88].get_regions()[0], &values);

    // ============================================================
    //  Slap Bass 2
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[89].get_regions()[0], &values);

    // ============================================================
    //  Slap Bass 1
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[90].get_regions()[0], &values);

    // ============================================================
    //  Fretless Bass
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[91].get_regions()[0], &values);

    // ============================================================
    //  Picked Bass
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[92].get_regions()[0], &values);

    // ============================================================
    //  Fingered Bass
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[93].get_regions()[0], &values);

    // ============================================================
    //  Acoustic Bass
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[94].get_regions()[0], &values);

    // ============================================================
    //  Guitar Harmonics
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[95].get_regions()[0], &values);

    // ============================================================
    //  DistortionGuitar
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[96].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[96].get_regions()[1], &values);

    // ============================================================
    //  Overdrive Guitar
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[97].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[97].get_regions()[1], &values);

    // ============================================================
    //  Guitar Mutes
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[98].get_regions()[0], &values);

    // ============================================================
    //  Clean Guitar
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[99].get_regions()[0], &values);

    // ============================================================
    //  Jazz Guitar
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[100].get_regions()[0], &values);

    // ============================================================
    //  Steel Guitar
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[101].get_regions()[0], &values);

    // ============================================================
    //  Nylon Guitar
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[102].get_regions()[0], &values);

    // ============================================================
    //  Bandoneon
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[103].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[103].get_regions()[1], &values);

    // ============================================================
    //  Harmonica
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[104].get_regions()[0], &values);

    // ============================================================
    //  Accordion
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[105].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[105].get_regions()[1], &values);

    // ============================================================
    //  Reed Organ
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[106].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[106].get_regions()[1], &values);

    // ============================================================
    //  Church Organ
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[107].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[107].get_regions()[1], &values);

    // ============================================================
    //  Organ 3
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[108].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[108].get_regions()[1], &values);

    // ============================================================
    //  Organ 2
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[109].get_regions()[0], &values);

    // ============================================================
    //  Organ 1
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[110].get_regions()[0], &values);

    // ============================================================
    //  Dulcimer
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[111].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[111].get_regions()[1], &values);

    // ============================================================
    //  Tubular Bells
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[112].get_regions()[0], &values);

    // ============================================================
    //  Xylophone
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[113].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[113].get_regions()[1], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[113].get_regions()[2], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[113].get_regions()[3], &values);

    // ============================================================
    //  Marimba
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[114].get_regions()[0], &values);

    // ============================================================
    //  Vibraphone
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[115].get_regions()[0], &values);

    // ============================================================
    //  MusicBox
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[116].get_regions()[0], &values);

    // ============================================================
    //  Glockenspiel
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[117].get_regions()[0], &values);

    // ============================================================
    //  Celesta
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[118].get_regions()[0], &values);

    // ============================================================
    //  Clavinet
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[119].get_regions()[0], &values);

    // ============================================================
    //  Harpsichord
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[120].get_regions()[0], &values);

    // ============================================================
    //  E.Piano 2
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[121].get_regions()[0], &values);

    // ============================================================
    //  E.Piano 1
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[122].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[122].get_regions()[1], &values);

    // ============================================================
    //  Honky Tonk
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[123].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[123].get_regions()[1], &values);

    // ============================================================
    //  Piano 3
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[124].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[124].get_regions()[1], &values);

    // ============================================================
    //  Piano 2
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[125].get_regions()[0], &values);

    // ============================================================
    //  Piano 1
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[126].get_regions()[0], &values);

    // ============================================================
    //  BariSax (TB) v2.3
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[127].get_regions()[0], &values);

    // ============================================================
    //  Tenor Sax (TB) v2.3
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[128].get_regions()[0], &values);

    // ============================================================
    //  AltoSax (TB) v2.3
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[129].get_regions()[0], &values);

    // ============================================================
    //  SopSax (TB) v2.3
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[130].get_regions()[0], &values);

    // ============================================================
    //  Choir Aahs
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[131].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[131].get_regions()[1], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[131].get_regions()[2], &values);

    // ============================================================
    //  Synth Strings 1
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[132].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[132].get_regions()[1], &values);

    // ============================================================
    //  Slow Strings LP
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[133].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[133].get_regions()[1], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[133].get_regions()[2], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[133].get_regions()[3], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[133].get_regions()[4], &values);

    // ============================================================
    //  Strings CLP
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[134].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[134].get_regions()[1], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[134].get_regions()[2], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[134].get_regions()[3], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[134].get_regions()[4], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[134].get_regions()[5], &values);

    // ============================================================
    //  Strings (Tremelo)
    // ============================================================
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[135].get_regions()[0], &values);
    let values: [f64; 39] = [
        0_f64, 0_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 0_f64, 1_f64, 1_f64,
        1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 1_f64, 1_f64, 1_f64,
        1_f64, 0_f64, 1_f64, 0_f64, 0_f64, 0_f64, 127_f64, 0_f64, 127_f64, 0_f64, 0_f64, 0_f64,
        0_f64,
    ];
    preset_util::check(&sf.get_presets()[135].get_regions()[1], &values);
}
