[package]
name = "oxisynth"
version = "0.0.5"
edition = "2021"

authors = ["Poly <<EMAIL>>"]
description = "Rust soundfont synthesizer"
keywords = ["audio", "soundfont"]
license = "LGPL-2.1"
repository = "https://github.com/PolyMeilex/oxisynth"
documentation = "https://docs.rs/oxisynth"

[features]
default=["sf3"]
i16-out = ["getrandom", "rand"]
sf3 = ["lewton"]

[dependencies]
oxisynth-chorus = { path = "../oxisynth-chorus" }
oxisynth-reverb = { path = "../oxisynth-reverb" }
soundfont = { path = "../soundfont-rs" }
pianorhythm_proto = { path = "../../proto" }
pianorhythm_shared = { path = "../../shared" }
uuid = { version = "1.7.0", features = ["v4"] }
num-traits = { version = "0.2", features = ["std"] }
log.workspace = true
serde = { workspace = true }

bitflags = "2.4"

# i16-out
getrandom = { version = "0.2", features = ["js"], optional = true }
rand = { version = "0.8.3", optional = true }

lewton = { version = "0.10.2", optional = true }

[target.'cfg(target_arch = "x86_64")'.dependencies]
instant = { version = "0.1.12" }

[target.'cfg(all(target_arch = "wasm32", target_os = "unknown"))'.dependencies]
instant = { version = "0.1.12", features = ["wasm-bindgen", "inaccurate"] }

[dev-dependencies]
env_logger = "0.11"
byte-slice-cast = "1.0.0"
