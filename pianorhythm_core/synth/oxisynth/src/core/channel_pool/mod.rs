mod channel;

use std::sync::Arc;
pub use channel::Channel;

use crate::{OxiError, Preset};

use super::InterpolationMethod;

pub struct ChannelPool(pub Vec<Channel>);

impl ChannelPool {
    pub fn new(len: usize, interpolation: InterpolationMethod) -> Self {
        Self(
            (0..len)
                .map(|id| {
                    let mut ch = Channel::new(id, None, None);
                    ch.set_interp_method(interpolation);
                    ch
                })
                .collect(),
        )
    }

    pub fn get(&self, id: usize) -> Result<&Channel, OxiError> {
        self.0.get(id).ok_or(OxiError::ChannelOutOfRange)
    }

    pub fn get_mut(&mut self, id: usize) -> Result<&mut Channel, OxiError> {
        self.0.get_mut(id).ok_or(OxiError::ChannelOutOfRange)
    }
}

impl ChannelPool {
    pub fn get_by_user_id_and_channel_id(&self, socket_id: u32, channel_id: usize) -> Result<&Channel, OxiError> {
        self.0.iter().find(|channel| channel.is_for_user(socket_id) && channel.id() == channel_id)
            .ok_or(OxiError::NoChannelFoundForUser { channel: channel_id as u8, socket_id })
    }

    pub fn get_mut_by_user_id_and_channel_id(&mut self, socket_id: u32, channel_id: usize) -> Result<&mut Channel, OxiError> {
        self.0.iter_mut().find(|channel| channel.is_for_user(socket_id) && channel.id() == channel_id)
            .ok_or(OxiError::NoChannelFoundForUser { channel: channel_id as u8, socket_id })
    }

    pub fn add_channels_by_user(&mut self, len: usize, socket_id: u32, preset: Option<Arc<Preset>>) {
        let channels: Vec<Channel> = (0..len)
            .map(|id| Channel::new(id, Some(socket_id), preset.clone()))
            .collect();
        self.0.extend(channels);
    }

    pub fn remove_channels_by_user(&mut self, socket_id: u32) {
        self.0.retain(|channel| channel.user_socket_id != Some(socket_id));
    }
}

impl std::ops::Deref for ChannelPool {
    type Target = Vec<Channel>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl std::ops::DerefMut for ChannelPool {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}
