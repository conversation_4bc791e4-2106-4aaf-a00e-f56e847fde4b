use std::ops::RangeInclusive;
use std::sync::Arc;

use uuid::Uuid;

use pianorhythm_proto::midi_renditions::AudioChannel;

use crate::arena::Index;
use crate::constants::piano_rhythm_midi_control_change;
use crate::core::InterpolationMethod;
use crate::GeneratorType;
use crate::midi_event::ControlFunction;
use crate::Tuning;

use super::super::soundfont::{Preset, SoundFont};

#[derive(Clone)]
struct CcList([u8; 128]);

impl std::ops::Index<ControlFunction> for CcList {
    type Output = u8;

    fn index(&self, index: ControlFunction) -> &Self::Output {
        &self.0[index as usize]
    }
}

impl std::ops::IndexMut<ControlFunction> for CcList {
    fn index_mut(&mut self, index: ControlFunction) -> &mut Self::Output {
        &mut self.0[index as usize]
    }
}

impl std::ops::Index<RangeInclusive<ControlFunction>> for CcList {
    type Output = [u8];

    fn index(&self, index: RangeInclusive<ControlFunction>) -> &Self::Output {
        let start = *index.start() as usize;
        let end = *index.end() as usize;
        &self.0[start..=end]
    }
}

impl std::ops::IndexMut<RangeInclusive<ControlFunction>> for CcList {
    fn index_mut(&mut self, index: RangeInclusive<ControlFunction>) -> &mut Self::Output {
        let start = *index.start() as usize;
        let end = *index.end() as usize;
        &mut self.0[start..=end]
    }
}

#[derive(Clone)]
pub struct Channel {
    id: usize,
    pub id_guid: String,
    pub user_socket_id: Option<u32>,
    active: bool,
    is_percussion_channel: bool,
    audio_channel: AudioChannel,

    sfontnum: Option<Index<SoundFont>>,

    banknum: u32,
    prognum: u8,

    preset: Option<Arc<Preset>>,

    key_pressure: [i8; 128],
    channel_pressure: u8,

    pitch_bend: u16,
    pitch_wheel_sensitivity: u8,

    cc: CcList,
    bank_msb: u8,

    interp_method: InterpolationMethod,
    tuning: Option<Tuning>,

    nrpn_select: i16,
    nrpn_active: i16,

    gen: [f32; 60],
    gen_abs: [i8; 60],
}

impl Channel {
    pub fn new(id: usize, user_socket_id: Option<u32>, preset: Option<Arc<Preset>>) -> Self {
        let is_percussion_channel = id == crate::DRUM_CHANNEL;

        let mut chan = Self {
            id,
            id_guid: Uuid::new_v4().to_string(),
            user_socket_id,
            is_percussion_channel,
            audio_channel: AudioChannel::new(),
            active: false,
            sfontnum: None,
            banknum: if is_percussion_channel { 128 } else { 0 },
            prognum: 0,

            preset,

            key_pressure: [0; 128],
            channel_pressure: 0,

            pitch_bend: 0,
            pitch_wheel_sensitivity: 0,

            cc: CcList([0; 128]),
            bank_msb: 0,

            interp_method: InterpolationMethod::default(),
            tuning: None,

            nrpn_select: 0,
            nrpn_active: 0,

            gen: [0f32; 60],
            gen_abs: [0; 60],
        };
        chan.init_ctrl(false);
        chan
    }

    pub fn init(&mut self, preset: Option<Arc<Preset>>) {
        self.prognum = 0;
        self.banknum = 0;
        self.sfontnum = None;

        self.preset = preset;
        self.interp_method = Default::default();
        self.tuning = None;
        self.nrpn_select = 0;
        self.nrpn_active = 0;
    }

    pub fn init_ctrl(&mut self, is_all_ctrl_off: bool) {
        self.channel_pressure = 0;
        self.pitch_bend = 0x2000;

        self.gen.fill(0.0);
        self.gen_abs.fill(0);

        if is_all_ctrl_off {
            for i in ControlFunction::iter_range(ControlFunction::MIN..ControlFunction::AllSoundOff)
                .filter(|i| !i.is_effects_n_depth())
                .filter(|i| !i.is_sound_controller_n())
                .filter(|i| {
                    !matches!(
                        i,
                        ControlFunction::BankSelect | ControlFunction::BankSelectLsb
                    )
                })
                .filter(|i| {
                    !matches!(
                        i,
                        ControlFunction::ChannelVolume | ControlFunction::ChannelVolumeLsb
                    )
                })
                .filter(|i| !matches!(i, ControlFunction::Pan | ControlFunction::PanLsb))
            {
                self.cc[i] = 0;
            }
        } else {
            self.cc.0.fill(0);
        }

        self.key_pressure.fill(0);

        self.cc[ControlFunction::RegisteredParameterNumberLsb] = 127;
        self.cc[ControlFunction::RegisteredParameterNumberMsb] = 127;
        self.cc[ControlFunction::NonRegisteredParameterNumberLsb] = 127;
        self.cc[ControlFunction::NonRegisteredParameterNumberMsb] = 127;
        self.cc[ControlFunction::ExpressionController] = 127;
        self.cc[ControlFunction::ExpressionControllerLsb] = 127;

        if !is_all_ctrl_off {
            self.pitch_wheel_sensitivity = 2;

            self.cc[ControlFunction::SoundController1..=ControlFunction::SoundController10]
                .fill(64);
            self.cc[ControlFunction::ChannelVolume] = 100;
            self.cc[ControlFunction::ChannelVolumeLsb] = 0;
            self.cc[ControlFunction::Pan] = 64;
            self.cc[ControlFunction::PanLsb] = 0;
        };

        self.update_audio_channel();
    }
}

impl Channel {
    pub fn id(&self) -> usize {
        self.id
    }

    pub fn sfontnum(&self) -> Option<Index<SoundFont>> {
        self.sfontnum
    }

    pub fn set_sfontnum(&mut self, sfontnum: Option<Index<SoundFont>>) {
        self.sfontnum = sfontnum;
    }

    pub fn banknum(&self) -> u32 {
        self.banknum
    }

    pub fn set_banknum(&mut self, banknum: u32) {
        self.banknum = banknum;
        self.audio_channel.set_bank(self.banknum);
    }

    pub fn prognum(&self) -> u8 {
        self.prognum
    }

    pub fn set_prognum(&mut self, prognum: u8) {
        self.prognum = prognum;
        self.audio_channel.set_preset(self.prognum.into());
    }

    pub fn preset(&self) -> Option<&Arc<Preset>> {
        self.preset.as_ref()
    }

    pub fn set_preset(&mut self, preset: Option<Arc<Preset>>) {
        self.preset = preset;
        if self.preset.is_none() {
            self.set_active_raw(false);
        }
        self.update_audio_channel_instrument();
    }

    pub fn key_pressure(&self, id: usize) -> i8 {
        self.key_pressure[id]
    }

    pub fn set_key_pressure(&mut self, id: usize, val: i8) {
        self.key_pressure[id] = val;
    }

    pub fn channel_pressure(&self) -> u8 {
        self.channel_pressure
    }

    pub fn set_channel_pressure(&mut self, val: u8) {
        self.channel_pressure = val;
        self.audio_channel.set_channel_pressure(self.channel_pressure.into());
    }

    pub fn pitch_bend(&self) -> u16 {
        self.pitch_bend
    }

    pub fn set_pitch_bend(&mut self, val: u16) {
        self.pitch_bend = val;
        self.audio_channel.set_pitch_bend(self.pitch_bend.into());
    }

    pub fn pitch_wheel_sensitivity(&self) -> u8 {
        self.pitch_wheel_sensitivity
    }

    pub fn set_pitch_wheel_sensitivity(&mut self, val: u8) {
        self.pitch_wheel_sensitivity = val;
        self.audio_channel.set_pitch_wheel_sensitivity(self.pitch_wheel_sensitivity.into());
    }

    pub fn cc(&self, id: usize) -> u8 {
        self.cc.0.get(id).copied().unwrap_or(0)
    }

    pub fn cc_mut(&mut self, id: usize) -> &mut u8 {
        &mut self.cc.0[id]
    }

    pub fn bank_msb(&self) -> u8 {
        self.bank_msb
    }

    pub fn set_bank_msb(&mut self, val: u8) {
        self.bank_msb = val;
        self.audio_channel.set_bank_msb(self.bank_msb.into());
    }

    pub fn interp_method(&self) -> InterpolationMethod {
        self.interp_method
    }

    pub fn set_interp_method(&mut self, new_method: InterpolationMethod) {
        self.interp_method = new_method;
    }

    pub fn tuning(&self) -> Option<&Tuning> {
        self.tuning.as_ref()
    }

    pub fn set_tuning(&mut self, val: Option<Tuning>) {
        self.tuning = val;
    }

    pub fn nrpn_select(&self) -> i16 {
        self.nrpn_select
    }

    pub fn set_nrpn_select(&mut self, value: i16) {
        self.nrpn_select = value;
    }

    pub fn nrpn_active(&self) -> i16 {
        self.nrpn_active
    }

    pub fn set_nrpn_active(&mut self, value: i16) {
        self.nrpn_active = value;
    }

    /// Retrieve the value of a generator. This function returns the value
    /// set by a previous call 'set_gen()' or by an NRPN message.
    ///
    /// Returns the value of the generator.
    pub fn gen(&self, id: GeneratorType) -> f32 {
        self.gen[id as usize]
    }

    pub fn set_gen(&mut self, id: GeneratorType, val: f32) {
        self.gen[id as usize] = val;
    }

    pub fn gen_abs(&self, id: GeneratorType) -> i8 {
        self.gen_abs[id as usize]
    }

    pub fn set_gen_abs(&mut self, id: GeneratorType, val: i8) {
        self.gen_abs[id as usize] = val;
    }
}

// Custom impl related to PianoRhythm
impl Channel {
    pub fn active(&self) -> bool {
        self.active
    }

    fn set_active_raw(&mut self, val: bool) {
        self.active = val;
        self.audio_channel.set_active(self.active);
    }

    pub fn set_active(&mut self, val: bool) {
        if self.preset.is_none() && val {
            return;
        }

        self.set_active_raw(val);
    }

    pub fn get_audio_channel(&self) -> &AudioChannel {
        &self.audio_channel
    }

    pub fn is_for_user(&self, socket_id: u32) -> bool {
        self.user_socket_id == Some(socket_id)
    }

    fn update_audio_channel_instrument(&mut self) {
        match self.preset() {
            None => self.audio_channel.clear_instrument(),
            Some(preset) => {
                let mut instrument =
                    if self.audio_channel.has_instrument() {
                        self.audio_channel.get_instrument().to_owned()
                    } else {
                        pianorhythm_proto::midi_renditions::Instrument::new()
                    };

                let bank_num = preset.banknum();
                instrument.set_bank(bank_num);
                instrument.set_preset(preset.num());
                let preset_name = preset.name().to_string();
                instrument.set_name(preset_name.clone());
                instrument.set_display_name(preset_name);
                instrument.set_is_drum_kit(bank_num == 120 || bank_num == 128);
                self.audio_channel.set_instrument(instrument);
            }
        }
    }

    pub fn update_audio_channel_after_cc(&mut self) {
        self.audio_channel.set_pan(self.cc(piano_rhythm_midi_control_change::PAN_MSB.into()).into());
        self.audio_channel.set_volume(self.cc(piano_rhythm_midi_control_change::MAIN_VOLUME_MSB.into()).into());
        self.audio_channel.set_expression(self.cc(piano_rhythm_midi_control_change::EXPRESSION_CONTROLLER_MSB.into()).into());
    }

    fn update_audio_channel(&mut self) {
        self.audio_channel.set_channel(self.id() as u32);
        self.update_audio_channel_instrument();
        self.update_audio_channel_after_cc();

        self.audio_channel.set_active(self.active);
        self.audio_channel.set_bank(self.banknum);
        self.audio_channel.set_bank_msb(self.bank_msb.into());
        self.audio_channel.set_preset(self.prognum.into());
        self.audio_channel.set_pitch_bend(self.pitch_bend.into());
        self.audio_channel.set_channel_pressure(self.channel_pressure.into());
        self.audio_channel.set_pitch_wheel_sensitivity(self.pitch_wheel_sensitivity.into());

        // #[cfg(target_arch = "wasm32")]
        // console_log!("Audio Channel Update: {:?}", self.audio_channel);
    }
}