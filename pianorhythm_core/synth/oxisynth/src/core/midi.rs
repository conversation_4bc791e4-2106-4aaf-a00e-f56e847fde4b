use pianorhythm_shared::midi::DRUM_CHANNEL;
use soundfont::raw::GeneralPalette;

use crate::{DEFAULT_PERCUSSION_KIT_BANK, DEFAULT_PERCUSSION_KIT_BANK2, Index, MAX_MIDI_CHANNEL, MidiEvent, OxiError, PianoRhythmSocketUser, SoundFont};
use crate::core::channel_pool::Channel;
use crate::core::font_bank::FontBank;
use crate::core::soundfont::{
    generator::{gen_scale_nrpn, GeneratorType},
    InstrumentZone, PresetZone,
};
use crate::core::soundfont::modulator::Mod;
use crate::core::voice_pool::{ModulateCtrl, Voice, VoiceAddMode, VoiceDescriptor, VoicePool};
use crate::midi_event::ControlFunction;

use super::Core;

pub(crate) fn handle_event(synth: &mut Core, event: Midi<PERSON>vent, user: &mut PianoRhythmSocketUser) -> Result<(), OxiError> {
    match event.check()? {
        MidiEvent::NoteOn { channel, key, vel } => {
            if channel as usize > MAX_MIDI_CHANNEL {
                return Err(OxiError::ChannelOutOfRange);
            }

            let gain = if user.is_client {
                synth.settings.gain
            } else {
                map_f32(user.gain, 0.0, 1.0, 0.0, synth.settings.gain)
            };

            if let Ok(user_channel) = synth.channels.get_by_user_id_and_channel_id(user.socket_id, channel as usize) {
                _ = self::noteon(
                    user_channel,
                    &mut synth.voices,
                    synth.ticks,
                    synth.settings.min_note_length_ticks,
                    gain,
                    key,
                    vel,
                    Some(user.socket_id),
                    synth.max_note_on_time,
                );
            }
        }
        MidiEvent::NoteOff { channel, key } => {
            if channel as usize > MAX_MIDI_CHANNEL {
                return Err(OxiError::ChannelOutOfRange);
            }

            self::noteoff_by_socket_id(
                &user.socket_id,
                synth.channels.get_by_user_id_and_channel_id(user.socket_id, channel as usize)?,
                &mut synth.voices,
                synth.min_note_length_ticks,
                key,
            );
        }
        MidiEvent::ControlChange {
            channel,
            ctrl,
            value,
        } => {
            if channel as usize > MAX_MIDI_CHANNEL {
                return Err(OxiError::ChannelOutOfRange);
            }

            self::cc(
                &user.socket_id,
                synth.channels.get_mut_by_user_id_and_channel_id(user.socket_id, channel as usize)?,
                &mut synth.voices,
                synth.settings.min_note_length_ticks,
                synth.settings.drums_channel_active,
                ctrl,
                value,
            );
        }
        MidiEvent::AllNotesOff { channel } => {
            if channel as usize > MAX_MIDI_CHANNEL {
                return Err(OxiError::ChannelOutOfRange);
            }

            synth.voices.all_notes_off_by_socket_id(
                &user.socket_id,
                synth.channels.get_by_user_id_and_channel_id(user.socket_id, channel as usize)?,
                synth.settings.min_note_length_ticks,
            );
        }
        MidiEvent::AllSoundOff { channel } => {
            if channel as usize > MAX_MIDI_CHANNEL {
                return Err(OxiError::ChannelOutOfRange);
            }

            synth.voices.all_sounds_off_by_socket_id(
                &user.socket_id,
                channel as usize,
            );
        }
        MidiEvent::PitchBend { channel, value } => {
            if channel as usize > MAX_MIDI_CHANNEL {
                return Err(OxiError::ChannelOutOfRange);
            }

            let channel = synth.channels.get_mut_by_user_id_and_channel_id(user.socket_id, channel as usize)?;

            channel.set_pitch_bend(value);
            synth
                .voices
                .modulate_voices(
                    channel,
                    ModulateCtrl::SF(GeneralPalette::PitchWheel),
                );
        }
        MidiEvent::ProgramChange {
            channel,
            program_id,
        } => {
            if channel as usize > MAX_MIDI_CHANNEL {
                return Err(OxiError::ChannelOutOfRange);
            }

            self::program_change(
                synth.channels.get_mut_by_user_id_and_channel_id(user.socket_id, channel as usize)?,
                &synth.font_bank,
                program_id,
                synth.settings.drums_channel_active,
            );
        }
        MidiEvent::ChannelPressure { channel, value } => {
            if channel as usize > MAX_MIDI_CHANNEL {
                return Err(OxiError::ChannelOutOfRange);
            }

            let channel = synth.channels.get_mut_by_user_id_and_channel_id(user.socket_id, channel as usize)?;

            channel.set_channel_pressure(value);
            synth
                .voices
                .modulate_voices_by_socket_id(
                    &user.socket_id,
                    channel,
                    ModulateCtrl::SF(GeneralPalette::ChannelPressure),
                );
        }
        MidiEvent::PolyphonicKeyPressure {
            channel,
            key,
            value,
        } => {
            if channel as usize > MAX_MIDI_CHANNEL {
                return Err(OxiError::ChannelOutOfRange);
            }

            let channel = synth.channels.get_mut_by_user_id_and_channel_id(user.socket_id, channel as usize)?;
            channel.set_key_pressure(key as usize, value as i8);
            synth.voices.key_pressure(channel, key);
        }
        MidiEvent::SystemReset => {
            synth.voices.system_reset_by_socket_id(&user.socket_id);

            let preset = synth.font_bank.find_preset(0, 0).map(|p| p.1);
            for channel in synth.channels.iter_mut().filter(|channel| channel.is_for_user(user.socket_id)) {
                channel.init(preset.clone());
                channel.init_ctrl(false);
            }

            if user.socket_id == pianorhythm_shared::SYNTH_CONSTS::ID::MIDI_SYNTH_SOCKET_ID {
                synth.chorus.reset();
                synth.reverb.reset();
            }
        }
        _ => {}
    };

    Ok(())
}

type MidiControlChange = u32;

const RPN_MSB: MidiControlChange = 101;
const RPN_LSB: MidiControlChange = 100;
const NRPN_MSB: MidiControlChange = 99;
const NRPN_LSB: MidiControlChange = 98;
const DATA_ENTRY_LSB: MidiControlChange = 38;

/// Change the value of a generator. This function allows to control
/// all synthesis parameters in real-time. The changes are additive,
/// i.e. they add up to the existing parameter value. This function is
/// similar to sending an NRPN message to the synthesizer. The
/// function accepts a float as the value of the parameter. The
/// parameter numbers and ranges are described in the SoundFont 2.01
/// specification, paragraph 8.1.3, page 48.
pub(crate) fn set_gen(
    channel: &mut Channel,
    voices: &mut VoicePool,
    param: GeneratorType,
    value: f32,
) {
    channel.set_gen(param, value);
    channel.set_gen_abs(param, 0);

    voices.set_gen(channel.id(), param, value);
}

/// Send a noteon message.
fn noteon(
    channel: &Channel,
    voices: &mut VoicePool,
    start_time: usize,
    min_note_length_ticks: usize,
    gain: f32,
    key: u8,
    vel: u8,
    socket_id: Option<u32>,
    max_note_on_time: Option<f64>,
) -> Result<(), OxiError> {
    if !channel.active() && channel.preset().is_some() {
        Err(OxiError::ChannelNotActive {
            channel: channel.id() as u8,
        })
    } else if vel == 0 {
        if let Some(socket_id) = socket_id {
            voices.noteoff_by_socket_id(&socket_id, channel, min_note_length_ticks, key);
        } else {
            voices.noteoff(channel, min_note_length_ticks, key);
        }
        Ok(())
    } else if channel.preset().is_none() {
        Err(OxiError::ChannelHasNoPreset {
            channel: channel.id() as u8,
        })
    } else {
        if let Some(socket_id) = socket_id {
            voices.release_voice_on_same_note_by_socket_id(
                &socket_id,
                channel,
                key,
                min_note_length_ticks,
            );
        } else {
            voices.release_voice_on_same_note(channel, key, min_note_length_ticks);
        }
        voices.noteid_add();

        inner_noteon(channel, voices, start_time, gain, key, vel, max_note_on_time, socket_id);
        Ok(())
    }
}

fn inner_noteon(
    channel: &Channel,
    voices: &mut VoicePool,
    start_time: usize,
    gain: f32,
    key: u8,
    vel: u8,
    max_note_on_time: Option<f64>,
    socket_id: Option<u32>,
) {
    fn preset_zone_inside_range(zone: &PresetZone, key: u8, vel: u8) -> bool {
        zone.key_low <= key && zone.key_high >= key && zone.vel_low <= vel && zone.vel_high >= vel
    }

    fn inst_zone_inside_range(zone: &InstrumentZone, key: u8, vel: u8) -> bool {
        zone.key_low <= key && zone.key_high >= key && zone.vel_low <= vel && zone.vel_high >= vel
    }

    let preset = &channel.preset().unwrap();

    // list for 'sorting' preset modulators
    let mod_list_new: Vec<Option<&Mod>> = (0..64).map(|_| None).collect();
    let mut mod_list: [Option<&Mod>; 64] = mod_list_new.try_into().unwrap();

    let mut global_preset_zone = preset.global_zone();

    // run thru all the zones of this preset
    for preset_zone in preset.zones().iter() {
        // check if the note falls into the key and velocity range of this preset
        if !preset_zone_inside_range(preset_zone, key, vel) {
            continue;
        }

        let Some(inst) = preset_zone.inst.as_ref() else {
            log::error!("Instrument for zone: {:?} is missing", preset_zone.name);
            continue;
        };

        let mut global_inst_zone = &inst.global_zone();

        // run thru all the zones of this instrument
        for inst_zone in inst.zones().iter() {
            let Some(sample) = inst_zone.sample.as_ref() else {
                continue;
            };

            if sample.sample_type().is_rom() {
                continue;
            }

            // check if the note falls into the key and velocity range of this instrument
            if !inst_zone_inside_range(inst_zone, key, vel) {
                continue;
            }

            // this is a good zone. allocate a new synthesis process and initialize it

            // Initialize Voice
            let init = |voice: &mut Voice| {
                voice.add_default_mods();

                // Instrument level, generators
                for gen in GeneratorType::iter() {
                    // SF 2.01 section 9.4 'bullet' 4:
                    //
                    // A generator in a local instrument zone supersedes a
                    // global instrument zone generator.  Both cases supersede
                    // the default generator -> voice_gen_set
                    if inst_zone.gen[gen].flags != 0 {
                        voice.gen_set(gen, inst_zone.gen[gen].val);
                    } else if let Some(global_inst_zone) = &global_inst_zone {
                        if global_inst_zone.gen[gen].flags as i32 != 0 {
                            voice.gen_set(gen, global_inst_zone.gen[gen].val);
                        }
                    } else {
                        // The generator has not been defined in this instrument.
                        // Do nothing, leave it at the default.
                    }
                }

                // global instrument zone, modulators: Put them all into a
                // list.
                let mut mod_list_count = 0;
                if let Some(global_inst_zone) = &mut global_inst_zone {
                    for m in global_inst_zone.mods.iter() {
                        mod_list[mod_list_count] = Some(m);
                        mod_list_count += 1;
                    }
                }

                // local instrument zone, modulators.
                // Replace modulators with the same definition in the list:
                // SF 2.01 page 69, 'bullet' 8
                for m in inst_zone.mods.iter() {
                    // 'Identical' modulators will be deleted by setting their
                    //  list entry to None. The list length is known, None
                    //  entries will be ignored later. SF2.01 section 9.5.1
                    //  page 69, 'bullet' 3 defines 'identical'.
                    mod_list
                        .iter_mut()
                        .take(mod_list_count)
                        .filter(|modulator| {
                            modulator
                                .as_ref()
                                .map(|modulator| m.test_identity(modulator))
                                .unwrap_or(false)
                        })
                        .for_each(|modulator| {
                            *modulator = None;
                        });

                    // Finally add the new modulator to to the list.
                    mod_list[mod_list_count] = Some(m);
                    mod_list_count += 1;
                }

                // Add instrument modulators (global / local) to the voice.
                mod_list
                    .iter()
                    .take(mod_list_count)
                    .flatten()
                    .for_each(|modulator| {
                        // disabled modulators CANNOT be skipped.

                        // Instrument modulators -supersede- existing (default)
                        // modulators.  SF 2.01 page 69, 'bullet' 6
                        voice.add_mod(modulator, VoiceAddMode::Overwrite);
                    });

                // Preset level, generators
                for gen in GeneratorType::iter() {
                    // SF 2.01 section 8.5 page 58: If some generators are
                    // encountered at preset level, they should be ignored
                    if matches!(
                        gen,
                        GeneratorType::StartAddrOfs
                            | GeneratorType::EndAddrOfs
                            | GeneratorType::StartLoopAddrOfs
                            | GeneratorType::EndLoopAddrOfs
                            | GeneratorType::StartAddrCoarseOfs
                            | GeneratorType::EndAddrCoarseOfs
                            | GeneratorType::StartLoopAddrCoarseOfs
                            | GeneratorType::KeyNum
                            | GeneratorType::Velocity
                            | GeneratorType::EndLoopAddrCoarseOfs
                            | GeneratorType::SampleMode
                            | GeneratorType::ExclusiveClass
                            | GeneratorType::OverrideRootKey
                    ) {
                        continue;
                    }

                    // SF 2.01 section 9.4 'bullet' 9: A generator in a
                    // local preset zone supersedes a global preset zone
                    // generator.  The effect is -added- to the destination
                    // summing node -> voice_gen_incr
                    if preset_zone.gen[gen].flags != 0 {
                        voice.gen_incr(gen, preset_zone.gen[gen].val);
                    } else if let Some(global_preset_zone) = &global_preset_zone {
                        if global_preset_zone.gen[gen].flags != 0 {
                            voice.gen_incr(gen, global_preset_zone.gen[gen].val);
                        }
                    } else {
                        // The generator has not been defined in this preset
                        // Do nothing, leave it unchanged.
                    }
                }

                // Global preset zone, modulators: put them all into a list.
                let mut mod_list_count = 0;
                if let Some(global_preset_zone) = &mut global_preset_zone {
                    for m in global_preset_zone.mods.iter() {
                        mod_list[mod_list_count] = Some(m);
                        mod_list_count += 1;
                    }
                }

                // Process the modulators of the local preset zone. Kick
                // out all identical modulators from the global preset zone
                // (SF 2.01 page 69, second-last bullet)
                for m in preset_zone.mods.iter() {
                    mod_list
                        .iter_mut()
                        .take(mod_list_count)
                        .filter(|modulator| {
                            modulator
                                .as_ref()
                                .map(|modulator| m.test_identity(modulator))
                                .unwrap_or(false)
                        })
                        .for_each(|modulator| {
                            *modulator = None;
                        });

                    // Finally add the new modulator to the list.
                    mod_list[mod_list_count] = Some(m);
                    mod_list_count += 1;
                }

                // Add preset modulators (global / local) to the voice.
                mod_list
                    .iter()
                    .take(mod_list_count)
                    .flatten()
                    // disabled modulators can be skipped.
                    .filter(|m| m.amount != 0.0)
                    .for_each(|m| {
                        // Preset modulators -add- to existing instrument
                        // default modulators. SF2.01 page 70 first bullet on
                        // page
                        voice.add_mod(m, VoiceAddMode::Add);
                    });

                // Store the ID of the first voice that was created by this noteon event.
                // Exclusive class may only terminate older voices.
                // That avoids killing voices, which have just been created.
                // (a noteon event can create several voice processes with the same exclusive
                // class - for example when using stereo samples)
            };

            let desc = VoiceDescriptor {
                sample: sample.clone(),
                channel,
                key,
                vel,
                start_time,
                gain,
                socket_id,
                max_note_on_time,
            };

            _ = voices.request_new_voice(desc, init);
        }
    }
}

/// Send a control change message.
fn cc(
    socket_id: &u32,
    channel: &mut Channel,
    voices: &mut VoicePool,
    min_note_length_ticks: usize,
    drums_channel_active: bool,
    num: u8,
    value: u8,
) {
    let Some(num) = ControlFunction::const_try_from(num) else {
        return;
    };

    *channel.cc_mut(num as usize) = value;

    use ControlFunction::*;
    // PianoRhythm
    match num {
        Pan | ExpressionController | ChannelVolume => {
            channel.update_audio_channel_after_cc();
        }
        _ => {}
    }

    match num {
        // Sustain
        DamperPedal => {
            if value < 64 {
                // sustain off
                voices.damp_voices_by_socket_id(socket_id, channel, min_note_length_ticks)
            } else {
                // sustain on
            }
        }

        BankSelect => {
            if channel.id() == 9 && drums_channel_active {
                // ignored
                return;
            }

            channel.set_bank_msb(value & 0x7f);

            // I fixed the handling of a MIDI bank select controller 0,
            // e.g., bank select MSB (or "coarse" bank select according to
            // my spec).  Prior to this fix a channel's bank number was only
            // changed upon reception of MIDI bank select controller 32,
            // e.g, bank select LSB (or "fine" bank-select according to my
            // spec). [KLE]
            // FIXME: is this correct? [PH]
            channel.set_banknum((value & 0x7f) as u32);
        }

        BankSelectLsb => {
            if channel.id() == 9 && drums_channel_active {
                // ignored
                return;
            }

            // FIXME: according to the Downloadable Sounds II specification,
            // bit 31 should be set when we receive the message on channel
            // 10 (drum channel)
            channel
                .set_banknum((value as u32 & 0x7f).wrapping_add((channel.bank_msb() as u32) << 7));
        }

        AllNotesOff => {
            voices.all_notes_off_by_socket_id(socket_id, channel, min_note_length_ticks);
        }

        AllSoundOff => {
            voices.all_sounds_off_by_socket_id(socket_id, channel.id());
        }

        ResetAllControllers => {
            channel.init_ctrl(true);
            voices.modulate_voices_all_by_socket_id(socket_id, channel);
        }

        DataEntryMsb => {
            let data: i32 = ((value as i32) << 7) + channel.cc(DATA_ENTRY_LSB as usize) as i32;

            if channel.nrpn_active() != 0 {
                let (nrpn_select, nrpn_msb, nrpn_lsb) = (
                    channel.nrpn_select(),
                    channel.cc(NRPN_MSB as usize),
                    channel.cc(NRPN_LSB as usize),
                );

                // SontFont 2.01 NRPN Message (Sect. 9.6, p. 74)
                if nrpn_msb == 120 && nrpn_lsb < 100 {
                    if (nrpn_select as i32) < GeneratorType::last() as i32 {
                        let scale_nrpn: f32 = gen_scale_nrpn(nrpn_select, data);

                        let param = GeneratorType::try_from(nrpn_select as u8).unwrap();
                        set_gen(channel, voices, param, scale_nrpn)
                    }

                    channel.set_nrpn_select(0); // Reset to 0
                }
            }
            // RPN is active: MSB = 0?
            else if channel.cc(RPN_MSB as usize) == 0 {
                match channel.cc(RPN_LSB as usize) {
                    // RPN_PITCH_BEND_RANGE
                    0 => pitch_wheel_sens(channel, voices, value),
                    // RPN_CHANNEL_FINE_TUNE
                    1 => {
                        // Fine tune is 14 bit over +/-1 semitone (+/- 100 cents, 8192 = center)
                        set_gen(
                            channel,
                            voices,
                            GeneratorType::FineTune,
                            ((data - 8192) as f64 / 8192.0f64 * 100.0f64) as f32,
                        );
                    }
                    // RPN_CHANNEL_COARSE_TUNE
                    2 => {
                        // Coarse tune is 7 bit and in semitones (64 is center)
                        set_gen(
                            channel,
                            voices,
                            GeneratorType::CoarseTune,
                            (value - 64) as f32,
                        );
                    }
                    // TODO: This is fishy, for some reason those are missing from FluidLite, but
                    // are present in Fluidsynth
                    // https://github.com/FluidSynth/fluidsynth/blob/fa5173cbaefed60121db057bad7be7686165f7cc/src/synth/fluid_synth.c#L1857

                    // RPN_TUNING_PROGRAM_CHANGE | RPN_TUNING_BANK_SELECT | RPN_MODULATION_DEPTH_RANGE
                    // 3 | 4 | 5 => {}
                    _ => {}
                }
            }
        }

        NonRegisteredParameterNumberMsb => {
            *channel.cc_mut(NRPN_LSB as usize) = 0;
            channel.set_nrpn_select(0);
            channel.set_nrpn_active(1);
        }

        NonRegisteredParameterNumberLsb => {
            // SontFont 2.01 NRPN Message (Sect. 9.6, p. 74)
            if channel.cc(NRPN_MSB as usize) == 120 {
                if value == 100 {
                    channel.set_nrpn_select(channel.nrpn_select() + 100);
                } else if value == 101 {
                    channel.set_nrpn_select(channel.nrpn_select() + 1000);
                } else if value == 102 {
                    channel.set_nrpn_select(channel.nrpn_select() + 10000);
                } else if value < 100 {
                    channel.set_nrpn_select(channel.nrpn_select() + value as i16);
                }
            }
            channel.set_nrpn_active(1);
        }

        RegisteredParameterNumberMsb | RegisteredParameterNumberLsb => channel.set_nrpn_active(0),
        _ => voices.modulate_voices_by_socket_id(socket_id, channel, ModulateCtrl::CC(num)),
    }
}

/// Set the pitch wheel sensitivity.
pub(crate) fn pitch_wheel_sens(channel: &mut Channel, voices: &mut VoicePool, val: u8) {
    channel.set_pitch_wheel_sensitivity(val);
    voices.modulate_voices(
        channel,
        ModulateCtrl::SF(GeneralPalette::PitchWheelSensitivity),
    );
}

/// Send a program change message.
pub(crate) fn program_change(
    channel: &mut Channel,
    font_bank: &FontBank,
    program_id: u8,
    drums_channel_active: bool,
) {
    let mut banknum = channel.banknum();
    // channel.set_prognum(program_id);

    let mut preset = if channel.id() == DRUM_CHANNEL.into() && drums_channel_active {
        font_bank.find_preset(DEFAULT_PERCUSSION_KIT_BANK as u32, program_id)
    } else {
        font_bank.find_preset(banknum, program_id)
    };

    if channel.id() == DRUM_CHANNEL.into() && drums_channel_active && preset.is_some() {
        banknum = DEFAULT_PERCUSSION_KIT_BANK as u32;
    }

    if preset.is_none() {
        if banknum != DEFAULT_PERCUSSION_KIT_BANK as u32 {
            // Check default bank
            if channel.id() != DRUM_CHANNEL.into() && preset.is_none() {
                preset = font_bank.find_preset(0, program_id);
                if (preset.is_some()) {
                    banknum = 0;
                }
            }

            // Check for other percussion bank
            if preset.is_none() {
                preset = font_bank.find_preset(120, program_id);

                if channel.id() == DRUM_CHANNEL.into() && drums_channel_active && preset.is_some() {
                    banknum = DEFAULT_PERCUSSION_KIT_BANK2 as u32;
                }
            }
        } else {
            preset = font_bank.find_preset(DEFAULT_PERCUSSION_KIT_BANK as u32, 0);
        }
    }

    let is_preset_none = preset.is_none();

    if channel.banknum() != banknum || channel.prognum() != program_id || channel.preset().is_none() && !is_preset_none {
        channel.set_banknum(banknum);
        channel.set_prognum(program_id);
        channel.set_sfontnum(preset.as_ref().map(|p| p.0));
        channel.set_preset(preset.map(|p| p.1));
        channel.set_active(true);
    } else if is_preset_none {
        channel.set_sfontnum(None);
        channel.set_preset(None);
        channel.set_active(false);
    }

    // channel.set_sfontnum(preset.as_ref().map(|p| p.0));
    // channel.set_preset(preset.map(|p| p.1));
}

fn map_f32(value: f32, low1: f32, high1: f32, low2: f32, high2: f32) -> f32 {
    return low2 + (value - low1) * (high2 - low2) / (high1 - low1);
}

// -----------------------------------

/**
Returns the program, bank, and SoundFont number of the preset on a given channel.
 */
pub fn get_program(channel: &Channel) -> (Option<Index<SoundFont>>, u32, u8) {
    (channel.sfontnum(), channel.banknum(), channel.prognum())
}

/**
Get a control value.
 */
pub fn get_cc(channel: &Channel, num: u16) -> u8 {
    channel.cc(num as usize)
}

/**
Select a bank.
 */
pub fn bank_select(channel: &mut Channel, bank: u32) {
    channel.set_banknum(bank);
}

pub fn noteoff_by_socket_id(
    socket_id: &u32,
    channel: &Channel,
    voices: &mut VoicePool,
    min_note_length_ticks: usize,
    key: u8,
) {
    voices.noteoff_by_socket_id(socket_id, channel, min_note_length_ticks, key);
}