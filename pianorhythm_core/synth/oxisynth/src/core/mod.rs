pub mod midi;
pub(crate) mod write;

pub mod channel_pool;
pub use channel_pool::Channel;
pub(crate) mod settings;
mod voice_pool;

mod conv;
pub use settings::InterpolationMethod;
pub(crate) use settings::Settings;

pub mod font_bank;

pub use oxisynth_chorus::Chorus;
pub use oxisynth_reverb::Reverb;

mod soundfont;
pub use soundfont::{generator::GeneratorType, Preset, SoundFont};

pub use voice_pool::VoicePool;
use write::OutputBuffer;

use self::channel_pool::ChannelPool;
use self::font_bank::FontBank;

use crate::{SettingsError, SynthDescriptor};

const BUFSIZE: usize = 64;
const BUFSIZE_F32: f32 = BUFSIZE as f32;

pub struct Core {
    ticks: usize,
    pub font_bank: FontBank,

    pub channels: ChannelPool,
    pub voices: VoicePool,

    pub reverb: Reverb,
    pub chorus: Chorus,

    pub settings: Settings,

    output: OutputBuffer,

    /// The minimum note length (in ticks) before it can be set to a note off state
    min_note_length_ticks: usize,
    pub max_note_on_time: Option<f64>,
}

impl Default for Core {
    fn default() -> Self {
        Self::new(Default::default()).unwrap()
    }
}

impl Core {
    pub fn new(desc: SynthDescriptor) -> Result<Self, SettingsError> {
        let settings: Settings = desc.try_into()?;

        let nbuf = if settings.audio_groups > settings.audio_channels {
            settings.audio_groups
        } else {
            settings.audio_channels
        };

        let min_note_length_ticks = (settings.min_note_length as f32 * settings.sample_rate / 1000.0) as usize;

        let mut synth = Self {
            ticks: 0,

            font_bank: FontBank::new(),

            channels: ChannelPool::new(settings.midi_channels as usize, settings.interpolation),
            voices: VoicePool::new(settings.polyphony as usize, settings.sample_rate),

            output: OutputBuffer::new(nbuf as usize),

            reverb: Reverb::new(),
            chorus: Chorus::new(settings.sample_rate),

            settings,
            min_note_length_ticks,
            max_note_on_time: None,
        };

        if synth.settings.drums_channel_active {
            synth.channels[9].set_banknum(128);
        }

        Ok(synth)
    }
}
