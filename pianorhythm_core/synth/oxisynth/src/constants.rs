#[warn(non_snake_case)]
pub mod piano_rhythm_midi_control_change {
    const LSB_MASK: u8 = 0x20;

    /// Bank select: most significant byte.
    pub const BANK_SELECT_MSB: u8 = 0x00;

    /// Bank select: least significant byte.
    pub const BANK_SELECT_LSB: u8 = BANK_SELECT_MSB | LSB_MASK;

    /// Modulation: most significant byte.
    pub const MODULATION_MSB: u8 = 0x01;

    /// Modulation: least significant byte.
    pub const MODULATION_LSB: u8 = MODULATION_MSB | LSB_MASK;

    /// Breath controller: most significant byte.
    pub const BREATH_CONTROLLER_MSB: u8 = 0x02;

    /// Breach controller: least significant byte.
    pub const BREATH_CONTROLLER_LSB: u8 = BREATH_CONTROLLER_MSB | LSB_MASK;

    /// Foot controller: most significant byte.
    pub const FOOT_CONTROLLER_MSB: u8 = 0x04;

    /// Foot controller: least significant byte.
    pub const FOOT_CONTROLLER_LSB: u8 = FOOT_CONTROLLER_MSB | LSB_MASK;

    /// Portamento: most significant byte.
    pub const PORTAMENTO_TIME_MSB: u8 = 0x05;

    /// Portamento: least significant byte.
    pub const PORTAMENTO_TIME_LSB: u8 = PORTAMENTO_TIME_MSB | LSB_MASK;

    /// Data entry: most significant byte.
    pub const DATA_ENTRY_MSB: u8 = 0x06;

    /// Data entry: least significant byte.
    pub const DATA_ENTRY_LSB: u8 = DATA_ENTRY_MSB | LSB_MASK;

    /// Main volume: most significant byte.
    pub const MAIN_VOLUME_MSB: u8 = 0x07;

    /// Main volume: least significant byte.
    pub const MAIN_VOLUME_LSB: u8 = MAIN_VOLUME_MSB | LSB_MASK;

    /// Balance: most significant byte.
    pub const BALANCE_MSB: u8 = 0x08;

    /// Balance: least significant byte.
    pub const BALANCE_LSB: u8 = BALANCE_MSB | LSB_MASK;

    /// Pan: most significant byte.
    pub const PAN_MSB: u8 = 0x0A;

    /// Pan: least significant byte.
    pub const PAN_LSB: u8 = PAN_MSB | LSB_MASK;

    /// Expression controller: most significant byte.
    pub const EXPRESSION_CONTROLLER_MSB: u8 = 0x0B;

    /// Expression controller: least significant byte.
    pub const EXPRESSION_CONTROLLER_LSB: u8 = EXPRESSION_CONTROLLER_MSB | LSB_MASK;

    /// Effect control 1: most significant byte.
    pub const EFFECT_CONTROL_1_MSB: u8 = 0x0C;

    /// Effect control 1: least significant byte.
    pub const EFFECT_CONTROL_1_LSB: u8 = EFFECT_CONTROL_1_MSB | LSB_MASK;

    /// Effect control 2: most significant byte.
    pub const EFFECT_CONTROL_2_MSB: u8 = 0x0D;

    /// Effect control 2: least significant byte.
    pub const EFFECT_CONTROL_2_LSB: u8 = EFFECT_CONTROL_2_MSB | LSB_MASK;

    /// General purpose controller 1: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_1_MSB: u8 = 0x10;

    /// General purpose controller 1: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_1_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_1_MSB | LSB_MASK;

    /// General purpose controller 2: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_2_MSB: u8 = 0x11;

    /// General purpose controller 2: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_2_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_2_MSB | LSB_MASK;

    /// General purpose controller 3: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_3_MSB: u8 = 0x12;

    /// General purpose controller 3: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_3_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_3_MSB | LSB_MASK;

    /// General purpose controller 4: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_4_MSB: u8 = 0x13;

    /// General purpose controller 4: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_4_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_4_MSB | LSB_MASK;

    /// Damper pedal.
    pub const DAMPER_PEDAL: u8 = 0x40;

    /// Portamento.
    pub const PORTAMENTO: u8 = 0x41;

    /// Sustenuto.
    pub const SUSTENUTO: u8 = 0x42;

    /// Soft pedal.
    pub const SOFT_PEDAL: u8 = 0x43;

    /// Legato footswitch.
    pub const LEGATO_FOOTSWITCH: u8 = 0x44;

    /// Hold 2.
    pub const HOLD_2: u8 = 0x45;

    /// Sound controller 1. Default: Timber variation
    pub const SOUND_CONTROLLER_1: u8 = 0x46;

    /// Sound controller 2. Default: Timber/harmonic content
    pub const SOUND_CONTROLLER_2: u8 = 0x47;

    /// Sound controller 3. Default: Release time
    pub const SOUND_CONTROLLER_3: u8 = 0x48;

    /// Sound controller 4. Default: Attack time
    pub const SOUND_CONTROLLER_4: u8 = 0x49;

    /// Sound controller 5.
    pub const SOUND_CONTROLLER_5: u8 = 0x4A;

    /// Sound controller 6.
    pub const SOUND_CONTROLLER_6: u8 = 0x4B;

    /// Sound controller 7.
    pub const SOUND_CONTROLLER_7: u8 = 0x4C;

    /// Sound controller 8.
    pub const SOUND_CONTROLLER_8: u8 = 0x4D;

    /// Sound controller 9.
    pub const SOUND_CONTROLLER_9: u8 = 0x4E;

    /// Sound controller 10.
    pub const SOUND_CONTROLLER_10: u8 = 0x4F;

    /// General purpose controller 5: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_5_MSB: u8 = 0x50;

    /// General purpose controller 6: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_6_MSB: u8 = 0x51;

    /// General purpose controller 7: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_7_MSB: u8 = 0x52;

    /// General purpose controller 8: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_8_MSB: u8 = 0x53;

    /// Portamento.
    pub const PORTAMENTO_CONTROL: u8 = 0x54;

    /// Effects depth 1. Formerly "External Effects Depth"
    pub const EFFECTS_1_DEPTH: u8 = 0x5B;

    /// Effects depth 2. Formerly "Tremolo Depth"
    pub const EFFECTS_2_DEPTH: u8 = 0x5C;

    /// Effects depth 3. Formerly "Chorus Depth"
    pub const EFFECTS_3_DEPTH: u8 = 0x5D;

    /// Effects depth 4. Formerly "Celeste Detune"
    pub const EFFECTS_4_DEPTH: u8 = 0x5E;

    /// Effects depth 5. Formerly "Phaser Depth"
    pub const EFFECTS_5_DEPTH: u8 = 0x5F;

    /// Non-registered parameter number: least significant byte.
    pub const NON_REGISTERED_PARAMETER_NUMBER_LSB: u8 = 0x62;

    /// Non-registered parameter number: most significant byte.
    pub const NON_REGISTERED_PARAMETER_NUMBER_MSB: u8 = 0x63;

    /// Registered parameter number: least significant byte.
    pub const REGISTERED_PARAMETER_NUMBER_LSB: u8 = 0x64;

    /// Registered parameter number: most significant byte.
    pub const REGISTERED_PARAMETER_NUMBER_MSB: u8 = 0x65;

    /// Mode message: all sound off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`
    pub const ALL_SOUND_OFF: u8 = 0x78;

    /// Mode message: reset all controllers.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`
    pub const RESET_ALL_CONTROLLERS: u8 = 0x79;

    /// Mode message: local control.
    ///
    /// When local control is on (default), the device responds to its local controls.
    /// When local control is off, it only responds to data recieved over MIDI.
    ///
    /// See the module [`local_control`] for possible values of the data byte
    /// (the third byte of the event).
    ///
    /// [`local_control`]: ./local_control/index.html
    pub const LOCAL_CONTROL: u8 = 0x7A;

    /// Constants for the data byte (3rd byte) of a local control control change event.
    pub mod local_control {
        /// Local control off: the device only responds to data recieved over MIDI.
        pub const LOCAL_CONTROL_OFF: u8 = 0;
        /// Local control on: the device also responds to local events (keys played, ...).
        pub const LOCAL_CONTROL_ON: u8 = 127;
    }

    /// Mode message: all notes off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    pub const ALL_NOTES_OFF: u8 = 0x7B;

    /// Mode message: omni mode off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    /// # Remark
    /// This message also causes all notes off.
    pub const OMNI_MODE_OFF: u8 = 0x7C;

    /// Mode message: omni mode on.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    /// # Remark
    /// This message also causes all notes off.
    pub const OMNI_MODE_ON: u8 = 0x7D;

    /// Mode message: mono mode on
    ///
    /// For this event, the data byte (the third byte of the event)
    /// indicates the number of channels (omni off) or `0` (omni on).
    /// # Remark
    /// This message also causes all notes off.
    pub const MONO_MODE_ON: u8 = 0x7E;

    /// Poly mode on
    ///
    /// # Remark
    /// This message also causes all notes off.
    pub const POLY_MODE_ON: u8 = 0x7F;
}