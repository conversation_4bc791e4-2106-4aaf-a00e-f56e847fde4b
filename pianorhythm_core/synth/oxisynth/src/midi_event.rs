use crate::error::{range_check, OxiError};
use serde::{Deserialize, Serialize};

pub type U7 = u8;
pub type U14 = u16;

#[derive(Debug, Co<PERSON>, <PERSON>lone, Eq, Serialize, Deserialize, PartialEq)]
pub enum MidiEvent {
    /// Send a noteon message.
    NoteOn {
        channel: u8,
        key: U7,
        vel: U7,
    },
    /// Send a noteoff message.
    NoteOff {
        channel: u8,
        key: U7,
    },
    /// Send a control change message.
    ControlChange {
        channel: u8,
        ctrl: U7,
        value: U7,
    },
    AllNotesOff {
        channel: u8,
    },
    AllSoundOff {
        channel: u8,
    },
    /// Send a pitch bend message.
    PitchBend {
        channel: u8,
        value: U14,
    },
    /// Send a program change message.
    ProgramChange {
        channel: u8,
        program_id: U7,
    },
    /// Set channel pressure
    ChannelPressure {
        channel: u8,
        value: U7,
    },
    /// Set key pressure (aftertouch)
    PolyphonicKeyPressure {
        channel: u8,
        key: U7,
        value: U7,
    },
    /// Send a reset.
    ///
    /// A reset turns all the notes off and resets the controller values.
    ///
    /// Purpose:
    /// Respond to the MIDI command 'system reset' (0xFF, big red 'panic' button)
    SystemReset,
    SystemResetWithChannel {
        channel: u8,
    },
    Unknown,
}

impl Default for MidiEvent {
    fn default() -> Self {
        MidiEvent::Unknown
    }
}

impl MidiEvent {
    pub fn check(self) -> Result<Self, OxiError> {
        match &self {
            MidiEvent::NoteOn { key, vel, .. } => {
                range_check(0..=127, key, OxiError::KeyOutOfRange)?;
                range_check(0..=127, vel, OxiError::VelocityOutOfRange)?;
            }
            MidiEvent::NoteOff { key, .. } => {
                range_check(0..=127, key, OxiError::KeyOutOfRange)?;
            }
            MidiEvent::ControlChange { ctrl, value, .. } => {
                range_check(0..=127, ctrl, OxiError::CtrlOutOfRange)?;
                range_check(0..=127, value, OxiError::CCValueOutOfRange)?;
            }
            MidiEvent::AllNotesOff { .. } => {}
            MidiEvent::AllSoundOff { .. } => {}
            MidiEvent::PitchBend { value, .. } => {
                range_check(0..=16383, value, OxiError::PithBendOutOfRange)?;
            }
            MidiEvent::ProgramChange { program_id, .. } => {
                range_check(0..=127, program_id, OxiError::ProgramOutOfRange)?;
            }
            MidiEvent::ChannelPressure { value, .. } => {
                range_check(0..=127, value, OxiError::ChannelPressureOutOfRange)?;
            }
            MidiEvent::PolyphonicKeyPressure { key, value, .. } => {
                range_check(0..=127, key, OxiError::KeyOutOfRange)?;
                range_check(0..=127, value, OxiError::KeyPressureOutOfRange)?;
            }
            MidiEvent::SystemReset => {}
            _ => {}
        };

        Ok(self)
    }

    pub fn to_message_type_u8(self) -> u8 {
        match &self {
            MidiEvent::NoteOn { .. } => 1,
            MidiEvent::NoteOff { .. } => 2,
            MidiEvent::ControlChange { .. } => 3,
            MidiEvent::AllNotesOff { .. } => 4,
            MidiEvent::AllSoundOff { .. } => 5,
            MidiEvent::PitchBend { .. } => 6,
            MidiEvent::ProgramChange { .. } => 7,
            MidiEvent::ChannelPressure { .. } => 8,
            MidiEvent::PolyphonicKeyPressure { .. } => 9,
            // MidiEvent::SocketUserGainChange { .. } => 10,
            MidiEvent::SystemReset => 11,
            MidiEvent::SystemResetWithChannel { .. } => 12,
            MidiEvent::Unknown => 0,
        }
    }

    pub fn to_message_string(self) -> String {
        match &self {
            MidiEvent::NoteOn { .. } => "NOTE_ON".to_owned(),
            MidiEvent::NoteOff { .. } => "NOTE_OFF".to_owned(),
            MidiEvent::ControlChange {
                channel,
                ctrl,
                value,
            } => {
                let mut control_type = {
                    match *ctrl {
                        MIDI_CONTROL_BYTES::ALL_NOTES_OFF => "ALL_NOTES_OFF",
                        MIDI_CONTROL_BYTES::ALL_SOUND_OFF => "ALL_SOUND_OFF",
                        MIDI_CONTROL_BYTES::OMNI_MODE_OFF => "OMNI_MODE_OFF",
                        MIDI_CONTROL_BYTES::MONO_MODE_ON => "MONO_MODE_ON",
                        MIDI_CONTROL_BYTES::POLY_MODE_ON => "POLY_MODE_ON",
                        MIDI_CONTROL_BYTES::OMNI_MODE_ON => "OMNI_MODE_ON",
                        MIDI_CONTROL_BYTES::MAIN_VOLUME_MSB => "MAIN_VOLUME_MSB",
                        MIDI_CONTROL_BYTES::MAIN_VOLUME_LSB => "MAIN_VOLUME_LSB",
                        MIDI_CONTROL_BYTES::FOOT_CONTROLLER_MSB => "FOOT_CONTROLLER_MSB",
                        MIDI_CONTROL_BYTES::MODULATION_MSB => "MODULATION_MSB",
                        MIDI_CONTROL_BYTES::MODULATION_LSB => "MODULATION_LSB",
                        MIDI_CONTROL_BYTES::BANK_SELECT_MSB => "BANK_SELECT_MSB",
                        MIDI_CONTROL_BYTES::BANK_SELECT_LSB => "BANK_SELECT_LSB",
                        MIDI_CONTROL_BYTES::FOOT_CONTROLLER_LSB => "FOOT_CONTROLLER_LSB",
                        MIDI_CONTROL_BYTES::RESET_ALL_CONTROLLERS => "RESET_ALL_CONTROLLERS",
                        MIDI_CONTROL_BYTES::PAN_MSB => "PAN_MSB",
                        MIDI_CONTROL_BYTES::PAN_LSB => "PAN_LSB",
                        MIDI_CONTROL_BYTES::DAMPER_PEDAL => "DAMPER_PEDAL",
                        MIDI_CONTROL_BYTES::PORTAMENTO => "PORTAMENTO",
                        MIDI_CONTROL_BYTES::SUSTENUTO => "SUSTENUTO",
                        MIDI_CONTROL_BYTES::SOFT_PEDAL => "SOFT_PEDAL",
                        MIDI_CONTROL_BYTES::LEGATO_FOOTSWITCH => "LEGATO_FOOTSWITCH",
                        MIDI_CONTROL_BYTES::HOLD_2 => "HOLD_2",
                        MIDI_CONTROL_BYTES::EFFECTS_1_DEPTH => "EFFECTS_1_DEPTH",
                        MIDI_CONTROL_BYTES::EFFECTS_2_DEPTH => "TREMELO_EFFECT",
                        MIDI_CONTROL_BYTES::EFFECTS_3_DEPTH => "CHORUS_EFFECT",
                        MIDI_CONTROL_BYTES::EFFECTS_4_DEPTH => "CELESTE_EFFECT",
                        MIDI_CONTROL_BYTES::EFFECTS_5_DEPTH => "PHASER_EFFECT",
                        _ => "CONTROL_CHANGE",
                    }
                };
                // return format!("ControlChange - {}", control_type).to_owned();
                return control_type.to_owned();
            }
            MidiEvent::AllNotesOff { .. } => "ALL_NOTES_OFF".to_owned(),
            MidiEvent::AllSoundOff { .. } => "ALL_SOUND_OFF".to_owned(),
            MidiEvent::PitchBend { .. } => "PITCH_BEND".to_owned(),
            MidiEvent::ProgramChange { .. } => "PROGRAM_CHANGE".to_owned(),
            MidiEvent::ChannelPressure { .. } => "CHANNEL_PRESSURE".to_owned(),
            MidiEvent::PolyphonicKeyPressure { .. } => "POLYPHONIC_KEY_PRESSURE".to_owned(),
            // MidiEvent::SocketUserGainChange { .. } => "SOCKET_USER_GAIN_CHANGE".to_owned(),
            MidiEvent::SystemReset => "SYSTEM_RESET".to_owned(),
            MidiEvent::SystemResetWithChannel { .. } => "SYSTEM_RESET".to_owned(),
            _ => "UNKNOWN_EVENT".to_owned()
        }
    }

    pub fn is_program_change(&self) -> bool {
        match self {
            MidiEvent::ProgramChange { .. } => true,
            _ => false
        }
    }

    pub fn is_channel_update_message(&self) -> bool {
        match self {
            MidiEvent::ControlChange { ctrl, .. }
            if *ctrl == MIDI_CONTROL_BYTES::BANK_SELECT_MSB || *ctrl == MIDI_CONTROL_BYTES::BANK_SELECT_LSB
            => true,
            MidiEvent::ControlChange { ctrl, .. }
            if *ctrl == MIDI_CONTROL_BYTES::MAIN_VOLUME_MSB || *ctrl == MIDI_CONTROL_BYTES::MAIN_VOLUME_LSB
            => true,
            MidiEvent::ControlChange { ctrl, .. }
            if *ctrl == MIDI_CONTROL_BYTES::PAN_MSB || *ctrl == MIDI_CONTROL_BYTES::PAN_LSB
            => true,
            MidiEvent::ControlChange { ctrl, .. } if *ctrl == MIDI_CONTROL_BYTES::VOLUME_FINE => true,
            MidiEvent::ControlChange { ctrl, .. } if *ctrl == MIDI_CONTROL_BYTES::EXPRESSION_FINE => true,
            MidiEvent::ControlChange { ctrl, .. } if *ctrl == MIDI_CONTROL_BYTES::REVERB_LEVEL => true,
            MidiEvent::ControlChange { ctrl, .. } if *ctrl == MIDI_CONTROL_BYTES::CHORUS_LEVEL => true,
            MidiEvent::ProgramChange { .. } => true,
            _ => false
        }
    }
}

macro_rules! u8_to_enum {
    (
        $(#[$meta:meta])*
        $vis:vis enum $name:ident {
            $($(#[$vmeta:meta])* $vname:ident $(= $val:expr)?,)*
        }
    ) => {
        $(#[$meta])*
        $vis enum $name {
            $($(#[$vmeta])* $vname $(= $val)?,)*
        }

        impl $name {
            #[allow(unused)]
            pub const fn const_try_from(v: u8) -> Option<Self> {
                match v {
                    $(x if x == $name::$vname as u8 => Some($name::$vname),)*
                    _ => None,
                }
            }
        }
    }
}

u8_to_enum!(
    #[allow(unused)]
    #[derive(Copy, Clone, Debug, Hash, Eq, PartialEq, PartialOrd, Ord)]
    pub enum ControlFunction {
        BankSelect = 0,
        ModulationWheel = 1,
        BreathController = 2,
        Undefined3 = 3,
        FootController = 4,
        PortamentoTime = 5,
        DataEntryMsb = 6,
        ChannelVolume = 7,
        Balance = 8,
        Undefined9 = 9,
        Pan = 10,
        ExpressionController = 11,
        EffectControl1 = 12,
        EffectControl2 = 13,
        Undefined14 = 14,
        Undefined15 = 15,
        GeneralPurposeController1 = 16,
        GeneralPurposeController2 = 17,
        GeneralPurposeController3 = 18,
        GeneralPurposeController4 = 19,
        Undefined20 = 20,
        Undefined21 = 21,
        Undefined22 = 22,
        Undefined23 = 23,
        Undefined24 = 24,
        Undefined25 = 25,
        Undefined26 = 26,
        Undefined27 = 27,
        Undefined28 = 28,
        Undefined29 = 29,
        Undefined30 = 30,
        Undefined31 = 31,
        BankSelectLsb = 32,
        ModulationWheelLsb = 33,
        BreathControllerLsb = 34,
        Undefined3Lsb = 35,
        FootControllerLsb = 36,
        PortamentoTimeLsb = 37,
        DataEntryLsb = 38,
        ChannelVolumeLsb = 39,
        BalanceLsb = 40,
        Undefined9Lsb = 41,
        PanLsb = 42,
        ExpressionControllerLsb = 43,
        EffectControl1Lsb = 44,
        EffectControl2Lsb = 45,
        Undefined14Lsb = 46,
        Undefined15Lsb = 47,
        GeneralPurposeController1Lsb = 48,
        GeneralPurposeController2Lsb = 49,
        GeneralPurposeController3Lsb = 50,
        GeneralPurposeController4Lsb = 51,
        Undefined20Lsb = 52,
        Undefined21Lsb = 53,
        Undefined22Lsb = 54,
        Undefined23Lsb = 55,
        Undefined24Lsb = 56,
        Undefined25Lsb = 57,
        Undefined26Lsb = 58,
        Undefined27Lsb = 59,
        Undefined28Lsb = 60,
        Undefined29Lsb = 61,
        Undefined30Lsb = 62,
        Undefined31Lsb = 63,
        DamperPedal = 64,
        PortamentoOnOff = 65,
        Sostenuto = 66,
        SoftPedal = 67,
        LegatoFootswitch = 68,
        Hold2 = 69,
        SoundController1 = 70,
        SoundController2 = 71,
        SoundController3 = 72,
        SoundController4 = 73,
        SoundController5 = 74,
        SoundController6 = 75,
        SoundController7 = 76,
        SoundController8 = 77,
        SoundController9 = 78,
        SoundController10 = 79,
        GeneralPurposeController5 = 80,
        GeneralPurposeController6 = 81,
        GeneralPurposeController7 = 82,
        GeneralPurposeController8 = 83,
        PortamentoControl = 84,
        Undefined85 = 85,
        Undefined86 = 86,
        Undefined87 = 87,
        Undefined88 = 88,
        Undefined89 = 89,
        Undefined90 = 90,
        Effects1Depth = 91,
        Effects2Depth = 92,
        Effects3Depth = 93,
        Effects4Depth = 94,
        Effects5Depth = 95,
        DataIncrement = 96,
        DataDecrement = 97,
        NonRegisteredParameterNumberLsb = 98,
        NonRegisteredParameterNumberMsb = 99,
        RegisteredParameterNumberLsb = 100,
        RegisteredParameterNumberMsb = 101,

        Undefined102 = 102,
        Undefined103 = 103,
        Undefined104 = 104,
        Undefined105 = 105,
        Undefined106 = 106,
        Undefined107 = 107,
        Undefined108 = 108,
        Undefined109 = 109,
        Undefined110 = 110,
        Undefined111 = 111,
        Undefined112 = 112,
        Undefined113 = 113,
        Undefined114 = 114,
        Undefined115 = 115,
        Undefined116 = 116,
        Undefined117 = 117,
        Undefined118 = 118,
        Undefined119 = 119,

        AllSoundOff = 120,
        ResetAllControllers = 121,
        LocalControl = 122,
        AllNotesOff = 123,
        OmniModeOn = 124,
        OmniModeOff = 125,
        MonoOperation = 126,
        PolyOperation = 127,
    }
);

/// Constants to represent controller change types.
///
/// A control change channel event is as follows:
/// * byte one: `CONTROL_CHANGE | channel`, where `channel` is the channel (0-16)
/// * byte two: controller type (0-127). This module contains constants for these types.
/// * byte three: new controller value
///
/// # Remark
/// Some control change types come in pairs: one with the most significant byte (MSB)
/// and one with the least significant byte (LSB).
mod MIDI_CONTROL_BYTES {
    const LSB_MASK: u8 = 0x20;
    pub const BANK_SELECT_COARSE: u8 = 0;
    pub const MODULATION_WHEEL_COARSE: u8 = 1;
    pub const BREATH_CONTROLLER_COARSE: u8 = 2;
    pub const FOOT_CONTROLLER_COARSE: u8 = 4;
    pub const PORTAMENTO_TIME_COARSE: u8 = 5;
    pub const DATA_ENTRY_COARSE: u8 = 6;
    pub const VOLUME_COARSE: u8 = 7;
    pub const BALANCE_COARSE: u8 = 8;
    pub const PAN_COARSE: u8 = 10;
    pub const EXPRESSION_COARSE: u8 = 11;
    pub const EFFECT_CONTROL_1_COARSE: u8 = 12;
    pub const EFFECT_CONTROL_2_COARSE: u8 = 13;
    pub const GENERAL_PURPOSE_SLIDER_1: u8 = 16;
    pub const GENERAL_PURPOSE_SLIDER_2: u8 = 17;
    pub const GENERAL_PURPOSE_SLIDER_3: u8 = 18;
    pub const GENERAL_PURPOSE_SLIDER_4: u8 = 19;
    pub const BANK_SELECT_FINE: u8 = 32;
    pub const MODULATION_WHEEL_FINE: u8 = 33;
    pub const BREATH_CONTROLLER_FINE: u8 = 34;
    pub const FOOT_CONTROLLER_FINE: u8 = 36;
    pub const PORTAMENTO_TIME_FINE: u8 = 37;
    pub const DATA_ENTRY_FINE: u8 = 38;
    pub const VOLUME_FINE: u8 = 39;
    pub const BALANCE_FINE: u8 = 40;
    pub const PAN_FINE: u8 = 42;
    pub const EXPRESSION_FINE: u8 = 43;
    pub const EFFECT_CONTROL_1_FINE: u8 = 44;
    pub const EFFECT_CONTROL_2_FINE: u8 = 45;
    pub const GENERAL_PURPOSE_BUTTON_1: u8 = 80;
    pub const GENERAL_PURPOSE_BUTTON_2: u8 = 81;
    pub const GENERAL_PURPOSE_BUTTON_3: u8 = 82;
    pub const GENERAL_PURPOSE_BUTTON_4: u8 = 83;
    pub const REVERB_LEVEL: u8 = 91;
    pub const TREMOLO_LEVEL: u8 = 92;
    pub const CHORUS_LEVEL: u8 = 93;
    pub const CELESTE_LEVEL: u8 = 94;
    pub const PHASER_LEVEL: u8 = 95;
    pub const DATA_INCREMENT: u8 = 96;
    pub const DATA_DECREMENT: u8 = 97;
    pub const ALL_CONTROLLERS_OFF: u8 = 121;
    pub const LOCAL_KEYBOARD: u8 = 122;

    /// Bank select: most significant byte.
    pub const BANK_SELECT_MSB: u8 = 0x00;

    /// Bank select: least significant byte.
    pub const BANK_SELECT_LSB: u8 = BANK_SELECT_MSB | LSB_MASK;

    /// Modulation: most significant byte.
    pub const MODULATION_MSB: u8 = 0x01;

    /// Modulation: least significant byte.
    pub const MODULATION_LSB: u8 = MODULATION_MSB | LSB_MASK;

    /// Breath controller: most significant byte.
    pub const BREATH_CONTROLLER_MSB: u8 = 0x02;

    /// Breach controller: least significant byte.
    pub const BREATH_CONTROLLER_LSB: u8 = BREATH_CONTROLLER_MSB | LSB_MASK;

    /// Foot controller: most significant byte.
    pub const FOOT_CONTROLLER_MSB: u8 = 0x04;

    /// Foot controller: least significant byte.
    pub const FOOT_CONTROLLER_LSB: u8 = FOOT_CONTROLLER_MSB | LSB_MASK;

    /// Portamento: most significant byte.
    pub const PORTAMENTO_TIME_MSB: u8 = 0x05;

    /// Portamento: least significant byte.
    pub const PORTAMENTO_TIME_LSB: u8 = PORTAMENTO_TIME_MSB | LSB_MASK;

    /// Data entry: most significant byte.
    pub const DATA_ENTRY_MSB: u8 = 0x06;

    /// Data entry: least significant byte.
    pub const DATA_ENTRY_LSB: u8 = DATA_ENTRY_MSB | LSB_MASK;

    /// Main volume: most significant byte.
    pub const MAIN_VOLUME_MSB: u8 = 0x07;

    /// Main volume: least significant byte.
    pub const MAIN_VOLUME_LSB: u8 = MAIN_VOLUME_MSB | LSB_MASK;

    /// Balance: most significant byte.
    pub const BALANCE_MSB: u8 = 0x08;

    /// Balance: least significant byte.
    pub const BALANCE_LSB: u8 = BALANCE_MSB | LSB_MASK;

    /// Pan: most significant byte.
    pub const PAN_MSB: u8 = 0x0A;

    /// Pan: least significant byte.
    pub const PAN_LSB: u8 = PAN_MSB | LSB_MASK;

    /// Expression controller: most significant byte.
    pub const EXPRESSION_CONTROLLER_MSB: u8 = 0x0B;

    /// Expression controller: least significant byte.
    pub const EXPRESSION_CONTROLLER_LSB: u8 = EXPRESSION_CONTROLLER_MSB | LSB_MASK;

    /// Effect control 1: most significant byte.
    pub const EFFECT_CONTROL_1_MSB: u8 = 0x0C;

    /// Effect control 1: least significant byte.
    pub const EFFECT_CONTROL_1_LSB: u8 = EFFECT_CONTROL_1_MSB | LSB_MASK;

    /// Effect control 2: most significant byte.
    pub const EFFECT_CONTROL_2_MSB: u8 = 0x0D;

    /// Effect control 2: least significant byte.
    pub const EFFECT_CONTROL_2_LSB: u8 = EFFECT_CONTROL_2_MSB | LSB_MASK;

    /// General purpose controller 1: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_1_MSB: u8 = 0x10;

    /// General purpose controller 1: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_1_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_1_MSB | LSB_MASK;

    /// General purpose controller 2: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_2_MSB: u8 = 0x11;

    /// General purpose controller 2: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_2_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_2_MSB | LSB_MASK;

    /// General purpose controller 3: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_3_MSB: u8 = 0x12;

    /// General purpose controller 3: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_3_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_3_MSB | LSB_MASK;

    /// General purpose controller 4: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_4_MSB: u8 = 0x13;

    /// General purpose controller 4: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_4_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_4_MSB | LSB_MASK;

    /// Damper pedal.
    pub const DAMPER_PEDAL: u8 = 0x40;

    /// Portamento.
    pub const PORTAMENTO: u8 = 0x41;

    /// Sustenuto.
    pub const SUSTENUTO: u8 = 0x42;

    /// Soft pedal.
    pub const SOFT_PEDAL: u8 = 0x43;

    /// Legato footswitch.
    pub const LEGATO_FOOTSWITCH: u8 = 0x44;

    /// Hold 2.
    pub const HOLD_2: u8 = 0x45;

    /// Sound controller 1. Default: Timber variation
    pub const SOUND_CONTROLLER_1: u8 = 0x46;

    /// Sound controller 2. Default: Timber/harmonic content
    pub const SOUND_CONTROLLER_2: u8 = 0x47;

    /// Sound controller 3. Default: Release time
    pub const SOUND_CONTROLLER_3: u8 = 0x48;

    /// Sound controller 4. Default: Attack time
    pub const SOUND_CONTROLLER_4: u8 = 0x49;

    /// Sound controller 5.
    pub const SOUND_CONTROLLER_5: u8 = 0x4A;

    /// Sound controller 6.
    pub const SOUND_CONTROLLER_6: u8 = 0x4B;

    /// Sound controller 7.
    pub const SOUND_CONTROLLER_7: u8 = 0x4C;

    /// Sound controller 8.
    pub const SOUND_CONTROLLER_8: u8 = 0x4D;

    /// Sound controller 9.
    pub const SOUND_CONTROLLER_9: u8 = 0x4E;

    /// Sound controller 10.
    pub const SOUND_CONTROLLER_10: u8 = 0x4F;

    /// General purpose controller 5: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_5_MSB: u8 = 0x50;

    /// General purpose controller 6: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_6_MSB: u8 = 0x51;

    /// General purpose controller 7: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_7_MSB: u8 = 0x52;

    /// General purpose controller 8: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_8_MSB: u8 = 0x53;

    /// Portamento.
    pub const PORTAMENTO_CONTROL: u8 = 0x54;

    /// Effects depth 1. Formerly "External Effects Depth"
    pub const EFFECTS_1_DEPTH: u8 = 0x5B;

    /// Effects depth 2. Formerly "Tremolo Depth"
    pub const EFFECTS_2_DEPTH: u8 = 0x5C;

    /// Effects depth 3. Formerly "Chorus Depth"
    pub const EFFECTS_3_DEPTH: u8 = 0x5D;

    /// Effects depth 4. Formerly "Celeste Detune"
    pub const EFFECTS_4_DEPTH: u8 = 0x5E;

    /// Effects depth 5. Formerly "Phaser Depth"
    pub const EFFECTS_5_DEPTH: u8 = 0x5F;

    /// Non-registered parameter number: least significant byte.
    pub const NON_REGISTERED_PARAMETER_NUMBER_LSB: u8 = 0x62;

    /// Non-registered parameter number: most significant byte.
    pub const NON_REGISTERED_PARAMETER_NUMBER_MSB: u8 = 0x63;

    /// Registered parameter number: least significant byte.
    pub const REGISTERED_PARAMETER_NUMBER_LSB: u8 = 0x64;

    /// Registered parameter number: most significant byte.
    pub const REGISTERED_PARAMETER_NUMBER_MSB: u8 = 0x65;

    /// Mode message: all sound off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`
    pub const ALL_SOUND_OFF: u8 = 0x78;

    /// Mode message: reset all controllers.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`
    pub const RESET_ALL_CONTROLLERS: u8 = 0x79;

    /// Mode message: local control.
    ///
    /// When local control is on (default), the device responds to its local controls.
    /// When local control is off, it only responds to data recieved over MIDI.
    ///
    /// See the module [`local_control`] for possible values of the data byte
    /// (the third byte of the event).
    ///
    /// [`local_control`]: ./local_control/index.html
    pub const LOCAL_CONTROL: u8 = 0x7A;

    /// Constants for the data byte (3rd byte) of a local control control change event.
    pub mod local_control {
        /// Local control off: the device only responds to data recieved over MIDI.
        pub const LOCAL_CONTROL_OFF: u8 = 0;
        /// Local control on: the device also responds to local events (keys played, ...).
        pub const LOCAL_CONTROL_ON: u8 = 127;
    }

    /// Mode message: all notes off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    pub const ALL_NOTES_OFF: u8 = 0x7B;

    /// Mode message: omni mode off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    /// # Remark
    /// This message also causes all notes off.
    pub const OMNI_MODE_OFF: u8 = 0x7C;

    /// Mode message: omni mode on.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    /// # Remark
    /// This message also causes all notes off.
    pub const OMNI_MODE_ON: u8 = 0x7D;

    /// Mode message: mono mode on
    ///
    /// For this event, the data byte (the third byte of the event)
    /// indicates the number of channels (omni off) or `0` (omni on).
    /// # Remark
    /// This message also causes all notes off.
    pub const MONO_MODE_ON: u8 = 0x7E;

    /// Poly mode on
    ///
    /// # Remark
    /// This message also causes all notes off.
    pub const POLY_MODE_ON: u8 = 0x7F;
}

#[allow(unused)]
impl ControlFunction {
    pub const MIN: Self = Self::BankSelect;
    pub const MAX: Self = Self::PolyOperation;

    pub fn iter() -> impl Iterator<Item = Self> {
        let mut range = ControlFunction::MIN as u8..=ControlFunction::MAX as u8;
        std::iter::from_fn(move || {
            let v = range.next()?;
            let v = ControlFunction::const_try_from(v).unwrap();

            Some(v)
        })
    }

    pub fn iter_range(v: impl std::ops::RangeBounds<Self>) -> impl Iterator<Item = Self> {
        let first = match v.start_bound() {
            std::ops::Bound::Included(v) => *v as u8,
            std::ops::Bound::Excluded(v) => *v as u8,
            std::ops::Bound::Unbounded => ControlFunction::MIN as u8,
        };
        let last = match v.end_bound() {
            std::ops::Bound::Included(v) => *v as u8 + 1,
            std::ops::Bound::Excluded(v) => *v as u8,
            std::ops::Bound::Unbounded => ControlFunction::MAX as u8,
        };

        let mut range = first..last;

        std::iter::from_fn(move || {
            let v = range.next()?;
            let v = ControlFunction::const_try_from(v).unwrap();

            Some(v)
        })
    }

    pub fn is_effects_n_depth(&self) -> bool {
        matches!(
            self,
            Self::Effects1Depth
                | Self::Effects2Depth
                | Self::Effects3Depth
                | Self::Effects4Depth
                | Self::Effects5Depth
        )
    }

    pub fn is_sound_controller_n(&self) -> bool {
        matches!(
            self,
            Self::SoundController1
                | Self::SoundController2
                | Self::SoundController3
                | Self::SoundController4
                | Self::SoundController5
                | Self::SoundController6
                | Self::SoundController7
                | Self::SoundController8
                | Self::SoundController9
                | Self::SoundController10
        )
    }
}

const _: () = {
    if ControlFunction::MIN as u8 != 0 {
        unreachable!();
    }

    if ControlFunction::MAX as u8 != 127 {
        unreachable!();
    }

    let mut i = 0;
    while i <= ControlFunction::MAX as u8 {
        let v = ControlFunction::const_try_from(i).unwrap();

        if v as u8 != i {
            unreachable!();
        }

        i += 1;
    }

    if i != ControlFunction::MAX as u8 + 1 {
        unreachable!();
    }

    if ControlFunction::const_try_from(i).is_some() {
        unreachable!();
    }
};

#[test]
fn abc() {
    for v in ControlFunction::iter() {
        println!("{v:?}");
    }
}
