[workspace]
resolver = "2"

members = [
    "core",
    "proto",
    "synth",
    "desktop",
    "shared",
    "bevy_renderer"
]

exclude = [
    "synth/simple-eq-master",
]

default-members = [
    "core",
    "proto",
    "shared",
    "desktop",
    "bevy_renderer"
]

[workspace.package]
rust-version = "1.90"
authors = ["Oak <<EMAIL>>"]
version = "0.2.0"
license = "MIT"
edition = "2021"
homepage = "https://github.com/BlackMIDIDevs/xsynth"
repository = "https://github.com/BlackMIDIDevs/xsynth"
readme = "README.md"
keywords = []
categories = []

[workspace.dependencies]
protobuf = "2.28.0"
wasm-bindgen = { version = "=0.2.100" }
wasm-bindgen-futures = "=0.4.50"
js-sys = { version = "=0.3.77" }
serde-wasm-bindgen = "0.6.3"
once_cell = "1.19.0"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = { version = "=1.0.140" }
midir = "0.10.1"
midly = "0.5.3"
lazy_static = "1.4.0"
log = "0.4.20"
chrono = { version = "0.4.31", features = ["serde"] }
derivative = "2.2.0"
seahash = "4.1.0"
cached = "0.48.1"
wasm-msgpack = "0.6.2"
rustc-hash = "1.1.0"
reactive-state = { version = "0.3.0" }
gloo = "0.11.0"
gloo-timers = "0.3.0"
gloo-events = "0.2.0"
gloo-storage = "0.3.0"
console_log = { version = "1", features = ["color"] }
cfg-if = "1.0.0"
crossbeam-channel = "0.5.11"
uuid = { version = "1.7.0", features = ["serde", "v4", "js"] }
log-panics = { version = "2", features = ["with-backtrace"]}

[workspace.dependencies.web-sys]
version = "=0.3.77"
features = [
    'BroadcastChannel',
    'AudioContext',
    'AudioDestinationNode',
    'AudioNode',
    'AudioWorklet',
    'AudioWorkletNode',
    'AudioWorkletNodeOptions',
    'AudioWorkletGlobalScope',
    'DedicatedWorkerGlobalScope',
    'CustomEvent',
    'CustomEventInit',
    'Event',
    'EventTarget',
    'EventListener',
    'MessageEvent',
    'Blob',
    'BlobPropertyBag',
    'Url',
    'console',
    'Window',
    'Document',
    'Worker',
    'WorkerGlobalScope',
    'WebSocket',
    'KeyboardEvent',
    'MouseEvent',
    'HtmlCanvasElement',
    'OffscreenCanvas',
    'OffscreenCanvasRenderingContext2d',
    'CanvasRenderingContext2d',
    'Element',
    'HtmlElement',
    'Node',
    'Navigator',
    'MessagePort'
]

[profile.dev]
opt-level = 1

# Enable high optimizations for dependencies (incl. Bevy), but not for our code:
[profile.dev.package."*"]
opt-level = 3

[profile.dev.package.pianorhythm_synth]
opt-level = 3

[profile.release]
lto = "fat"
opt-level = 3 # Optimize for binary size
strip = true # Automatically strip symbols from the binary.
debug = false
# panic = "abort" # Strip expensive panic clean-up logic
codegen-units = 1 # Compile crates one after another so the compiler can optimize better