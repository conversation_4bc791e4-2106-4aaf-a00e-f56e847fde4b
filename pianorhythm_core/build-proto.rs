extern crate protoc_rust;

fn main() {
    let bin = protoc_bin_vendored::protoc_bin_path().unwrap();

    protoc_rust::Codegen::new()
        .protoc_path(bin)
        .out_dir("src")
        .include("raw")
        .inputs(&["raw/client-message.proto"])
        .inputs(&["raw/world-renditions.proto"])
        .inputs(&["raw/user-renditions.proto"])
        .inputs(&["raw/server-message.proto"])
        .inputs(&["raw/room-renditions.proto"])
        .inputs(&["raw/pianorhythm-events.proto"])
        .inputs(&["raw/pianorhythm-actions.proto"])
        .inputs(&["raw/pianorhythm-effects.proto"])
        .inputs(&["raw/pianorhythm-app-renditions.proto"])
        .inputs(&["raw/midi-renditions.proto"])
        .run()
        .expect("Running protoc failed.");
}