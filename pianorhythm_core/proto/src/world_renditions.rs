// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `world-renditions.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(<PERSON>ial<PERSON><PERSON>,<PERSON><PERSON>,Default)]
pub struct WorldVector3 {
    // message fields
    pub x: f32,
    pub y: f32,
    pub z: f32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WorldVector3 {
    fn default() -> &'a WorldVector3 {
        <WorldVector3 as ::protobuf::Message>::default_instance()
    }
}

impl WorldVector3 {
    pub fn new() -> WorldVector3 {
        ::std::default::Default::default()
    }

    // float x = 1;


    pub fn get_x(&self) -> f32 {
        self.x
    }
    pub fn clear_x(&mut self) {
        self.x = 0.;
    }

    // Param is passed by value, moved
    pub fn set_x(&mut self, v: f32) {
        self.x = v;
    }

    // float y = 2;


    pub fn get_y(&self) -> f32 {
        self.y
    }
    pub fn clear_y(&mut self) {
        self.y = 0.;
    }

    // Param is passed by value, moved
    pub fn set_y(&mut self, v: f32) {
        self.y = v;
    }

    // float z = 3;


    pub fn get_z(&self) -> f32 {
        self.z
    }
    pub fn clear_z(&mut self) {
        self.z = 0.;
    }

    // Param is passed by value, moved
    pub fn set_z(&mut self, v: f32) {
        self.z = v;
    }
}

impl ::protobuf::Message for WorldVector3 {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed32 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_float()?;
                    self.x = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed32 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_float()?;
                    self.y = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed32 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_float()?;
                    self.z = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.x != 0. {
            my_size += 5;
        }
        if self.y != 0. {
            my_size += 5;
        }
        if self.z != 0. {
            my_size += 5;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.x != 0. {
            os.write_float(1, self.x)?;
        }
        if self.y != 0. {
            os.write_float(2, self.y)?;
        }
        if self.z != 0. {
            os.write_float(3, self.z)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WorldVector3 {
        WorldVector3::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeFloat>(
                "x",
                |m: &WorldVector3| { &m.x },
                |m: &mut WorldVector3| { &mut m.x },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeFloat>(
                "y",
                |m: &WorldVector3| { &m.y },
                |m: &mut WorldVector3| { &mut m.y },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeFloat>(
                "z",
                |m: &WorldVector3| { &m.z },
                |m: &mut WorldVector3| { &mut m.z },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<WorldVector3>(
                "WorldVector3",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static WorldVector3 {
        static instance: ::protobuf::rt::LazyV2<WorldVector3> = ::protobuf::rt::LazyV2::INIT;
        instance.get(WorldVector3::new)
    }
}

impl ::protobuf::Clear for WorldVector3 {
    fn clear(&mut self) {
        self.x = 0.;
        self.y = 0.;
        self.z = 0.;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for WorldVector3 {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldVector3 {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct WorldAvatarItem {
    // message fields
    pub item_key: ::std::string::String,
    pub title: ::std::string::String,
    pub item_type: WorldItemType,
    pub item_color1: ::std::string::String,
    // message oneof groups
    pub _description: ::std::option::Option<WorldAvatarItem_oneof__description>,
    pub _item_color2: ::std::option::Option<WorldAvatarItem_oneof__item_color2>,
    pub _position_offset: ::std::option::Option<WorldAvatarItem_oneof__position_offset>,
    pub _rotation_offset: ::std::option::Option<WorldAvatarItem_oneof__rotation_offset>,
    pub _scale_offset: ::std::option::Option<WorldAvatarItem_oneof__scale_offset>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WorldAvatarItem {
    fn default() -> &'a WorldAvatarItem {
        <WorldAvatarItem as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarItem_oneof__description {
    description(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarItem_oneof__item_color2 {
    item_color2(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarItem_oneof__position_offset {
    position_offset(WorldVector3),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarItem_oneof__rotation_offset {
    rotation_offset(WorldVector3),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarItem_oneof__scale_offset {
    scale_offset(WorldVector3),
}

impl WorldAvatarItem {
    pub fn new() -> WorldAvatarItem {
        ::std::default::Default::default()
    }

    // string item_key = 1;


    pub fn get_item_key(&self) -> &str {
        &self.item_key
    }
    pub fn clear_item_key(&mut self) {
        self.item_key.clear();
    }

    // Param is passed by value, moved
    pub fn set_item_key(&mut self, v: ::std::string::String) {
        self.item_key = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_item_key(&mut self) -> &mut ::std::string::String {
        &mut self.item_key
    }

    // Take field
    pub fn take_item_key(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.item_key, ::std::string::String::new())
    }

    // string title = 2;


    pub fn get_title(&self) -> &str {
        &self.title
    }
    pub fn clear_title(&mut self) {
        self.title.clear();
    }

    // Param is passed by value, moved
    pub fn set_title(&mut self, v: ::std::string::String) {
        self.title = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_title(&mut self) -> &mut ::std::string::String {
        &mut self.title
    }

    // Take field
    pub fn take_title(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.title, ::std::string::String::new())
    }

    // string description = 3;


    pub fn get_description(&self) -> &str {
        match self._description {
            ::std::option::Option::Some(WorldAvatarItem_oneof__description::description(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_description(&mut self) {
        self._description = ::std::option::Option::None;
    }

    pub fn has_description(&self) -> bool {
        match self._description {
            ::std::option::Option::Some(WorldAvatarItem_oneof__description::description(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_description(&mut self, v: ::std::string::String) {
        self._description = ::std::option::Option::Some(WorldAvatarItem_oneof__description::description(v))
    }

    // Mutable pointer to the field.
    pub fn mut_description(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarItem_oneof__description::description(_)) = self._description {
        } else {
            self._description = ::std::option::Option::Some(WorldAvatarItem_oneof__description::description(::std::string::String::new()));
        }
        match self._description {
            ::std::option::Option::Some(WorldAvatarItem_oneof__description::description(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_description(&mut self) -> ::std::string::String {
        if self.has_description() {
            match self._description.take() {
                ::std::option::Option::Some(WorldAvatarItem_oneof__description::description(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldItemType item_type = 4;


    pub fn get_item_type(&self) -> WorldItemType {
        self.item_type
    }
    pub fn clear_item_type(&mut self) {
        self.item_type = WorldItemType::UNKNOWN;
    }

    // Param is passed by value, moved
    pub fn set_item_type(&mut self, v: WorldItemType) {
        self.item_type = v;
    }

    // string item_color1 = 5;


    pub fn get_item_color1(&self) -> &str {
        &self.item_color1
    }
    pub fn clear_item_color1(&mut self) {
        self.item_color1.clear();
    }

    // Param is passed by value, moved
    pub fn set_item_color1(&mut self, v: ::std::string::String) {
        self.item_color1 = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_item_color1(&mut self) -> &mut ::std::string::String {
        &mut self.item_color1
    }

    // Take field
    pub fn take_item_color1(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.item_color1, ::std::string::String::new())
    }

    // string item_color2 = 6;


    pub fn get_item_color2(&self) -> &str {
        match self._item_color2 {
            ::std::option::Option::Some(WorldAvatarItem_oneof__item_color2::item_color2(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_item_color2(&mut self) {
        self._item_color2 = ::std::option::Option::None;
    }

    pub fn has_item_color2(&self) -> bool {
        match self._item_color2 {
            ::std::option::Option::Some(WorldAvatarItem_oneof__item_color2::item_color2(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_item_color2(&mut self, v: ::std::string::String) {
        self._item_color2 = ::std::option::Option::Some(WorldAvatarItem_oneof__item_color2::item_color2(v))
    }

    // Mutable pointer to the field.
    pub fn mut_item_color2(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarItem_oneof__item_color2::item_color2(_)) = self._item_color2 {
        } else {
            self._item_color2 = ::std::option::Option::Some(WorldAvatarItem_oneof__item_color2::item_color2(::std::string::String::new()));
        }
        match self._item_color2 {
            ::std::option::Option::Some(WorldAvatarItem_oneof__item_color2::item_color2(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_item_color2(&mut self) -> ::std::string::String {
        if self.has_item_color2() {
            match self._item_color2.take() {
                ::std::option::Option::Some(WorldAvatarItem_oneof__item_color2::item_color2(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 position_offset = 7;


    pub fn get_position_offset(&self) -> &WorldVector3 {
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarItem_oneof__position_offset::position_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_position_offset(&mut self) {
        self._position_offset = ::std::option::Option::None;
    }

    pub fn has_position_offset(&self) -> bool {
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarItem_oneof__position_offset::position_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_position_offset(&mut self, v: WorldVector3) {
        self._position_offset = ::std::option::Option::Some(WorldAvatarItem_oneof__position_offset::position_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_position_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarItem_oneof__position_offset::position_offset(_)) = self._position_offset {
        } else {
            self._position_offset = ::std::option::Option::Some(WorldAvatarItem_oneof__position_offset::position_offset(WorldVector3::new()));
        }
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarItem_oneof__position_offset::position_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_position_offset(&mut self) -> WorldVector3 {
        if self.has_position_offset() {
            match self._position_offset.take() {
                ::std::option::Option::Some(WorldAvatarItem_oneof__position_offset::position_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 rotation_offset = 8;


    pub fn get_rotation_offset(&self) -> &WorldVector3 {
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarItem_oneof__rotation_offset::rotation_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_rotation_offset(&mut self) {
        self._rotation_offset = ::std::option::Option::None;
    }

    pub fn has_rotation_offset(&self) -> bool {
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarItem_oneof__rotation_offset::rotation_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_rotation_offset(&mut self, v: WorldVector3) {
        self._rotation_offset = ::std::option::Option::Some(WorldAvatarItem_oneof__rotation_offset::rotation_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_rotation_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarItem_oneof__rotation_offset::rotation_offset(_)) = self._rotation_offset {
        } else {
            self._rotation_offset = ::std::option::Option::Some(WorldAvatarItem_oneof__rotation_offset::rotation_offset(WorldVector3::new()));
        }
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarItem_oneof__rotation_offset::rotation_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_rotation_offset(&mut self) -> WorldVector3 {
        if self.has_rotation_offset() {
            match self._rotation_offset.take() {
                ::std::option::Option::Some(WorldAvatarItem_oneof__rotation_offset::rotation_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 scale_offset = 9;


    pub fn get_scale_offset(&self) -> &WorldVector3 {
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarItem_oneof__scale_offset::scale_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_scale_offset(&mut self) {
        self._scale_offset = ::std::option::Option::None;
    }

    pub fn has_scale_offset(&self) -> bool {
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarItem_oneof__scale_offset::scale_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_scale_offset(&mut self, v: WorldVector3) {
        self._scale_offset = ::std::option::Option::Some(WorldAvatarItem_oneof__scale_offset::scale_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_scale_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarItem_oneof__scale_offset::scale_offset(_)) = self._scale_offset {
        } else {
            self._scale_offset = ::std::option::Option::Some(WorldAvatarItem_oneof__scale_offset::scale_offset(WorldVector3::new()));
        }
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarItem_oneof__scale_offset::scale_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_scale_offset(&mut self) -> WorldVector3 {
        if self.has_scale_offset() {
            match self._scale_offset.take() {
                ::std::option::Option::Some(WorldAvatarItem_oneof__scale_offset::scale_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }
}

impl ::protobuf::Message for WorldAvatarItem {
    fn is_initialized(&self) -> bool {
        if let Some(WorldAvatarItem_oneof__position_offset::position_offset(ref v)) = self._position_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(WorldAvatarItem_oneof__rotation_offset::rotation_offset(ref v)) = self._rotation_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(WorldAvatarItem_oneof__scale_offset::scale_offset(ref v)) = self._scale_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.item_key)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.title)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._description = ::std::option::Option::Some(WorldAvatarItem_oneof__description::description(is.read_string()?));
                },
                4 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.item_type, 4, &mut self.unknown_fields)?
                },
                5 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.item_color1)?;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._item_color2 = ::std::option::Option::Some(WorldAvatarItem_oneof__item_color2::item_color2(is.read_string()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._position_offset = ::std::option::Option::Some(WorldAvatarItem_oneof__position_offset::position_offset(is.read_message()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._rotation_offset = ::std::option::Option::Some(WorldAvatarItem_oneof__rotation_offset::rotation_offset(is.read_message()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._scale_offset = ::std::option::Option::Some(WorldAvatarItem_oneof__scale_offset::scale_offset(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.item_key.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.item_key);
        }
        if !self.title.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.title);
        }
        if self.item_type != WorldItemType::UNKNOWN {
            my_size += ::protobuf::rt::enum_size(4, self.item_type);
        }
        if !self.item_color1.is_empty() {
            my_size += ::protobuf::rt::string_size(5, &self.item_color1);
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarItem_oneof__description::description(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._item_color2 {
            match v {
                &WorldAvatarItem_oneof__item_color2::item_color2(ref v) => {
                    my_size += ::protobuf::rt::string_size(6, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._position_offset {
            match v {
                &WorldAvatarItem_oneof__position_offset::position_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._rotation_offset {
            match v {
                &WorldAvatarItem_oneof__rotation_offset::rotation_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._scale_offset {
            match v {
                &WorldAvatarItem_oneof__scale_offset::scale_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.item_key.is_empty() {
            os.write_string(1, &self.item_key)?;
        }
        if !self.title.is_empty() {
            os.write_string(2, &self.title)?;
        }
        if self.item_type != WorldItemType::UNKNOWN {
            os.write_enum(4, ::protobuf::ProtobufEnum::value(&self.item_type))?;
        }
        if !self.item_color1.is_empty() {
            os.write_string(5, &self.item_color1)?;
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarItem_oneof__description::description(ref v) => {
                    os.write_string(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._item_color2 {
            match v {
                &WorldAvatarItem_oneof__item_color2::item_color2(ref v) => {
                    os.write_string(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._position_offset {
            match v {
                &WorldAvatarItem_oneof__position_offset::position_offset(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._rotation_offset {
            match v {
                &WorldAvatarItem_oneof__rotation_offset::rotation_offset(ref v) => {
                    os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._scale_offset {
            match v {
                &WorldAvatarItem_oneof__scale_offset::scale_offset(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WorldAvatarItem {
        WorldAvatarItem::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "item_key",
                |m: &WorldAvatarItem| { &m.item_key },
                |m: &mut WorldAvatarItem| { &mut m.item_key },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "title",
                |m: &WorldAvatarItem| { &m.title },
                |m: &mut WorldAvatarItem| { &mut m.title },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "description",
                WorldAvatarItem::has_description,
                WorldAvatarItem::get_description,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<WorldItemType>>(
                "item_type",
                |m: &WorldAvatarItem| { &m.item_type },
                |m: &mut WorldAvatarItem| { &mut m.item_type },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "item_color1",
                |m: &WorldAvatarItem| { &m.item_color1 },
                |m: &mut WorldAvatarItem| { &mut m.item_color1 },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "item_color2",
                WorldAvatarItem::has_item_color2,
                WorldAvatarItem::get_item_color2,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "position_offset",
                WorldAvatarItem::has_position_offset,
                WorldAvatarItem::get_position_offset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "rotation_offset",
                WorldAvatarItem::has_rotation_offset,
                WorldAvatarItem::get_rotation_offset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "scale_offset",
                WorldAvatarItem::has_scale_offset,
                WorldAvatarItem::get_scale_offset,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<WorldAvatarItem>(
                "WorldAvatarItem",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static WorldAvatarItem {
        static instance: ::protobuf::rt::LazyV2<WorldAvatarItem> = ::protobuf::rt::LazyV2::INIT;
        instance.get(WorldAvatarItem::new)
    }
}

impl ::protobuf::Clear for WorldAvatarItem {
    fn clear(&mut self) {
        self.item_key.clear();
        self.title.clear();
        self._description = ::std::option::Option::None;
        self.item_type = WorldItemType::UNKNOWN;
        self.item_color1.clear();
        self._item_color2 = ::std::option::Option::None;
        self._position_offset = ::std::option::Option::None;
        self._rotation_offset = ::std::option::Option::None;
        self._scale_offset = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for WorldAvatarItem {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAvatarItem {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct WorldAvatarSpecies {
    // message fields
    pub title: ::std::string::String,
    pub species: WorldSpecies,
    // message oneof groups
    pub _description: ::std::option::Option<WorldAvatarSpecies_oneof__description>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WorldAvatarSpecies {
    fn default() -> &'a WorldAvatarSpecies {
        <WorldAvatarSpecies as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarSpecies_oneof__description {
    description(::std::string::String),
}

impl WorldAvatarSpecies {
    pub fn new() -> WorldAvatarSpecies {
        ::std::default::Default::default()
    }

    // string title = 1;


    pub fn get_title(&self) -> &str {
        &self.title
    }
    pub fn clear_title(&mut self) {
        self.title.clear();
    }

    // Param is passed by value, moved
    pub fn set_title(&mut self, v: ::std::string::String) {
        self.title = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_title(&mut self) -> &mut ::std::string::String {
        &mut self.title
    }

    // Take field
    pub fn take_title(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.title, ::std::string::String::new())
    }

    // string description = 2;


    pub fn get_description(&self) -> &str {
        match self._description {
            ::std::option::Option::Some(WorldAvatarSpecies_oneof__description::description(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_description(&mut self) {
        self._description = ::std::option::Option::None;
    }

    pub fn has_description(&self) -> bool {
        match self._description {
            ::std::option::Option::Some(WorldAvatarSpecies_oneof__description::description(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_description(&mut self, v: ::std::string::String) {
        self._description = ::std::option::Option::Some(WorldAvatarSpecies_oneof__description::description(v))
    }

    // Mutable pointer to the field.
    pub fn mut_description(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarSpecies_oneof__description::description(_)) = self._description {
        } else {
            self._description = ::std::option::Option::Some(WorldAvatarSpecies_oneof__description::description(::std::string::String::new()));
        }
        match self._description {
            ::std::option::Option::Some(WorldAvatarSpecies_oneof__description::description(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_description(&mut self) -> ::std::string::String {
        if self.has_description() {
            match self._description.take() {
                ::std::option::Option::Some(WorldAvatarSpecies_oneof__description::description(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldSpecies species = 3;


    pub fn get_species(&self) -> WorldSpecies {
        self.species
    }
    pub fn clear_species(&mut self) {
        self.species = WorldSpecies::HUMAN;
    }

    // Param is passed by value, moved
    pub fn set_species(&mut self, v: WorldSpecies) {
        self.species = v;
    }
}

impl ::protobuf::Message for WorldAvatarSpecies {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.title)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._description = ::std::option::Option::Some(WorldAvatarSpecies_oneof__description::description(is.read_string()?));
                },
                3 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.species, 3, &mut self.unknown_fields)?
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.title.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.title);
        }
        if self.species != WorldSpecies::HUMAN {
            my_size += ::protobuf::rt::enum_size(3, self.species);
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarSpecies_oneof__description::description(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.title.is_empty() {
            os.write_string(1, &self.title)?;
        }
        if self.species != WorldSpecies::HUMAN {
            os.write_enum(3, ::protobuf::ProtobufEnum::value(&self.species))?;
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarSpecies_oneof__description::description(ref v) => {
                    os.write_string(2, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WorldAvatarSpecies {
        WorldAvatarSpecies::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "title",
                |m: &WorldAvatarSpecies| { &m.title },
                |m: &mut WorldAvatarSpecies| { &mut m.title },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "description",
                WorldAvatarSpecies::has_description,
                WorldAvatarSpecies::get_description,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<WorldSpecies>>(
                "species",
                |m: &WorldAvatarSpecies| { &m.species },
                |m: &mut WorldAvatarSpecies| { &mut m.species },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<WorldAvatarSpecies>(
                "WorldAvatarSpecies",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static WorldAvatarSpecies {
        static instance: ::protobuf::rt::LazyV2<WorldAvatarSpecies> = ::protobuf::rt::LazyV2::INIT;
        instance.get(WorldAvatarSpecies::new)
    }
}

impl ::protobuf::Clear for WorldAvatarSpecies {
    fn clear(&mut self) {
        self.title.clear();
        self._description = ::std::option::Option::None;
        self.species = WorldSpecies::HUMAN;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for WorldAvatarSpecies {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAvatarSpecies {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct WorldAvatarClothing {
    // message fields
    pub item_key: ::std::string::String,
    pub title: ::std::string::String,
    pub item_type: WorldItemType,
    pub item_color1: ::std::string::String,
    // message oneof groups
    pub _description: ::std::option::Option<WorldAvatarClothing_oneof__description>,
    pub _item_color2: ::std::option::Option<WorldAvatarClothing_oneof__item_color2>,
    pub _position_offset: ::std::option::Option<WorldAvatarClothing_oneof__position_offset>,
    pub _rotation_offset: ::std::option::Option<WorldAvatarClothing_oneof__rotation_offset>,
    pub _scale_offset: ::std::option::Option<WorldAvatarClothing_oneof__scale_offset>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WorldAvatarClothing {
    fn default() -> &'a WorldAvatarClothing {
        <WorldAvatarClothing as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarClothing_oneof__description {
    description(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarClothing_oneof__item_color2 {
    item_color2(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarClothing_oneof__position_offset {
    position_offset(WorldVector3),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarClothing_oneof__rotation_offset {
    rotation_offset(WorldVector3),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarClothing_oneof__scale_offset {
    scale_offset(WorldVector3),
}

impl WorldAvatarClothing {
    pub fn new() -> WorldAvatarClothing {
        ::std::default::Default::default()
    }

    // string item_key = 1;


    pub fn get_item_key(&self) -> &str {
        &self.item_key
    }
    pub fn clear_item_key(&mut self) {
        self.item_key.clear();
    }

    // Param is passed by value, moved
    pub fn set_item_key(&mut self, v: ::std::string::String) {
        self.item_key = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_item_key(&mut self) -> &mut ::std::string::String {
        &mut self.item_key
    }

    // Take field
    pub fn take_item_key(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.item_key, ::std::string::String::new())
    }

    // string title = 2;


    pub fn get_title(&self) -> &str {
        &self.title
    }
    pub fn clear_title(&mut self) {
        self.title.clear();
    }

    // Param is passed by value, moved
    pub fn set_title(&mut self, v: ::std::string::String) {
        self.title = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_title(&mut self) -> &mut ::std::string::String {
        &mut self.title
    }

    // Take field
    pub fn take_title(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.title, ::std::string::String::new())
    }

    // string description = 3;


    pub fn get_description(&self) -> &str {
        match self._description {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__description::description(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_description(&mut self) {
        self._description = ::std::option::Option::None;
    }

    pub fn has_description(&self) -> bool {
        match self._description {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__description::description(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_description(&mut self, v: ::std::string::String) {
        self._description = ::std::option::Option::Some(WorldAvatarClothing_oneof__description::description(v))
    }

    // Mutable pointer to the field.
    pub fn mut_description(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarClothing_oneof__description::description(_)) = self._description {
        } else {
            self._description = ::std::option::Option::Some(WorldAvatarClothing_oneof__description::description(::std::string::String::new()));
        }
        match self._description {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__description::description(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_description(&mut self) -> ::std::string::String {
        if self.has_description() {
            match self._description.take() {
                ::std::option::Option::Some(WorldAvatarClothing_oneof__description::description(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldItemType item_type = 4;


    pub fn get_item_type(&self) -> WorldItemType {
        self.item_type
    }
    pub fn clear_item_type(&mut self) {
        self.item_type = WorldItemType::UNKNOWN;
    }

    // Param is passed by value, moved
    pub fn set_item_type(&mut self, v: WorldItemType) {
        self.item_type = v;
    }

    // string item_color1 = 5;


    pub fn get_item_color1(&self) -> &str {
        &self.item_color1
    }
    pub fn clear_item_color1(&mut self) {
        self.item_color1.clear();
    }

    // Param is passed by value, moved
    pub fn set_item_color1(&mut self, v: ::std::string::String) {
        self.item_color1 = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_item_color1(&mut self) -> &mut ::std::string::String {
        &mut self.item_color1
    }

    // Take field
    pub fn take_item_color1(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.item_color1, ::std::string::String::new())
    }

    // string item_color2 = 6;


    pub fn get_item_color2(&self) -> &str {
        match self._item_color2 {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__item_color2::item_color2(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_item_color2(&mut self) {
        self._item_color2 = ::std::option::Option::None;
    }

    pub fn has_item_color2(&self) -> bool {
        match self._item_color2 {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__item_color2::item_color2(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_item_color2(&mut self, v: ::std::string::String) {
        self._item_color2 = ::std::option::Option::Some(WorldAvatarClothing_oneof__item_color2::item_color2(v))
    }

    // Mutable pointer to the field.
    pub fn mut_item_color2(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarClothing_oneof__item_color2::item_color2(_)) = self._item_color2 {
        } else {
            self._item_color2 = ::std::option::Option::Some(WorldAvatarClothing_oneof__item_color2::item_color2(::std::string::String::new()));
        }
        match self._item_color2 {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__item_color2::item_color2(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_item_color2(&mut self) -> ::std::string::String {
        if self.has_item_color2() {
            match self._item_color2.take() {
                ::std::option::Option::Some(WorldAvatarClothing_oneof__item_color2::item_color2(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 position_offset = 7;


    pub fn get_position_offset(&self) -> &WorldVector3 {
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__position_offset::position_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_position_offset(&mut self) {
        self._position_offset = ::std::option::Option::None;
    }

    pub fn has_position_offset(&self) -> bool {
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__position_offset::position_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_position_offset(&mut self, v: WorldVector3) {
        self._position_offset = ::std::option::Option::Some(WorldAvatarClothing_oneof__position_offset::position_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_position_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarClothing_oneof__position_offset::position_offset(_)) = self._position_offset {
        } else {
            self._position_offset = ::std::option::Option::Some(WorldAvatarClothing_oneof__position_offset::position_offset(WorldVector3::new()));
        }
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__position_offset::position_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_position_offset(&mut self) -> WorldVector3 {
        if self.has_position_offset() {
            match self._position_offset.take() {
                ::std::option::Option::Some(WorldAvatarClothing_oneof__position_offset::position_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 rotation_offset = 8;


    pub fn get_rotation_offset(&self) -> &WorldVector3 {
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__rotation_offset::rotation_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_rotation_offset(&mut self) {
        self._rotation_offset = ::std::option::Option::None;
    }

    pub fn has_rotation_offset(&self) -> bool {
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__rotation_offset::rotation_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_rotation_offset(&mut self, v: WorldVector3) {
        self._rotation_offset = ::std::option::Option::Some(WorldAvatarClothing_oneof__rotation_offset::rotation_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_rotation_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarClothing_oneof__rotation_offset::rotation_offset(_)) = self._rotation_offset {
        } else {
            self._rotation_offset = ::std::option::Option::Some(WorldAvatarClothing_oneof__rotation_offset::rotation_offset(WorldVector3::new()));
        }
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__rotation_offset::rotation_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_rotation_offset(&mut self) -> WorldVector3 {
        if self.has_rotation_offset() {
            match self._rotation_offset.take() {
                ::std::option::Option::Some(WorldAvatarClothing_oneof__rotation_offset::rotation_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 scale_offset = 9;


    pub fn get_scale_offset(&self) -> &WorldVector3 {
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__scale_offset::scale_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_scale_offset(&mut self) {
        self._scale_offset = ::std::option::Option::None;
    }

    pub fn has_scale_offset(&self) -> bool {
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__scale_offset::scale_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_scale_offset(&mut self, v: WorldVector3) {
        self._scale_offset = ::std::option::Option::Some(WorldAvatarClothing_oneof__scale_offset::scale_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_scale_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarClothing_oneof__scale_offset::scale_offset(_)) = self._scale_offset {
        } else {
            self._scale_offset = ::std::option::Option::Some(WorldAvatarClothing_oneof__scale_offset::scale_offset(WorldVector3::new()));
        }
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarClothing_oneof__scale_offset::scale_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_scale_offset(&mut self) -> WorldVector3 {
        if self.has_scale_offset() {
            match self._scale_offset.take() {
                ::std::option::Option::Some(WorldAvatarClothing_oneof__scale_offset::scale_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }
}

impl ::protobuf::Message for WorldAvatarClothing {
    fn is_initialized(&self) -> bool {
        if let Some(WorldAvatarClothing_oneof__position_offset::position_offset(ref v)) = self._position_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(WorldAvatarClothing_oneof__rotation_offset::rotation_offset(ref v)) = self._rotation_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(WorldAvatarClothing_oneof__scale_offset::scale_offset(ref v)) = self._scale_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.item_key)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.title)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._description = ::std::option::Option::Some(WorldAvatarClothing_oneof__description::description(is.read_string()?));
                },
                4 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.item_type, 4, &mut self.unknown_fields)?
                },
                5 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.item_color1)?;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._item_color2 = ::std::option::Option::Some(WorldAvatarClothing_oneof__item_color2::item_color2(is.read_string()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._position_offset = ::std::option::Option::Some(WorldAvatarClothing_oneof__position_offset::position_offset(is.read_message()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._rotation_offset = ::std::option::Option::Some(WorldAvatarClothing_oneof__rotation_offset::rotation_offset(is.read_message()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._scale_offset = ::std::option::Option::Some(WorldAvatarClothing_oneof__scale_offset::scale_offset(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.item_key.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.item_key);
        }
        if !self.title.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.title);
        }
        if self.item_type != WorldItemType::UNKNOWN {
            my_size += ::protobuf::rt::enum_size(4, self.item_type);
        }
        if !self.item_color1.is_empty() {
            my_size += ::protobuf::rt::string_size(5, &self.item_color1);
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarClothing_oneof__description::description(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._item_color2 {
            match v {
                &WorldAvatarClothing_oneof__item_color2::item_color2(ref v) => {
                    my_size += ::protobuf::rt::string_size(6, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._position_offset {
            match v {
                &WorldAvatarClothing_oneof__position_offset::position_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._rotation_offset {
            match v {
                &WorldAvatarClothing_oneof__rotation_offset::rotation_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._scale_offset {
            match v {
                &WorldAvatarClothing_oneof__scale_offset::scale_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.item_key.is_empty() {
            os.write_string(1, &self.item_key)?;
        }
        if !self.title.is_empty() {
            os.write_string(2, &self.title)?;
        }
        if self.item_type != WorldItemType::UNKNOWN {
            os.write_enum(4, ::protobuf::ProtobufEnum::value(&self.item_type))?;
        }
        if !self.item_color1.is_empty() {
            os.write_string(5, &self.item_color1)?;
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarClothing_oneof__description::description(ref v) => {
                    os.write_string(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._item_color2 {
            match v {
                &WorldAvatarClothing_oneof__item_color2::item_color2(ref v) => {
                    os.write_string(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._position_offset {
            match v {
                &WorldAvatarClothing_oneof__position_offset::position_offset(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._rotation_offset {
            match v {
                &WorldAvatarClothing_oneof__rotation_offset::rotation_offset(ref v) => {
                    os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._scale_offset {
            match v {
                &WorldAvatarClothing_oneof__scale_offset::scale_offset(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WorldAvatarClothing {
        WorldAvatarClothing::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "item_key",
                |m: &WorldAvatarClothing| { &m.item_key },
                |m: &mut WorldAvatarClothing| { &mut m.item_key },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "title",
                |m: &WorldAvatarClothing| { &m.title },
                |m: &mut WorldAvatarClothing| { &mut m.title },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "description",
                WorldAvatarClothing::has_description,
                WorldAvatarClothing::get_description,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<WorldItemType>>(
                "item_type",
                |m: &WorldAvatarClothing| { &m.item_type },
                |m: &mut WorldAvatarClothing| { &mut m.item_type },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "item_color1",
                |m: &WorldAvatarClothing| { &m.item_color1 },
                |m: &mut WorldAvatarClothing| { &mut m.item_color1 },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "item_color2",
                WorldAvatarClothing::has_item_color2,
                WorldAvatarClothing::get_item_color2,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "position_offset",
                WorldAvatarClothing::has_position_offset,
                WorldAvatarClothing::get_position_offset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "rotation_offset",
                WorldAvatarClothing::has_rotation_offset,
                WorldAvatarClothing::get_rotation_offset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "scale_offset",
                WorldAvatarClothing::has_scale_offset,
                WorldAvatarClothing::get_scale_offset,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<WorldAvatarClothing>(
                "WorldAvatarClothing",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static WorldAvatarClothing {
        static instance: ::protobuf::rt::LazyV2<WorldAvatarClothing> = ::protobuf::rt::LazyV2::INIT;
        instance.get(WorldAvatarClothing::new)
    }
}

impl ::protobuf::Clear for WorldAvatarClothing {
    fn clear(&mut self) {
        self.item_key.clear();
        self.title.clear();
        self._description = ::std::option::Option::None;
        self.item_type = WorldItemType::UNKNOWN;
        self.item_color1.clear();
        self._item_color2 = ::std::option::Option::None;
        self._position_offset = ::std::option::Option::None;
        self._rotation_offset = ::std::option::Option::None;
        self._scale_offset = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for WorldAvatarClothing {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAvatarClothing {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct WorldAvatarArmor {
    // message fields
    pub item_key: ::std::string::String,
    pub title: ::std::string::String,
    pub item_type: WorldItemType,
    pub item_color1: ::std::string::String,
    pub item_color2: ::std::string::String,
    // message oneof groups
    pub _description: ::std::option::Option<WorldAvatarArmor_oneof__description>,
    pub _position_offset: ::std::option::Option<WorldAvatarArmor_oneof__position_offset>,
    pub _rotation_offset: ::std::option::Option<WorldAvatarArmor_oneof__rotation_offset>,
    pub _scale_offset: ::std::option::Option<WorldAvatarArmor_oneof__scale_offset>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WorldAvatarArmor {
    fn default() -> &'a WorldAvatarArmor {
        <WorldAvatarArmor as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarArmor_oneof__description {
    description(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarArmor_oneof__position_offset {
    position_offset(WorldVector3),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarArmor_oneof__rotation_offset {
    rotation_offset(WorldVector3),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarArmor_oneof__scale_offset {
    scale_offset(WorldVector3),
}

impl WorldAvatarArmor {
    pub fn new() -> WorldAvatarArmor {
        ::std::default::Default::default()
    }

    // string item_key = 1;


    pub fn get_item_key(&self) -> &str {
        &self.item_key
    }
    pub fn clear_item_key(&mut self) {
        self.item_key.clear();
    }

    // Param is passed by value, moved
    pub fn set_item_key(&mut self, v: ::std::string::String) {
        self.item_key = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_item_key(&mut self) -> &mut ::std::string::String {
        &mut self.item_key
    }

    // Take field
    pub fn take_item_key(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.item_key, ::std::string::String::new())
    }

    // string title = 2;


    pub fn get_title(&self) -> &str {
        &self.title
    }
    pub fn clear_title(&mut self) {
        self.title.clear();
    }

    // Param is passed by value, moved
    pub fn set_title(&mut self, v: ::std::string::String) {
        self.title = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_title(&mut self) -> &mut ::std::string::String {
        &mut self.title
    }

    // Take field
    pub fn take_title(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.title, ::std::string::String::new())
    }

    // string description = 3;


    pub fn get_description(&self) -> &str {
        match self._description {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__description::description(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_description(&mut self) {
        self._description = ::std::option::Option::None;
    }

    pub fn has_description(&self) -> bool {
        match self._description {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__description::description(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_description(&mut self, v: ::std::string::String) {
        self._description = ::std::option::Option::Some(WorldAvatarArmor_oneof__description::description(v))
    }

    // Mutable pointer to the field.
    pub fn mut_description(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarArmor_oneof__description::description(_)) = self._description {
        } else {
            self._description = ::std::option::Option::Some(WorldAvatarArmor_oneof__description::description(::std::string::String::new()));
        }
        match self._description {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__description::description(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_description(&mut self) -> ::std::string::String {
        if self.has_description() {
            match self._description.take() {
                ::std::option::Option::Some(WorldAvatarArmor_oneof__description::description(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldItemType item_type = 4;


    pub fn get_item_type(&self) -> WorldItemType {
        self.item_type
    }
    pub fn clear_item_type(&mut self) {
        self.item_type = WorldItemType::UNKNOWN;
    }

    // Param is passed by value, moved
    pub fn set_item_type(&mut self, v: WorldItemType) {
        self.item_type = v;
    }

    // string item_color1 = 5;


    pub fn get_item_color1(&self) -> &str {
        &self.item_color1
    }
    pub fn clear_item_color1(&mut self) {
        self.item_color1.clear();
    }

    // Param is passed by value, moved
    pub fn set_item_color1(&mut self, v: ::std::string::String) {
        self.item_color1 = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_item_color1(&mut self) -> &mut ::std::string::String {
        &mut self.item_color1
    }

    // Take field
    pub fn take_item_color1(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.item_color1, ::std::string::String::new())
    }

    // string item_color2 = 6;


    pub fn get_item_color2(&self) -> &str {
        &self.item_color2
    }
    pub fn clear_item_color2(&mut self) {
        self.item_color2.clear();
    }

    // Param is passed by value, moved
    pub fn set_item_color2(&mut self, v: ::std::string::String) {
        self.item_color2 = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_item_color2(&mut self) -> &mut ::std::string::String {
        &mut self.item_color2
    }

    // Take field
    pub fn take_item_color2(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.item_color2, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 position_offset = 7;


    pub fn get_position_offset(&self) -> &WorldVector3 {
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__position_offset::position_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_position_offset(&mut self) {
        self._position_offset = ::std::option::Option::None;
    }

    pub fn has_position_offset(&self) -> bool {
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__position_offset::position_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_position_offset(&mut self, v: WorldVector3) {
        self._position_offset = ::std::option::Option::Some(WorldAvatarArmor_oneof__position_offset::position_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_position_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarArmor_oneof__position_offset::position_offset(_)) = self._position_offset {
        } else {
            self._position_offset = ::std::option::Option::Some(WorldAvatarArmor_oneof__position_offset::position_offset(WorldVector3::new()));
        }
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__position_offset::position_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_position_offset(&mut self) -> WorldVector3 {
        if self.has_position_offset() {
            match self._position_offset.take() {
                ::std::option::Option::Some(WorldAvatarArmor_oneof__position_offset::position_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 rotation_offset = 8;


    pub fn get_rotation_offset(&self) -> &WorldVector3 {
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__rotation_offset::rotation_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_rotation_offset(&mut self) {
        self._rotation_offset = ::std::option::Option::None;
    }

    pub fn has_rotation_offset(&self) -> bool {
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__rotation_offset::rotation_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_rotation_offset(&mut self, v: WorldVector3) {
        self._rotation_offset = ::std::option::Option::Some(WorldAvatarArmor_oneof__rotation_offset::rotation_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_rotation_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarArmor_oneof__rotation_offset::rotation_offset(_)) = self._rotation_offset {
        } else {
            self._rotation_offset = ::std::option::Option::Some(WorldAvatarArmor_oneof__rotation_offset::rotation_offset(WorldVector3::new()));
        }
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__rotation_offset::rotation_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_rotation_offset(&mut self) -> WorldVector3 {
        if self.has_rotation_offset() {
            match self._rotation_offset.take() {
                ::std::option::Option::Some(WorldAvatarArmor_oneof__rotation_offset::rotation_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 scale_offset = 9;


    pub fn get_scale_offset(&self) -> &WorldVector3 {
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__scale_offset::scale_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_scale_offset(&mut self) {
        self._scale_offset = ::std::option::Option::None;
    }

    pub fn has_scale_offset(&self) -> bool {
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__scale_offset::scale_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_scale_offset(&mut self, v: WorldVector3) {
        self._scale_offset = ::std::option::Option::Some(WorldAvatarArmor_oneof__scale_offset::scale_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_scale_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarArmor_oneof__scale_offset::scale_offset(_)) = self._scale_offset {
        } else {
            self._scale_offset = ::std::option::Option::Some(WorldAvatarArmor_oneof__scale_offset::scale_offset(WorldVector3::new()));
        }
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarArmor_oneof__scale_offset::scale_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_scale_offset(&mut self) -> WorldVector3 {
        if self.has_scale_offset() {
            match self._scale_offset.take() {
                ::std::option::Option::Some(WorldAvatarArmor_oneof__scale_offset::scale_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }
}

impl ::protobuf::Message for WorldAvatarArmor {
    fn is_initialized(&self) -> bool {
        if let Some(WorldAvatarArmor_oneof__position_offset::position_offset(ref v)) = self._position_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(WorldAvatarArmor_oneof__rotation_offset::rotation_offset(ref v)) = self._rotation_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(WorldAvatarArmor_oneof__scale_offset::scale_offset(ref v)) = self._scale_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.item_key)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.title)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._description = ::std::option::Option::Some(WorldAvatarArmor_oneof__description::description(is.read_string()?));
                },
                4 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.item_type, 4, &mut self.unknown_fields)?
                },
                5 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.item_color1)?;
                },
                6 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.item_color2)?;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._position_offset = ::std::option::Option::Some(WorldAvatarArmor_oneof__position_offset::position_offset(is.read_message()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._rotation_offset = ::std::option::Option::Some(WorldAvatarArmor_oneof__rotation_offset::rotation_offset(is.read_message()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._scale_offset = ::std::option::Option::Some(WorldAvatarArmor_oneof__scale_offset::scale_offset(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.item_key.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.item_key);
        }
        if !self.title.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.title);
        }
        if self.item_type != WorldItemType::UNKNOWN {
            my_size += ::protobuf::rt::enum_size(4, self.item_type);
        }
        if !self.item_color1.is_empty() {
            my_size += ::protobuf::rt::string_size(5, &self.item_color1);
        }
        if !self.item_color2.is_empty() {
            my_size += ::protobuf::rt::string_size(6, &self.item_color2);
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarArmor_oneof__description::description(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._position_offset {
            match v {
                &WorldAvatarArmor_oneof__position_offset::position_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._rotation_offset {
            match v {
                &WorldAvatarArmor_oneof__rotation_offset::rotation_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._scale_offset {
            match v {
                &WorldAvatarArmor_oneof__scale_offset::scale_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.item_key.is_empty() {
            os.write_string(1, &self.item_key)?;
        }
        if !self.title.is_empty() {
            os.write_string(2, &self.title)?;
        }
        if self.item_type != WorldItemType::UNKNOWN {
            os.write_enum(4, ::protobuf::ProtobufEnum::value(&self.item_type))?;
        }
        if !self.item_color1.is_empty() {
            os.write_string(5, &self.item_color1)?;
        }
        if !self.item_color2.is_empty() {
            os.write_string(6, &self.item_color2)?;
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarArmor_oneof__description::description(ref v) => {
                    os.write_string(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._position_offset {
            match v {
                &WorldAvatarArmor_oneof__position_offset::position_offset(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._rotation_offset {
            match v {
                &WorldAvatarArmor_oneof__rotation_offset::rotation_offset(ref v) => {
                    os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._scale_offset {
            match v {
                &WorldAvatarArmor_oneof__scale_offset::scale_offset(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WorldAvatarArmor {
        WorldAvatarArmor::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "item_key",
                |m: &WorldAvatarArmor| { &m.item_key },
                |m: &mut WorldAvatarArmor| { &mut m.item_key },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "title",
                |m: &WorldAvatarArmor| { &m.title },
                |m: &mut WorldAvatarArmor| { &mut m.title },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "description",
                WorldAvatarArmor::has_description,
                WorldAvatarArmor::get_description,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<WorldItemType>>(
                "item_type",
                |m: &WorldAvatarArmor| { &m.item_type },
                |m: &mut WorldAvatarArmor| { &mut m.item_type },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "item_color1",
                |m: &WorldAvatarArmor| { &m.item_color1 },
                |m: &mut WorldAvatarArmor| { &mut m.item_color1 },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "item_color2",
                |m: &WorldAvatarArmor| { &m.item_color2 },
                |m: &mut WorldAvatarArmor| { &mut m.item_color2 },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "position_offset",
                WorldAvatarArmor::has_position_offset,
                WorldAvatarArmor::get_position_offset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "rotation_offset",
                WorldAvatarArmor::has_rotation_offset,
                WorldAvatarArmor::get_rotation_offset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "scale_offset",
                WorldAvatarArmor::has_scale_offset,
                WorldAvatarArmor::get_scale_offset,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<WorldAvatarArmor>(
                "WorldAvatarArmor",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static WorldAvatarArmor {
        static instance: ::protobuf::rt::LazyV2<WorldAvatarArmor> = ::protobuf::rt::LazyV2::INIT;
        instance.get(WorldAvatarArmor::new)
    }
}

impl ::protobuf::Clear for WorldAvatarArmor {
    fn clear(&mut self) {
        self.item_key.clear();
        self.title.clear();
        self._description = ::std::option::Option::None;
        self.item_type = WorldItemType::UNKNOWN;
        self.item_color1.clear();
        self.item_color2.clear();
        self._position_offset = ::std::option::Option::None;
        self._rotation_offset = ::std::option::Option::None;
        self._scale_offset = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for WorldAvatarArmor {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAvatarArmor {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct WorldAvatarPants {
    // message fields
    pub item_key: ::std::string::String,
    pub title: ::std::string::String,
    pub item_type: WorldItemType,
    pub item_color1: ::std::string::String,
    // message oneof groups
    pub _description: ::std::option::Option<WorldAvatarPants_oneof__description>,
    pub _item_color2: ::std::option::Option<WorldAvatarPants_oneof__item_color2>,
    pub _position_offset: ::std::option::Option<WorldAvatarPants_oneof__position_offset>,
    pub _rotation_offset: ::std::option::Option<WorldAvatarPants_oneof__rotation_offset>,
    pub _scale_offset: ::std::option::Option<WorldAvatarPants_oneof__scale_offset>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WorldAvatarPants {
    fn default() -> &'a WorldAvatarPants {
        <WorldAvatarPants as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarPants_oneof__description {
    description(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarPants_oneof__item_color2 {
    item_color2(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarPants_oneof__position_offset {
    position_offset(WorldVector3),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarPants_oneof__rotation_offset {
    rotation_offset(WorldVector3),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarPants_oneof__scale_offset {
    scale_offset(WorldVector3),
}

impl WorldAvatarPants {
    pub fn new() -> WorldAvatarPants {
        ::std::default::Default::default()
    }

    // string item_key = 1;


    pub fn get_item_key(&self) -> &str {
        &self.item_key
    }
    pub fn clear_item_key(&mut self) {
        self.item_key.clear();
    }

    // Param is passed by value, moved
    pub fn set_item_key(&mut self, v: ::std::string::String) {
        self.item_key = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_item_key(&mut self) -> &mut ::std::string::String {
        &mut self.item_key
    }

    // Take field
    pub fn take_item_key(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.item_key, ::std::string::String::new())
    }

    // string title = 2;


    pub fn get_title(&self) -> &str {
        &self.title
    }
    pub fn clear_title(&mut self) {
        self.title.clear();
    }

    // Param is passed by value, moved
    pub fn set_title(&mut self, v: ::std::string::String) {
        self.title = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_title(&mut self) -> &mut ::std::string::String {
        &mut self.title
    }

    // Take field
    pub fn take_title(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.title, ::std::string::String::new())
    }

    // string description = 3;


    pub fn get_description(&self) -> &str {
        match self._description {
            ::std::option::Option::Some(WorldAvatarPants_oneof__description::description(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_description(&mut self) {
        self._description = ::std::option::Option::None;
    }

    pub fn has_description(&self) -> bool {
        match self._description {
            ::std::option::Option::Some(WorldAvatarPants_oneof__description::description(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_description(&mut self, v: ::std::string::String) {
        self._description = ::std::option::Option::Some(WorldAvatarPants_oneof__description::description(v))
    }

    // Mutable pointer to the field.
    pub fn mut_description(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarPants_oneof__description::description(_)) = self._description {
        } else {
            self._description = ::std::option::Option::Some(WorldAvatarPants_oneof__description::description(::std::string::String::new()));
        }
        match self._description {
            ::std::option::Option::Some(WorldAvatarPants_oneof__description::description(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_description(&mut self) -> ::std::string::String {
        if self.has_description() {
            match self._description.take() {
                ::std::option::Option::Some(WorldAvatarPants_oneof__description::description(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldItemType item_type = 4;


    pub fn get_item_type(&self) -> WorldItemType {
        self.item_type
    }
    pub fn clear_item_type(&mut self) {
        self.item_type = WorldItemType::UNKNOWN;
    }

    // Param is passed by value, moved
    pub fn set_item_type(&mut self, v: WorldItemType) {
        self.item_type = v;
    }

    // string item_color1 = 5;


    pub fn get_item_color1(&self) -> &str {
        &self.item_color1
    }
    pub fn clear_item_color1(&mut self) {
        self.item_color1.clear();
    }

    // Param is passed by value, moved
    pub fn set_item_color1(&mut self, v: ::std::string::String) {
        self.item_color1 = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_item_color1(&mut self) -> &mut ::std::string::String {
        &mut self.item_color1
    }

    // Take field
    pub fn take_item_color1(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.item_color1, ::std::string::String::new())
    }

    // string item_color2 = 6;


    pub fn get_item_color2(&self) -> &str {
        match self._item_color2 {
            ::std::option::Option::Some(WorldAvatarPants_oneof__item_color2::item_color2(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_item_color2(&mut self) {
        self._item_color2 = ::std::option::Option::None;
    }

    pub fn has_item_color2(&self) -> bool {
        match self._item_color2 {
            ::std::option::Option::Some(WorldAvatarPants_oneof__item_color2::item_color2(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_item_color2(&mut self, v: ::std::string::String) {
        self._item_color2 = ::std::option::Option::Some(WorldAvatarPants_oneof__item_color2::item_color2(v))
    }

    // Mutable pointer to the field.
    pub fn mut_item_color2(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarPants_oneof__item_color2::item_color2(_)) = self._item_color2 {
        } else {
            self._item_color2 = ::std::option::Option::Some(WorldAvatarPants_oneof__item_color2::item_color2(::std::string::String::new()));
        }
        match self._item_color2 {
            ::std::option::Option::Some(WorldAvatarPants_oneof__item_color2::item_color2(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_item_color2(&mut self) -> ::std::string::String {
        if self.has_item_color2() {
            match self._item_color2.take() {
                ::std::option::Option::Some(WorldAvatarPants_oneof__item_color2::item_color2(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 position_offset = 7;


    pub fn get_position_offset(&self) -> &WorldVector3 {
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarPants_oneof__position_offset::position_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_position_offset(&mut self) {
        self._position_offset = ::std::option::Option::None;
    }

    pub fn has_position_offset(&self) -> bool {
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarPants_oneof__position_offset::position_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_position_offset(&mut self, v: WorldVector3) {
        self._position_offset = ::std::option::Option::Some(WorldAvatarPants_oneof__position_offset::position_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_position_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarPants_oneof__position_offset::position_offset(_)) = self._position_offset {
        } else {
            self._position_offset = ::std::option::Option::Some(WorldAvatarPants_oneof__position_offset::position_offset(WorldVector3::new()));
        }
        match self._position_offset {
            ::std::option::Option::Some(WorldAvatarPants_oneof__position_offset::position_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_position_offset(&mut self) -> WorldVector3 {
        if self.has_position_offset() {
            match self._position_offset.take() {
                ::std::option::Option::Some(WorldAvatarPants_oneof__position_offset::position_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 rotation_offset = 8;


    pub fn get_rotation_offset(&self) -> &WorldVector3 {
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarPants_oneof__rotation_offset::rotation_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_rotation_offset(&mut self) {
        self._rotation_offset = ::std::option::Option::None;
    }

    pub fn has_rotation_offset(&self) -> bool {
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarPants_oneof__rotation_offset::rotation_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_rotation_offset(&mut self, v: WorldVector3) {
        self._rotation_offset = ::std::option::Option::Some(WorldAvatarPants_oneof__rotation_offset::rotation_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_rotation_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarPants_oneof__rotation_offset::rotation_offset(_)) = self._rotation_offset {
        } else {
            self._rotation_offset = ::std::option::Option::Some(WorldAvatarPants_oneof__rotation_offset::rotation_offset(WorldVector3::new()));
        }
        match self._rotation_offset {
            ::std::option::Option::Some(WorldAvatarPants_oneof__rotation_offset::rotation_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_rotation_offset(&mut self) -> WorldVector3 {
        if self.has_rotation_offset() {
            match self._rotation_offset.take() {
                ::std::option::Option::Some(WorldAvatarPants_oneof__rotation_offset::rotation_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldVector3 scale_offset = 9;


    pub fn get_scale_offset(&self) -> &WorldVector3 {
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarPants_oneof__scale_offset::scale_offset(ref v)) => v,
            _ => <WorldVector3 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_scale_offset(&mut self) {
        self._scale_offset = ::std::option::Option::None;
    }

    pub fn has_scale_offset(&self) -> bool {
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarPants_oneof__scale_offset::scale_offset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_scale_offset(&mut self, v: WorldVector3) {
        self._scale_offset = ::std::option::Option::Some(WorldAvatarPants_oneof__scale_offset::scale_offset(v))
    }

    // Mutable pointer to the field.
    pub fn mut_scale_offset(&mut self) -> &mut WorldVector3 {
        if let ::std::option::Option::Some(WorldAvatarPants_oneof__scale_offset::scale_offset(_)) = self._scale_offset {
        } else {
            self._scale_offset = ::std::option::Option::Some(WorldAvatarPants_oneof__scale_offset::scale_offset(WorldVector3::new()));
        }
        match self._scale_offset {
            ::std::option::Option::Some(WorldAvatarPants_oneof__scale_offset::scale_offset(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_scale_offset(&mut self) -> WorldVector3 {
        if self.has_scale_offset() {
            match self._scale_offset.take() {
                ::std::option::Option::Some(WorldAvatarPants_oneof__scale_offset::scale_offset(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldVector3::new()
        }
    }
}

impl ::protobuf::Message for WorldAvatarPants {
    fn is_initialized(&self) -> bool {
        if let Some(WorldAvatarPants_oneof__position_offset::position_offset(ref v)) = self._position_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(WorldAvatarPants_oneof__rotation_offset::rotation_offset(ref v)) = self._rotation_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(WorldAvatarPants_oneof__scale_offset::scale_offset(ref v)) = self._scale_offset {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.item_key)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.title)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._description = ::std::option::Option::Some(WorldAvatarPants_oneof__description::description(is.read_string()?));
                },
                4 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.item_type, 4, &mut self.unknown_fields)?
                },
                5 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.item_color1)?;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._item_color2 = ::std::option::Option::Some(WorldAvatarPants_oneof__item_color2::item_color2(is.read_string()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._position_offset = ::std::option::Option::Some(WorldAvatarPants_oneof__position_offset::position_offset(is.read_message()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._rotation_offset = ::std::option::Option::Some(WorldAvatarPants_oneof__rotation_offset::rotation_offset(is.read_message()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._scale_offset = ::std::option::Option::Some(WorldAvatarPants_oneof__scale_offset::scale_offset(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.item_key.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.item_key);
        }
        if !self.title.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.title);
        }
        if self.item_type != WorldItemType::UNKNOWN {
            my_size += ::protobuf::rt::enum_size(4, self.item_type);
        }
        if !self.item_color1.is_empty() {
            my_size += ::protobuf::rt::string_size(5, &self.item_color1);
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarPants_oneof__description::description(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._item_color2 {
            match v {
                &WorldAvatarPants_oneof__item_color2::item_color2(ref v) => {
                    my_size += ::protobuf::rt::string_size(6, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._position_offset {
            match v {
                &WorldAvatarPants_oneof__position_offset::position_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._rotation_offset {
            match v {
                &WorldAvatarPants_oneof__rotation_offset::rotation_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._scale_offset {
            match v {
                &WorldAvatarPants_oneof__scale_offset::scale_offset(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.item_key.is_empty() {
            os.write_string(1, &self.item_key)?;
        }
        if !self.title.is_empty() {
            os.write_string(2, &self.title)?;
        }
        if self.item_type != WorldItemType::UNKNOWN {
            os.write_enum(4, ::protobuf::ProtobufEnum::value(&self.item_type))?;
        }
        if !self.item_color1.is_empty() {
            os.write_string(5, &self.item_color1)?;
        }
        if let ::std::option::Option::Some(ref v) = self._description {
            match v {
                &WorldAvatarPants_oneof__description::description(ref v) => {
                    os.write_string(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._item_color2 {
            match v {
                &WorldAvatarPants_oneof__item_color2::item_color2(ref v) => {
                    os.write_string(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._position_offset {
            match v {
                &WorldAvatarPants_oneof__position_offset::position_offset(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._rotation_offset {
            match v {
                &WorldAvatarPants_oneof__rotation_offset::rotation_offset(ref v) => {
                    os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._scale_offset {
            match v {
                &WorldAvatarPants_oneof__scale_offset::scale_offset(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WorldAvatarPants {
        WorldAvatarPants::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "item_key",
                |m: &WorldAvatarPants| { &m.item_key },
                |m: &mut WorldAvatarPants| { &mut m.item_key },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "title",
                |m: &WorldAvatarPants| { &m.title },
                |m: &mut WorldAvatarPants| { &mut m.title },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "description",
                WorldAvatarPants::has_description,
                WorldAvatarPants::get_description,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<WorldItemType>>(
                "item_type",
                |m: &WorldAvatarPants| { &m.item_type },
                |m: &mut WorldAvatarPants| { &mut m.item_type },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "item_color1",
                |m: &WorldAvatarPants| { &m.item_color1 },
                |m: &mut WorldAvatarPants| { &mut m.item_color1 },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "item_color2",
                WorldAvatarPants::has_item_color2,
                WorldAvatarPants::get_item_color2,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "position_offset",
                WorldAvatarPants::has_position_offset,
                WorldAvatarPants::get_position_offset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "rotation_offset",
                WorldAvatarPants::has_rotation_offset,
                WorldAvatarPants::get_rotation_offset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldVector3>(
                "scale_offset",
                WorldAvatarPants::has_scale_offset,
                WorldAvatarPants::get_scale_offset,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<WorldAvatarPants>(
                "WorldAvatarPants",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static WorldAvatarPants {
        static instance: ::protobuf::rt::LazyV2<WorldAvatarPants> = ::protobuf::rt::LazyV2::INIT;
        instance.get(WorldAvatarPants::new)
    }
}

impl ::protobuf::Clear for WorldAvatarPants {
    fn clear(&mut self) {
        self.item_key.clear();
        self.title.clear();
        self._description = ::std::option::Option::None;
        self.item_type = WorldItemType::UNKNOWN;
        self.item_color1.clear();
        self._item_color2 = ::std::option::Option::None;
        self._position_offset = ::std::option::Option::None;
        self._rotation_offset = ::std::option::Option::None;
        self._scale_offset = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for WorldAvatarPants {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAvatarPants {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct WorldAvatarUserData {
    // message fields
    pub gender: WorldGender,
    pub species: WorldSpecies,
    pub clothing: ::protobuf::SingularPtrField<WorldAvatarClothing>,
    pub hair: ::protobuf::SingularPtrField<WorldAvatarItem>,
    pub facial_hair: ::protobuf::SingularPtrField<WorldAvatarItem>,
    pub accessory: ::protobuf::SingularPtrField<WorldAvatarItem>,
    pub helmet: ::protobuf::SingularPtrField<WorldAvatarArmor>,
    pub body_armor: ::protobuf::SingularPtrField<WorldAvatarArmor>,
    pub pants: ::protobuf::SingularPtrField<WorldAvatarPants>,
    // message oneof groups
    pub _eye_color_left: ::std::option::Option<WorldAvatarUserData_oneof__eye_color_left>,
    pub _eye_color_right: ::std::option::Option<WorldAvatarUserData_oneof__eye_color_right>,
    pub _eyebrow_color_left: ::std::option::Option<WorldAvatarUserData_oneof__eyebrow_color_left>,
    pub _eyebrow_color_right: ::std::option::Option<WorldAvatarUserData_oneof__eyebrow_color_right>,
    pub _preset_avatar: ::std::option::Option<WorldAvatarUserData_oneof__preset_avatar>,
    pub _species_color: ::std::option::Option<WorldAvatarUserData_oneof__species_color>,
    pub _hair_color: ::std::option::Option<WorldAvatarUserData_oneof__hair_color>,
    pub _facial_hair_color: ::std::option::Option<WorldAvatarUserData_oneof__facial_hair_color>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WorldAvatarUserData {
    fn default() -> &'a WorldAvatarUserData {
        <WorldAvatarUserData as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarUserData_oneof__eye_color_left {
    eye_color_left(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarUserData_oneof__eye_color_right {
    eye_color_right(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarUserData_oneof__eyebrow_color_left {
    eyebrow_color_left(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarUserData_oneof__eyebrow_color_right {
    eyebrow_color_right(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarUserData_oneof__preset_avatar {
    preset_avatar(WorldAssetPresetCharacterType),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarUserData_oneof__species_color {
    species_color(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarUserData_oneof__hair_color {
    hair_color(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldAvatarUserData_oneof__facial_hair_color {
    facial_hair_color(::std::string::String),
}

impl WorldAvatarUserData {
    pub fn new() -> WorldAvatarUserData {
        ::std::default::Default::default()
    }

    // string eye_color_left = 1;


    pub fn get_eye_color_left(&self) -> &str {
        match self._eye_color_left {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_left::eye_color_left(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_eye_color_left(&mut self) {
        self._eye_color_left = ::std::option::Option::None;
    }

    pub fn has_eye_color_left(&self) -> bool {
        match self._eye_color_left {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_left::eye_color_left(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_eye_color_left(&mut self, v: ::std::string::String) {
        self._eye_color_left = ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_left::eye_color_left(v))
    }

    // Mutable pointer to the field.
    pub fn mut_eye_color_left(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_left::eye_color_left(_)) = self._eye_color_left {
        } else {
            self._eye_color_left = ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_left::eye_color_left(::std::string::String::new()));
        }
        match self._eye_color_left {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_left::eye_color_left(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_eye_color_left(&mut self) -> ::std::string::String {
        if self.has_eye_color_left() {
            match self._eye_color_left.take() {
                ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_left::eye_color_left(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string eye_color_right = 2;


    pub fn get_eye_color_right(&self) -> &str {
        match self._eye_color_right {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_right::eye_color_right(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_eye_color_right(&mut self) {
        self._eye_color_right = ::std::option::Option::None;
    }

    pub fn has_eye_color_right(&self) -> bool {
        match self._eye_color_right {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_right::eye_color_right(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_eye_color_right(&mut self, v: ::std::string::String) {
        self._eye_color_right = ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_right::eye_color_right(v))
    }

    // Mutable pointer to the field.
    pub fn mut_eye_color_right(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_right::eye_color_right(_)) = self._eye_color_right {
        } else {
            self._eye_color_right = ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_right::eye_color_right(::std::string::String::new()));
        }
        match self._eye_color_right {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_right::eye_color_right(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_eye_color_right(&mut self) -> ::std::string::String {
        if self.has_eye_color_right() {
            match self._eye_color_right.take() {
                ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_right::eye_color_right(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string eyebrow_color_left = 3;


    pub fn get_eyebrow_color_left(&self) -> &str {
        match self._eyebrow_color_left {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_eyebrow_color_left(&mut self) {
        self._eyebrow_color_left = ::std::option::Option::None;
    }

    pub fn has_eyebrow_color_left(&self) -> bool {
        match self._eyebrow_color_left {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_eyebrow_color_left(&mut self, v: ::std::string::String) {
        self._eyebrow_color_left = ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(v))
    }

    // Mutable pointer to the field.
    pub fn mut_eyebrow_color_left(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(_)) = self._eyebrow_color_left {
        } else {
            self._eyebrow_color_left = ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(::std::string::String::new()));
        }
        match self._eyebrow_color_left {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_eyebrow_color_left(&mut self) -> ::std::string::String {
        if self.has_eyebrow_color_left() {
            match self._eyebrow_color_left.take() {
                ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string eyebrow_color_right = 4;


    pub fn get_eyebrow_color_right(&self) -> &str {
        match self._eyebrow_color_right {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_eyebrow_color_right(&mut self) {
        self._eyebrow_color_right = ::std::option::Option::None;
    }

    pub fn has_eyebrow_color_right(&self) -> bool {
        match self._eyebrow_color_right {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_eyebrow_color_right(&mut self, v: ::std::string::String) {
        self._eyebrow_color_right = ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(v))
    }

    // Mutable pointer to the field.
    pub fn mut_eyebrow_color_right(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(_)) = self._eyebrow_color_right {
        } else {
            self._eyebrow_color_right = ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(::std::string::String::new()));
        }
        match self._eyebrow_color_right {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_eyebrow_color_right(&mut self) -> ::std::string::String {
        if self.has_eyebrow_color_right() {
            match self._eyebrow_color_right.take() {
                ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldGender gender = 5;


    pub fn get_gender(&self) -> WorldGender {
        self.gender
    }
    pub fn clear_gender(&mut self) {
        self.gender = WorldGender::MALE;
    }

    // Param is passed by value, moved
    pub fn set_gender(&mut self, v: WorldGender) {
        self.gender = v;
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldSpecies species = 6;


    pub fn get_species(&self) -> WorldSpecies {
        self.species
    }
    pub fn clear_species(&mut self) {
        self.species = WorldSpecies::HUMAN;
    }

    // Param is passed by value, moved
    pub fn set_species(&mut self, v: WorldSpecies) {
        self.species = v;
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAssetPresetCharacterType preset_avatar = 7;


    pub fn get_preset_avatar(&self) -> WorldAssetPresetCharacterType {
        match self._preset_avatar {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__preset_avatar::preset_avatar(v)) => v,
            _ => WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_UNSPECIFIED,
        }
    }
    pub fn clear_preset_avatar(&mut self) {
        self._preset_avatar = ::std::option::Option::None;
    }

    pub fn has_preset_avatar(&self) -> bool {
        match self._preset_avatar {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__preset_avatar::preset_avatar(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_preset_avatar(&mut self, v: WorldAssetPresetCharacterType) {
        self._preset_avatar = ::std::option::Option::Some(WorldAvatarUserData_oneof__preset_avatar::preset_avatar(v))
    }

    // string species_color = 8;


    pub fn get_species_color(&self) -> &str {
        match self._species_color {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__species_color::species_color(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_species_color(&mut self) {
        self._species_color = ::std::option::Option::None;
    }

    pub fn has_species_color(&self) -> bool {
        match self._species_color {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__species_color::species_color(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_species_color(&mut self, v: ::std::string::String) {
        self._species_color = ::std::option::Option::Some(WorldAvatarUserData_oneof__species_color::species_color(v))
    }

    // Mutable pointer to the field.
    pub fn mut_species_color(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarUserData_oneof__species_color::species_color(_)) = self._species_color {
        } else {
            self._species_color = ::std::option::Option::Some(WorldAvatarUserData_oneof__species_color::species_color(::std::string::String::new()));
        }
        match self._species_color {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__species_color::species_color(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_species_color(&mut self) -> ::std::string::String {
        if self.has_species_color() {
            match self._species_color.take() {
                ::std::option::Option::Some(WorldAvatarUserData_oneof__species_color::species_color(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string hair_color = 9;


    pub fn get_hair_color(&self) -> &str {
        match self._hair_color {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__hair_color::hair_color(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_hair_color(&mut self) {
        self._hair_color = ::std::option::Option::None;
    }

    pub fn has_hair_color(&self) -> bool {
        match self._hair_color {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__hair_color::hair_color(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_hair_color(&mut self, v: ::std::string::String) {
        self._hair_color = ::std::option::Option::Some(WorldAvatarUserData_oneof__hair_color::hair_color(v))
    }

    // Mutable pointer to the field.
    pub fn mut_hair_color(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarUserData_oneof__hair_color::hair_color(_)) = self._hair_color {
        } else {
            self._hair_color = ::std::option::Option::Some(WorldAvatarUserData_oneof__hair_color::hair_color(::std::string::String::new()));
        }
        match self._hair_color {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__hair_color::hair_color(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_hair_color(&mut self) -> ::std::string::String {
        if self.has_hair_color() {
            match self._hair_color.take() {
                ::std::option::Option::Some(WorldAvatarUserData_oneof__hair_color::hair_color(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string facial_hair_color = 10;


    pub fn get_facial_hair_color(&self) -> &str {
        match self._facial_hair_color {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_facial_hair_color(&mut self) {
        self._facial_hair_color = ::std::option::Option::None;
    }

    pub fn has_facial_hair_color(&self) -> bool {
        match self._facial_hair_color {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_facial_hair_color(&mut self, v: ::std::string::String) {
        self._facial_hair_color = ::std::option::Option::Some(WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(v))
    }

    // Mutable pointer to the field.
    pub fn mut_facial_hair_color(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(_)) = self._facial_hair_color {
        } else {
            self._facial_hair_color = ::std::option::Option::Some(WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(::std::string::String::new()));
        }
        match self._facial_hair_color {
            ::std::option::Option::Some(WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_facial_hair_color(&mut self) -> ::std::string::String {
        if self.has_facial_hair_color() {
            match self._facial_hair_color.take() {
                ::std::option::Option::Some(WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarClothing clothing = 11;


    pub fn get_clothing(&self) -> &WorldAvatarClothing {
        self.clothing.as_ref().unwrap_or_else(|| <WorldAvatarClothing as ::protobuf::Message>::default_instance())
    }
    pub fn clear_clothing(&mut self) {
        self.clothing.clear();
    }

    pub fn has_clothing(&self) -> bool {
        self.clothing.is_some()
    }

    // Param is passed by value, moved
    pub fn set_clothing(&mut self, v: WorldAvatarClothing) {
        self.clothing = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_clothing(&mut self) -> &mut WorldAvatarClothing {
        if self.clothing.is_none() {
            self.clothing.set_default();
        }
        self.clothing.as_mut().unwrap()
    }

    // Take field
    pub fn take_clothing(&mut self) -> WorldAvatarClothing {
        self.clothing.take().unwrap_or_else(|| WorldAvatarClothing::new())
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarItem hair = 12;


    pub fn get_hair(&self) -> &WorldAvatarItem {
        self.hair.as_ref().unwrap_or_else(|| <WorldAvatarItem as ::protobuf::Message>::default_instance())
    }
    pub fn clear_hair(&mut self) {
        self.hair.clear();
    }

    pub fn has_hair(&self) -> bool {
        self.hair.is_some()
    }

    // Param is passed by value, moved
    pub fn set_hair(&mut self, v: WorldAvatarItem) {
        self.hair = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_hair(&mut self) -> &mut WorldAvatarItem {
        if self.hair.is_none() {
            self.hair.set_default();
        }
        self.hair.as_mut().unwrap()
    }

    // Take field
    pub fn take_hair(&mut self) -> WorldAvatarItem {
        self.hair.take().unwrap_or_else(|| WorldAvatarItem::new())
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarItem facial_hair = 13;


    pub fn get_facial_hair(&self) -> &WorldAvatarItem {
        self.facial_hair.as_ref().unwrap_or_else(|| <WorldAvatarItem as ::protobuf::Message>::default_instance())
    }
    pub fn clear_facial_hair(&mut self) {
        self.facial_hair.clear();
    }

    pub fn has_facial_hair(&self) -> bool {
        self.facial_hair.is_some()
    }

    // Param is passed by value, moved
    pub fn set_facial_hair(&mut self, v: WorldAvatarItem) {
        self.facial_hair = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_facial_hair(&mut self) -> &mut WorldAvatarItem {
        if self.facial_hair.is_none() {
            self.facial_hair.set_default();
        }
        self.facial_hair.as_mut().unwrap()
    }

    // Take field
    pub fn take_facial_hair(&mut self) -> WorldAvatarItem {
        self.facial_hair.take().unwrap_or_else(|| WorldAvatarItem::new())
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarItem accessory = 14;


    pub fn get_accessory(&self) -> &WorldAvatarItem {
        self.accessory.as_ref().unwrap_or_else(|| <WorldAvatarItem as ::protobuf::Message>::default_instance())
    }
    pub fn clear_accessory(&mut self) {
        self.accessory.clear();
    }

    pub fn has_accessory(&self) -> bool {
        self.accessory.is_some()
    }

    // Param is passed by value, moved
    pub fn set_accessory(&mut self, v: WorldAvatarItem) {
        self.accessory = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_accessory(&mut self) -> &mut WorldAvatarItem {
        if self.accessory.is_none() {
            self.accessory.set_default();
        }
        self.accessory.as_mut().unwrap()
    }

    // Take field
    pub fn take_accessory(&mut self) -> WorldAvatarItem {
        self.accessory.take().unwrap_or_else(|| WorldAvatarItem::new())
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarArmor helmet = 15;


    pub fn get_helmet(&self) -> &WorldAvatarArmor {
        self.helmet.as_ref().unwrap_or_else(|| <WorldAvatarArmor as ::protobuf::Message>::default_instance())
    }
    pub fn clear_helmet(&mut self) {
        self.helmet.clear();
    }

    pub fn has_helmet(&self) -> bool {
        self.helmet.is_some()
    }

    // Param is passed by value, moved
    pub fn set_helmet(&mut self, v: WorldAvatarArmor) {
        self.helmet = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_helmet(&mut self) -> &mut WorldAvatarArmor {
        if self.helmet.is_none() {
            self.helmet.set_default();
        }
        self.helmet.as_mut().unwrap()
    }

    // Take field
    pub fn take_helmet(&mut self) -> WorldAvatarArmor {
        self.helmet.take().unwrap_or_else(|| WorldAvatarArmor::new())
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarArmor body_armor = 16;


    pub fn get_body_armor(&self) -> &WorldAvatarArmor {
        self.body_armor.as_ref().unwrap_or_else(|| <WorldAvatarArmor as ::protobuf::Message>::default_instance())
    }
    pub fn clear_body_armor(&mut self) {
        self.body_armor.clear();
    }

    pub fn has_body_armor(&self) -> bool {
        self.body_armor.is_some()
    }

    // Param is passed by value, moved
    pub fn set_body_armor(&mut self, v: WorldAvatarArmor) {
        self.body_armor = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_body_armor(&mut self) -> &mut WorldAvatarArmor {
        if self.body_armor.is_none() {
            self.body_armor.set_default();
        }
        self.body_armor.as_mut().unwrap()
    }

    // Take field
    pub fn take_body_armor(&mut self) -> WorldAvatarArmor {
        self.body_armor.take().unwrap_or_else(|| WorldAvatarArmor::new())
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarPants pants = 17;


    pub fn get_pants(&self) -> &WorldAvatarPants {
        self.pants.as_ref().unwrap_or_else(|| <WorldAvatarPants as ::protobuf::Message>::default_instance())
    }
    pub fn clear_pants(&mut self) {
        self.pants.clear();
    }

    pub fn has_pants(&self) -> bool {
        self.pants.is_some()
    }

    // Param is passed by value, moved
    pub fn set_pants(&mut self, v: WorldAvatarPants) {
        self.pants = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_pants(&mut self) -> &mut WorldAvatarPants {
        if self.pants.is_none() {
            self.pants.set_default();
        }
        self.pants.as_mut().unwrap()
    }

    // Take field
    pub fn take_pants(&mut self) -> WorldAvatarPants {
        self.pants.take().unwrap_or_else(|| WorldAvatarPants::new())
    }
}

impl ::protobuf::Message for WorldAvatarUserData {
    fn is_initialized(&self) -> bool {
        for v in &self.clothing {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.hair {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.facial_hair {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.accessory {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.helmet {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.body_armor {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.pants {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._eye_color_left = ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_left::eye_color_left(is.read_string()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._eye_color_right = ::std::option::Option::Some(WorldAvatarUserData_oneof__eye_color_right::eye_color_right(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._eyebrow_color_left = ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(is.read_string()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._eyebrow_color_right = ::std::option::Option::Some(WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(is.read_string()?));
                },
                5 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.gender, 5, &mut self.unknown_fields)?
                },
                6 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.species, 6, &mut self.unknown_fields)?
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._preset_avatar = ::std::option::Option::Some(WorldAvatarUserData_oneof__preset_avatar::preset_avatar(is.read_enum()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._species_color = ::std::option::Option::Some(WorldAvatarUserData_oneof__species_color::species_color(is.read_string()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._hair_color = ::std::option::Option::Some(WorldAvatarUserData_oneof__hair_color::hair_color(is.read_string()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._facial_hair_color = ::std::option::Option::Some(WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(is.read_string()?));
                },
                11 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.clothing)?;
                },
                12 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.hair)?;
                },
                13 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.facial_hair)?;
                },
                14 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.accessory)?;
                },
                15 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.helmet)?;
                },
                16 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.body_armor)?;
                },
                17 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.pants)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.gender != WorldGender::MALE {
            my_size += ::protobuf::rt::enum_size(5, self.gender);
        }
        if self.species != WorldSpecies::HUMAN {
            my_size += ::protobuf::rt::enum_size(6, self.species);
        }
        if let Some(ref v) = self.clothing.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.hair.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.facial_hair.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.accessory.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.helmet.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.body_armor.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.pants.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let ::std::option::Option::Some(ref v) = self._eye_color_left {
            match v {
                &WorldAvatarUserData_oneof__eye_color_left::eye_color_left(ref v) => {
                    my_size += ::protobuf::rt::string_size(1, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eye_color_right {
            match v {
                &WorldAvatarUserData_oneof__eye_color_right::eye_color_right(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eyebrow_color_left {
            match v {
                &WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eyebrow_color_right {
            match v {
                &WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(ref v) => {
                    my_size += ::protobuf::rt::string_size(4, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._preset_avatar {
            match v {
                &WorldAvatarUserData_oneof__preset_avatar::preset_avatar(v) => {
                    my_size += ::protobuf::rt::enum_size(7, v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._species_color {
            match v {
                &WorldAvatarUserData_oneof__species_color::species_color(ref v) => {
                    my_size += ::protobuf::rt::string_size(8, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._hair_color {
            match v {
                &WorldAvatarUserData_oneof__hair_color::hair_color(ref v) => {
                    my_size += ::protobuf::rt::string_size(9, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._facial_hair_color {
            match v {
                &WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(ref v) => {
                    my_size += ::protobuf::rt::string_size(10, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.gender != WorldGender::MALE {
            os.write_enum(5, ::protobuf::ProtobufEnum::value(&self.gender))?;
        }
        if self.species != WorldSpecies::HUMAN {
            os.write_enum(6, ::protobuf::ProtobufEnum::value(&self.species))?;
        }
        if let Some(ref v) = self.clothing.as_ref() {
            os.write_tag(11, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.hair.as_ref() {
            os.write_tag(12, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.facial_hair.as_ref() {
            os.write_tag(13, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.accessory.as_ref() {
            os.write_tag(14, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.helmet.as_ref() {
            os.write_tag(15, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.body_armor.as_ref() {
            os.write_tag(16, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.pants.as_ref() {
            os.write_tag(17, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let ::std::option::Option::Some(ref v) = self._eye_color_left {
            match v {
                &WorldAvatarUserData_oneof__eye_color_left::eye_color_left(ref v) => {
                    os.write_string(1, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eye_color_right {
            match v {
                &WorldAvatarUserData_oneof__eye_color_right::eye_color_right(ref v) => {
                    os.write_string(2, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eyebrow_color_left {
            match v {
                &WorldAvatarUserData_oneof__eyebrow_color_left::eyebrow_color_left(ref v) => {
                    os.write_string(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eyebrow_color_right {
            match v {
                &WorldAvatarUserData_oneof__eyebrow_color_right::eyebrow_color_right(ref v) => {
                    os.write_string(4, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._preset_avatar {
            match v {
                &WorldAvatarUserData_oneof__preset_avatar::preset_avatar(v) => {
                    os.write_enum(7, ::protobuf::ProtobufEnum::value(&v))?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._species_color {
            match v {
                &WorldAvatarUserData_oneof__species_color::species_color(ref v) => {
                    os.write_string(8, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._hair_color {
            match v {
                &WorldAvatarUserData_oneof__hair_color::hair_color(ref v) => {
                    os.write_string(9, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._facial_hair_color {
            match v {
                &WorldAvatarUserData_oneof__facial_hair_color::facial_hair_color(ref v) => {
                    os.write_string(10, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WorldAvatarUserData {
        WorldAvatarUserData::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "eye_color_left",
                WorldAvatarUserData::has_eye_color_left,
                WorldAvatarUserData::get_eye_color_left,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "eye_color_right",
                WorldAvatarUserData::has_eye_color_right,
                WorldAvatarUserData::get_eye_color_right,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "eyebrow_color_left",
                WorldAvatarUserData::has_eyebrow_color_left,
                WorldAvatarUserData::get_eyebrow_color_left,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "eyebrow_color_right",
                WorldAvatarUserData::has_eyebrow_color_right,
                WorldAvatarUserData::get_eyebrow_color_right,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<WorldGender>>(
                "gender",
                |m: &WorldAvatarUserData| { &m.gender },
                |m: &mut WorldAvatarUserData| { &mut m.gender },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<WorldSpecies>>(
                "species",
                |m: &WorldAvatarUserData| { &m.species },
                |m: &mut WorldAvatarUserData| { &mut m.species },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_enum_accessor::<_, WorldAssetPresetCharacterType>(
                "preset_avatar",
                WorldAvatarUserData::has_preset_avatar,
                WorldAvatarUserData::get_preset_avatar,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "species_color",
                WorldAvatarUserData::has_species_color,
                WorldAvatarUserData::get_species_color,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "hair_color",
                WorldAvatarUserData::has_hair_color,
                WorldAvatarUserData::get_hair_color,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "facial_hair_color",
                WorldAvatarUserData::has_facial_hair_color,
                WorldAvatarUserData::get_facial_hair_color,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<WorldAvatarClothing>>(
                "clothing",
                |m: &WorldAvatarUserData| { &m.clothing },
                |m: &mut WorldAvatarUserData| { &mut m.clothing },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<WorldAvatarItem>>(
                "hair",
                |m: &WorldAvatarUserData| { &m.hair },
                |m: &mut WorldAvatarUserData| { &mut m.hair },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<WorldAvatarItem>>(
                "facial_hair",
                |m: &WorldAvatarUserData| { &m.facial_hair },
                |m: &mut WorldAvatarUserData| { &mut m.facial_hair },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<WorldAvatarItem>>(
                "accessory",
                |m: &WorldAvatarUserData| { &m.accessory },
                |m: &mut WorldAvatarUserData| { &mut m.accessory },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<WorldAvatarArmor>>(
                "helmet",
                |m: &WorldAvatarUserData| { &m.helmet },
                |m: &mut WorldAvatarUserData| { &mut m.helmet },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<WorldAvatarArmor>>(
                "body_armor",
                |m: &WorldAvatarUserData| { &m.body_armor },
                |m: &mut WorldAvatarUserData| { &mut m.body_armor },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<WorldAvatarPants>>(
                "pants",
                |m: &WorldAvatarUserData| { &m.pants },
                |m: &mut WorldAvatarUserData| { &mut m.pants },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<WorldAvatarUserData>(
                "WorldAvatarUserData",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static WorldAvatarUserData {
        static instance: ::protobuf::rt::LazyV2<WorldAvatarUserData> = ::protobuf::rt::LazyV2::INIT;
        instance.get(WorldAvatarUserData::new)
    }
}

impl ::protobuf::Clear for WorldAvatarUserData {
    fn clear(&mut self) {
        self._eye_color_left = ::std::option::Option::None;
        self._eye_color_right = ::std::option::Option::None;
        self._eyebrow_color_left = ::std::option::Option::None;
        self._eyebrow_color_right = ::std::option::Option::None;
        self.gender = WorldGender::MALE;
        self.species = WorldSpecies::HUMAN;
        self._preset_avatar = ::std::option::Option::None;
        self._species_color = ::std::option::Option::None;
        self._hair_color = ::std::option::Option::None;
        self._facial_hair_color = ::std::option::Option::None;
        self.clothing.clear();
        self.hair.clear();
        self.facial_hair.clear();
        self.accessory.clear();
        self.helmet.clear();
        self.body_armor.clear();
        self.pants.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for WorldAvatarUserData {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAvatarUserData {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AvatarCustomizationData {
    // message oneof groups
    pub _eye_color_left: ::std::option::Option<AvatarCustomizationData_oneof__eye_color_left>,
    pub _eye_color_right: ::std::option::Option<AvatarCustomizationData_oneof__eye_color_right>,
    pub _eyebrow_color_left: ::std::option::Option<AvatarCustomizationData_oneof__eyebrow_color_left>,
    pub _eyebrow_color_right: ::std::option::Option<AvatarCustomizationData_oneof__eyebrow_color_right>,
    pub _gender: ::std::option::Option<AvatarCustomizationData_oneof__gender>,
    pub _species: ::std::option::Option<AvatarCustomizationData_oneof__species>,
    pub _preset_avatar: ::std::option::Option<AvatarCustomizationData_oneof__preset_avatar>,
    pub _species_color: ::std::option::Option<AvatarCustomizationData_oneof__species_color>,
    pub _hair_color: ::std::option::Option<AvatarCustomizationData_oneof__hair_color>,
    pub _facial_hair_color: ::std::option::Option<AvatarCustomizationData_oneof__facial_hair_color>,
    pub _clothing: ::std::option::Option<AvatarCustomizationData_oneof__clothing>,
    pub _hair: ::std::option::Option<AvatarCustomizationData_oneof__hair>,
    pub _facial_hair: ::std::option::Option<AvatarCustomizationData_oneof__facial_hair>,
    pub _accessory: ::std::option::Option<AvatarCustomizationData_oneof__accessory>,
    pub _helmet: ::std::option::Option<AvatarCustomizationData_oneof__helmet>,
    pub _body_armor: ::std::option::Option<AvatarCustomizationData_oneof__body_armor>,
    pub _pants: ::std::option::Option<AvatarCustomizationData_oneof__pants>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AvatarCustomizationData {
    fn default() -> &'a AvatarCustomizationData {
        <AvatarCustomizationData as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__eye_color_left {
    eye_color_left(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__eye_color_right {
    eye_color_right(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__eyebrow_color_left {
    eyebrow_color_left(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__eyebrow_color_right {
    eyebrow_color_right(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__gender {
    gender(WorldGender),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__species {
    species(WorldSpecies),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__preset_avatar {
    preset_avatar(WorldAssetPresetCharacterType),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__species_color {
    species_color(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__hair_color {
    hair_color(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__facial_hair_color {
    facial_hair_color(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__clothing {
    clothing(WorldAvatarClothing),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__hair {
    hair(WorldAvatarItem),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__facial_hair {
    facial_hair(WorldAvatarItem),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__accessory {
    accessory(WorldAvatarItem),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__helmet {
    helmet(WorldAvatarArmor),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__body_armor {
    body_armor(WorldAvatarArmor),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCustomizationData_oneof__pants {
    pants(WorldAvatarPants),
}

impl AvatarCustomizationData {
    pub fn new() -> AvatarCustomizationData {
        ::std::default::Default::default()
    }

    // string eye_color_left = 1;


    pub fn get_eye_color_left(&self) -> &str {
        match self._eye_color_left {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_left::eye_color_left(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_eye_color_left(&mut self) {
        self._eye_color_left = ::std::option::Option::None;
    }

    pub fn has_eye_color_left(&self) -> bool {
        match self._eye_color_left {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_left::eye_color_left(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_eye_color_left(&mut self, v: ::std::string::String) {
        self._eye_color_left = ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_left::eye_color_left(v))
    }

    // Mutable pointer to the field.
    pub fn mut_eye_color_left(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_left::eye_color_left(_)) = self._eye_color_left {
        } else {
            self._eye_color_left = ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_left::eye_color_left(::std::string::String::new()));
        }
        match self._eye_color_left {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_left::eye_color_left(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_eye_color_left(&mut self) -> ::std::string::String {
        if self.has_eye_color_left() {
            match self._eye_color_left.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_left::eye_color_left(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string eye_color_right = 2;


    pub fn get_eye_color_right(&self) -> &str {
        match self._eye_color_right {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_right::eye_color_right(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_eye_color_right(&mut self) {
        self._eye_color_right = ::std::option::Option::None;
    }

    pub fn has_eye_color_right(&self) -> bool {
        match self._eye_color_right {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_right::eye_color_right(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_eye_color_right(&mut self, v: ::std::string::String) {
        self._eye_color_right = ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_right::eye_color_right(v))
    }

    // Mutable pointer to the field.
    pub fn mut_eye_color_right(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_right::eye_color_right(_)) = self._eye_color_right {
        } else {
            self._eye_color_right = ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_right::eye_color_right(::std::string::String::new()));
        }
        match self._eye_color_right {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_right::eye_color_right(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_eye_color_right(&mut self) -> ::std::string::String {
        if self.has_eye_color_right() {
            match self._eye_color_right.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_right::eye_color_right(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string eyebrow_color_left = 3;


    pub fn get_eyebrow_color_left(&self) -> &str {
        match self._eyebrow_color_left {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_eyebrow_color_left(&mut self) {
        self._eyebrow_color_left = ::std::option::Option::None;
    }

    pub fn has_eyebrow_color_left(&self) -> bool {
        match self._eyebrow_color_left {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_eyebrow_color_left(&mut self, v: ::std::string::String) {
        self._eyebrow_color_left = ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(v))
    }

    // Mutable pointer to the field.
    pub fn mut_eyebrow_color_left(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(_)) = self._eyebrow_color_left {
        } else {
            self._eyebrow_color_left = ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(::std::string::String::new()));
        }
        match self._eyebrow_color_left {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_eyebrow_color_left(&mut self) -> ::std::string::String {
        if self.has_eyebrow_color_left() {
            match self._eyebrow_color_left.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string eyebrow_color_right = 4;


    pub fn get_eyebrow_color_right(&self) -> &str {
        match self._eyebrow_color_right {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_eyebrow_color_right(&mut self) {
        self._eyebrow_color_right = ::std::option::Option::None;
    }

    pub fn has_eyebrow_color_right(&self) -> bool {
        match self._eyebrow_color_right {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_eyebrow_color_right(&mut self, v: ::std::string::String) {
        self._eyebrow_color_right = ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(v))
    }

    // Mutable pointer to the field.
    pub fn mut_eyebrow_color_right(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(_)) = self._eyebrow_color_right {
        } else {
            self._eyebrow_color_right = ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(::std::string::String::new()));
        }
        match self._eyebrow_color_right {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_eyebrow_color_right(&mut self) -> ::std::string::String {
        if self.has_eyebrow_color_right() {
            match self._eyebrow_color_right.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldGender gender = 5;


    pub fn get_gender(&self) -> WorldGender {
        match self._gender {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__gender::gender(v)) => v,
            _ => WorldGender::MALE,
        }
    }
    pub fn clear_gender(&mut self) {
        self._gender = ::std::option::Option::None;
    }

    pub fn has_gender(&self) -> bool {
        match self._gender {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__gender::gender(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_gender(&mut self, v: WorldGender) {
        self._gender = ::std::option::Option::Some(AvatarCustomizationData_oneof__gender::gender(v))
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldSpecies species = 6;


    pub fn get_species(&self) -> WorldSpecies {
        match self._species {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__species::species(v)) => v,
            _ => WorldSpecies::HUMAN,
        }
    }
    pub fn clear_species(&mut self) {
        self._species = ::std::option::Option::None;
    }

    pub fn has_species(&self) -> bool {
        match self._species {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__species::species(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_species(&mut self, v: WorldSpecies) {
        self._species = ::std::option::Option::Some(AvatarCustomizationData_oneof__species::species(v))
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAssetPresetCharacterType preset_avatar = 7;


    pub fn get_preset_avatar(&self) -> WorldAssetPresetCharacterType {
        match self._preset_avatar {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__preset_avatar::preset_avatar(v)) => v,
            _ => WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_UNSPECIFIED,
        }
    }
    pub fn clear_preset_avatar(&mut self) {
        self._preset_avatar = ::std::option::Option::None;
    }

    pub fn has_preset_avatar(&self) -> bool {
        match self._preset_avatar {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__preset_avatar::preset_avatar(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_preset_avatar(&mut self, v: WorldAssetPresetCharacterType) {
        self._preset_avatar = ::std::option::Option::Some(AvatarCustomizationData_oneof__preset_avatar::preset_avatar(v))
    }

    // string species_color = 8;


    pub fn get_species_color(&self) -> &str {
        match self._species_color {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__species_color::species_color(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_species_color(&mut self) {
        self._species_color = ::std::option::Option::None;
    }

    pub fn has_species_color(&self) -> bool {
        match self._species_color {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__species_color::species_color(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_species_color(&mut self, v: ::std::string::String) {
        self._species_color = ::std::option::Option::Some(AvatarCustomizationData_oneof__species_color::species_color(v))
    }

    // Mutable pointer to the field.
    pub fn mut_species_color(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__species_color::species_color(_)) = self._species_color {
        } else {
            self._species_color = ::std::option::Option::Some(AvatarCustomizationData_oneof__species_color::species_color(::std::string::String::new()));
        }
        match self._species_color {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__species_color::species_color(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_species_color(&mut self) -> ::std::string::String {
        if self.has_species_color() {
            match self._species_color.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__species_color::species_color(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string hair_color = 9;


    pub fn get_hair_color(&self) -> &str {
        match self._hair_color {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__hair_color::hair_color(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_hair_color(&mut self) {
        self._hair_color = ::std::option::Option::None;
    }

    pub fn has_hair_color(&self) -> bool {
        match self._hair_color {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__hair_color::hair_color(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_hair_color(&mut self, v: ::std::string::String) {
        self._hair_color = ::std::option::Option::Some(AvatarCustomizationData_oneof__hair_color::hair_color(v))
    }

    // Mutable pointer to the field.
    pub fn mut_hair_color(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__hair_color::hair_color(_)) = self._hair_color {
        } else {
            self._hair_color = ::std::option::Option::Some(AvatarCustomizationData_oneof__hair_color::hair_color(::std::string::String::new()));
        }
        match self._hair_color {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__hair_color::hair_color(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_hair_color(&mut self) -> ::std::string::String {
        if self.has_hair_color() {
            match self._hair_color.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__hair_color::hair_color(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string facial_hair_color = 10;


    pub fn get_facial_hair_color(&self) -> &str {
        match self._facial_hair_color {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_facial_hair_color(&mut self) {
        self._facial_hair_color = ::std::option::Option::None;
    }

    pub fn has_facial_hair_color(&self) -> bool {
        match self._facial_hair_color {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_facial_hair_color(&mut self, v: ::std::string::String) {
        self._facial_hair_color = ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(v))
    }

    // Mutable pointer to the field.
    pub fn mut_facial_hair_color(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(_)) = self._facial_hair_color {
        } else {
            self._facial_hair_color = ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(::std::string::String::new()));
        }
        match self._facial_hair_color {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_facial_hair_color(&mut self) -> ::std::string::String {
        if self.has_facial_hair_color() {
            match self._facial_hair_color.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarClothing clothing = 11;


    pub fn get_clothing(&self) -> &WorldAvatarClothing {
        match self._clothing {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__clothing::clothing(ref v)) => v,
            _ => <WorldAvatarClothing as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_clothing(&mut self) {
        self._clothing = ::std::option::Option::None;
    }

    pub fn has_clothing(&self) -> bool {
        match self._clothing {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__clothing::clothing(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clothing(&mut self, v: WorldAvatarClothing) {
        self._clothing = ::std::option::Option::Some(AvatarCustomizationData_oneof__clothing::clothing(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clothing(&mut self) -> &mut WorldAvatarClothing {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__clothing::clothing(_)) = self._clothing {
        } else {
            self._clothing = ::std::option::Option::Some(AvatarCustomizationData_oneof__clothing::clothing(WorldAvatarClothing::new()));
        }
        match self._clothing {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__clothing::clothing(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clothing(&mut self) -> WorldAvatarClothing {
        if self.has_clothing() {
            match self._clothing.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__clothing::clothing(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldAvatarClothing::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarItem hair = 12;


    pub fn get_hair(&self) -> &WorldAvatarItem {
        match self._hair {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__hair::hair(ref v)) => v,
            _ => <WorldAvatarItem as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_hair(&mut self) {
        self._hair = ::std::option::Option::None;
    }

    pub fn has_hair(&self) -> bool {
        match self._hair {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__hair::hair(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_hair(&mut self, v: WorldAvatarItem) {
        self._hair = ::std::option::Option::Some(AvatarCustomizationData_oneof__hair::hair(v))
    }

    // Mutable pointer to the field.
    pub fn mut_hair(&mut self) -> &mut WorldAvatarItem {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__hair::hair(_)) = self._hair {
        } else {
            self._hair = ::std::option::Option::Some(AvatarCustomizationData_oneof__hair::hair(WorldAvatarItem::new()));
        }
        match self._hair {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__hair::hair(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_hair(&mut self) -> WorldAvatarItem {
        if self.has_hair() {
            match self._hair.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__hair::hair(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldAvatarItem::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarItem facial_hair = 13;


    pub fn get_facial_hair(&self) -> &WorldAvatarItem {
        match self._facial_hair {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair::facial_hair(ref v)) => v,
            _ => <WorldAvatarItem as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_facial_hair(&mut self) {
        self._facial_hair = ::std::option::Option::None;
    }

    pub fn has_facial_hair(&self) -> bool {
        match self._facial_hair {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair::facial_hair(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_facial_hair(&mut self, v: WorldAvatarItem) {
        self._facial_hair = ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair::facial_hair(v))
    }

    // Mutable pointer to the field.
    pub fn mut_facial_hair(&mut self) -> &mut WorldAvatarItem {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair::facial_hair(_)) = self._facial_hair {
        } else {
            self._facial_hair = ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair::facial_hair(WorldAvatarItem::new()));
        }
        match self._facial_hair {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair::facial_hair(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_facial_hair(&mut self) -> WorldAvatarItem {
        if self.has_facial_hair() {
            match self._facial_hair.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair::facial_hair(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldAvatarItem::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarItem accessory = 14;


    pub fn get_accessory(&self) -> &WorldAvatarItem {
        match self._accessory {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__accessory::accessory(ref v)) => v,
            _ => <WorldAvatarItem as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_accessory(&mut self) {
        self._accessory = ::std::option::Option::None;
    }

    pub fn has_accessory(&self) -> bool {
        match self._accessory {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__accessory::accessory(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_accessory(&mut self, v: WorldAvatarItem) {
        self._accessory = ::std::option::Option::Some(AvatarCustomizationData_oneof__accessory::accessory(v))
    }

    // Mutable pointer to the field.
    pub fn mut_accessory(&mut self) -> &mut WorldAvatarItem {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__accessory::accessory(_)) = self._accessory {
        } else {
            self._accessory = ::std::option::Option::Some(AvatarCustomizationData_oneof__accessory::accessory(WorldAvatarItem::new()));
        }
        match self._accessory {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__accessory::accessory(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_accessory(&mut self) -> WorldAvatarItem {
        if self.has_accessory() {
            match self._accessory.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__accessory::accessory(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldAvatarItem::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarArmor helmet = 15;


    pub fn get_helmet(&self) -> &WorldAvatarArmor {
        match self._helmet {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__helmet::helmet(ref v)) => v,
            _ => <WorldAvatarArmor as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_helmet(&mut self) {
        self._helmet = ::std::option::Option::None;
    }

    pub fn has_helmet(&self) -> bool {
        match self._helmet {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__helmet::helmet(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_helmet(&mut self, v: WorldAvatarArmor) {
        self._helmet = ::std::option::Option::Some(AvatarCustomizationData_oneof__helmet::helmet(v))
    }

    // Mutable pointer to the field.
    pub fn mut_helmet(&mut self) -> &mut WorldAvatarArmor {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__helmet::helmet(_)) = self._helmet {
        } else {
            self._helmet = ::std::option::Option::Some(AvatarCustomizationData_oneof__helmet::helmet(WorldAvatarArmor::new()));
        }
        match self._helmet {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__helmet::helmet(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_helmet(&mut self) -> WorldAvatarArmor {
        if self.has_helmet() {
            match self._helmet.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__helmet::helmet(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldAvatarArmor::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarArmor body_armor = 16;


    pub fn get_body_armor(&self) -> &WorldAvatarArmor {
        match self._body_armor {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__body_armor::body_armor(ref v)) => v,
            _ => <WorldAvatarArmor as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_body_armor(&mut self) {
        self._body_armor = ::std::option::Option::None;
    }

    pub fn has_body_armor(&self) -> bool {
        match self._body_armor {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__body_armor::body_armor(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_body_armor(&mut self, v: WorldAvatarArmor) {
        self._body_armor = ::std::option::Option::Some(AvatarCustomizationData_oneof__body_armor::body_armor(v))
    }

    // Mutable pointer to the field.
    pub fn mut_body_armor(&mut self) -> &mut WorldAvatarArmor {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__body_armor::body_armor(_)) = self._body_armor {
        } else {
            self._body_armor = ::std::option::Option::Some(AvatarCustomizationData_oneof__body_armor::body_armor(WorldAvatarArmor::new()));
        }
        match self._body_armor {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__body_armor::body_armor(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_body_armor(&mut self) -> WorldAvatarArmor {
        if self.has_body_armor() {
            match self._body_armor.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__body_armor::body_armor(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldAvatarArmor::new()
        }
    }

    // .PianoRhythm.Serialization.WorldRenditions.WorldAvatarPants pants = 17;


    pub fn get_pants(&self) -> &WorldAvatarPants {
        match self._pants {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__pants::pants(ref v)) => v,
            _ => <WorldAvatarPants as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_pants(&mut self) {
        self._pants = ::std::option::Option::None;
    }

    pub fn has_pants(&self) -> bool {
        match self._pants {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__pants::pants(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pants(&mut self, v: WorldAvatarPants) {
        self._pants = ::std::option::Option::Some(AvatarCustomizationData_oneof__pants::pants(v))
    }

    // Mutable pointer to the field.
    pub fn mut_pants(&mut self) -> &mut WorldAvatarPants {
        if let ::std::option::Option::Some(AvatarCustomizationData_oneof__pants::pants(_)) = self._pants {
        } else {
            self._pants = ::std::option::Option::Some(AvatarCustomizationData_oneof__pants::pants(WorldAvatarPants::new()));
        }
        match self._pants {
            ::std::option::Option::Some(AvatarCustomizationData_oneof__pants::pants(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_pants(&mut self) -> WorldAvatarPants {
        if self.has_pants() {
            match self._pants.take() {
                ::std::option::Option::Some(AvatarCustomizationData_oneof__pants::pants(v)) => v,
                _ => panic!(),
            }
        } else {
            WorldAvatarPants::new()
        }
    }
}

impl ::protobuf::Message for AvatarCustomizationData {
    fn is_initialized(&self) -> bool {
        if let Some(AvatarCustomizationData_oneof__clothing::clothing(ref v)) = self._clothing {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AvatarCustomizationData_oneof__hair::hair(ref v)) = self._hair {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AvatarCustomizationData_oneof__facial_hair::facial_hair(ref v)) = self._facial_hair {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AvatarCustomizationData_oneof__accessory::accessory(ref v)) = self._accessory {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AvatarCustomizationData_oneof__helmet::helmet(ref v)) = self._helmet {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AvatarCustomizationData_oneof__body_armor::body_armor(ref v)) = self._body_armor {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AvatarCustomizationData_oneof__pants::pants(ref v)) = self._pants {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._eye_color_left = ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_left::eye_color_left(is.read_string()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._eye_color_right = ::std::option::Option::Some(AvatarCustomizationData_oneof__eye_color_right::eye_color_right(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._eyebrow_color_left = ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(is.read_string()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._eyebrow_color_right = ::std::option::Option::Some(AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(is.read_string()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._gender = ::std::option::Option::Some(AvatarCustomizationData_oneof__gender::gender(is.read_enum()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._species = ::std::option::Option::Some(AvatarCustomizationData_oneof__species::species(is.read_enum()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._preset_avatar = ::std::option::Option::Some(AvatarCustomizationData_oneof__preset_avatar::preset_avatar(is.read_enum()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._species_color = ::std::option::Option::Some(AvatarCustomizationData_oneof__species_color::species_color(is.read_string()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._hair_color = ::std::option::Option::Some(AvatarCustomizationData_oneof__hair_color::hair_color(is.read_string()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._facial_hair_color = ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(is.read_string()?));
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._clothing = ::std::option::Option::Some(AvatarCustomizationData_oneof__clothing::clothing(is.read_message()?));
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._hair = ::std::option::Option::Some(AvatarCustomizationData_oneof__hair::hair(is.read_message()?));
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._facial_hair = ::std::option::Option::Some(AvatarCustomizationData_oneof__facial_hair::facial_hair(is.read_message()?));
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._accessory = ::std::option::Option::Some(AvatarCustomizationData_oneof__accessory::accessory(is.read_message()?));
                },
                15 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._helmet = ::std::option::Option::Some(AvatarCustomizationData_oneof__helmet::helmet(is.read_message()?));
                },
                16 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._body_armor = ::std::option::Option::Some(AvatarCustomizationData_oneof__body_armor::body_armor(is.read_message()?));
                },
                17 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._pants = ::std::option::Option::Some(AvatarCustomizationData_oneof__pants::pants(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let ::std::option::Option::Some(ref v) = self._eye_color_left {
            match v {
                &AvatarCustomizationData_oneof__eye_color_left::eye_color_left(ref v) => {
                    my_size += ::protobuf::rt::string_size(1, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eye_color_right {
            match v {
                &AvatarCustomizationData_oneof__eye_color_right::eye_color_right(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eyebrow_color_left {
            match v {
                &AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eyebrow_color_right {
            match v {
                &AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(ref v) => {
                    my_size += ::protobuf::rt::string_size(4, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._gender {
            match v {
                &AvatarCustomizationData_oneof__gender::gender(v) => {
                    my_size += ::protobuf::rt::enum_size(5, v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._species {
            match v {
                &AvatarCustomizationData_oneof__species::species(v) => {
                    my_size += ::protobuf::rt::enum_size(6, v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._preset_avatar {
            match v {
                &AvatarCustomizationData_oneof__preset_avatar::preset_avatar(v) => {
                    my_size += ::protobuf::rt::enum_size(7, v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._species_color {
            match v {
                &AvatarCustomizationData_oneof__species_color::species_color(ref v) => {
                    my_size += ::protobuf::rt::string_size(8, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._hair_color {
            match v {
                &AvatarCustomizationData_oneof__hair_color::hair_color(ref v) => {
                    my_size += ::protobuf::rt::string_size(9, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._facial_hair_color {
            match v {
                &AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(ref v) => {
                    my_size += ::protobuf::rt::string_size(10, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._clothing {
            match v {
                &AvatarCustomizationData_oneof__clothing::clothing(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._hair {
            match v {
                &AvatarCustomizationData_oneof__hair::hair(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._facial_hair {
            match v {
                &AvatarCustomizationData_oneof__facial_hair::facial_hair(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._accessory {
            match v {
                &AvatarCustomizationData_oneof__accessory::accessory(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._helmet {
            match v {
                &AvatarCustomizationData_oneof__helmet::helmet(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._body_armor {
            match v {
                &AvatarCustomizationData_oneof__body_armor::body_armor(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pants {
            match v {
                &AvatarCustomizationData_oneof__pants::pants(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let ::std::option::Option::Some(ref v) = self._eye_color_left {
            match v {
                &AvatarCustomizationData_oneof__eye_color_left::eye_color_left(ref v) => {
                    os.write_string(1, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eye_color_right {
            match v {
                &AvatarCustomizationData_oneof__eye_color_right::eye_color_right(ref v) => {
                    os.write_string(2, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eyebrow_color_left {
            match v {
                &AvatarCustomizationData_oneof__eyebrow_color_left::eyebrow_color_left(ref v) => {
                    os.write_string(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._eyebrow_color_right {
            match v {
                &AvatarCustomizationData_oneof__eyebrow_color_right::eyebrow_color_right(ref v) => {
                    os.write_string(4, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._gender {
            match v {
                &AvatarCustomizationData_oneof__gender::gender(v) => {
                    os.write_enum(5, ::protobuf::ProtobufEnum::value(&v))?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._species {
            match v {
                &AvatarCustomizationData_oneof__species::species(v) => {
                    os.write_enum(6, ::protobuf::ProtobufEnum::value(&v))?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._preset_avatar {
            match v {
                &AvatarCustomizationData_oneof__preset_avatar::preset_avatar(v) => {
                    os.write_enum(7, ::protobuf::ProtobufEnum::value(&v))?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._species_color {
            match v {
                &AvatarCustomizationData_oneof__species_color::species_color(ref v) => {
                    os.write_string(8, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._hair_color {
            match v {
                &AvatarCustomizationData_oneof__hair_color::hair_color(ref v) => {
                    os.write_string(9, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._facial_hair_color {
            match v {
                &AvatarCustomizationData_oneof__facial_hair_color::facial_hair_color(ref v) => {
                    os.write_string(10, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._clothing {
            match v {
                &AvatarCustomizationData_oneof__clothing::clothing(ref v) => {
                    os.write_tag(11, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._hair {
            match v {
                &AvatarCustomizationData_oneof__hair::hair(ref v) => {
                    os.write_tag(12, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._facial_hair {
            match v {
                &AvatarCustomizationData_oneof__facial_hair::facial_hair(ref v) => {
                    os.write_tag(13, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._accessory {
            match v {
                &AvatarCustomizationData_oneof__accessory::accessory(ref v) => {
                    os.write_tag(14, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._helmet {
            match v {
                &AvatarCustomizationData_oneof__helmet::helmet(ref v) => {
                    os.write_tag(15, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._body_armor {
            match v {
                &AvatarCustomizationData_oneof__body_armor::body_armor(ref v) => {
                    os.write_tag(16, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pants {
            match v {
                &AvatarCustomizationData_oneof__pants::pants(ref v) => {
                    os.write_tag(17, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AvatarCustomizationData {
        AvatarCustomizationData::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "eye_color_left",
                AvatarCustomizationData::has_eye_color_left,
                AvatarCustomizationData::get_eye_color_left,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "eye_color_right",
                AvatarCustomizationData::has_eye_color_right,
                AvatarCustomizationData::get_eye_color_right,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "eyebrow_color_left",
                AvatarCustomizationData::has_eyebrow_color_left,
                AvatarCustomizationData::get_eyebrow_color_left,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "eyebrow_color_right",
                AvatarCustomizationData::has_eyebrow_color_right,
                AvatarCustomizationData::get_eyebrow_color_right,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_enum_accessor::<_, WorldGender>(
                "gender",
                AvatarCustomizationData::has_gender,
                AvatarCustomizationData::get_gender,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_enum_accessor::<_, WorldSpecies>(
                "species",
                AvatarCustomizationData::has_species,
                AvatarCustomizationData::get_species,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_enum_accessor::<_, WorldAssetPresetCharacterType>(
                "preset_avatar",
                AvatarCustomizationData::has_preset_avatar,
                AvatarCustomizationData::get_preset_avatar,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "species_color",
                AvatarCustomizationData::has_species_color,
                AvatarCustomizationData::get_species_color,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "hair_color",
                AvatarCustomizationData::has_hair_color,
                AvatarCustomizationData::get_hair_color,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "facial_hair_color",
                AvatarCustomizationData::has_facial_hair_color,
                AvatarCustomizationData::get_facial_hair_color,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldAvatarClothing>(
                "clothing",
                AvatarCustomizationData::has_clothing,
                AvatarCustomizationData::get_clothing,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldAvatarItem>(
                "hair",
                AvatarCustomizationData::has_hair,
                AvatarCustomizationData::get_hair,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldAvatarItem>(
                "facial_hair",
                AvatarCustomizationData::has_facial_hair,
                AvatarCustomizationData::get_facial_hair,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldAvatarItem>(
                "accessory",
                AvatarCustomizationData::has_accessory,
                AvatarCustomizationData::get_accessory,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldAvatarArmor>(
                "helmet",
                AvatarCustomizationData::has_helmet,
                AvatarCustomizationData::get_helmet,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldAvatarArmor>(
                "body_armor",
                AvatarCustomizationData::has_body_armor,
                AvatarCustomizationData::get_body_armor,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WorldAvatarPants>(
                "pants",
                AvatarCustomizationData::has_pants,
                AvatarCustomizationData::get_pants,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AvatarCustomizationData>(
                "AvatarCustomizationData",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AvatarCustomizationData {
        static instance: ::protobuf::rt::LazyV2<AvatarCustomizationData> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AvatarCustomizationData::new)
    }
}

impl ::protobuf::Clear for AvatarCustomizationData {
    fn clear(&mut self) {
        self._eye_color_left = ::std::option::Option::None;
        self._eye_color_right = ::std::option::Option::None;
        self._eyebrow_color_left = ::std::option::Option::None;
        self._eyebrow_color_right = ::std::option::Option::None;
        self._gender = ::std::option::Option::None;
        self._species = ::std::option::Option::None;
        self._preset_avatar = ::std::option::Option::None;
        self._species_color = ::std::option::Option::None;
        self._hair_color = ::std::option::Option::None;
        self._facial_hair_color = ::std::option::Option::None;
        self._clothing = ::std::option::Option::None;
        self._hair = ::std::option::Option::None;
        self._facial_hair = ::std::option::Option::None;
        self._accessory = ::std::option::Option::None;
        self._helmet = ::std::option::Option::None;
        self._body_armor = ::std::option::Option::None;
        self._pants = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AvatarCustomizationData {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AvatarCustomizationData {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum WorldSpecies {
    HUMAN = 0,
    GOBLIN = 1,
    COW = 2,
    ZOMBIE = 3,
    PUG = 4,
}

impl ::protobuf::ProtobufEnum for WorldSpecies {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<WorldSpecies> {
        match value {
            0 => ::std::option::Option::Some(WorldSpecies::HUMAN),
            1 => ::std::option::Option::Some(WorldSpecies::GOBLIN),
            2 => ::std::option::Option::Some(WorldSpecies::COW),
            3 => ::std::option::Option::Some(WorldSpecies::ZOMBIE),
            4 => ::std::option::Option::Some(WorldSpecies::PUG),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [WorldSpecies] = &[
            WorldSpecies::HUMAN,
            WorldSpecies::GOBLIN,
            WorldSpecies::COW,
            WorldSpecies::ZOMBIE,
            WorldSpecies::PUG,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<WorldSpecies>("WorldSpecies", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for WorldSpecies {
}

impl ::std::default::Default for WorldSpecies {
    fn default() -> Self {
        WorldSpecies::HUMAN
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldSpecies {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum WorldGender {
    MALE = 0,
    FEMALE = 1,
    OTHER = 2,
}

impl ::protobuf::ProtobufEnum for WorldGender {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<WorldGender> {
        match value {
            0 => ::std::option::Option::Some(WorldGender::MALE),
            1 => ::std::option::Option::Some(WorldGender::FEMALE),
            2 => ::std::option::Option::Some(WorldGender::OTHER),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [WorldGender] = &[
            WorldGender::MALE,
            WorldGender::FEMALE,
            WorldGender::OTHER,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<WorldGender>("WorldGender", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for WorldGender {
}

impl ::std::default::Default for WorldGender {
    fn default() -> Self {
        WorldGender::MALE
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldGender {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum WorldItemType {
    UNKNOWN = 0,
    HAIR = 1,
    FACIAL_HAIR = 2,
    CLOTHING = 3,
    PANTS = 4,
    HELMET = 5,
    ARMOR = 6,
    WEAPON = 7,
    BACK = 8,
    ACCESSORY = 9,
    PRESET_AVATAR = 10,
}

impl ::protobuf::ProtobufEnum for WorldItemType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<WorldItemType> {
        match value {
            0 => ::std::option::Option::Some(WorldItemType::UNKNOWN),
            1 => ::std::option::Option::Some(WorldItemType::HAIR),
            2 => ::std::option::Option::Some(WorldItemType::FACIAL_HAIR),
            3 => ::std::option::Option::Some(WorldItemType::CLOTHING),
            4 => ::std::option::Option::Some(WorldItemType::PANTS),
            5 => ::std::option::Option::Some(WorldItemType::HELMET),
            6 => ::std::option::Option::Some(WorldItemType::ARMOR),
            7 => ::std::option::Option::Some(WorldItemType::WEAPON),
            8 => ::std::option::Option::Some(WorldItemType::BACK),
            9 => ::std::option::Option::Some(WorldItemType::ACCESSORY),
            10 => ::std::option::Option::Some(WorldItemType::PRESET_AVATAR),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [WorldItemType] = &[
            WorldItemType::UNKNOWN,
            WorldItemType::HAIR,
            WorldItemType::FACIAL_HAIR,
            WorldItemType::CLOTHING,
            WorldItemType::PANTS,
            WorldItemType::HELMET,
            WorldItemType::ARMOR,
            WorldItemType::WEAPON,
            WorldItemType::BACK,
            WorldItemType::ACCESSORY,
            WorldItemType::PRESET_AVATAR,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<WorldItemType>("WorldItemType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for WorldItemType {
}

impl ::std::default::Default for WorldItemType {
    fn default() -> Self {
        WorldItemType::UNKNOWN
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldItemType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum WorldAssetHelmetType {
    HELMET_TYPE_UNSPECIFIED = 0,
    HELMET_TYPE_CROWN = 1,
    HELMET_TYPE_FANCY_CROWN = 2,
    HELMET_TYPE_CHEF_HAT = 3,
    HELMET_TYPE_FISH_ARMORED_CAT_FISH = 4,
    HELMET_TYPE_FISH_BETTA = 5,
    HELMET_TYPE_FISH_BLACK_LION_FISH = 6,
    HELMET_TYPE_FISH_BLUE_GOLD_FISH = 7,
    HELMET_TYPE_FISH_CORAL_GROUPER = 8,
    HELMET_TYPE_FISH_CLOWN_FISH = 9,
    HELMET_TYPE_FISH_BLOB_FISH = 10,
    HELMET_TYPE_FISH_ZEBRA_CLOWN_FISH = 11,
    HELMET_TYPE_FISH_BLUE_TANG = 12,
    HELMET_TYPE_FISH_BUTTERFLY_FISH = 13,
    HELMET_TYPE_FISH_CARDINAL_FISH = 14,
    HELMET_TYPE_FISH_COWFISH = 15,
    HELMET_TYPE_FISH_FLATFISH = 16,
    HELMET_TYPE_FISH_FLOWER_HORN = 17,
    HELMET_TYPE_FISH_GOBLIN_SHARK = 18,
    HELMET_TYPE_FISH_HUMPHEAD = 19,
    HELMET_TYPE_FISH_KOI = 20,
    HELMET_TYPE_FISH_LIONFISH = 21,
    HELMET_TYPE_FISH_MANDARIN_FISH = 22,
    HELMET_TYPE_FISH_MOORISH_IDOL = 23,
    HELMET_TYPE_FISH_PARROT_FISH = 24,
    HELMET_TYPE_FISH_PIRANHA = 25,
    HELMET_TYPE_FISH_PUFFER = 26,
    HELMET_TYPE_FISH_RED_SNAPPER = 27,
    HELMET_TYPE_FISH_ROYAL_GRAMMA = 28,
    HELMET_TYPE_FISH_SHARK = 29,
    HELMET_TYPE_FISH_SUNFISH = 30,
    HELMET_TYPE_FISH_SWORDFISH = 31,
    HELMET_TYPE_FISH_TANG = 32,
    HELMET_TYPE_FISH_TETRA = 33,
    HELMET_TYPE_FISH_TUNA = 34,
    HELMET_TYPE_FISH_TURBOT = 35,
    HELMET_TYPE_FISH_YELLOW_TANG = 36,
}

impl ::protobuf::ProtobufEnum for WorldAssetHelmetType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<WorldAssetHelmetType> {
        match value {
            0 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_UNSPECIFIED),
            1 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_CROWN),
            2 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FANCY_CROWN),
            3 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_CHEF_HAT),
            4 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_ARMORED_CAT_FISH),
            5 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_BETTA),
            6 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_BLACK_LION_FISH),
            7 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_BLUE_GOLD_FISH),
            8 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_CORAL_GROUPER),
            9 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_CLOWN_FISH),
            10 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_BLOB_FISH),
            11 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_ZEBRA_CLOWN_FISH),
            12 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_BLUE_TANG),
            13 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_BUTTERFLY_FISH),
            14 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_CARDINAL_FISH),
            15 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_COWFISH),
            16 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_FLATFISH),
            17 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_FLOWER_HORN),
            18 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_GOBLIN_SHARK),
            19 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_HUMPHEAD),
            20 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_KOI),
            21 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_LIONFISH),
            22 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_MANDARIN_FISH),
            23 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_MOORISH_IDOL),
            24 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_PARROT_FISH),
            25 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_PIRANHA),
            26 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_PUFFER),
            27 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_RED_SNAPPER),
            28 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_ROYAL_GRAMMA),
            29 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_SHARK),
            30 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_SUNFISH),
            31 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_SWORDFISH),
            32 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_TANG),
            33 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_TETRA),
            34 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_TUNA),
            35 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_TURBOT),
            36 => ::std::option::Option::Some(WorldAssetHelmetType::HELMET_TYPE_FISH_YELLOW_TANG),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [WorldAssetHelmetType] = &[
            WorldAssetHelmetType::HELMET_TYPE_UNSPECIFIED,
            WorldAssetHelmetType::HELMET_TYPE_CROWN,
            WorldAssetHelmetType::HELMET_TYPE_FANCY_CROWN,
            WorldAssetHelmetType::HELMET_TYPE_CHEF_HAT,
            WorldAssetHelmetType::HELMET_TYPE_FISH_ARMORED_CAT_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_BETTA,
            WorldAssetHelmetType::HELMET_TYPE_FISH_BLACK_LION_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_BLUE_GOLD_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_CORAL_GROUPER,
            WorldAssetHelmetType::HELMET_TYPE_FISH_CLOWN_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_BLOB_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_ZEBRA_CLOWN_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_BLUE_TANG,
            WorldAssetHelmetType::HELMET_TYPE_FISH_BUTTERFLY_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_CARDINAL_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_COWFISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_FLATFISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_FLOWER_HORN,
            WorldAssetHelmetType::HELMET_TYPE_FISH_GOBLIN_SHARK,
            WorldAssetHelmetType::HELMET_TYPE_FISH_HUMPHEAD,
            WorldAssetHelmetType::HELMET_TYPE_FISH_KOI,
            WorldAssetHelmetType::HELMET_TYPE_FISH_LIONFISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_MANDARIN_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_MOORISH_IDOL,
            WorldAssetHelmetType::HELMET_TYPE_FISH_PARROT_FISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_PIRANHA,
            WorldAssetHelmetType::HELMET_TYPE_FISH_PUFFER,
            WorldAssetHelmetType::HELMET_TYPE_FISH_RED_SNAPPER,
            WorldAssetHelmetType::HELMET_TYPE_FISH_ROYAL_GRAMMA,
            WorldAssetHelmetType::HELMET_TYPE_FISH_SHARK,
            WorldAssetHelmetType::HELMET_TYPE_FISH_SUNFISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_SWORDFISH,
            WorldAssetHelmetType::HELMET_TYPE_FISH_TANG,
            WorldAssetHelmetType::HELMET_TYPE_FISH_TETRA,
            WorldAssetHelmetType::HELMET_TYPE_FISH_TUNA,
            WorldAssetHelmetType::HELMET_TYPE_FISH_TURBOT,
            WorldAssetHelmetType::HELMET_TYPE_FISH_YELLOW_TANG,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<WorldAssetHelmetType>("WorldAssetHelmetType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for WorldAssetHelmetType {
}

impl ::std::default::Default for WorldAssetHelmetType {
    fn default() -> Self {
        WorldAssetHelmetType::HELMET_TYPE_UNSPECIFIED
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAssetHelmetType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum WorldAssetHairType {
    HAIR_TYPE_UNSPECIFIED = 0,
    HAIR_TYPE_HAIR_1 = 1,
    HAIR_TYPE_HAIR_2 = 2,
}

impl ::protobuf::ProtobufEnum for WorldAssetHairType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<WorldAssetHairType> {
        match value {
            0 => ::std::option::Option::Some(WorldAssetHairType::HAIR_TYPE_UNSPECIFIED),
            1 => ::std::option::Option::Some(WorldAssetHairType::HAIR_TYPE_HAIR_1),
            2 => ::std::option::Option::Some(WorldAssetHairType::HAIR_TYPE_HAIR_2),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [WorldAssetHairType] = &[
            WorldAssetHairType::HAIR_TYPE_UNSPECIFIED,
            WorldAssetHairType::HAIR_TYPE_HAIR_1,
            WorldAssetHairType::HAIR_TYPE_HAIR_2,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<WorldAssetHairType>("WorldAssetHairType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for WorldAssetHairType {
}

impl ::std::default::Default for WorldAssetHairType {
    fn default() -> Self {
        WorldAssetHairType::HAIR_TYPE_UNSPECIFIED
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAssetHairType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum WorldAssetClothingType {
    CLOTHING_TYPE_UNSPECIFIED = 0,
    CLOTHING_TYPE_CHEF = 1,
}

impl ::protobuf::ProtobufEnum for WorldAssetClothingType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<WorldAssetClothingType> {
        match value {
            0 => ::std::option::Option::Some(WorldAssetClothingType::CLOTHING_TYPE_UNSPECIFIED),
            1 => ::std::option::Option::Some(WorldAssetClothingType::CLOTHING_TYPE_CHEF),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [WorldAssetClothingType] = &[
            WorldAssetClothingType::CLOTHING_TYPE_UNSPECIFIED,
            WorldAssetClothingType::CLOTHING_TYPE_CHEF,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<WorldAssetClothingType>("WorldAssetClothingType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for WorldAssetClothingType {
}

impl ::std::default::Default for WorldAssetClothingType {
    fn default() -> Self {
        WorldAssetClothingType::CLOTHING_TYPE_UNSPECIFIED
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAssetClothingType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum WorldAssetPresetCharacterType {
    PRESET_CHARACTER_TYPE_UNSPECIFIED = 0,
    PRESET_CHARACTER_TYPE_WITCH = 1,
    PRESET_CHARACTER_TYPE_WIZARD = 2,
    PRESET_CHARACTER_TYPE_SUIT_MALE = 3,
    PRESET_CHARACTER_TYPE_DOCTOR_MALE_OLD = 4,
    PRESET_CHARACTER_TYPE_DOCTOR_MALE_YOUNG = 5,
    PRESET_CHARACTER_TYPE_SUIT_FEMALE = 6,
    PRESET_CHARACTER_TYPE_KIMONO_MALE = 7,
    PRESET_CHARACTER_TYPE_KIMONO_FEMALE = 8,
    PRESET_CHARACTER_TYPE_NINJA_MALE = 9,
    PRESET_CHARACTER_TYPE_NINJA_FEMALE = 10,
    PRESET_CHARACTER_TYPE_NINJA_MALE_HAIR = 11,
    PRESET_CHARACTER_TYPE_OLD_CLASSY_MALE = 12,
    PRESET_CHARACTER_TYPE_OLD_CLASSY_FEMALE = 13,
    PRESET_CHARACTER_TYPE_PIRATE_MALE = 14,
    PRESET_CHARACTER_TYPE_PIRATE_FEMALE = 15,
    PRESET_CHARACTER_TYPE_KNIGHT_MALE = 16,
    PRESET_CHARACTER_TYPE_CASUAL_MALE = 17,
    PRESET_CHARACTER_TYPE_CASUAL_FEMALE = 18,
    PRESET_CHARACTER_TYPE_VIKING_MALE = 19,
    PRESET_CHARACTER_TYPE_VIKING_FEMALE = 20,
}

impl ::protobuf::ProtobufEnum for WorldAssetPresetCharacterType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<WorldAssetPresetCharacterType> {
        match value {
            0 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_UNSPECIFIED),
            1 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_WITCH),
            2 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_WIZARD),
            3 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_SUIT_MALE),
            4 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_DOCTOR_MALE_OLD),
            5 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_DOCTOR_MALE_YOUNG),
            6 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_SUIT_FEMALE),
            7 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_KIMONO_MALE),
            8 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_KIMONO_FEMALE),
            9 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_NINJA_MALE),
            10 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_NINJA_FEMALE),
            11 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_NINJA_MALE_HAIR),
            12 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_OLD_CLASSY_MALE),
            13 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_OLD_CLASSY_FEMALE),
            14 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_PIRATE_MALE),
            15 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_PIRATE_FEMALE),
            16 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_KNIGHT_MALE),
            17 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_CASUAL_MALE),
            18 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_CASUAL_FEMALE),
            19 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_VIKING_MALE),
            20 => ::std::option::Option::Some(WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_VIKING_FEMALE),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [WorldAssetPresetCharacterType] = &[
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_UNSPECIFIED,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_WITCH,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_WIZARD,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_SUIT_MALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_DOCTOR_MALE_OLD,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_DOCTOR_MALE_YOUNG,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_SUIT_FEMALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_KIMONO_MALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_KIMONO_FEMALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_NINJA_MALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_NINJA_FEMALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_NINJA_MALE_HAIR,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_OLD_CLASSY_MALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_OLD_CLASSY_FEMALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_PIRATE_MALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_PIRATE_FEMALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_KNIGHT_MALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_CASUAL_MALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_CASUAL_FEMALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_VIKING_MALE,
            WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_VIKING_FEMALE,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<WorldAssetPresetCharacterType>("WorldAssetPresetCharacterType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for WorldAssetPresetCharacterType {
}

impl ::std::default::Default for WorldAssetPresetCharacterType {
    fn default() -> Self {
        WorldAssetPresetCharacterType::PRESET_CHARACTER_TYPE_UNSPECIFIED
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldAssetPresetCharacterType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x16world-renditions.proto\x12)PianoRhythm.Serialization.WorldRenditio\
    ns\"8\n\x0cWorldVector3\x12\x0c\n\x01x\x18\x01\x20\x01(\x02R\x01x\x12\
    \x0c\n\x01y\x18\x02\x20\x01(\x02R\x01y\x12\x0c\n\x01z\x18\x03\x20\x01(\
    \x02R\x01z\"\x8f\x05\n\x0fWorldAvatarItem\x12\x19\n\x08item_key\x18\x01\
    \x20\x01(\tR\x07itemKey\x12\x14\n\x05title\x18\x02\x20\x01(\tR\x05title\
    \x12%\n\x0bdescription\x18\x03\x20\x01(\tH\0R\x0bdescription\x88\x01\x01\
    \x12U\n\titem_type\x18\x04\x20\x01(\x0e28.PianoRhythm.Serialization.Worl\
    dRenditions.WorldItemTypeR\x08itemType\x12\x1f\n\x0bitem_color1\x18\x05\
    \x20\x01(\tR\nitemColor1\x12$\n\x0bitem_color2\x18\x06\x20\x01(\tH\x01R\
    \nitemColor2\x88\x01\x01\x12e\n\x0fposition_offset\x18\x07\x20\x01(\x0b2\
    7.PianoRhythm.Serialization.WorldRenditions.WorldVector3H\x02R\x0epositi\
    onOffset\x88\x01\x01\x12e\n\x0frotation_offset\x18\x08\x20\x01(\x0b27.Pi\
    anoRhythm.Serialization.WorldRenditions.WorldVector3H\x03R\x0erotationOf\
    fset\x88\x01\x01\x12_\n\x0cscale_offset\x18\t\x20\x01(\x0b27.PianoRhythm\
    .Serialization.WorldRenditions.WorldVector3H\x04R\x0bscaleOffset\x88\x01\
    \x01B\x0e\n\x0c_descriptionB\x0e\n\x0c_item_color2B\x12\n\x10_position_o\
    ffsetB\x12\n\x10_rotation_offsetB\x0f\n\r_scale_offset\"\xb4\x01\n\x12Wo\
    rldAvatarSpecies\x12\x14\n\x05title\x18\x01\x20\x01(\tR\x05title\x12%\n\
    \x0bdescription\x18\x02\x20\x01(\tH\0R\x0bdescription\x88\x01\x01\x12Q\n\
    \x07species\x18\x03\x20\x01(\x0e27.PianoRhythm.Serialization.WorldRendit\
    ions.WorldSpeciesR\x07speciesB\x0e\n\x0c_description\"\x93\x05\n\x13Worl\
    dAvatarClothing\x12\x19\n\x08item_key\x18\x01\x20\x01(\tR\x07itemKey\x12\
    \x14\n\x05title\x18\x02\x20\x01(\tR\x05title\x12%\n\x0bdescription\x18\
    \x03\x20\x01(\tH\0R\x0bdescription\x88\x01\x01\x12U\n\titem_type\x18\x04\
    \x20\x01(\x0e28.PianoRhythm.Serialization.WorldRenditions.WorldItemTypeR\
    \x08itemType\x12\x1f\n\x0bitem_color1\x18\x05\x20\x01(\tR\nitemColor1\
    \x12$\n\x0bitem_color2\x18\x06\x20\x01(\tH\x01R\nitemColor2\x88\x01\x01\
    \x12e\n\x0fposition_offset\x18\x07\x20\x01(\x0b27.PianoRhythm.Serializat\
    ion.WorldRenditions.WorldVector3H\x02R\x0epositionOffset\x88\x01\x01\x12\
    e\n\x0frotation_offset\x18\x08\x20\x01(\x0b27.PianoRhythm.Serialization.\
    WorldRenditions.WorldVector3H\x03R\x0erotationOffset\x88\x01\x01\x12_\n\
    \x0cscale_offset\x18\t\x20\x01(\x0b27.PianoRhythm.Serialization.WorldRen\
    ditions.WorldVector3H\x04R\x0bscaleOffset\x88\x01\x01B\x0e\n\x0c_descrip\
    tionB\x0e\n\x0c_item_color2B\x12\n\x10_position_offsetB\x12\n\x10_rotati\
    on_offsetB\x0f\n\r_scale_offset\"\xfb\x04\n\x10WorldAvatarArmor\x12\x19\
    \n\x08item_key\x18\x01\x20\x01(\tR\x07itemKey\x12\x14\n\x05title\x18\x02\
    \x20\x01(\tR\x05title\x12%\n\x0bdescription\x18\x03\x20\x01(\tH\0R\x0bde\
    scription\x88\x01\x01\x12U\n\titem_type\x18\x04\x20\x01(\x0e28.PianoRhyt\
    hm.Serialization.WorldRenditions.WorldItemTypeR\x08itemType\x12\x1f\n\
    \x0bitem_color1\x18\x05\x20\x01(\tR\nitemColor1\x12\x1f\n\x0bitem_color2\
    \x18\x06\x20\x01(\tR\nitemColor2\x12e\n\x0fposition_offset\x18\x07\x20\
    \x01(\x0b27.PianoRhythm.Serialization.WorldRenditions.WorldVector3H\x01R\
    \x0epositionOffset\x88\x01\x01\x12e\n\x0frotation_offset\x18\x08\x20\x01\
    (\x0b27.PianoRhythm.Serialization.WorldRenditions.WorldVector3H\x02R\x0e\
    rotationOffset\x88\x01\x01\x12_\n\x0cscale_offset\x18\t\x20\x01(\x0b27.P\
    ianoRhythm.Serialization.WorldRenditions.WorldVector3H\x03R\x0bscaleOffs\
    et\x88\x01\x01B\x0e\n\x0c_descriptionB\x12\n\x10_position_offsetB\x12\n\
    \x10_rotation_offsetB\x0f\n\r_scale_offset\"\x90\x05\n\x10WorldAvatarPan\
    ts\x12\x19\n\x08item_key\x18\x01\x20\x01(\tR\x07itemKey\x12\x14\n\x05tit\
    le\x18\x02\x20\x01(\tR\x05title\x12%\n\x0bdescription\x18\x03\x20\x01(\t\
    H\0R\x0bdescription\x88\x01\x01\x12U\n\titem_type\x18\x04\x20\x01(\x0e28\
    .PianoRhythm.Serialization.WorldRenditions.WorldItemTypeR\x08itemType\
    \x12\x1f\n\x0bitem_color1\x18\x05\x20\x01(\tR\nitemColor1\x12$\n\x0bitem\
    _color2\x18\x06\x20\x01(\tH\x01R\nitemColor2\x88\x01\x01\x12e\n\x0fposit\
    ion_offset\x18\x07\x20\x01(\x0b27.PianoRhythm.Serialization.WorldRenditi\
    ons.WorldVector3H\x02R\x0epositionOffset\x88\x01\x01\x12e\n\x0frotation_\
    offset\x18\x08\x20\x01(\x0b27.PianoRhythm.Serialization.WorldRenditions.\
    WorldVector3H\x03R\x0erotationOffset\x88\x01\x01\x12_\n\x0cscale_offset\
    \x18\t\x20\x01(\x0b27.PianoRhythm.Serialization.WorldRenditions.WorldVec\
    tor3H\x04R\x0bscaleOffset\x88\x01\x01B\x0e\n\x0c_descriptionB\x0e\n\x0c_\
    item_color2B\x12\n\x10_position_offsetB\x12\n\x10_rotation_offsetB\x0f\n\
    \r_scale_offset\"\xf1\n\n\x13WorldAvatarUserData\x12)\n\x0eeye_color_lef\
    t\x18\x01\x20\x01(\tH\0R\x0ceyeColorLeft\x88\x01\x01\x12+\n\x0feye_color\
    _right\x18\x02\x20\x01(\tH\x01R\reyeColorRight\x88\x01\x01\x121\n\x12eye\
    brow_color_left\x18\x03\x20\x01(\tH\x02R\x10eyebrowColorLeft\x88\x01\x01\
    \x123\n\x13eyebrow_color_right\x18\x04\x20\x01(\tH\x03R\x11eyebrowColorR\
    ight\x88\x01\x01\x12N\n\x06gender\x18\x05\x20\x01(\x0e26.PianoRhythm.Ser\
    ialization.WorldRenditions.WorldGenderR\x06gender\x12Q\n\x07species\x18\
    \x06\x20\x01(\x0e27.PianoRhythm.Serialization.WorldRenditions.WorldSpeci\
    esR\x07species\x12r\n\rpreset_avatar\x18\x07\x20\x01(\x0e2H.PianoRhythm.\
    Serialization.WorldRenditions.WorldAssetPresetCharacterTypeH\x04R\x0cpre\
    setAvatar\x88\x01\x01\x12(\n\rspecies_color\x18\x08\x20\x01(\tH\x05R\x0c\
    speciesColor\x88\x01\x01\x12\"\n\nhair_color\x18\t\x20\x01(\tH\x06R\thai\
    rColor\x88\x01\x01\x12/\n\x11facial_hair_color\x18\n\x20\x01(\tH\x07R\
    \x0ffacialHairColor\x88\x01\x01\x12Z\n\x08clothing\x18\x0b\x20\x01(\x0b2\
    >.PianoRhythm.Serialization.WorldRenditions.WorldAvatarClothingR\x08clot\
    hing\x12N\n\x04hair\x18\x0c\x20\x01(\x0b2:.PianoRhythm.Serialization.Wor\
    ldRenditions.WorldAvatarItemR\x04hair\x12[\n\x0bfacial_hair\x18\r\x20\
    \x01(\x0b2:.PianoRhythm.Serialization.WorldRenditions.WorldAvatarItemR\n\
    facialHair\x12X\n\taccessory\x18\x0e\x20\x01(\x0b2:.PianoRhythm.Serializ\
    ation.WorldRenditions.WorldAvatarItemR\taccessory\x12S\n\x06helmet\x18\
    \x0f\x20\x01(\x0b2;.PianoRhythm.Serialization.WorldRenditions.WorldAvata\
    rArmorR\x06helmet\x12Z\n\nbody_armor\x18\x10\x20\x01(\x0b2;.PianoRhythm.\
    Serialization.WorldRenditions.WorldAvatarArmorR\tbodyArmor\x12Q\n\x05pan\
    ts\x18\x11\x20\x01(\x0b2;.PianoRhythm.Serialization.WorldRenditions.Worl\
    dAvatarPantsR\x05pantsB\x11\n\x0f_eye_color_leftB\x12\n\x10_eye_color_ri\
    ghtB\x15\n\x13_eyebrow_color_leftB\x16\n\x14_eyebrow_color_rightB\x10\n\
    \x0e_preset_avatarB\x10\n\x0e_species_colorB\r\n\x0b_hair_colorB\x14\n\
    \x12_facial_hair_color\"\x91\x0c\n\x17AvatarCustomizationData\x12)\n\x0e\
    eye_color_left\x18\x01\x20\x01(\tH\0R\x0ceyeColorLeft\x88\x01\x01\x12+\n\
    \x0feye_color_right\x18\x02\x20\x01(\tH\x01R\reyeColorRight\x88\x01\x01\
    \x121\n\x12eyebrow_color_left\x18\x03\x20\x01(\tH\x02R\x10eyebrowColorLe\
    ft\x88\x01\x01\x123\n\x13eyebrow_color_right\x18\x04\x20\x01(\tH\x03R\
    \x11eyebrowColorRight\x88\x01\x01\x12S\n\x06gender\x18\x05\x20\x01(\x0e2\
    6.PianoRhythm.Serialization.WorldRenditions.WorldGenderH\x04R\x06gender\
    \x88\x01\x01\x12V\n\x07species\x18\x06\x20\x01(\x0e27.PianoRhythm.Serial\
    ization.WorldRenditions.WorldSpeciesH\x05R\x07species\x88\x01\x01\x12r\n\
    \rpreset_avatar\x18\x07\x20\x01(\x0e2H.PianoRhythm.Serialization.WorldRe\
    nditions.WorldAssetPresetCharacterTypeH\x06R\x0cpresetAvatar\x88\x01\x01\
    \x12(\n\rspecies_color\x18\x08\x20\x01(\tH\x07R\x0cspeciesColor\x88\x01\
    \x01\x12\"\n\nhair_color\x18\t\x20\x01(\tH\x08R\thairColor\x88\x01\x01\
    \x12/\n\x11facial_hair_color\x18\n\x20\x01(\tH\tR\x0ffacialHairColor\x88\
    \x01\x01\x12_\n\x08clothing\x18\x0b\x20\x01(\x0b2>.PianoRhythm.Serializa\
    tion.WorldRenditions.WorldAvatarClothingH\nR\x08clothing\x88\x01\x01\x12\
    S\n\x04hair\x18\x0c\x20\x01(\x0b2:.PianoRhythm.Serialization.WorldRendit\
    ions.WorldAvatarItemH\x0bR\x04hair\x88\x01\x01\x12`\n\x0bfacial_hair\x18\
    \r\x20\x01(\x0b2:.PianoRhythm.Serialization.WorldRenditions.WorldAvatarI\
    temH\x0cR\nfacialHair\x88\x01\x01\x12]\n\taccessory\x18\x0e\x20\x01(\x0b\
    2:.PianoRhythm.Serialization.WorldRenditions.WorldAvatarItemH\rR\taccess\
    ory\x88\x01\x01\x12X\n\x06helmet\x18\x0f\x20\x01(\x0b2;.PianoRhythm.Seri\
    alization.WorldRenditions.WorldAvatarArmorH\x0eR\x06helmet\x88\x01\x01\
    \x12_\n\nbody_armor\x18\x10\x20\x01(\x0b2;.PianoRhythm.Serialization.Wor\
    ldRenditions.WorldAvatarArmorH\x0fR\tbodyArmor\x88\x01\x01\x12V\n\x05pan\
    ts\x18\x11\x20\x01(\x0b2;.PianoRhythm.Serialization.WorldRenditions.Worl\
    dAvatarPantsH\x10R\x05pants\x88\x01\x01B\x11\n\x0f_eye_color_leftB\x12\n\
    \x10_eye_color_rightB\x15\n\x13_eyebrow_color_leftB\x16\n\x14_eyebrow_co\
    lor_rightB\t\n\x07_genderB\n\n\x08_speciesB\x10\n\x0e_preset_avatarB\x10\
    \n\x0e_species_colorB\r\n\x0b_hair_colorB\x14\n\x12_facial_hair_colorB\
    \x0b\n\t_clothingB\x07\n\x05_hairB\x0e\n\x0c_facial_hairB\x0c\n\n_access\
    oryB\t\n\x07_helmetB\r\n\x0b_body_armorB\x08\n\x06_pants*C\n\x0cWorldSpe\
    cies\x12\t\n\x05HUMAN\x10\0\x12\n\n\x06GOBLIN\x10\x01\x12\x07\n\x03COW\
    \x10\x02\x12\n\n\x06ZOMBIE\x10\x03\x12\x07\n\x03PUG\x10\x04*.\n\x0bWorld\
    Gender\x12\x08\n\x04MALE\x10\0\x12\n\n\x06FEMALE\x10\x01\x12\t\n\x05OTHE\
    R\x10\x02*\x9f\x01\n\rWorldItemType\x12\x0b\n\x07UNKNOWN\x10\0\x12\x08\n\
    \x04HAIR\x10\x01\x12\x0f\n\x0bFACIAL_HAIR\x10\x02\x12\x0c\n\x08CLOTHING\
    \x10\x03\x12\t\n\x05PANTS\x10\x04\x12\n\n\x06HELMET\x10\x05\x12\t\n\x05A\
    RMOR\x10\x06\x12\n\n\x06WEAPON\x10\x07\x12\x08\n\x04BACK\x10\x08\x12\r\n\
    \tACCESSORY\x10\t\x12\x11\n\rPRESET_AVATAR\x10\n*\xb2\t\n\x14WorldAssetH\
    elmetType\x12\x1b\n\x17HELMET_TYPE_UNSPECIFIED\x10\0\x12\x15\n\x11HELMET\
    _TYPE_CROWN\x10\x01\x12\x1b\n\x17HELMET_TYPE_FANCY_CROWN\x10\x02\x12\x18\
    \n\x14HELMET_TYPE_CHEF_HAT\x10\x03\x12%\n!HELMET_TYPE_FISH_ARMORED_CAT_F\
    ISH\x10\x04\x12\x1a\n\x16HELMET_TYPE_FISH_BETTA\x10\x05\x12$\n\x20HELMET\
    _TYPE_FISH_BLACK_LION_FISH\x10\x06\x12#\n\x1fHELMET_TYPE_FISH_BLUE_GOLD_\
    FISH\x10\x07\x12\"\n\x1eHELMET_TYPE_FISH_CORAL_GROUPER\x10\x08\x12\x1f\n\
    \x1bHELMET_TYPE_FISH_CLOWN_FISH\x10\t\x12\x1e\n\x1aHELMET_TYPE_FISH_BLOB\
    _FISH\x10\n\x12%\n!HELMET_TYPE_FISH_ZEBRA_CLOWN_FISH\x10\x0b\x12\x1e\n\
    \x1aHELMET_TYPE_FISH_BLUE_TANG\x10\x0c\x12#\n\x1fHELMET_TYPE_FISH_BUTTER\
    FLY_FISH\x10\r\x12\"\n\x1eHELMET_TYPE_FISH_CARDINAL_FISH\x10\x0e\x12\x1c\
    \n\x18HELMET_TYPE_FISH_COWFISH\x10\x0f\x12\x1d\n\x19HELMET_TYPE_FISH_FLA\
    TFISH\x10\x10\x12\x20\n\x1cHELMET_TYPE_FISH_FLOWER_HORN\x10\x11\x12!\n\
    \x1dHELMET_TYPE_FISH_GOBLIN_SHARK\x10\x12\x12\x1d\n\x19HELMET_TYPE_FISH_\
    HUMPHEAD\x10\x13\x12\x18\n\x14HELMET_TYPE_FISH_KOI\x10\x14\x12\x1d\n\x19\
    HELMET_TYPE_FISH_LIONFISH\x10\x15\x12\"\n\x1eHELMET_TYPE_FISH_MANDARIN_F\
    ISH\x10\x16\x12!\n\x1dHELMET_TYPE_FISH_MOORISH_IDOL\x10\x17\x12\x20\n\
    \x1cHELMET_TYPE_FISH_PARROT_FISH\x10\x18\x12\x1c\n\x18HELMET_TYPE_FISH_P\
    IRANHA\x10\x19\x12\x1b\n\x17HELMET_TYPE_FISH_PUFFER\x10\x1a\x12\x20\n\
    \x1cHELMET_TYPE_FISH_RED_SNAPPER\x10\x1b\x12!\n\x1dHELMET_TYPE_FISH_ROYA\
    L_GRAMMA\x10\x1c\x12\x1a\n\x16HELMET_TYPE_FISH_SHARK\x10\x1d\x12\x1c\n\
    \x18HELMET_TYPE_FISH_SUNFISH\x10\x1e\x12\x1e\n\x1aHELMET_TYPE_FISH_SWORD\
    FISH\x10\x1f\x12\x19\n\x15HELMET_TYPE_FISH_TANG\x10\x20\x12\x1a\n\x16HEL\
    MET_TYPE_FISH_TETRA\x10!\x12\x19\n\x15HELMET_TYPE_FISH_TUNA\x10\"\x12\
    \x1b\n\x17HELMET_TYPE_FISH_TURBOT\x10#\x12\x20\n\x1cHELMET_TYPE_FISH_YEL\
    LOW_TANG\x10$*[\n\x12WorldAssetHairType\x12\x19\n\x15HAIR_TYPE_UNSPECIFI\
    ED\x10\0\x12\x14\n\x10HAIR_TYPE_HAIR_1\x10\x01\x12\x14\n\x10HAIR_TYPE_HA\
    IR_2\x10\x02*O\n\x16WorldAssetClothingType\x12\x1d\n\x19CLOTHING_TYPE_UN\
    SPECIFIED\x10\0\x12\x16\n\x12CLOTHING_TYPE_CHEF\x10\x01*\xe5\x06\n\x1dWo\
    rldAssetPresetCharacterType\x12%\n!PRESET_CHARACTER_TYPE_UNSPECIFIED\x10\
    \0\x12\x1f\n\x1bPRESET_CHARACTER_TYPE_WITCH\x10\x01\x12\x20\n\x1cPRESET_\
    CHARACTER_TYPE_WIZARD\x10\x02\x12#\n\x1fPRESET_CHARACTER_TYPE_SUIT_MALE\
    \x10\x03\x12)\n%PRESET_CHARACTER_TYPE_DOCTOR_MALE_OLD\x10\x04\x12+\n'PRE\
    SET_CHARACTER_TYPE_DOCTOR_MALE_YOUNG\x10\x05\x12%\n!PRESET_CHARACTER_TYP\
    E_SUIT_FEMALE\x10\x06\x12%\n!PRESET_CHARACTER_TYPE_KIMONO_MALE\x10\x07\
    \x12'\n#PRESET_CHARACTER_TYPE_KIMONO_FEMALE\x10\x08\x12$\n\x20PRESET_CHA\
    RACTER_TYPE_NINJA_MALE\x10\t\x12&\n\"PRESET_CHARACTER_TYPE_NINJA_FEMALE\
    \x10\n\x12)\n%PRESET_CHARACTER_TYPE_NINJA_MALE_HAIR\x10\x0b\x12)\n%PRESE\
    T_CHARACTER_TYPE_OLD_CLASSY_MALE\x10\x0c\x12+\n'PRESET_CHARACTER_TYPE_OL\
    D_CLASSY_FEMALE\x10\r\x12%\n!PRESET_CHARACTER_TYPE_PIRATE_MALE\x10\x0e\
    \x12'\n#PRESET_CHARACTER_TYPE_PIRATE_FEMALE\x10\x0f\x12%\n!PRESET_CHARAC\
    TER_TYPE_KNIGHT_MALE\x10\x10\x12%\n!PRESET_CHARACTER_TYPE_CASUAL_MALE\
    \x10\x11\x12'\n#PRESET_CHARACTER_TYPE_CASUAL_FEMALE\x10\x12\x12%\n!PRESE\
    T_CHARACTER_TYPE_VIKING_MALE\x10\x13\x12'\n#PRESET_CHARACTER_TYPE_VIKING\
    _FEMALE\x10\x14b\x06proto3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
