// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `room-renditions.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,Default)]
pub struct RoomHostDetails {
    // message fields
    pub ContinentCode: ::std::string::String,
    // message oneof groups
    pub _CountryCode: ::std::option::Option<RoomHostDetails_oneof__CountryCode>,
    pub _NoteBufferInterval: ::std::option::Option<RoomHostDetails_oneof__NoteBufferInterval>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomHostDetails {
    fn default() -> &'a RoomHostDetails {
        <RoomHostDetails as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomHostDetails_oneof__CountryCode {
    CountryCode(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomHostDetails_oneof__NoteBufferInterval {
    NoteBufferInterval(u32),
}

impl RoomHostDetails {
    pub fn new() -> RoomHostDetails {
        ::std::default::Default::default()
    }

    // string ContinentCode = 1;


    pub fn get_ContinentCode(&self) -> &str {
        &self.ContinentCode
    }
    pub fn clear_ContinentCode(&mut self) {
        self.ContinentCode.clear();
    }

    // Param is passed by value, moved
    pub fn set_ContinentCode(&mut self, v: ::std::string::String) {
        self.ContinentCode = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_ContinentCode(&mut self) -> &mut ::std::string::String {
        &mut self.ContinentCode
    }

    // Take field
    pub fn take_ContinentCode(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.ContinentCode, ::std::string::String::new())
    }

    // string CountryCode = 2;


    pub fn get_CountryCode(&self) -> &str {
        match self._CountryCode {
            ::std::option::Option::Some(RoomHostDetails_oneof__CountryCode::CountryCode(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_CountryCode(&mut self) {
        self._CountryCode = ::std::option::Option::None;
    }

    pub fn has_CountryCode(&self) -> bool {
        match self._CountryCode {
            ::std::option::Option::Some(RoomHostDetails_oneof__CountryCode::CountryCode(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_CountryCode(&mut self, v: ::std::string::String) {
        self._CountryCode = ::std::option::Option::Some(RoomHostDetails_oneof__CountryCode::CountryCode(v))
    }

    // Mutable pointer to the field.
    pub fn mut_CountryCode(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(RoomHostDetails_oneof__CountryCode::CountryCode(_)) = self._CountryCode {
        } else {
            self._CountryCode = ::std::option::Option::Some(RoomHostDetails_oneof__CountryCode::CountryCode(::std::string::String::new()));
        }
        match self._CountryCode {
            ::std::option::Option::Some(RoomHostDetails_oneof__CountryCode::CountryCode(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_CountryCode(&mut self) -> ::std::string::String {
        if self.has_CountryCode() {
            match self._CountryCode.take() {
                ::std::option::Option::Some(RoomHostDetails_oneof__CountryCode::CountryCode(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // uint32 NoteBufferInterval = 3;


    pub fn get_NoteBufferInterval(&self) -> u32 {
        match self._NoteBufferInterval {
            ::std::option::Option::Some(RoomHostDetails_oneof__NoteBufferInterval::NoteBufferInterval(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_NoteBufferInterval(&mut self) {
        self._NoteBufferInterval = ::std::option::Option::None;
    }

    pub fn has_NoteBufferInterval(&self) -> bool {
        match self._NoteBufferInterval {
            ::std::option::Option::Some(RoomHostDetails_oneof__NoteBufferInterval::NoteBufferInterval(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_NoteBufferInterval(&mut self, v: u32) {
        self._NoteBufferInterval = ::std::option::Option::Some(RoomHostDetails_oneof__NoteBufferInterval::NoteBufferInterval(v))
    }
}

impl ::protobuf::Message for RoomHostDetails {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.ContinentCode)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._CountryCode = ::std::option::Option::Some(RoomHostDetails_oneof__CountryCode::CountryCode(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._NoteBufferInterval = ::std::option::Option::Some(RoomHostDetails_oneof__NoteBufferInterval::NoteBufferInterval(is.read_uint32()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.ContinentCode.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.ContinentCode);
        }
        if let ::std::option::Option::Some(ref v) = self._CountryCode {
            match v {
                &RoomHostDetails_oneof__CountryCode::CountryCode(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._NoteBufferInterval {
            match v {
                &RoomHostDetails_oneof__NoteBufferInterval::NoteBufferInterval(v) => {
                    my_size += ::protobuf::rt::value_size(3, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.ContinentCode.is_empty() {
            os.write_string(1, &self.ContinentCode)?;
        }
        if let ::std::option::Option::Some(ref v) = self._CountryCode {
            match v {
                &RoomHostDetails_oneof__CountryCode::CountryCode(ref v) => {
                    os.write_string(2, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._NoteBufferInterval {
            match v {
                &RoomHostDetails_oneof__NoteBufferInterval::NoteBufferInterval(v) => {
                    os.write_uint32(3, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomHostDetails {
        RoomHostDetails::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "ContinentCode",
                |m: &RoomHostDetails| { &m.ContinentCode },
                |m: &mut RoomHostDetails| { &mut m.ContinentCode },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "CountryCode",
                RoomHostDetails::has_CountryCode,
                RoomHostDetails::get_CountryCode,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "NoteBufferInterval",
                RoomHostDetails::has_NoteBufferInterval,
                RoomHostDetails::get_NoteBufferInterval,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomHostDetails>(
                "RoomHostDetails",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomHostDetails {
        static instance: ::protobuf::rt::LazyV2<RoomHostDetails> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomHostDetails::new)
    }
}

impl ::protobuf::Clear for RoomHostDetails {
    fn clear(&mut self) {
        self.ContinentCode.clear();
        self._CountryCode = ::std::option::Option::None;
        self._NoteBufferInterval = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomHostDetails {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomHostDetails {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomStageVisualEffects {
    // message oneof groups
    pub _rain: ::std::option::Option<RoomStageVisualEffects_oneof__rain>,
    pub _grass: ::std::option::Option<RoomStageVisualEffects_oneof__grass>,
    pub _postProcess_blackWhite: ::std::option::Option<RoomStageVisualEffects_oneof__postProcess_blackWhite>,
    pub _postProcess_downSample: ::std::option::Option<RoomStageVisualEffects_oneof__postProcess_downSample>,
    pub _postProcess_toneMap: ::std::option::Option<RoomStageVisualEffects_oneof__postProcess_toneMap>,
    pub _postProcess_emboss: ::std::option::Option<RoomStageVisualEffects_oneof__postProcess_emboss>,
    pub _postProcess_vignette: ::std::option::Option<RoomStageVisualEffects_oneof__postProcess_vignette>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomStageVisualEffects {
    fn default() -> &'a RoomStageVisualEffects {
        <RoomStageVisualEffects as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageVisualEffects_oneof__rain {
    rain(bool),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageVisualEffects_oneof__grass {
    grass(bool),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageVisualEffects_oneof__postProcess_blackWhite {
    postProcess_blackWhite(bool),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageVisualEffects_oneof__postProcess_downSample {
    postProcess_downSample(bool),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageVisualEffects_oneof__postProcess_toneMap {
    postProcess_toneMap(bool),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageVisualEffects_oneof__postProcess_emboss {
    postProcess_emboss(bool),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageVisualEffects_oneof__postProcess_vignette {
    postProcess_vignette(bool),
}

impl RoomStageVisualEffects {
    pub fn new() -> RoomStageVisualEffects {
        ::std::default::Default::default()
    }

    // bool rain = 1;


    pub fn get_rain(&self) -> bool {
        match self._rain {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__rain::rain(v)) => v,
            _ => false,
        }
    }
    pub fn clear_rain(&mut self) {
        self._rain = ::std::option::Option::None;
    }

    pub fn has_rain(&self) -> bool {
        match self._rain {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__rain::rain(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_rain(&mut self, v: bool) {
        self._rain = ::std::option::Option::Some(RoomStageVisualEffects_oneof__rain::rain(v))
    }

    // bool grass = 2;


    pub fn get_grass(&self) -> bool {
        match self._grass {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__grass::grass(v)) => v,
            _ => false,
        }
    }
    pub fn clear_grass(&mut self) {
        self._grass = ::std::option::Option::None;
    }

    pub fn has_grass(&self) -> bool {
        match self._grass {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__grass::grass(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_grass(&mut self, v: bool) {
        self._grass = ::std::option::Option::Some(RoomStageVisualEffects_oneof__grass::grass(v))
    }

    // bool postProcess_blackWhite = 3;


    pub fn get_postProcess_blackWhite(&self) -> bool {
        match self._postProcess_blackWhite {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_blackWhite::postProcess_blackWhite(v)) => v,
            _ => false,
        }
    }
    pub fn clear_postProcess_blackWhite(&mut self) {
        self._postProcess_blackWhite = ::std::option::Option::None;
    }

    pub fn has_postProcess_blackWhite(&self) -> bool {
        match self._postProcess_blackWhite {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_blackWhite::postProcess_blackWhite(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_postProcess_blackWhite(&mut self, v: bool) {
        self._postProcess_blackWhite = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_blackWhite::postProcess_blackWhite(v))
    }

    // bool postProcess_downSample = 4;


    pub fn get_postProcess_downSample(&self) -> bool {
        match self._postProcess_downSample {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_downSample::postProcess_downSample(v)) => v,
            _ => false,
        }
    }
    pub fn clear_postProcess_downSample(&mut self) {
        self._postProcess_downSample = ::std::option::Option::None;
    }

    pub fn has_postProcess_downSample(&self) -> bool {
        match self._postProcess_downSample {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_downSample::postProcess_downSample(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_postProcess_downSample(&mut self, v: bool) {
        self._postProcess_downSample = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_downSample::postProcess_downSample(v))
    }

    // bool postProcess_toneMap = 5;


    pub fn get_postProcess_toneMap(&self) -> bool {
        match self._postProcess_toneMap {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_toneMap::postProcess_toneMap(v)) => v,
            _ => false,
        }
    }
    pub fn clear_postProcess_toneMap(&mut self) {
        self._postProcess_toneMap = ::std::option::Option::None;
    }

    pub fn has_postProcess_toneMap(&self) -> bool {
        match self._postProcess_toneMap {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_toneMap::postProcess_toneMap(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_postProcess_toneMap(&mut self, v: bool) {
        self._postProcess_toneMap = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_toneMap::postProcess_toneMap(v))
    }

    // bool postProcess_emboss = 6;


    pub fn get_postProcess_emboss(&self) -> bool {
        match self._postProcess_emboss {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_emboss::postProcess_emboss(v)) => v,
            _ => false,
        }
    }
    pub fn clear_postProcess_emboss(&mut self) {
        self._postProcess_emboss = ::std::option::Option::None;
    }

    pub fn has_postProcess_emboss(&self) -> bool {
        match self._postProcess_emboss {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_emboss::postProcess_emboss(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_postProcess_emboss(&mut self, v: bool) {
        self._postProcess_emboss = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_emboss::postProcess_emboss(v))
    }

    // bool postProcess_vignette = 7;


    pub fn get_postProcess_vignette(&self) -> bool {
        match self._postProcess_vignette {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_vignette::postProcess_vignette(v)) => v,
            _ => false,
        }
    }
    pub fn clear_postProcess_vignette(&mut self) {
        self._postProcess_vignette = ::std::option::Option::None;
    }

    pub fn has_postProcess_vignette(&self) -> bool {
        match self._postProcess_vignette {
            ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_vignette::postProcess_vignette(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_postProcess_vignette(&mut self, v: bool) {
        self._postProcess_vignette = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_vignette::postProcess_vignette(v))
    }
}

impl ::protobuf::Message for RoomStageVisualEffects {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._rain = ::std::option::Option::Some(RoomStageVisualEffects_oneof__rain::rain(is.read_bool()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._grass = ::std::option::Option::Some(RoomStageVisualEffects_oneof__grass::grass(is.read_bool()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._postProcess_blackWhite = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_blackWhite::postProcess_blackWhite(is.read_bool()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._postProcess_downSample = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_downSample::postProcess_downSample(is.read_bool()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._postProcess_toneMap = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_toneMap::postProcess_toneMap(is.read_bool()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._postProcess_emboss = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_emboss::postProcess_emboss(is.read_bool()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._postProcess_vignette = ::std::option::Option::Some(RoomStageVisualEffects_oneof__postProcess_vignette::postProcess_vignette(is.read_bool()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let ::std::option::Option::Some(ref v) = self._rain {
            match v {
                &RoomStageVisualEffects_oneof__rain::rain(v) => {
                    my_size += 2;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._grass {
            match v {
                &RoomStageVisualEffects_oneof__grass::grass(v) => {
                    my_size += 2;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_blackWhite {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_blackWhite::postProcess_blackWhite(v) => {
                    my_size += 2;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_downSample {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_downSample::postProcess_downSample(v) => {
                    my_size += 2;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_toneMap {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_toneMap::postProcess_toneMap(v) => {
                    my_size += 2;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_emboss {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_emboss::postProcess_emboss(v) => {
                    my_size += 2;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_vignette {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_vignette::postProcess_vignette(v) => {
                    my_size += 2;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let ::std::option::Option::Some(ref v) = self._rain {
            match v {
                &RoomStageVisualEffects_oneof__rain::rain(v) => {
                    os.write_bool(1, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._grass {
            match v {
                &RoomStageVisualEffects_oneof__grass::grass(v) => {
                    os.write_bool(2, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_blackWhite {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_blackWhite::postProcess_blackWhite(v) => {
                    os.write_bool(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_downSample {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_downSample::postProcess_downSample(v) => {
                    os.write_bool(4, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_toneMap {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_toneMap::postProcess_toneMap(v) => {
                    os.write_bool(5, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_emboss {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_emboss::postProcess_emboss(v) => {
                    os.write_bool(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._postProcess_vignette {
            match v {
                &RoomStageVisualEffects_oneof__postProcess_vignette::postProcess_vignette(v) => {
                    os.write_bool(7, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomStageVisualEffects {
        RoomStageVisualEffects::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "rain",
                RoomStageVisualEffects::has_rain,
                RoomStageVisualEffects::get_rain,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "grass",
                RoomStageVisualEffects::has_grass,
                RoomStageVisualEffects::get_grass,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "postProcess_blackWhite",
                RoomStageVisualEffects::has_postProcess_blackWhite,
                RoomStageVisualEffects::get_postProcess_blackWhite,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "postProcess_downSample",
                RoomStageVisualEffects::has_postProcess_downSample,
                RoomStageVisualEffects::get_postProcess_downSample,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "postProcess_toneMap",
                RoomStageVisualEffects::has_postProcess_toneMap,
                RoomStageVisualEffects::get_postProcess_toneMap,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "postProcess_emboss",
                RoomStageVisualEffects::has_postProcess_emboss,
                RoomStageVisualEffects::get_postProcess_emboss,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "postProcess_vignette",
                RoomStageVisualEffects::has_postProcess_vignette,
                RoomStageVisualEffects::get_postProcess_vignette,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomStageVisualEffects>(
                "RoomStageVisualEffects",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomStageVisualEffects {
        static instance: ::protobuf::rt::LazyV2<RoomStageVisualEffects> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomStageVisualEffects::new)
    }
}

impl ::protobuf::Clear for RoomStageVisualEffects {
    fn clear(&mut self) {
        self._rain = ::std::option::Option::None;
        self._grass = ::std::option::Option::None;
        self._postProcess_blackWhite = ::std::option::Option::None;
        self._postProcess_downSample = ::std::option::Option::None;
        self._postProcess_toneMap = ::std::option::Option::None;
        self._postProcess_emboss = ::std::option::Option::None;
        self._postProcess_vignette = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomStageVisualEffects {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomStageVisualEffects {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomStageAudioEffect {
    // message fields
    pub volume: f32,
    pub field_loop: bool,
    pub playbackRate: f32,
    pub pan: f32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomStageAudioEffect {
    fn default() -> &'a RoomStageAudioEffect {
        <RoomStageAudioEffect as ::protobuf::Message>::default_instance()
    }
}

impl RoomStageAudioEffect {
    pub fn new() -> RoomStageAudioEffect {
        ::std::default::Default::default()
    }

    // float volume = 1;


    pub fn get_volume(&self) -> f32 {
        self.volume
    }
    pub fn clear_volume(&mut self) {
        self.volume = 0.;
    }

    // Param is passed by value, moved
    pub fn set_volume(&mut self, v: f32) {
        self.volume = v;
    }

    // bool loop = 2;


    pub fn get_field_loop(&self) -> bool {
        self.field_loop
    }
    pub fn clear_field_loop(&mut self) {
        self.field_loop = false;
    }

    // Param is passed by value, moved
    pub fn set_field_loop(&mut self, v: bool) {
        self.field_loop = v;
    }

    // float playbackRate = 3;


    pub fn get_playbackRate(&self) -> f32 {
        self.playbackRate
    }
    pub fn clear_playbackRate(&mut self) {
        self.playbackRate = 0.;
    }

    // Param is passed by value, moved
    pub fn set_playbackRate(&mut self, v: f32) {
        self.playbackRate = v;
    }

    // float pan = 4;


    pub fn get_pan(&self) -> f32 {
        self.pan
    }
    pub fn clear_pan(&mut self) {
        self.pan = 0.;
    }

    // Param is passed by value, moved
    pub fn set_pan(&mut self, v: f32) {
        self.pan = v;
    }
}

impl ::protobuf::Message for RoomStageAudioEffect {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed32 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_float()?;
                    self.volume = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.field_loop = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed32 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_float()?;
                    self.playbackRate = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed32 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_float()?;
                    self.pan = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.volume != 0. {
            my_size += 5;
        }
        if self.field_loop != false {
            my_size += 2;
        }
        if self.playbackRate != 0. {
            my_size += 5;
        }
        if self.pan != 0. {
            my_size += 5;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.volume != 0. {
            os.write_float(1, self.volume)?;
        }
        if self.field_loop != false {
            os.write_bool(2, self.field_loop)?;
        }
        if self.playbackRate != 0. {
            os.write_float(3, self.playbackRate)?;
        }
        if self.pan != 0. {
            os.write_float(4, self.pan)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomStageAudioEffect {
        RoomStageAudioEffect::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeFloat>(
                "volume",
                |m: &RoomStageAudioEffect| { &m.volume },
                |m: &mut RoomStageAudioEffect| { &mut m.volume },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "loop",
                |m: &RoomStageAudioEffect| { &m.field_loop },
                |m: &mut RoomStageAudioEffect| { &mut m.field_loop },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeFloat>(
                "playbackRate",
                |m: &RoomStageAudioEffect| { &m.playbackRate },
                |m: &mut RoomStageAudioEffect| { &mut m.playbackRate },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeFloat>(
                "pan",
                |m: &RoomStageAudioEffect| { &m.pan },
                |m: &mut RoomStageAudioEffect| { &mut m.pan },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomStageAudioEffect>(
                "RoomStageAudioEffect",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomStageAudioEffect {
        static instance: ::protobuf::rt::LazyV2<RoomStageAudioEffect> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomStageAudioEffect::new)
    }
}

impl ::protobuf::Clear for RoomStageAudioEffect {
    fn clear(&mut self) {
        self.volume = 0.;
        self.field_loop = false;
        self.playbackRate = 0.;
        self.pan = 0.;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomStageAudioEffect {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomStageAudioEffect {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomStageAudioEffects {
    // message oneof groups
    pub _rain: ::std::option::Option<RoomStageAudioEffects_oneof__rain>,
    pub _wind: ::std::option::Option<RoomStageAudioEffects_oneof__wind>,
    pub _birds: ::std::option::Option<RoomStageAudioEffects_oneof__birds>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomStageAudioEffects {
    fn default() -> &'a RoomStageAudioEffects {
        <RoomStageAudioEffects as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageAudioEffects_oneof__rain {
    rain(RoomStageAudioEffect),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageAudioEffects_oneof__wind {
    wind(RoomStageAudioEffect),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomStageAudioEffects_oneof__birds {
    birds(RoomStageAudioEffect),
}

impl RoomStageAudioEffects {
    pub fn new() -> RoomStageAudioEffects {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageAudioEffect rain = 1;


    pub fn get_rain(&self) -> &RoomStageAudioEffect {
        match self._rain {
            ::std::option::Option::Some(RoomStageAudioEffects_oneof__rain::rain(ref v)) => v,
            _ => <RoomStageAudioEffect as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_rain(&mut self) {
        self._rain = ::std::option::Option::None;
    }

    pub fn has_rain(&self) -> bool {
        match self._rain {
            ::std::option::Option::Some(RoomStageAudioEffects_oneof__rain::rain(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_rain(&mut self, v: RoomStageAudioEffect) {
        self._rain = ::std::option::Option::Some(RoomStageAudioEffects_oneof__rain::rain(v))
    }

    // Mutable pointer to the field.
    pub fn mut_rain(&mut self) -> &mut RoomStageAudioEffect {
        if let ::std::option::Option::Some(RoomStageAudioEffects_oneof__rain::rain(_)) = self._rain {
        } else {
            self._rain = ::std::option::Option::Some(RoomStageAudioEffects_oneof__rain::rain(RoomStageAudioEffect::new()));
        }
        match self._rain {
            ::std::option::Option::Some(RoomStageAudioEffects_oneof__rain::rain(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_rain(&mut self) -> RoomStageAudioEffect {
        if self.has_rain() {
            match self._rain.take() {
                ::std::option::Option::Some(RoomStageAudioEffects_oneof__rain::rain(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomStageAudioEffect::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageAudioEffect wind = 2;


    pub fn get_wind(&self) -> &RoomStageAudioEffect {
        match self._wind {
            ::std::option::Option::Some(RoomStageAudioEffects_oneof__wind::wind(ref v)) => v,
            _ => <RoomStageAudioEffect as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_wind(&mut self) {
        self._wind = ::std::option::Option::None;
    }

    pub fn has_wind(&self) -> bool {
        match self._wind {
            ::std::option::Option::Some(RoomStageAudioEffects_oneof__wind::wind(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_wind(&mut self, v: RoomStageAudioEffect) {
        self._wind = ::std::option::Option::Some(RoomStageAudioEffects_oneof__wind::wind(v))
    }

    // Mutable pointer to the field.
    pub fn mut_wind(&mut self) -> &mut RoomStageAudioEffect {
        if let ::std::option::Option::Some(RoomStageAudioEffects_oneof__wind::wind(_)) = self._wind {
        } else {
            self._wind = ::std::option::Option::Some(RoomStageAudioEffects_oneof__wind::wind(RoomStageAudioEffect::new()));
        }
        match self._wind {
            ::std::option::Option::Some(RoomStageAudioEffects_oneof__wind::wind(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_wind(&mut self) -> RoomStageAudioEffect {
        if self.has_wind() {
            match self._wind.take() {
                ::std::option::Option::Some(RoomStageAudioEffects_oneof__wind::wind(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomStageAudioEffect::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageAudioEffect birds = 3;


    pub fn get_birds(&self) -> &RoomStageAudioEffect {
        match self._birds {
            ::std::option::Option::Some(RoomStageAudioEffects_oneof__birds::birds(ref v)) => v,
            _ => <RoomStageAudioEffect as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_birds(&mut self) {
        self._birds = ::std::option::Option::None;
    }

    pub fn has_birds(&self) -> bool {
        match self._birds {
            ::std::option::Option::Some(RoomStageAudioEffects_oneof__birds::birds(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_birds(&mut self, v: RoomStageAudioEffect) {
        self._birds = ::std::option::Option::Some(RoomStageAudioEffects_oneof__birds::birds(v))
    }

    // Mutable pointer to the field.
    pub fn mut_birds(&mut self) -> &mut RoomStageAudioEffect {
        if let ::std::option::Option::Some(RoomStageAudioEffects_oneof__birds::birds(_)) = self._birds {
        } else {
            self._birds = ::std::option::Option::Some(RoomStageAudioEffects_oneof__birds::birds(RoomStageAudioEffect::new()));
        }
        match self._birds {
            ::std::option::Option::Some(RoomStageAudioEffects_oneof__birds::birds(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_birds(&mut self) -> RoomStageAudioEffect {
        if self.has_birds() {
            match self._birds.take() {
                ::std::option::Option::Some(RoomStageAudioEffects_oneof__birds::birds(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomStageAudioEffect::new()
        }
    }
}

impl ::protobuf::Message for RoomStageAudioEffects {
    fn is_initialized(&self) -> bool {
        if let Some(RoomStageAudioEffects_oneof__rain::rain(ref v)) = self._rain {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(RoomStageAudioEffects_oneof__wind::wind(ref v)) = self._wind {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(RoomStageAudioEffects_oneof__birds::birds(ref v)) = self._birds {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._rain = ::std::option::Option::Some(RoomStageAudioEffects_oneof__rain::rain(is.read_message()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._wind = ::std::option::Option::Some(RoomStageAudioEffects_oneof__wind::wind(is.read_message()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._birds = ::std::option::Option::Some(RoomStageAudioEffects_oneof__birds::birds(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let ::std::option::Option::Some(ref v) = self._rain {
            match v {
                &RoomStageAudioEffects_oneof__rain::rain(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._wind {
            match v {
                &RoomStageAudioEffects_oneof__wind::wind(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._birds {
            match v {
                &RoomStageAudioEffects_oneof__birds::birds(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let ::std::option::Option::Some(ref v) = self._rain {
            match v {
                &RoomStageAudioEffects_oneof__rain::rain(ref v) => {
                    os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._wind {
            match v {
                &RoomStageAudioEffects_oneof__wind::wind(ref v) => {
                    os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._birds {
            match v {
                &RoomStageAudioEffects_oneof__birds::birds(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomStageAudioEffects {
        RoomStageAudioEffects::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomStageAudioEffect>(
                "rain",
                RoomStageAudioEffects::has_rain,
                RoomStageAudioEffects::get_rain,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomStageAudioEffect>(
                "wind",
                RoomStageAudioEffects::has_wind,
                RoomStageAudioEffects::get_wind,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomStageAudioEffect>(
                "birds",
                RoomStageAudioEffects::has_birds,
                RoomStageAudioEffects::get_birds,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomStageAudioEffects>(
                "RoomStageAudioEffects",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomStageAudioEffects {
        static instance: ::protobuf::rt::LazyV2<RoomStageAudioEffects> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomStageAudioEffects::new)
    }
}

impl ::protobuf::Clear for RoomStageAudioEffects {
    fn clear(&mut self) {
        self._rain = ::std::option::Option::None;
        self._wind = ::std::option::Option::None;
        self._birds = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomStageAudioEffects {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomStageAudioEffects {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomStageDetails {
    // message fields
    pub stage: RoomStages,
    pub effects: ::protobuf::SingularPtrField<RoomStageVisualEffects>,
    pub audioEffects: ::protobuf::SingularPtrField<RoomStageAudioEffects>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomStageDetails {
    fn default() -> &'a RoomStageDetails {
        <RoomStageDetails as ::protobuf::Message>::default_instance()
    }
}

impl RoomStageDetails {
    pub fn new() -> RoomStageDetails {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStages stage = 1;


    pub fn get_stage(&self) -> RoomStages {
        self.stage
    }
    pub fn clear_stage(&mut self) {
        self.stage = RoomStages::UNKNOWN;
    }

    // Param is passed by value, moved
    pub fn set_stage(&mut self, v: RoomStages) {
        self.stage = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageVisualEffects effects = 2;


    pub fn get_effects(&self) -> &RoomStageVisualEffects {
        self.effects.as_ref().unwrap_or_else(|| <RoomStageVisualEffects as ::protobuf::Message>::default_instance())
    }
    pub fn clear_effects(&mut self) {
        self.effects.clear();
    }

    pub fn has_effects(&self) -> bool {
        self.effects.is_some()
    }

    // Param is passed by value, moved
    pub fn set_effects(&mut self, v: RoomStageVisualEffects) {
        self.effects = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_effects(&mut self) -> &mut RoomStageVisualEffects {
        if self.effects.is_none() {
            self.effects.set_default();
        }
        self.effects.as_mut().unwrap()
    }

    // Take field
    pub fn take_effects(&mut self) -> RoomStageVisualEffects {
        self.effects.take().unwrap_or_else(|| RoomStageVisualEffects::new())
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageAudioEffects audioEffects = 3;


    pub fn get_audioEffects(&self) -> &RoomStageAudioEffects {
        self.audioEffects.as_ref().unwrap_or_else(|| <RoomStageAudioEffects as ::protobuf::Message>::default_instance())
    }
    pub fn clear_audioEffects(&mut self) {
        self.audioEffects.clear();
    }

    pub fn has_audioEffects(&self) -> bool {
        self.audioEffects.is_some()
    }

    // Param is passed by value, moved
    pub fn set_audioEffects(&mut self, v: RoomStageAudioEffects) {
        self.audioEffects = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_audioEffects(&mut self) -> &mut RoomStageAudioEffects {
        if self.audioEffects.is_none() {
            self.audioEffects.set_default();
        }
        self.audioEffects.as_mut().unwrap()
    }

    // Take field
    pub fn take_audioEffects(&mut self) -> RoomStageAudioEffects {
        self.audioEffects.take().unwrap_or_else(|| RoomStageAudioEffects::new())
    }
}

impl ::protobuf::Message for RoomStageDetails {
    fn is_initialized(&self) -> bool {
        for v in &self.effects {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.audioEffects {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.stage, 1, &mut self.unknown_fields)?
                },
                2 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.effects)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.audioEffects)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.stage != RoomStages::UNKNOWN {
            my_size += ::protobuf::rt::enum_size(1, self.stage);
        }
        if let Some(ref v) = self.effects.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.audioEffects.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.stage != RoomStages::UNKNOWN {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.stage))?;
        }
        if let Some(ref v) = self.effects.as_ref() {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.audioEffects.as_ref() {
            os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomStageDetails {
        RoomStageDetails::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<RoomStages>>(
                "stage",
                |m: &RoomStageDetails| { &m.stage },
                |m: &mut RoomStageDetails| { &mut m.stage },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RoomStageVisualEffects>>(
                "effects",
                |m: &RoomStageDetails| { &m.effects },
                |m: &mut RoomStageDetails| { &mut m.effects },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RoomStageAudioEffects>>(
                "audioEffects",
                |m: &RoomStageDetails| { &m.audioEffects },
                |m: &mut RoomStageDetails| { &mut m.audioEffects },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomStageDetails>(
                "RoomStageDetails",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomStageDetails {
        static instance: ::protobuf::rt::LazyV2<RoomStageDetails> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomStageDetails::new)
    }
}

impl ::protobuf::Clear for RoomStageDetails {
    fn clear(&mut self) {
        self.stage = RoomStages::UNKNOWN;
        self.effects.clear();
        self.audioEffects.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomStageDetails {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomStageDetails {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomSettingsMeta {
    // message fields
    pub AutoRemove: bool,
    pub Unique: bool,
    pub Persistent: bool,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomSettingsMeta {
    fn default() -> &'a RoomSettingsMeta {
        <RoomSettingsMeta as ::protobuf::Message>::default_instance()
    }
}

impl RoomSettingsMeta {
    pub fn new() -> RoomSettingsMeta {
        ::std::default::Default::default()
    }

    // bool AutoRemove = 1;


    pub fn get_AutoRemove(&self) -> bool {
        self.AutoRemove
    }
    pub fn clear_AutoRemove(&mut self) {
        self.AutoRemove = false;
    }

    // Param is passed by value, moved
    pub fn set_AutoRemove(&mut self, v: bool) {
        self.AutoRemove = v;
    }

    // bool Unique = 2;


    pub fn get_Unique(&self) -> bool {
        self.Unique
    }
    pub fn clear_Unique(&mut self) {
        self.Unique = false;
    }

    // Param is passed by value, moved
    pub fn set_Unique(&mut self, v: bool) {
        self.Unique = v;
    }

    // bool Persistent = 3;


    pub fn get_Persistent(&self) -> bool {
        self.Persistent
    }
    pub fn clear_Persistent(&mut self) {
        self.Persistent = false;
    }

    // Param is passed by value, moved
    pub fn set_Persistent(&mut self, v: bool) {
        self.Persistent = v;
    }
}

impl ::protobuf::Message for RoomSettingsMeta {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AutoRemove = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.Unique = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.Persistent = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.AutoRemove != false {
            my_size += 2;
        }
        if self.Unique != false {
            my_size += 2;
        }
        if self.Persistent != false {
            my_size += 2;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.AutoRemove != false {
            os.write_bool(1, self.AutoRemove)?;
        }
        if self.Unique != false {
            os.write_bool(2, self.Unique)?;
        }
        if self.Persistent != false {
            os.write_bool(3, self.Persistent)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomSettingsMeta {
        RoomSettingsMeta::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AutoRemove",
                |m: &RoomSettingsMeta| { &m.AutoRemove },
                |m: &mut RoomSettingsMeta| { &mut m.AutoRemove },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "Unique",
                |m: &RoomSettingsMeta| { &m.Unique },
                |m: &mut RoomSettingsMeta| { &mut m.Unique },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "Persistent",
                |m: &RoomSettingsMeta| { &m.Persistent },
                |m: &mut RoomSettingsMeta| { &mut m.Persistent },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomSettingsMeta>(
                "RoomSettingsMeta",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomSettingsMeta {
        static instance: ::protobuf::rt::LazyV2<RoomSettingsMeta> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomSettingsMeta::new)
    }
}

impl ::protobuf::Clear for RoomSettingsMeta {
    fn clear(&mut self) {
        self.AutoRemove = false;
        self.Unique = false;
        self.Persistent = false;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomSettingsMeta {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomSettingsMeta {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct BasicRoomDto {
    // message fields
    pub roomName: ::std::string::String,
    pub roomID: ::std::string::String,
    pub roomType: ::std::string::String,
    pub userCount: i32,
    pub maxUsers: i32,
    pub isPasswordProtected: bool,
    pub roomOwner: ::std::string::String,
    pub roomStage: RoomStages,
    pub createdDate: ::std::string::String,
    // message oneof groups
    pub _hostDetails: ::std::option::Option<BasicRoomDto_oneof__hostDetails>,
    pub _StageDetails: ::std::option::Option<BasicRoomDto_oneof__StageDetails>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a BasicRoomDto {
    fn default() -> &'a BasicRoomDto {
        <BasicRoomDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum BasicRoomDto_oneof__hostDetails {
    hostDetails(RoomHostDetails),
}

#[derive(Clone,PartialEq,Debug)]
pub enum BasicRoomDto_oneof__StageDetails {
    StageDetails(RoomStageDetails),
}

impl BasicRoomDto {
    pub fn new() -> BasicRoomDto {
        ::std::default::Default::default()
    }

    // string roomName = 1;


    pub fn get_roomName(&self) -> &str {
        &self.roomName
    }
    pub fn clear_roomName(&mut self) {
        self.roomName.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomName(&mut self, v: ::std::string::String) {
        self.roomName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomName(&mut self) -> &mut ::std::string::String {
        &mut self.roomName
    }

    // Take field
    pub fn take_roomName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomName, ::std::string::String::new())
    }

    // string roomID = 2;


    pub fn get_roomID(&self) -> &str {
        &self.roomID
    }
    pub fn clear_roomID(&mut self) {
        self.roomID.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.roomID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        &mut self.roomID
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomID, ::std::string::String::new())
    }

    // string roomType = 3;


    pub fn get_roomType(&self) -> &str {
        &self.roomType
    }
    pub fn clear_roomType(&mut self) {
        self.roomType.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomType(&mut self, v: ::std::string::String) {
        self.roomType = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomType(&mut self) -> &mut ::std::string::String {
        &mut self.roomType
    }

    // Take field
    pub fn take_roomType(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomType, ::std::string::String::new())
    }

    // int32 userCount = 4;


    pub fn get_userCount(&self) -> i32 {
        self.userCount
    }
    pub fn clear_userCount(&mut self) {
        self.userCount = 0;
    }

    // Param is passed by value, moved
    pub fn set_userCount(&mut self, v: i32) {
        self.userCount = v;
    }

    // int32 maxUsers = 5;


    pub fn get_maxUsers(&self) -> i32 {
        self.maxUsers
    }
    pub fn clear_maxUsers(&mut self) {
        self.maxUsers = 0;
    }

    // Param is passed by value, moved
    pub fn set_maxUsers(&mut self, v: i32) {
        self.maxUsers = v;
    }

    // bool isPasswordProtected = 6;


    pub fn get_isPasswordProtected(&self) -> bool {
        self.isPasswordProtected
    }
    pub fn clear_isPasswordProtected(&mut self) {
        self.isPasswordProtected = false;
    }

    // Param is passed by value, moved
    pub fn set_isPasswordProtected(&mut self, v: bool) {
        self.isPasswordProtected = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomHostDetails hostDetails = 7;


    pub fn get_hostDetails(&self) -> &RoomHostDetails {
        match self._hostDetails {
            ::std::option::Option::Some(BasicRoomDto_oneof__hostDetails::hostDetails(ref v)) => v,
            _ => <RoomHostDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_hostDetails(&mut self) {
        self._hostDetails = ::std::option::Option::None;
    }

    pub fn has_hostDetails(&self) -> bool {
        match self._hostDetails {
            ::std::option::Option::Some(BasicRoomDto_oneof__hostDetails::hostDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_hostDetails(&mut self, v: RoomHostDetails) {
        self._hostDetails = ::std::option::Option::Some(BasicRoomDto_oneof__hostDetails::hostDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_hostDetails(&mut self) -> &mut RoomHostDetails {
        if let ::std::option::Option::Some(BasicRoomDto_oneof__hostDetails::hostDetails(_)) = self._hostDetails {
        } else {
            self._hostDetails = ::std::option::Option::Some(BasicRoomDto_oneof__hostDetails::hostDetails(RoomHostDetails::new()));
        }
        match self._hostDetails {
            ::std::option::Option::Some(BasicRoomDto_oneof__hostDetails::hostDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_hostDetails(&mut self) -> RoomHostDetails {
        if self.has_hostDetails() {
            match self._hostDetails.take() {
                ::std::option::Option::Some(BasicRoomDto_oneof__hostDetails::hostDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomHostDetails::new()
        }
    }

    // string roomOwner = 8;


    pub fn get_roomOwner(&self) -> &str {
        &self.roomOwner
    }
    pub fn clear_roomOwner(&mut self) {
        self.roomOwner.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomOwner(&mut self, v: ::std::string::String) {
        self.roomOwner = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomOwner(&mut self) -> &mut ::std::string::String {
        &mut self.roomOwner
    }

    // Take field
    pub fn take_roomOwner(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomOwner, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStages roomStage = 9;


    pub fn get_roomStage(&self) -> RoomStages {
        self.roomStage
    }
    pub fn clear_roomStage(&mut self) {
        self.roomStage = RoomStages::UNKNOWN;
    }

    // Param is passed by value, moved
    pub fn set_roomStage(&mut self, v: RoomStages) {
        self.roomStage = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageDetails StageDetails = 10;


    pub fn get_StageDetails(&self) -> &RoomStageDetails {
        match self._StageDetails {
            ::std::option::Option::Some(BasicRoomDto_oneof__StageDetails::StageDetails(ref v)) => v,
            _ => <RoomStageDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_StageDetails(&mut self) {
        self._StageDetails = ::std::option::Option::None;
    }

    pub fn has_StageDetails(&self) -> bool {
        match self._StageDetails {
            ::std::option::Option::Some(BasicRoomDto_oneof__StageDetails::StageDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_StageDetails(&mut self, v: RoomStageDetails) {
        self._StageDetails = ::std::option::Option::Some(BasicRoomDto_oneof__StageDetails::StageDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_StageDetails(&mut self) -> &mut RoomStageDetails {
        if let ::std::option::Option::Some(BasicRoomDto_oneof__StageDetails::StageDetails(_)) = self._StageDetails {
        } else {
            self._StageDetails = ::std::option::Option::Some(BasicRoomDto_oneof__StageDetails::StageDetails(RoomStageDetails::new()));
        }
        match self._StageDetails {
            ::std::option::Option::Some(BasicRoomDto_oneof__StageDetails::StageDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_StageDetails(&mut self) -> RoomStageDetails {
        if self.has_StageDetails() {
            match self._StageDetails.take() {
                ::std::option::Option::Some(BasicRoomDto_oneof__StageDetails::StageDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomStageDetails::new()
        }
    }

    // string createdDate = 11;


    pub fn get_createdDate(&self) -> &str {
        &self.createdDate
    }
    pub fn clear_createdDate(&mut self) {
        self.createdDate.clear();
    }

    // Param is passed by value, moved
    pub fn set_createdDate(&mut self, v: ::std::string::String) {
        self.createdDate = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_createdDate(&mut self) -> &mut ::std::string::String {
        &mut self.createdDate
    }

    // Take field
    pub fn take_createdDate(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.createdDate, ::std::string::String::new())
    }
}

impl ::protobuf::Message for BasicRoomDto {
    fn is_initialized(&self) -> bool {
        if let Some(BasicRoomDto_oneof__hostDetails::hostDetails(ref v)) = self._hostDetails {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(BasicRoomDto_oneof__StageDetails::StageDetails(ref v)) = self._StageDetails {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomName)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomID)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomType)?;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.userCount = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.maxUsers = tmp;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isPasswordProtected = tmp;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._hostDetails = ::std::option::Option::Some(BasicRoomDto_oneof__hostDetails::hostDetails(is.read_message()?));
                },
                8 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomOwner)?;
                },
                9 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.roomStage, 9, &mut self.unknown_fields)?
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._StageDetails = ::std::option::Option::Some(BasicRoomDto_oneof__StageDetails::StageDetails(is.read_message()?));
                },
                11 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.createdDate)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.roomName.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.roomName);
        }
        if !self.roomID.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.roomID);
        }
        if !self.roomType.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.roomType);
        }
        if self.userCount != 0 {
            my_size += ::protobuf::rt::value_size(4, self.userCount, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.maxUsers != 0 {
            my_size += ::protobuf::rt::value_size(5, self.maxUsers, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.isPasswordProtected != false {
            my_size += 2;
        }
        if !self.roomOwner.is_empty() {
            my_size += ::protobuf::rt::string_size(8, &self.roomOwner);
        }
        if self.roomStage != RoomStages::UNKNOWN {
            my_size += ::protobuf::rt::enum_size(9, self.roomStage);
        }
        if !self.createdDate.is_empty() {
            my_size += ::protobuf::rt::string_size(11, &self.createdDate);
        }
        if let ::std::option::Option::Some(ref v) = self._hostDetails {
            match v {
                &BasicRoomDto_oneof__hostDetails::hostDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._StageDetails {
            match v {
                &BasicRoomDto_oneof__StageDetails::StageDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.roomName.is_empty() {
            os.write_string(1, &self.roomName)?;
        }
        if !self.roomID.is_empty() {
            os.write_string(2, &self.roomID)?;
        }
        if !self.roomType.is_empty() {
            os.write_string(3, &self.roomType)?;
        }
        if self.userCount != 0 {
            os.write_int32(4, self.userCount)?;
        }
        if self.maxUsers != 0 {
            os.write_int32(5, self.maxUsers)?;
        }
        if self.isPasswordProtected != false {
            os.write_bool(6, self.isPasswordProtected)?;
        }
        if !self.roomOwner.is_empty() {
            os.write_string(8, &self.roomOwner)?;
        }
        if self.roomStage != RoomStages::UNKNOWN {
            os.write_enum(9, ::protobuf::ProtobufEnum::value(&self.roomStage))?;
        }
        if !self.createdDate.is_empty() {
            os.write_string(11, &self.createdDate)?;
        }
        if let ::std::option::Option::Some(ref v) = self._hostDetails {
            match v {
                &BasicRoomDto_oneof__hostDetails::hostDetails(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._StageDetails {
            match v {
                &BasicRoomDto_oneof__StageDetails::StageDetails(ref v) => {
                    os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> BasicRoomDto {
        BasicRoomDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomName",
                |m: &BasicRoomDto| { &m.roomName },
                |m: &mut BasicRoomDto| { &mut m.roomName },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomID",
                |m: &BasicRoomDto| { &m.roomID },
                |m: &mut BasicRoomDto| { &mut m.roomID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomType",
                |m: &BasicRoomDto| { &m.roomType },
                |m: &mut BasicRoomDto| { &mut m.roomType },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "userCount",
                |m: &BasicRoomDto| { &m.userCount },
                |m: &mut BasicRoomDto| { &mut m.userCount },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "maxUsers",
                |m: &BasicRoomDto| { &m.maxUsers },
                |m: &mut BasicRoomDto| { &mut m.maxUsers },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isPasswordProtected",
                |m: &BasicRoomDto| { &m.isPasswordProtected },
                |m: &mut BasicRoomDto| { &mut m.isPasswordProtected },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomHostDetails>(
                "hostDetails",
                BasicRoomDto::has_hostDetails,
                BasicRoomDto::get_hostDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomOwner",
                |m: &BasicRoomDto| { &m.roomOwner },
                |m: &mut BasicRoomDto| { &mut m.roomOwner },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<RoomStages>>(
                "roomStage",
                |m: &BasicRoomDto| { &m.roomStage },
                |m: &mut BasicRoomDto| { &mut m.roomStage },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomStageDetails>(
                "StageDetails",
                BasicRoomDto::has_StageDetails,
                BasicRoomDto::get_StageDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "createdDate",
                |m: &BasicRoomDto| { &m.createdDate },
                |m: &mut BasicRoomDto| { &mut m.createdDate },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<BasicRoomDto>(
                "BasicRoomDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static BasicRoomDto {
        static instance: ::protobuf::rt::LazyV2<BasicRoomDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(BasicRoomDto::new)
    }
}

impl ::protobuf::Clear for BasicRoomDto {
    fn clear(&mut self) {
        self.roomName.clear();
        self.roomID.clear();
        self.roomType.clear();
        self.userCount = 0;
        self.maxUsers = 0;
        self.isPasswordProtected = false;
        self._hostDetails = ::std::option::Option::None;
        self.roomOwner.clear();
        self.roomStage = RoomStages::UNKNOWN;
        self._StageDetails = ::std::option::Option::None;
        self.createdDate.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for BasicRoomDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for BasicRoomDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomDto {
    // message fields
    pub roomName: ::std::string::String,
    pub roomID: ::std::string::String,
    pub roomOwner: ::std::string::String,
    pub users: ::protobuf::RepeatedField<::std::string::String>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomDto {
    fn default() -> &'a RoomDto {
        <RoomDto as ::protobuf::Message>::default_instance()
    }
}

impl RoomDto {
    pub fn new() -> RoomDto {
        ::std::default::Default::default()
    }

    // string roomName = 1;


    pub fn get_roomName(&self) -> &str {
        &self.roomName
    }
    pub fn clear_roomName(&mut self) {
        self.roomName.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomName(&mut self, v: ::std::string::String) {
        self.roomName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomName(&mut self) -> &mut ::std::string::String {
        &mut self.roomName
    }

    // Take field
    pub fn take_roomName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomName, ::std::string::String::new())
    }

    // string roomID = 2;


    pub fn get_roomID(&self) -> &str {
        &self.roomID
    }
    pub fn clear_roomID(&mut self) {
        self.roomID.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.roomID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        &mut self.roomID
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomID, ::std::string::String::new())
    }

    // string roomOwner = 3;


    pub fn get_roomOwner(&self) -> &str {
        &self.roomOwner
    }
    pub fn clear_roomOwner(&mut self) {
        self.roomOwner.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomOwner(&mut self, v: ::std::string::String) {
        self.roomOwner = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomOwner(&mut self) -> &mut ::std::string::String {
        &mut self.roomOwner
    }

    // Take field
    pub fn take_roomOwner(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomOwner, ::std::string::String::new())
    }

    // repeated string users = 4;


    pub fn get_users(&self) -> &[::std::string::String] {
        &self.users
    }
    pub fn clear_users(&mut self) {
        self.users.clear();
    }

    // Param is passed by value, moved
    pub fn set_users(&mut self, v: ::protobuf::RepeatedField<::std::string::String>) {
        self.users = v;
    }

    // Mutable pointer to the field.
    pub fn mut_users(&mut self) -> &mut ::protobuf::RepeatedField<::std::string::String> {
        &mut self.users
    }

    // Take field
    pub fn take_users(&mut self) -> ::protobuf::RepeatedField<::std::string::String> {
        ::std::mem::replace(&mut self.users, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for RoomDto {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomName)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomID)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomOwner)?;
                },
                4 => {
                    ::protobuf::rt::read_repeated_string_into(wire_type, is, &mut self.users)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.roomName.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.roomName);
        }
        if !self.roomID.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.roomID);
        }
        if !self.roomOwner.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.roomOwner);
        }
        for value in &self.users {
            my_size += ::protobuf::rt::string_size(4, &value);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.roomName.is_empty() {
            os.write_string(1, &self.roomName)?;
        }
        if !self.roomID.is_empty() {
            os.write_string(2, &self.roomID)?;
        }
        if !self.roomOwner.is_empty() {
            os.write_string(3, &self.roomOwner)?;
        }
        for v in &self.users {
            os.write_string(4, &v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomDto {
        RoomDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomName",
                |m: &RoomDto| { &m.roomName },
                |m: &mut RoomDto| { &mut m.roomName },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomID",
                |m: &RoomDto| { &m.roomID },
                |m: &mut RoomDto| { &mut m.roomID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomOwner",
                |m: &RoomDto| { &m.roomOwner },
                |m: &mut RoomDto| { &mut m.roomOwner },
            ));
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "users",
                |m: &RoomDto| { &m.users },
                |m: &mut RoomDto| { &mut m.users },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomDto>(
                "RoomDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomDto {
        static instance: ::protobuf::rt::LazyV2<RoomDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomDto::new)
    }
}

impl ::protobuf::Clear for RoomDto {
    fn clear(&mut self) {
        self.roomName.clear();
        self.roomID.clear();
        self.roomOwner.clear();
        self.users.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomSettings {
    // message fields
    pub MaxPlayers: i32,
    pub MaxChatHistory: i32,
    pub RoomOwner: ::std::string::String,
    pub RoomStatus: RoomStatus,
    pub RoomType: RoomType,
    pub OnlyOwnerCanPlay: bool,
    pub OnlyOwnerCanChat: bool,
    pub AllowBlackMidi: bool,
    pub AllowGuests: bool,
    pub AllowBots: bool,
    pub OnlyMods: bool,
    pub NoChatAllowed: bool,
    pub NoPlayingAllowed: bool,
    pub FilterProfanity: bool,
    pub Meta: ::protobuf::SingularPtrField<RoomSettingsMeta>,
    pub StageDetails: ::protobuf::SingularPtrField<RoomStageDetails>,
    // message oneof groups
    pub _WelcomeMessage: ::std::option::Option<RoomSettings_oneof__WelcomeMessage>,
    pub _Password: ::std::option::Option<RoomSettings_oneof__Password>,
    pub _StageDetailsJSON: ::std::option::Option<RoomSettings_oneof__StageDetailsJSON>,
    pub _HostDetails: ::std::option::Option<RoomSettings_oneof__HostDetails>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomSettings {
    fn default() -> &'a RoomSettings {
        <RoomSettings as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomSettings_oneof__WelcomeMessage {
    WelcomeMessage(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomSettings_oneof__Password {
    Password(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomSettings_oneof__StageDetailsJSON {
    StageDetailsJSON(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomSettings_oneof__HostDetails {
    HostDetails(RoomHostDetails),
}

impl RoomSettings {
    pub fn new() -> RoomSettings {
        ::std::default::Default::default()
    }

    // string WelcomeMessage = 1;


    pub fn get_WelcomeMessage(&self) -> &str {
        match self._WelcomeMessage {
            ::std::option::Option::Some(RoomSettings_oneof__WelcomeMessage::WelcomeMessage(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_WelcomeMessage(&mut self) {
        self._WelcomeMessage = ::std::option::Option::None;
    }

    pub fn has_WelcomeMessage(&self) -> bool {
        match self._WelcomeMessage {
            ::std::option::Option::Some(RoomSettings_oneof__WelcomeMessage::WelcomeMessage(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_WelcomeMessage(&mut self, v: ::std::string::String) {
        self._WelcomeMessage = ::std::option::Option::Some(RoomSettings_oneof__WelcomeMessage::WelcomeMessage(v))
    }

    // Mutable pointer to the field.
    pub fn mut_WelcomeMessage(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(RoomSettings_oneof__WelcomeMessage::WelcomeMessage(_)) = self._WelcomeMessage {
        } else {
            self._WelcomeMessage = ::std::option::Option::Some(RoomSettings_oneof__WelcomeMessage::WelcomeMessage(::std::string::String::new()));
        }
        match self._WelcomeMessage {
            ::std::option::Option::Some(RoomSettings_oneof__WelcomeMessage::WelcomeMessage(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_WelcomeMessage(&mut self) -> ::std::string::String {
        if self.has_WelcomeMessage() {
            match self._WelcomeMessage.take() {
                ::std::option::Option::Some(RoomSettings_oneof__WelcomeMessage::WelcomeMessage(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // int32 MaxPlayers = 2;


    pub fn get_MaxPlayers(&self) -> i32 {
        self.MaxPlayers
    }
    pub fn clear_MaxPlayers(&mut self) {
        self.MaxPlayers = 0;
    }

    // Param is passed by value, moved
    pub fn set_MaxPlayers(&mut self, v: i32) {
        self.MaxPlayers = v;
    }

    // int32 MaxChatHistory = 3;


    pub fn get_MaxChatHistory(&self) -> i32 {
        self.MaxChatHistory
    }
    pub fn clear_MaxChatHistory(&mut self) {
        self.MaxChatHistory = 0;
    }

    // Param is passed by value, moved
    pub fn set_MaxChatHistory(&mut self, v: i32) {
        self.MaxChatHistory = v;
    }

    // string RoomOwner = 4;


    pub fn get_RoomOwner(&self) -> &str {
        &self.RoomOwner
    }
    pub fn clear_RoomOwner(&mut self) {
        self.RoomOwner.clear();
    }

    // Param is passed by value, moved
    pub fn set_RoomOwner(&mut self, v: ::std::string::String) {
        self.RoomOwner = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_RoomOwner(&mut self) -> &mut ::std::string::String {
        &mut self.RoomOwner
    }

    // Take field
    pub fn take_RoomOwner(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.RoomOwner, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStatus RoomStatus = 5;


    pub fn get_RoomStatus(&self) -> RoomStatus {
        self.RoomStatus
    }
    pub fn clear_RoomStatus(&mut self) {
        self.RoomStatus = RoomStatus::Public;
    }

    // Param is passed by value, moved
    pub fn set_RoomStatus(&mut self, v: RoomStatus) {
        self.RoomStatus = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomType RoomType = 6;


    pub fn get_RoomType(&self) -> RoomType {
        self.RoomType
    }
    pub fn clear_RoomType(&mut self) {
        self.RoomType = RoomType::Lobby;
    }

    // Param is passed by value, moved
    pub fn set_RoomType(&mut self, v: RoomType) {
        self.RoomType = v;
    }

    // string Password = 7;


    pub fn get_Password(&self) -> &str {
        match self._Password {
            ::std::option::Option::Some(RoomSettings_oneof__Password::Password(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_Password(&mut self) {
        self._Password = ::std::option::Option::None;
    }

    pub fn has_Password(&self) -> bool {
        match self._Password {
            ::std::option::Option::Some(RoomSettings_oneof__Password::Password(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_Password(&mut self, v: ::std::string::String) {
        self._Password = ::std::option::Option::Some(RoomSettings_oneof__Password::Password(v))
    }

    // Mutable pointer to the field.
    pub fn mut_Password(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(RoomSettings_oneof__Password::Password(_)) = self._Password {
        } else {
            self._Password = ::std::option::Option::Some(RoomSettings_oneof__Password::Password(::std::string::String::new()));
        }
        match self._Password {
            ::std::option::Option::Some(RoomSettings_oneof__Password::Password(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_Password(&mut self) -> ::std::string::String {
        if self.has_Password() {
            match self._Password.take() {
                ::std::option::Option::Some(RoomSettings_oneof__Password::Password(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // bool OnlyOwnerCanPlay = 8;


    pub fn get_OnlyOwnerCanPlay(&self) -> bool {
        self.OnlyOwnerCanPlay
    }
    pub fn clear_OnlyOwnerCanPlay(&mut self) {
        self.OnlyOwnerCanPlay = false;
    }

    // Param is passed by value, moved
    pub fn set_OnlyOwnerCanPlay(&mut self, v: bool) {
        self.OnlyOwnerCanPlay = v;
    }

    // bool OnlyOwnerCanChat = 9;


    pub fn get_OnlyOwnerCanChat(&self) -> bool {
        self.OnlyOwnerCanChat
    }
    pub fn clear_OnlyOwnerCanChat(&mut self) {
        self.OnlyOwnerCanChat = false;
    }

    // Param is passed by value, moved
    pub fn set_OnlyOwnerCanChat(&mut self, v: bool) {
        self.OnlyOwnerCanChat = v;
    }

    // bool AllowBlackMidi = 10;


    pub fn get_AllowBlackMidi(&self) -> bool {
        self.AllowBlackMidi
    }
    pub fn clear_AllowBlackMidi(&mut self) {
        self.AllowBlackMidi = false;
    }

    // Param is passed by value, moved
    pub fn set_AllowBlackMidi(&mut self, v: bool) {
        self.AllowBlackMidi = v;
    }

    // bool AllowGuests = 11;


    pub fn get_AllowGuests(&self) -> bool {
        self.AllowGuests
    }
    pub fn clear_AllowGuests(&mut self) {
        self.AllowGuests = false;
    }

    // Param is passed by value, moved
    pub fn set_AllowGuests(&mut self, v: bool) {
        self.AllowGuests = v;
    }

    // bool AllowBots = 12;


    pub fn get_AllowBots(&self) -> bool {
        self.AllowBots
    }
    pub fn clear_AllowBots(&mut self) {
        self.AllowBots = false;
    }

    // Param is passed by value, moved
    pub fn set_AllowBots(&mut self, v: bool) {
        self.AllowBots = v;
    }

    // bool OnlyMods = 13;


    pub fn get_OnlyMods(&self) -> bool {
        self.OnlyMods
    }
    pub fn clear_OnlyMods(&mut self) {
        self.OnlyMods = false;
    }

    // Param is passed by value, moved
    pub fn set_OnlyMods(&mut self, v: bool) {
        self.OnlyMods = v;
    }

    // bool NoChatAllowed = 14;


    pub fn get_NoChatAllowed(&self) -> bool {
        self.NoChatAllowed
    }
    pub fn clear_NoChatAllowed(&mut self) {
        self.NoChatAllowed = false;
    }

    // Param is passed by value, moved
    pub fn set_NoChatAllowed(&mut self, v: bool) {
        self.NoChatAllowed = v;
    }

    // bool NoPlayingAllowed = 15;


    pub fn get_NoPlayingAllowed(&self) -> bool {
        self.NoPlayingAllowed
    }
    pub fn clear_NoPlayingAllowed(&mut self) {
        self.NoPlayingAllowed = false;
    }

    // Param is passed by value, moved
    pub fn set_NoPlayingAllowed(&mut self, v: bool) {
        self.NoPlayingAllowed = v;
    }

    // bool FilterProfanity = 16;


    pub fn get_FilterProfanity(&self) -> bool {
        self.FilterProfanity
    }
    pub fn clear_FilterProfanity(&mut self) {
        self.FilterProfanity = false;
    }

    // Param is passed by value, moved
    pub fn set_FilterProfanity(&mut self, v: bool) {
        self.FilterProfanity = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomSettingsMeta Meta = 17;


    pub fn get_Meta(&self) -> &RoomSettingsMeta {
        self.Meta.as_ref().unwrap_or_else(|| <RoomSettingsMeta as ::protobuf::Message>::default_instance())
    }
    pub fn clear_Meta(&mut self) {
        self.Meta.clear();
    }

    pub fn has_Meta(&self) -> bool {
        self.Meta.is_some()
    }

    // Param is passed by value, moved
    pub fn set_Meta(&mut self, v: RoomSettingsMeta) {
        self.Meta = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_Meta(&mut self) -> &mut RoomSettingsMeta {
        if self.Meta.is_none() {
            self.Meta.set_default();
        }
        self.Meta.as_mut().unwrap()
    }

    // Take field
    pub fn take_Meta(&mut self) -> RoomSettingsMeta {
        self.Meta.take().unwrap_or_else(|| RoomSettingsMeta::new())
    }

    // string StageDetailsJSON = 18;


    pub fn get_StageDetailsJSON(&self) -> &str {
        match self._StageDetailsJSON {
            ::std::option::Option::Some(RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_StageDetailsJSON(&mut self) {
        self._StageDetailsJSON = ::std::option::Option::None;
    }

    pub fn has_StageDetailsJSON(&self) -> bool {
        match self._StageDetailsJSON {
            ::std::option::Option::Some(RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_StageDetailsJSON(&mut self, v: ::std::string::String) {
        self._StageDetailsJSON = ::std::option::Option::Some(RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(v))
    }

    // Mutable pointer to the field.
    pub fn mut_StageDetailsJSON(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(_)) = self._StageDetailsJSON {
        } else {
            self._StageDetailsJSON = ::std::option::Option::Some(RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(::std::string::String::new()));
        }
        match self._StageDetailsJSON {
            ::std::option::Option::Some(RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_StageDetailsJSON(&mut self) -> ::std::string::String {
        if self.has_StageDetailsJSON() {
            match self._StageDetailsJSON.take() {
                ::std::option::Option::Some(RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomHostDetails HostDetails = 19;


    pub fn get_HostDetails(&self) -> &RoomHostDetails {
        match self._HostDetails {
            ::std::option::Option::Some(RoomSettings_oneof__HostDetails::HostDetails(ref v)) => v,
            _ => <RoomHostDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_HostDetails(&mut self) {
        self._HostDetails = ::std::option::Option::None;
    }

    pub fn has_HostDetails(&self) -> bool {
        match self._HostDetails {
            ::std::option::Option::Some(RoomSettings_oneof__HostDetails::HostDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_HostDetails(&mut self, v: RoomHostDetails) {
        self._HostDetails = ::std::option::Option::Some(RoomSettings_oneof__HostDetails::HostDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_HostDetails(&mut self) -> &mut RoomHostDetails {
        if let ::std::option::Option::Some(RoomSettings_oneof__HostDetails::HostDetails(_)) = self._HostDetails {
        } else {
            self._HostDetails = ::std::option::Option::Some(RoomSettings_oneof__HostDetails::HostDetails(RoomHostDetails::new()));
        }
        match self._HostDetails {
            ::std::option::Option::Some(RoomSettings_oneof__HostDetails::HostDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_HostDetails(&mut self) -> RoomHostDetails {
        if self.has_HostDetails() {
            match self._HostDetails.take() {
                ::std::option::Option::Some(RoomSettings_oneof__HostDetails::HostDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomHostDetails::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageDetails StageDetails = 21;


    pub fn get_StageDetails(&self) -> &RoomStageDetails {
        self.StageDetails.as_ref().unwrap_or_else(|| <RoomStageDetails as ::protobuf::Message>::default_instance())
    }
    pub fn clear_StageDetails(&mut self) {
        self.StageDetails.clear();
    }

    pub fn has_StageDetails(&self) -> bool {
        self.StageDetails.is_some()
    }

    // Param is passed by value, moved
    pub fn set_StageDetails(&mut self, v: RoomStageDetails) {
        self.StageDetails = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_StageDetails(&mut self) -> &mut RoomStageDetails {
        if self.StageDetails.is_none() {
            self.StageDetails.set_default();
        }
        self.StageDetails.as_mut().unwrap()
    }

    // Take field
    pub fn take_StageDetails(&mut self) -> RoomStageDetails {
        self.StageDetails.take().unwrap_or_else(|| RoomStageDetails::new())
    }
}

impl ::protobuf::Message for RoomSettings {
    fn is_initialized(&self) -> bool {
        for v in &self.Meta {
            if !v.is_initialized() {
                return false;
            }
        };
        if let Some(RoomSettings_oneof__HostDetails::HostDetails(ref v)) = self._HostDetails {
            if !v.is_initialized() {
                return false;
            }
        }
        for v in &self.StageDetails {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._WelcomeMessage = ::std::option::Option::Some(RoomSettings_oneof__WelcomeMessage::WelcomeMessage(is.read_string()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.MaxPlayers = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.MaxChatHistory = tmp;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.RoomOwner)?;
                },
                5 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.RoomStatus, 5, &mut self.unknown_fields)?
                },
                6 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.RoomType, 6, &mut self.unknown_fields)?
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._Password = ::std::option::Option::Some(RoomSettings_oneof__Password::Password(is.read_string()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.OnlyOwnerCanPlay = tmp;
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.OnlyOwnerCanChat = tmp;
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AllowBlackMidi = tmp;
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AllowGuests = tmp;
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AllowBots = tmp;
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.OnlyMods = tmp;
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.NoChatAllowed = tmp;
                },
                15 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.NoPlayingAllowed = tmp;
                },
                16 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.FilterProfanity = tmp;
                },
                17 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.Meta)?;
                },
                18 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._StageDetailsJSON = ::std::option::Option::Some(RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(is.read_string()?));
                },
                19 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._HostDetails = ::std::option::Option::Some(RoomSettings_oneof__HostDetails::HostDetails(is.read_message()?));
                },
                21 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.StageDetails)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.MaxPlayers != 0 {
            my_size += ::protobuf::rt::value_size(2, self.MaxPlayers, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.MaxChatHistory != 0 {
            my_size += ::protobuf::rt::value_size(3, self.MaxChatHistory, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.RoomOwner.is_empty() {
            my_size += ::protobuf::rt::string_size(4, &self.RoomOwner);
        }
        if self.RoomStatus != RoomStatus::Public {
            my_size += ::protobuf::rt::enum_size(5, self.RoomStatus);
        }
        if self.RoomType != RoomType::Lobby {
            my_size += ::protobuf::rt::enum_size(6, self.RoomType);
        }
        if self.OnlyOwnerCanPlay != false {
            my_size += 2;
        }
        if self.OnlyOwnerCanChat != false {
            my_size += 2;
        }
        if self.AllowBlackMidi != false {
            my_size += 2;
        }
        if self.AllowGuests != false {
            my_size += 2;
        }
        if self.AllowBots != false {
            my_size += 2;
        }
        if self.OnlyMods != false {
            my_size += 2;
        }
        if self.NoChatAllowed != false {
            my_size += 2;
        }
        if self.NoPlayingAllowed != false {
            my_size += 2;
        }
        if self.FilterProfanity != false {
            my_size += 3;
        }
        if let Some(ref v) = self.Meta.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.StageDetails.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let ::std::option::Option::Some(ref v) = self._WelcomeMessage {
            match v {
                &RoomSettings_oneof__WelcomeMessage::WelcomeMessage(ref v) => {
                    my_size += ::protobuf::rt::string_size(1, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._Password {
            match v {
                &RoomSettings_oneof__Password::Password(ref v) => {
                    my_size += ::protobuf::rt::string_size(7, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._StageDetailsJSON {
            match v {
                &RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(ref v) => {
                    my_size += ::protobuf::rt::string_size(18, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._HostDetails {
            match v {
                &RoomSettings_oneof__HostDetails::HostDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.MaxPlayers != 0 {
            os.write_int32(2, self.MaxPlayers)?;
        }
        if self.MaxChatHistory != 0 {
            os.write_int32(3, self.MaxChatHistory)?;
        }
        if !self.RoomOwner.is_empty() {
            os.write_string(4, &self.RoomOwner)?;
        }
        if self.RoomStatus != RoomStatus::Public {
            os.write_enum(5, ::protobuf::ProtobufEnum::value(&self.RoomStatus))?;
        }
        if self.RoomType != RoomType::Lobby {
            os.write_enum(6, ::protobuf::ProtobufEnum::value(&self.RoomType))?;
        }
        if self.OnlyOwnerCanPlay != false {
            os.write_bool(8, self.OnlyOwnerCanPlay)?;
        }
        if self.OnlyOwnerCanChat != false {
            os.write_bool(9, self.OnlyOwnerCanChat)?;
        }
        if self.AllowBlackMidi != false {
            os.write_bool(10, self.AllowBlackMidi)?;
        }
        if self.AllowGuests != false {
            os.write_bool(11, self.AllowGuests)?;
        }
        if self.AllowBots != false {
            os.write_bool(12, self.AllowBots)?;
        }
        if self.OnlyMods != false {
            os.write_bool(13, self.OnlyMods)?;
        }
        if self.NoChatAllowed != false {
            os.write_bool(14, self.NoChatAllowed)?;
        }
        if self.NoPlayingAllowed != false {
            os.write_bool(15, self.NoPlayingAllowed)?;
        }
        if self.FilterProfanity != false {
            os.write_bool(16, self.FilterProfanity)?;
        }
        if let Some(ref v) = self.Meta.as_ref() {
            os.write_tag(17, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.StageDetails.as_ref() {
            os.write_tag(21, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let ::std::option::Option::Some(ref v) = self._WelcomeMessage {
            match v {
                &RoomSettings_oneof__WelcomeMessage::WelcomeMessage(ref v) => {
                    os.write_string(1, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._Password {
            match v {
                &RoomSettings_oneof__Password::Password(ref v) => {
                    os.write_string(7, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._StageDetailsJSON {
            match v {
                &RoomSettings_oneof__StageDetailsJSON::StageDetailsJSON(ref v) => {
                    os.write_string(18, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._HostDetails {
            match v {
                &RoomSettings_oneof__HostDetails::HostDetails(ref v) => {
                    os.write_tag(19, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomSettings {
        RoomSettings::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "WelcomeMessage",
                RoomSettings::has_WelcomeMessage,
                RoomSettings::get_WelcomeMessage,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "MaxPlayers",
                |m: &RoomSettings| { &m.MaxPlayers },
                |m: &mut RoomSettings| { &mut m.MaxPlayers },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "MaxChatHistory",
                |m: &RoomSettings| { &m.MaxChatHistory },
                |m: &mut RoomSettings| { &mut m.MaxChatHistory },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "RoomOwner",
                |m: &RoomSettings| { &m.RoomOwner },
                |m: &mut RoomSettings| { &mut m.RoomOwner },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<RoomStatus>>(
                "RoomStatus",
                |m: &RoomSettings| { &m.RoomStatus },
                |m: &mut RoomSettings| { &mut m.RoomStatus },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<RoomType>>(
                "RoomType",
                |m: &RoomSettings| { &m.RoomType },
                |m: &mut RoomSettings| { &mut m.RoomType },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "Password",
                RoomSettings::has_Password,
                RoomSettings::get_Password,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "OnlyOwnerCanPlay",
                |m: &RoomSettings| { &m.OnlyOwnerCanPlay },
                |m: &mut RoomSettings| { &mut m.OnlyOwnerCanPlay },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "OnlyOwnerCanChat",
                |m: &RoomSettings| { &m.OnlyOwnerCanChat },
                |m: &mut RoomSettings| { &mut m.OnlyOwnerCanChat },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AllowBlackMidi",
                |m: &RoomSettings| { &m.AllowBlackMidi },
                |m: &mut RoomSettings| { &mut m.AllowBlackMidi },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AllowGuests",
                |m: &RoomSettings| { &m.AllowGuests },
                |m: &mut RoomSettings| { &mut m.AllowGuests },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AllowBots",
                |m: &RoomSettings| { &m.AllowBots },
                |m: &mut RoomSettings| { &mut m.AllowBots },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "OnlyMods",
                |m: &RoomSettings| { &m.OnlyMods },
                |m: &mut RoomSettings| { &mut m.OnlyMods },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "NoChatAllowed",
                |m: &RoomSettings| { &m.NoChatAllowed },
                |m: &mut RoomSettings| { &mut m.NoChatAllowed },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "NoPlayingAllowed",
                |m: &RoomSettings| { &m.NoPlayingAllowed },
                |m: &mut RoomSettings| { &mut m.NoPlayingAllowed },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "FilterProfanity",
                |m: &RoomSettings| { &m.FilterProfanity },
                |m: &mut RoomSettings| { &mut m.FilterProfanity },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RoomSettingsMeta>>(
                "Meta",
                |m: &RoomSettings| { &m.Meta },
                |m: &mut RoomSettings| { &mut m.Meta },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "StageDetailsJSON",
                RoomSettings::has_StageDetailsJSON,
                RoomSettings::get_StageDetailsJSON,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomHostDetails>(
                "HostDetails",
                RoomSettings::has_HostDetails,
                RoomSettings::get_HostDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RoomStageDetails>>(
                "StageDetails",
                |m: &RoomSettings| { &m.StageDetails },
                |m: &mut RoomSettings| { &mut m.StageDetails },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomSettings>(
                "RoomSettings",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomSettings {
        static instance: ::protobuf::rt::LazyV2<RoomSettings> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomSettings::new)
    }
}

impl ::protobuf::Clear for RoomSettings {
    fn clear(&mut self) {
        self._WelcomeMessage = ::std::option::Option::None;
        self.MaxPlayers = 0;
        self.MaxChatHistory = 0;
        self.RoomOwner.clear();
        self.RoomStatus = RoomStatus::Public;
        self.RoomType = RoomType::Lobby;
        self._Password = ::std::option::Option::None;
        self.OnlyOwnerCanPlay = false;
        self.OnlyOwnerCanChat = false;
        self.AllowBlackMidi = false;
        self.AllowGuests = false;
        self.AllowBots = false;
        self.OnlyMods = false;
        self.NoChatAllowed = false;
        self.NoPlayingAllowed = false;
        self.FilterProfanity = false;
        self.Meta.clear();
        self._StageDetailsJSON = ::std::option::Option::None;
        self._HostDetails = ::std::option::Option::None;
        self.StageDetails.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomSettings {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomSettings {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomFullDetails {
    // message fields
    pub roomName: ::std::string::String,
    pub roomID: ::std::string::String,
    pub settings: ::protobuf::SingularPtrField<RoomSettings>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomFullDetails {
    fn default() -> &'a RoomFullDetails {
        <RoomFullDetails as ::protobuf::Message>::default_instance()
    }
}

impl RoomFullDetails {
    pub fn new() -> RoomFullDetails {
        ::std::default::Default::default()
    }

    // string roomName = 1;


    pub fn get_roomName(&self) -> &str {
        &self.roomName
    }
    pub fn clear_roomName(&mut self) {
        self.roomName.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomName(&mut self, v: ::std::string::String) {
        self.roomName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomName(&mut self) -> &mut ::std::string::String {
        &mut self.roomName
    }

    // Take field
    pub fn take_roomName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomName, ::std::string::String::new())
    }

    // string roomID = 2;


    pub fn get_roomID(&self) -> &str {
        &self.roomID
    }
    pub fn clear_roomID(&mut self) {
        self.roomID.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.roomID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        &mut self.roomID
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomID, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomSettings settings = 3;


    pub fn get_settings(&self) -> &RoomSettings {
        self.settings.as_ref().unwrap_or_else(|| <RoomSettings as ::protobuf::Message>::default_instance())
    }
    pub fn clear_settings(&mut self) {
        self.settings.clear();
    }

    pub fn has_settings(&self) -> bool {
        self.settings.is_some()
    }

    // Param is passed by value, moved
    pub fn set_settings(&mut self, v: RoomSettings) {
        self.settings = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_settings(&mut self) -> &mut RoomSettings {
        if self.settings.is_none() {
            self.settings.set_default();
        }
        self.settings.as_mut().unwrap()
    }

    // Take field
    pub fn take_settings(&mut self) -> RoomSettings {
        self.settings.take().unwrap_or_else(|| RoomSettings::new())
    }
}

impl ::protobuf::Message for RoomFullDetails {
    fn is_initialized(&self) -> bool {
        for v in &self.settings {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomName)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomID)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.settings)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.roomName.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.roomName);
        }
        if !self.roomID.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.roomID);
        }
        if let Some(ref v) = self.settings.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.roomName.is_empty() {
            os.write_string(1, &self.roomName)?;
        }
        if !self.roomID.is_empty() {
            os.write_string(2, &self.roomID)?;
        }
        if let Some(ref v) = self.settings.as_ref() {
            os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomFullDetails {
        RoomFullDetails::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomName",
                |m: &RoomFullDetails| { &m.roomName },
                |m: &mut RoomFullDetails| { &mut m.roomName },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomID",
                |m: &RoomFullDetails| { &m.roomID },
                |m: &mut RoomFullDetails| { &mut m.roomID },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RoomSettings>>(
                "settings",
                |m: &RoomFullDetails| { &m.settings },
                |m: &mut RoomFullDetails| { &mut m.settings },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomFullDetails>(
                "RoomFullDetails",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomFullDetails {
        static instance: ::protobuf::rt::LazyV2<RoomFullDetails> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomFullDetails::new)
    }
}

impl ::protobuf::Clear for RoomFullDetails {
    fn clear(&mut self) {
        self.roomName.clear();
        self.roomID.clear();
        self.settings.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomFullDetails {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomFullDetails {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomSettingsBasicDto {
    // message fields
    pub MaxPlayers: i32,
    pub MaxChatHistory: i32,
    pub RoomStatus: RoomStatus,
    pub OnlyOwnerCanPlay: bool,
    pub OnlyOwnerCanChat: bool,
    pub AllowBlackMidi: bool,
    pub AllowGuests: bool,
    pub AllowBots: bool,
    pub OnlyMods: bool,
    pub NoChatAllowed: bool,
    pub NoPlayingAllowed: bool,
    pub FilterProfanity: bool,
    pub StageDetails: ::protobuf::SingularPtrField<RoomStageDetails>,
    // message oneof groups
    pub _HostDetails: ::std::option::Option<RoomSettingsBasicDto_oneof__HostDetails>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomSettingsBasicDto {
    fn default() -> &'a RoomSettingsBasicDto {
        <RoomSettingsBasicDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomSettingsBasicDto_oneof__HostDetails {
    HostDetails(RoomHostDetails),
}

impl RoomSettingsBasicDto {
    pub fn new() -> RoomSettingsBasicDto {
        ::std::default::Default::default()
    }

    // int32 MaxPlayers = 1;


    pub fn get_MaxPlayers(&self) -> i32 {
        self.MaxPlayers
    }
    pub fn clear_MaxPlayers(&mut self) {
        self.MaxPlayers = 0;
    }

    // Param is passed by value, moved
    pub fn set_MaxPlayers(&mut self, v: i32) {
        self.MaxPlayers = v;
    }

    // int32 MaxChatHistory = 2;


    pub fn get_MaxChatHistory(&self) -> i32 {
        self.MaxChatHistory
    }
    pub fn clear_MaxChatHistory(&mut self) {
        self.MaxChatHistory = 0;
    }

    // Param is passed by value, moved
    pub fn set_MaxChatHistory(&mut self, v: i32) {
        self.MaxChatHistory = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStatus RoomStatus = 3;


    pub fn get_RoomStatus(&self) -> RoomStatus {
        self.RoomStatus
    }
    pub fn clear_RoomStatus(&mut self) {
        self.RoomStatus = RoomStatus::Public;
    }

    // Param is passed by value, moved
    pub fn set_RoomStatus(&mut self, v: RoomStatus) {
        self.RoomStatus = v;
    }

    // bool OnlyOwnerCanPlay = 4;


    pub fn get_OnlyOwnerCanPlay(&self) -> bool {
        self.OnlyOwnerCanPlay
    }
    pub fn clear_OnlyOwnerCanPlay(&mut self) {
        self.OnlyOwnerCanPlay = false;
    }

    // Param is passed by value, moved
    pub fn set_OnlyOwnerCanPlay(&mut self, v: bool) {
        self.OnlyOwnerCanPlay = v;
    }

    // bool OnlyOwnerCanChat = 5;


    pub fn get_OnlyOwnerCanChat(&self) -> bool {
        self.OnlyOwnerCanChat
    }
    pub fn clear_OnlyOwnerCanChat(&mut self) {
        self.OnlyOwnerCanChat = false;
    }

    // Param is passed by value, moved
    pub fn set_OnlyOwnerCanChat(&mut self, v: bool) {
        self.OnlyOwnerCanChat = v;
    }

    // bool AllowBlackMidi = 6;


    pub fn get_AllowBlackMidi(&self) -> bool {
        self.AllowBlackMidi
    }
    pub fn clear_AllowBlackMidi(&mut self) {
        self.AllowBlackMidi = false;
    }

    // Param is passed by value, moved
    pub fn set_AllowBlackMidi(&mut self, v: bool) {
        self.AllowBlackMidi = v;
    }

    // bool AllowGuests = 7;


    pub fn get_AllowGuests(&self) -> bool {
        self.AllowGuests
    }
    pub fn clear_AllowGuests(&mut self) {
        self.AllowGuests = false;
    }

    // Param is passed by value, moved
    pub fn set_AllowGuests(&mut self, v: bool) {
        self.AllowGuests = v;
    }

    // bool AllowBots = 8;


    pub fn get_AllowBots(&self) -> bool {
        self.AllowBots
    }
    pub fn clear_AllowBots(&mut self) {
        self.AllowBots = false;
    }

    // Param is passed by value, moved
    pub fn set_AllowBots(&mut self, v: bool) {
        self.AllowBots = v;
    }

    // bool OnlyMods = 9;


    pub fn get_OnlyMods(&self) -> bool {
        self.OnlyMods
    }
    pub fn clear_OnlyMods(&mut self) {
        self.OnlyMods = false;
    }

    // Param is passed by value, moved
    pub fn set_OnlyMods(&mut self, v: bool) {
        self.OnlyMods = v;
    }

    // bool NoChatAllowed = 10;


    pub fn get_NoChatAllowed(&self) -> bool {
        self.NoChatAllowed
    }
    pub fn clear_NoChatAllowed(&mut self) {
        self.NoChatAllowed = false;
    }

    // Param is passed by value, moved
    pub fn set_NoChatAllowed(&mut self, v: bool) {
        self.NoChatAllowed = v;
    }

    // bool NoPlayingAllowed = 11;


    pub fn get_NoPlayingAllowed(&self) -> bool {
        self.NoPlayingAllowed
    }
    pub fn clear_NoPlayingAllowed(&mut self) {
        self.NoPlayingAllowed = false;
    }

    // Param is passed by value, moved
    pub fn set_NoPlayingAllowed(&mut self, v: bool) {
        self.NoPlayingAllowed = v;
    }

    // bool FilterProfanity = 12;


    pub fn get_FilterProfanity(&self) -> bool {
        self.FilterProfanity
    }
    pub fn clear_FilterProfanity(&mut self) {
        self.FilterProfanity = false;
    }

    // Param is passed by value, moved
    pub fn set_FilterProfanity(&mut self, v: bool) {
        self.FilterProfanity = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomHostDetails HostDetails = 13;


    pub fn get_HostDetails(&self) -> &RoomHostDetails {
        match self._HostDetails {
            ::std::option::Option::Some(RoomSettingsBasicDto_oneof__HostDetails::HostDetails(ref v)) => v,
            _ => <RoomHostDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_HostDetails(&mut self) {
        self._HostDetails = ::std::option::Option::None;
    }

    pub fn has_HostDetails(&self) -> bool {
        match self._HostDetails {
            ::std::option::Option::Some(RoomSettingsBasicDto_oneof__HostDetails::HostDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_HostDetails(&mut self, v: RoomHostDetails) {
        self._HostDetails = ::std::option::Option::Some(RoomSettingsBasicDto_oneof__HostDetails::HostDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_HostDetails(&mut self) -> &mut RoomHostDetails {
        if let ::std::option::Option::Some(RoomSettingsBasicDto_oneof__HostDetails::HostDetails(_)) = self._HostDetails {
        } else {
            self._HostDetails = ::std::option::Option::Some(RoomSettingsBasicDto_oneof__HostDetails::HostDetails(RoomHostDetails::new()));
        }
        match self._HostDetails {
            ::std::option::Option::Some(RoomSettingsBasicDto_oneof__HostDetails::HostDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_HostDetails(&mut self) -> RoomHostDetails {
        if self.has_HostDetails() {
            match self._HostDetails.take() {
                ::std::option::Option::Some(RoomSettingsBasicDto_oneof__HostDetails::HostDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomHostDetails::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageDetails StageDetails = 14;


    pub fn get_StageDetails(&self) -> &RoomStageDetails {
        self.StageDetails.as_ref().unwrap_or_else(|| <RoomStageDetails as ::protobuf::Message>::default_instance())
    }
    pub fn clear_StageDetails(&mut self) {
        self.StageDetails.clear();
    }

    pub fn has_StageDetails(&self) -> bool {
        self.StageDetails.is_some()
    }

    // Param is passed by value, moved
    pub fn set_StageDetails(&mut self, v: RoomStageDetails) {
        self.StageDetails = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_StageDetails(&mut self) -> &mut RoomStageDetails {
        if self.StageDetails.is_none() {
            self.StageDetails.set_default();
        }
        self.StageDetails.as_mut().unwrap()
    }

    // Take field
    pub fn take_StageDetails(&mut self) -> RoomStageDetails {
        self.StageDetails.take().unwrap_or_else(|| RoomStageDetails::new())
    }
}

impl ::protobuf::Message for RoomSettingsBasicDto {
    fn is_initialized(&self) -> bool {
        if let Some(RoomSettingsBasicDto_oneof__HostDetails::HostDetails(ref v)) = self._HostDetails {
            if !v.is_initialized() {
                return false;
            }
        }
        for v in &self.StageDetails {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.MaxPlayers = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.MaxChatHistory = tmp;
                },
                3 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.RoomStatus, 3, &mut self.unknown_fields)?
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.OnlyOwnerCanPlay = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.OnlyOwnerCanChat = tmp;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AllowBlackMidi = tmp;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AllowGuests = tmp;
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AllowBots = tmp;
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.OnlyMods = tmp;
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.NoChatAllowed = tmp;
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.NoPlayingAllowed = tmp;
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.FilterProfanity = tmp;
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._HostDetails = ::std::option::Option::Some(RoomSettingsBasicDto_oneof__HostDetails::HostDetails(is.read_message()?));
                },
                14 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.StageDetails)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.MaxPlayers != 0 {
            my_size += ::protobuf::rt::value_size(1, self.MaxPlayers, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.MaxChatHistory != 0 {
            my_size += ::protobuf::rt::value_size(2, self.MaxChatHistory, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.RoomStatus != RoomStatus::Public {
            my_size += ::protobuf::rt::enum_size(3, self.RoomStatus);
        }
        if self.OnlyOwnerCanPlay != false {
            my_size += 2;
        }
        if self.OnlyOwnerCanChat != false {
            my_size += 2;
        }
        if self.AllowBlackMidi != false {
            my_size += 2;
        }
        if self.AllowGuests != false {
            my_size += 2;
        }
        if self.AllowBots != false {
            my_size += 2;
        }
        if self.OnlyMods != false {
            my_size += 2;
        }
        if self.NoChatAllowed != false {
            my_size += 2;
        }
        if self.NoPlayingAllowed != false {
            my_size += 2;
        }
        if self.FilterProfanity != false {
            my_size += 2;
        }
        if let Some(ref v) = self.StageDetails.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let ::std::option::Option::Some(ref v) = self._HostDetails {
            match v {
                &RoomSettingsBasicDto_oneof__HostDetails::HostDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.MaxPlayers != 0 {
            os.write_int32(1, self.MaxPlayers)?;
        }
        if self.MaxChatHistory != 0 {
            os.write_int32(2, self.MaxChatHistory)?;
        }
        if self.RoomStatus != RoomStatus::Public {
            os.write_enum(3, ::protobuf::ProtobufEnum::value(&self.RoomStatus))?;
        }
        if self.OnlyOwnerCanPlay != false {
            os.write_bool(4, self.OnlyOwnerCanPlay)?;
        }
        if self.OnlyOwnerCanChat != false {
            os.write_bool(5, self.OnlyOwnerCanChat)?;
        }
        if self.AllowBlackMidi != false {
            os.write_bool(6, self.AllowBlackMidi)?;
        }
        if self.AllowGuests != false {
            os.write_bool(7, self.AllowGuests)?;
        }
        if self.AllowBots != false {
            os.write_bool(8, self.AllowBots)?;
        }
        if self.OnlyMods != false {
            os.write_bool(9, self.OnlyMods)?;
        }
        if self.NoChatAllowed != false {
            os.write_bool(10, self.NoChatAllowed)?;
        }
        if self.NoPlayingAllowed != false {
            os.write_bool(11, self.NoPlayingAllowed)?;
        }
        if self.FilterProfanity != false {
            os.write_bool(12, self.FilterProfanity)?;
        }
        if let Some(ref v) = self.StageDetails.as_ref() {
            os.write_tag(14, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let ::std::option::Option::Some(ref v) = self._HostDetails {
            match v {
                &RoomSettingsBasicDto_oneof__HostDetails::HostDetails(ref v) => {
                    os.write_tag(13, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomSettingsBasicDto {
        RoomSettingsBasicDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "MaxPlayers",
                |m: &RoomSettingsBasicDto| { &m.MaxPlayers },
                |m: &mut RoomSettingsBasicDto| { &mut m.MaxPlayers },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "MaxChatHistory",
                |m: &RoomSettingsBasicDto| { &m.MaxChatHistory },
                |m: &mut RoomSettingsBasicDto| { &mut m.MaxChatHistory },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<RoomStatus>>(
                "RoomStatus",
                |m: &RoomSettingsBasicDto| { &m.RoomStatus },
                |m: &mut RoomSettingsBasicDto| { &mut m.RoomStatus },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "OnlyOwnerCanPlay",
                |m: &RoomSettingsBasicDto| { &m.OnlyOwnerCanPlay },
                |m: &mut RoomSettingsBasicDto| { &mut m.OnlyOwnerCanPlay },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "OnlyOwnerCanChat",
                |m: &RoomSettingsBasicDto| { &m.OnlyOwnerCanChat },
                |m: &mut RoomSettingsBasicDto| { &mut m.OnlyOwnerCanChat },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AllowBlackMidi",
                |m: &RoomSettingsBasicDto| { &m.AllowBlackMidi },
                |m: &mut RoomSettingsBasicDto| { &mut m.AllowBlackMidi },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AllowGuests",
                |m: &RoomSettingsBasicDto| { &m.AllowGuests },
                |m: &mut RoomSettingsBasicDto| { &mut m.AllowGuests },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AllowBots",
                |m: &RoomSettingsBasicDto| { &m.AllowBots },
                |m: &mut RoomSettingsBasicDto| { &mut m.AllowBots },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "OnlyMods",
                |m: &RoomSettingsBasicDto| { &m.OnlyMods },
                |m: &mut RoomSettingsBasicDto| { &mut m.OnlyMods },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "NoChatAllowed",
                |m: &RoomSettingsBasicDto| { &m.NoChatAllowed },
                |m: &mut RoomSettingsBasicDto| { &mut m.NoChatAllowed },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "NoPlayingAllowed",
                |m: &RoomSettingsBasicDto| { &m.NoPlayingAllowed },
                |m: &mut RoomSettingsBasicDto| { &mut m.NoPlayingAllowed },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "FilterProfanity",
                |m: &RoomSettingsBasicDto| { &m.FilterProfanity },
                |m: &mut RoomSettingsBasicDto| { &mut m.FilterProfanity },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomHostDetails>(
                "HostDetails",
                RoomSettingsBasicDto::has_HostDetails,
                RoomSettingsBasicDto::get_HostDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RoomStageDetails>>(
                "StageDetails",
                |m: &RoomSettingsBasicDto| { &m.StageDetails },
                |m: &mut RoomSettingsBasicDto| { &mut m.StageDetails },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomSettingsBasicDto>(
                "RoomSettingsBasicDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomSettingsBasicDto {
        static instance: ::protobuf::rt::LazyV2<RoomSettingsBasicDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomSettingsBasicDto::new)
    }
}

impl ::protobuf::Clear for RoomSettingsBasicDto {
    fn clear(&mut self) {
        self.MaxPlayers = 0;
        self.MaxChatHistory = 0;
        self.RoomStatus = RoomStatus::Public;
        self.OnlyOwnerCanPlay = false;
        self.OnlyOwnerCanChat = false;
        self.AllowBlackMidi = false;
        self.AllowGuests = false;
        self.AllowBots = false;
        self.OnlyMods = false;
        self.NoChatAllowed = false;
        self.NoPlayingAllowed = false;
        self.FilterProfanity = false;
        self._HostDetails = ::std::option::Option::None;
        self.StageDetails.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomSettingsBasicDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomSettingsBasicDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomProfileBasicInfoDto {
    // message fields
    pub roomName: ::std::string::String,
    pub roomID: ::std::string::String,
    pub roomOwner: ::std::string::String,
    pub settings: ::protobuf::SingularPtrField<RoomSettingsBasicDto>,
    pub roomStage: RoomStages,
    pub StageDetails: ::protobuf::SingularPtrField<RoomStageDetails>,
    // message oneof groups
    pub _stageDetailsJSON: ::std::option::Option<RoomProfileBasicInfoDto_oneof__stageDetailsJSON>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomProfileBasicInfoDto {
    fn default() -> &'a RoomProfileBasicInfoDto {
        <RoomProfileBasicInfoDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomProfileBasicInfoDto_oneof__stageDetailsJSON {
    stageDetailsJSON(::std::string::String),
}

impl RoomProfileBasicInfoDto {
    pub fn new() -> RoomProfileBasicInfoDto {
        ::std::default::Default::default()
    }

    // string roomName = 1;


    pub fn get_roomName(&self) -> &str {
        &self.roomName
    }
    pub fn clear_roomName(&mut self) {
        self.roomName.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomName(&mut self, v: ::std::string::String) {
        self.roomName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomName(&mut self) -> &mut ::std::string::String {
        &mut self.roomName
    }

    // Take field
    pub fn take_roomName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomName, ::std::string::String::new())
    }

    // string roomID = 2;


    pub fn get_roomID(&self) -> &str {
        &self.roomID
    }
    pub fn clear_roomID(&mut self) {
        self.roomID.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.roomID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        &mut self.roomID
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomID, ::std::string::String::new())
    }

    // string roomOwner = 3;


    pub fn get_roomOwner(&self) -> &str {
        &self.roomOwner
    }
    pub fn clear_roomOwner(&mut self) {
        self.roomOwner.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomOwner(&mut self, v: ::std::string::String) {
        self.roomOwner = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomOwner(&mut self) -> &mut ::std::string::String {
        &mut self.roomOwner
    }

    // Take field
    pub fn take_roomOwner(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomOwner, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomSettingsBasicDto settings = 4;


    pub fn get_settings(&self) -> &RoomSettingsBasicDto {
        self.settings.as_ref().unwrap_or_else(|| <RoomSettingsBasicDto as ::protobuf::Message>::default_instance())
    }
    pub fn clear_settings(&mut self) {
        self.settings.clear();
    }

    pub fn has_settings(&self) -> bool {
        self.settings.is_some()
    }

    // Param is passed by value, moved
    pub fn set_settings(&mut self, v: RoomSettingsBasicDto) {
        self.settings = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_settings(&mut self) -> &mut RoomSettingsBasicDto {
        if self.settings.is_none() {
            self.settings.set_default();
        }
        self.settings.as_mut().unwrap()
    }

    // Take field
    pub fn take_settings(&mut self) -> RoomSettingsBasicDto {
        self.settings.take().unwrap_or_else(|| RoomSettingsBasicDto::new())
    }

    // string stageDetailsJSON = 5;


    pub fn get_stageDetailsJSON(&self) -> &str {
        match self._stageDetailsJSON {
            ::std::option::Option::Some(RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_stageDetailsJSON(&mut self) {
        self._stageDetailsJSON = ::std::option::Option::None;
    }

    pub fn has_stageDetailsJSON(&self) -> bool {
        match self._stageDetailsJSON {
            ::std::option::Option::Some(RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_stageDetailsJSON(&mut self, v: ::std::string::String) {
        self._stageDetailsJSON = ::std::option::Option::Some(RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(v))
    }

    // Mutable pointer to the field.
    pub fn mut_stageDetailsJSON(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(_)) = self._stageDetailsJSON {
        } else {
            self._stageDetailsJSON = ::std::option::Option::Some(RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(::std::string::String::new()));
        }
        match self._stageDetailsJSON {
            ::std::option::Option::Some(RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_stageDetailsJSON(&mut self) -> ::std::string::String {
        if self.has_stageDetailsJSON() {
            match self._stageDetailsJSON.take() {
                ::std::option::Option::Some(RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStages roomStage = 6;


    pub fn get_roomStage(&self) -> RoomStages {
        self.roomStage
    }
    pub fn clear_roomStage(&mut self) {
        self.roomStage = RoomStages::UNKNOWN;
    }

    // Param is passed by value, moved
    pub fn set_roomStage(&mut self, v: RoomStages) {
        self.roomStage = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageDetails StageDetails = 8;


    pub fn get_StageDetails(&self) -> &RoomStageDetails {
        self.StageDetails.as_ref().unwrap_or_else(|| <RoomStageDetails as ::protobuf::Message>::default_instance())
    }
    pub fn clear_StageDetails(&mut self) {
        self.StageDetails.clear();
    }

    pub fn has_StageDetails(&self) -> bool {
        self.StageDetails.is_some()
    }

    // Param is passed by value, moved
    pub fn set_StageDetails(&mut self, v: RoomStageDetails) {
        self.StageDetails = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_StageDetails(&mut self) -> &mut RoomStageDetails {
        if self.StageDetails.is_none() {
            self.StageDetails.set_default();
        }
        self.StageDetails.as_mut().unwrap()
    }

    // Take field
    pub fn take_StageDetails(&mut self) -> RoomStageDetails {
        self.StageDetails.take().unwrap_or_else(|| RoomStageDetails::new())
    }
}

impl ::protobuf::Message for RoomProfileBasicInfoDto {
    fn is_initialized(&self) -> bool {
        for v in &self.settings {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.StageDetails {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomName)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomID)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomOwner)?;
                },
                4 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.settings)?;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._stageDetailsJSON = ::std::option::Option::Some(RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(is.read_string()?));
                },
                6 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.roomStage, 6, &mut self.unknown_fields)?
                },
                8 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.StageDetails)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.roomName.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.roomName);
        }
        if !self.roomID.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.roomID);
        }
        if !self.roomOwner.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.roomOwner);
        }
        if let Some(ref v) = self.settings.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.roomStage != RoomStages::UNKNOWN {
            my_size += ::protobuf::rt::enum_size(6, self.roomStage);
        }
        if let Some(ref v) = self.StageDetails.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let ::std::option::Option::Some(ref v) = self._stageDetailsJSON {
            match v {
                &RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(ref v) => {
                    my_size += ::protobuf::rt::string_size(5, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.roomName.is_empty() {
            os.write_string(1, &self.roomName)?;
        }
        if !self.roomID.is_empty() {
            os.write_string(2, &self.roomID)?;
        }
        if !self.roomOwner.is_empty() {
            os.write_string(3, &self.roomOwner)?;
        }
        if let Some(ref v) = self.settings.as_ref() {
            os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.roomStage != RoomStages::UNKNOWN {
            os.write_enum(6, ::protobuf::ProtobufEnum::value(&self.roomStage))?;
        }
        if let Some(ref v) = self.StageDetails.as_ref() {
            os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let ::std::option::Option::Some(ref v) = self._stageDetailsJSON {
            match v {
                &RoomProfileBasicInfoDto_oneof__stageDetailsJSON::stageDetailsJSON(ref v) => {
                    os.write_string(5, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomProfileBasicInfoDto {
        RoomProfileBasicInfoDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomName",
                |m: &RoomProfileBasicInfoDto| { &m.roomName },
                |m: &mut RoomProfileBasicInfoDto| { &mut m.roomName },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomID",
                |m: &RoomProfileBasicInfoDto| { &m.roomID },
                |m: &mut RoomProfileBasicInfoDto| { &mut m.roomID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomOwner",
                |m: &RoomProfileBasicInfoDto| { &m.roomOwner },
                |m: &mut RoomProfileBasicInfoDto| { &mut m.roomOwner },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RoomSettingsBasicDto>>(
                "settings",
                |m: &RoomProfileBasicInfoDto| { &m.settings },
                |m: &mut RoomProfileBasicInfoDto| { &mut m.settings },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "stageDetailsJSON",
                RoomProfileBasicInfoDto::has_stageDetailsJSON,
                RoomProfileBasicInfoDto::get_stageDetailsJSON,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<RoomStages>>(
                "roomStage",
                |m: &RoomProfileBasicInfoDto| { &m.roomStage },
                |m: &mut RoomProfileBasicInfoDto| { &mut m.roomStage },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RoomStageDetails>>(
                "StageDetails",
                |m: &RoomProfileBasicInfoDto| { &m.StageDetails },
                |m: &mut RoomProfileBasicInfoDto| { &mut m.StageDetails },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomProfileBasicInfoDto>(
                "RoomProfileBasicInfoDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomProfileBasicInfoDto {
        static instance: ::protobuf::rt::LazyV2<RoomProfileBasicInfoDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomProfileBasicInfoDto::new)
    }
}

impl ::protobuf::Clear for RoomProfileBasicInfoDto {
    fn clear(&mut self) {
        self.roomName.clear();
        self.roomID.clear();
        self.roomOwner.clear();
        self.settings.clear();
        self._stageDetailsJSON = ::std::option::Option::None;
        self.roomStage = RoomStages::UNKNOWN;
        self.StageDetails.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomProfileBasicInfoDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomProfileBasicInfoDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum RoomStatus {
    Public = 0,
    Private = 1,
}

impl ::protobuf::ProtobufEnum for RoomStatus {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<RoomStatus> {
        match value {
            0 => ::std::option::Option::Some(RoomStatus::Public),
            1 => ::std::option::Option::Some(RoomStatus::Private),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [RoomStatus] = &[
            RoomStatus::Public,
            RoomStatus::Private,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<RoomStatus>("RoomStatus", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for RoomStatus {
}

impl ::std::default::Default for RoomStatus {
    fn default() -> Self {
        RoomStatus::Public
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomStatus {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum RoomType {
    Lobby = 0,
    LobbyTurnBased = 1,
    LobbyOrchestra = 2,
    LobbyPRO = 3,
    WorldLobby = 4,
    World = 5,
    Normal = 6,
    TurnBased = 7,
    Game = 8,
    LobbyGuest = 9,
    SoloGame = 10,
    Orchestra = 11,
    SoloTraining = 12,
    Unknown = 99,
}

impl ::protobuf::ProtobufEnum for RoomType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<RoomType> {
        match value {
            0 => ::std::option::Option::Some(RoomType::Lobby),
            1 => ::std::option::Option::Some(RoomType::LobbyTurnBased),
            2 => ::std::option::Option::Some(RoomType::LobbyOrchestra),
            3 => ::std::option::Option::Some(RoomType::LobbyPRO),
            4 => ::std::option::Option::Some(RoomType::WorldLobby),
            5 => ::std::option::Option::Some(RoomType::World),
            6 => ::std::option::Option::Some(RoomType::Normal),
            7 => ::std::option::Option::Some(RoomType::TurnBased),
            8 => ::std::option::Option::Some(RoomType::Game),
            9 => ::std::option::Option::Some(RoomType::LobbyGuest),
            10 => ::std::option::Option::Some(RoomType::SoloGame),
            11 => ::std::option::Option::Some(RoomType::Orchestra),
            12 => ::std::option::Option::Some(RoomType::SoloTraining),
            99 => ::std::option::Option::Some(RoomType::Unknown),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [RoomType] = &[
            RoomType::Lobby,
            RoomType::LobbyTurnBased,
            RoomType::LobbyOrchestra,
            RoomType::LobbyPRO,
            RoomType::WorldLobby,
            RoomType::World,
            RoomType::Normal,
            RoomType::TurnBased,
            RoomType::Game,
            RoomType::LobbyGuest,
            RoomType::SoloGame,
            RoomType::Orchestra,
            RoomType::SoloTraining,
            RoomType::Unknown,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<RoomType>("RoomType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for RoomType {
}

impl ::std::default::Default for RoomType {
    fn default() -> Self {
        RoomType::Lobby
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum RoomStages {
    UNKNOWN = 0,
    THE_VOID = 1,
    CLOUD_PALACE = 2,
    KINGS_HALL = 3,
    GRASSLAND = 4,
    TORII_GATE = 5,
    MUSIC_STUDIO = 6,
    ARENA = 7,
    FOREST = 8,
}

impl ::protobuf::ProtobufEnum for RoomStages {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<RoomStages> {
        match value {
            0 => ::std::option::Option::Some(RoomStages::UNKNOWN),
            1 => ::std::option::Option::Some(RoomStages::THE_VOID),
            2 => ::std::option::Option::Some(RoomStages::CLOUD_PALACE),
            3 => ::std::option::Option::Some(RoomStages::KINGS_HALL),
            4 => ::std::option::Option::Some(RoomStages::GRASSLAND),
            5 => ::std::option::Option::Some(RoomStages::TORII_GATE),
            6 => ::std::option::Option::Some(RoomStages::MUSIC_STUDIO),
            7 => ::std::option::Option::Some(RoomStages::ARENA),
            8 => ::std::option::Option::Some(RoomStages::FOREST),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [RoomStages] = &[
            RoomStages::UNKNOWN,
            RoomStages::THE_VOID,
            RoomStages::CLOUD_PALACE,
            RoomStages::KINGS_HALL,
            RoomStages::GRASSLAND,
            RoomStages::TORII_GATE,
            RoomStages::MUSIC_STUDIO,
            RoomStages::ARENA,
            RoomStages::FOREST,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<RoomStages>("RoomStages", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for RoomStages {
}

impl ::std::default::Default for RoomStages {
    fn default() -> Self {
        RoomStages::UNKNOWN
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomStages {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x15room-renditions.proto\x12(PianoRhythm.Serialization.RoomRenditions\
    \"\xba\x01\n\x0fRoomHostDetails\x12$\n\rContinentCode\x18\x01\x20\x01(\t\
    R\rContinentCode\x12%\n\x0bCountryCode\x18\x02\x20\x01(\tH\0R\x0bCountry\
    Code\x88\x01\x01\x123\n\x12NoteBufferInterval\x18\x03\x20\x01(\rH\x01R\
    \x12NoteBufferInterval\x88\x01\x01B\x0e\n\x0c_CountryCodeB\x15\n\x13_Not\
    eBufferInterval\"\xf7\x03\n\x16RoomStageVisualEffects\x12\x17\n\x04rain\
    \x18\x01\x20\x01(\x08H\0R\x04rain\x88\x01\x01\x12\x19\n\x05grass\x18\x02\
    \x20\x01(\x08H\x01R\x05grass\x88\x01\x01\x12:\n\x16postProcess_blackWhit\
    e\x18\x03\x20\x01(\x08H\x02R\x15postProcessBlackWhite\x88\x01\x01\x12:\n\
    \x16postProcess_downSample\x18\x04\x20\x01(\x08H\x03R\x15postProcessDown\
    Sample\x88\x01\x01\x124\n\x13postProcess_toneMap\x18\x05\x20\x01(\x08H\
    \x04R\x12postProcessToneMap\x88\x01\x01\x122\n\x12postProcess_emboss\x18\
    \x06\x20\x01(\x08H\x05R\x11postProcessEmboss\x88\x01\x01\x126\n\x14postP\
    rocess_vignette\x18\x07\x20\x01(\x08H\x06R\x13postProcessVignette\x88\
    \x01\x01B\x07\n\x05_rainB\x08\n\x06_grassB\x19\n\x17_postProcess_blackWh\
    iteB\x19\n\x17_postProcess_downSampleB\x16\n\x14_postProcess_toneMapB\
    \x15\n\x13_postProcess_embossB\x17\n\x15_postProcess_vignette\"x\n\x14Ro\
    omStageAudioEffect\x12\x16\n\x06volume\x18\x01\x20\x01(\x02R\x06volume\
    \x12\x12\n\x04loop\x18\x02\x20\x01(\x08R\x04loop\x12\"\n\x0cplaybackRate\
    \x18\x03\x20\x01(\x02R\x0cplaybackRate\x12\x10\n\x03pan\x18\x04\x20\x01(\
    \x02R\x03pan\"\xc0\x02\n\x15RoomStageAudioEffects\x12W\n\x04rain\x18\x01\
    \x20\x01(\x0b2>.PianoRhythm.Serialization.RoomRenditions.RoomStageAudioE\
    ffectH\0R\x04rain\x88\x01\x01\x12W\n\x04wind\x18\x02\x20\x01(\x0b2>.Pian\
    oRhythm.Serialization.RoomRenditions.RoomStageAudioEffectH\x01R\x04wind\
    \x88\x01\x01\x12Y\n\x05birds\x18\x03\x20\x01(\x0b2>.PianoRhythm.Serializ\
    ation.RoomRenditions.RoomStageAudioEffectH\x02R\x05birds\x88\x01\x01B\
    \x07\n\x05_rainB\x07\n\x05_windB\x08\n\x06_birds\"\x9f\x02\n\x10RoomStag\
    eDetails\x12J\n\x05stage\x18\x01\x20\x01(\x0e24.PianoRhythm.Serializatio\
    n.RoomRenditions.RoomStagesR\x05stage\x12Z\n\x07effects\x18\x02\x20\x01(\
    \<EMAIL>\
    \x07effects\x12c\n\x0caudioEffects\x18\x03\x20\x01(\x0b2?.PianoRhythm.Se\
    rialization.RoomRenditions.RoomStageAudioEffectsR\x0caudioEffects\"j\n\
    \x10RoomSettingsMeta\x12\x1e\n\nAutoRemove\x18\x01\x20\x01(\x08R\nAutoRe\
    move\x12\x16\n\x06Unique\x18\x02\x20\x01(\x08R\x06Unique\x12\x1e\n\nPers\
    istent\x18\x03\x20\x01(\x08R\nPersistent\"\xc6\x04\n\x0cBasicRoomDto\x12\
    \x1a\n\x08roomName\x18\x01\x20\x01(\tR\x08roomName\x12\x16\n\x06roomID\
    \x18\x02\x20\x01(\tR\x06roomID\x12\x1a\n\x08roomType\x18\x03\x20\x01(\tR\
    \x08roomType\x12\x1c\n\tuserCount\x18\x04\x20\x01(\x05R\tuserCount\x12\
    \x1a\n\x08maxUsers\x18\x05\x20\x01(\x05R\x08maxUsers\x120\n\x13isPasswor\
    dProtected\x18\x06\x20\x01(\x08R\x13isPasswordProtected\x12`\n\x0bhostDe\
    tails\x18\x07\x20\x01(\x0b29.PianoRhythm.Serialization.RoomRenditions.Ro\
    omHostDetailsH\0R\x0bhostDetails\x88\x01\x01\x12\x1c\n\troomOwner\x18\
    \x08\x20\x01(\tR\troomOwner\x12R\n\troomStage\x18\t\x20\x01(\x0e24.Piano\
    Rhythm.Serialization.RoomRenditions.RoomStagesR\troomStage\x12c\n\x0cSta\
    geDetails\x18\n\x20\x01(\x0b2:.PianoRhythm.Serialization.RoomRenditions.\
    RoomStageDetailsH\x01R\x0cStageDetails\x88\x01\x01\x12\x20\n\x0bcreatedD\
    ate\x18\x0b\x20\x01(\tR\x0bcreatedDateB\x0e\n\x0c_hostDetailsB\x0f\n\r_S\
    tageDetails\"q\n\x07RoomDto\x12\x1a\n\x08roomName\x18\x01\x20\x01(\tR\
    \x08roomName\x12\x16\n\x06roomID\x18\x02\x20\x01(\tR\x06roomID\x12\x1c\n\
    \troomOwner\x18\x03\x20\x01(\tR\troomOwner\x12\x14\n\x05users\x18\x04\
    \x20\x03(\tR\x05users\"\xc8\x08\n\x0cRoomSettings\x12+\n\x0eWelcomeMessa\
    ge\x18\x01\x20\x01(\tH\0R\x0eWelcomeMessage\x88\x01\x01\x12\x1e\n\nMaxPl\
    ayers\x18\x02\x20\x01(\x05R\nMaxPlayers\x12&\n\x0eMaxChatHistory\x18\x03\
    \x20\x01(\x05R\x0eMaxChatHistory\x12\x1c\n\tRoomOwner\x18\x04\x20\x01(\t\
    R\tRoomOwner\x12T\n\nRoomStatus\x18\x05\x20\x01(\x0e24.PianoRhythm.Seria\
    lization.RoomRenditions.RoomStatusR\nRoomStatus\x12N\n\x08RoomType\x18\
    \x06\x20\x01(\x0e22.PianoRhythm.Serialization.RoomRenditions.RoomTypeR\
    \x08RoomType\x12\x1f\n\x08Password\x18\x07\x20\x01(\tH\x01R\x08Password\
    \x88\x01\x01\x12*\n\x10OnlyOwnerCanPlay\x18\x08\x20\x01(\x08R\x10OnlyOwn\
    erCanPlay\x12*\n\x10OnlyOwnerCanChat\x18\t\x20\x01(\x08R\x10OnlyOwnerCan\
    Chat\x12&\n\x0eAllowBlackMidi\x18\n\x20\x01(\x08R\x0eAllowBlackMidi\x12\
    \x20\n\x0bAllowGuests\x18\x0b\x20\x01(\x08R\x0bAllowGuests\x12\x1c\n\tAl\
    lowBots\x18\x0c\x20\x01(\x08R\tAllowBots\x12\x1a\n\x08OnlyMods\x18\r\x20\
    \x01(\x08R\x08OnlyMods\x12$\n\rNoChatAllowed\x18\x0e\x20\x01(\x08R\rNoCh\
    atAllowed\x12*\n\x10NoPlayingAllowed\x18\x0f\x20\x01(\x08R\x10NoPlayingA\
    llowed\x12(\n\x0fFilterProfanity\x18\x10\x20\x01(\x08R\x0fFilterProfanit\
    y\x12N\n\x04Meta\x18\x11\x20\x01(\x0b2:.PianoRhythm.Serialization.RoomRe\
    nditions.RoomSettingsMetaR\x04Meta\x12/\n\x10StageDetailsJSON\x18\x12\
    \x20\x01(\tH\x02R\x10StageDetailsJSON\x88\x01\x01\x12`\n\x0bHostDetails\
    \x18\x13\x20\x01(\x0b29.PianoRhythm.Serialization.RoomRenditions.RoomHos\
    tDetailsH\x03R\x0bHostDetails\x88\x01\x01\x12^\n\x0cStageDetails\x18\x15\
    \x20\x01(\x0b2:.PianoRhythm.Serialization.RoomRenditions.RoomStageDetail\
    sR\x0cStageDetailsB\x11\n\x0f_WelcomeMessageB\x0b\n\t_PasswordB\x13\n\
    \x11_StageDetailsJSONB\x0e\n\x0c_HostDetails\"\x99\x01\n\x0fRoomFullDeta\
    ils\x12\x1a\n\x08roomName\x18\x01\x20\x01(\tR\x08roomName\x12\x16\n\x06r\
    oomID\x18\x02\x20\x01(\tR\x06roomID\x12R\n\x08settings\x18\x03\x20\x01(\
    \x0b26.PianoRhythm.Serialization.RoomRenditions.RoomSettingsR\x08setting\
    s\"\xde\x05\n\x14RoomSettingsBasicDto\x12\x1e\n\nMaxPlayers\x18\x01\x20\
    \x01(\x05R\nMaxPlayers\x12&\n\x0eMaxChatHistory\x18\x02\x20\x01(\x05R\
    \x0eMaxChatHistory\x12T\n\nRoomStatus\x18\x03\x20\x01(\x0e24.PianoRhythm\
    .Serialization.RoomRenditions.RoomStatusR\nRoomStatus\x12*\n\x10OnlyOwne\
    rCanPlay\x18\x04\x20\x01(\x08R\x10OnlyOwnerCanPlay\x12*\n\x10OnlyOwnerCa\
    nChat\x18\x05\x20\x01(\x08R\x10OnlyOwnerCanChat\x12&\n\x0eAllowBlackMidi\
    \x18\x06\x20\x01(\x08R\x0eAllowBlackMidi\x12\x20\n\x0bAllowGuests\x18\
    \x07\x20\x01(\x08R\x0bAllowGuests\x12\x1c\n\tAllowBots\x18\x08\x20\x01(\
    \x08R\tAllowBots\x12\x1a\n\x08OnlyMods\x18\t\x20\x01(\x08R\x08OnlyMods\
    \x12$\n\rNoChatAllowed\x18\n\x20\x01(\x08R\rNoChatAllowed\x12*\n\x10NoPl\
    ayingAllowed\x18\x0b\x20\x01(\x08R\x10NoPlayingAllowed\x12(\n\x0fFilterP\
    rofanity\x18\x0c\x20\x01(\x08R\x0fFilterProfanity\x12`\n\x0bHostDetails\
    \x18\r\x20\x01(\x0b29.PianoRhythm.Serialization.RoomRenditions.RoomHostD\
    etailsH\0R\x0bHostDetails\x88\x01\x01\x12^\n\x0cStageDetails\x18\x0e\x20\
    \x01(\x0b2:.PianoRhythm.Serialization.RoomRenditions.RoomStageDetailsR\
    \x0cStageDetailsB\x0e\n\x0c_HostDetails\"\xc1\x03\n\x17RoomProfileBasicI\
    nfoDto\x12\x1a\n\x08roomName\x18\x01\x20\x01(\tR\x08roomName\x12\x16\n\
    \x06roomID\x18\x02\x20\x01(\tR\x06roomID\x12\x1c\n\troomOwner\x18\x03\
    \x20\x01(\tR\troomOwner\x12Z\n\x08settings\x18\x04\x20\x01(\x0b2>.PianoR\
    hythm.Serialization.RoomRenditions.RoomSettingsBasicDtoR\x08settings\x12\
    /\n\x10stageDetailsJSON\x18\x05\x20\x01(\tH\0R\x10stageDetailsJSON\x88\
    \x01\x01\x12R\n\troomStage\x18\x06\x20\x01(\x0e24.PianoRhythm.Serializat\
    ion.RoomRenditions.RoomStagesR\troomStage\x12^\n\x0cStageDetails\x18\x08\
    \x20\x01(\x0b2:.PianoRhythm.Serialization.RoomRenditions.RoomStageDetail\
    sR\x0cStageDetailsB\x13\n\x11_stageDetailsJSON*%\n\nRoomStatus\x12\n\n\
    \x06Public\x10\0\x12\x0b\n\x07Private\x10\x01*\xd7\x01\n\x08RoomType\x12\
    \t\n\x05Lobby\x10\0\x12\x12\n\x0eLobbyTurnBased\x10\x01\x12\x12\n\x0eLob\
    byOrchestra\x10\x02\x12\x0c\n\x08LobbyPRO\x10\x03\x12\x0e\n\nWorldLobby\
    \x10\x04\x12\t\n\x05World\x10\x05\x12\n\n\x06Normal\x10\x06\x12\r\n\tTur\
    nBased\x10\x07\x12\x08\n\x04Game\x10\x08\x12\x0e\n\nLobbyGuest\x10\t\x12\
    \x0c\n\x08SoloGame\x10\n\x12\r\n\tOrchestra\x10\x0b\x12\x10\n\x0cSoloTra\
    ining\x10\x0c\x12\x0b\n\x07Unknown\x10c*\x91\x01\n\nRoomStages\x12\x0b\n\
    \x07UNKNOWN\x10\0\x12\x0c\n\x08THE_VOID\x10\x01\x12\x10\n\x0cCLOUD_PALAC\
    E\x10\x02\x12\x0e\n\nKINGS_HALL\x10\x03\x12\r\n\tGRASSLAND\x10\x04\x12\
    \x0e\n\nTORII_GATE\x10\x05\x12\x10\n\x0cMUSIC_STUDIO\x10\x06\x12\t\n\x05\
    ARENA\x10\x07\x12\n\n\x06FOREST\x10\x08b\x06proto3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
