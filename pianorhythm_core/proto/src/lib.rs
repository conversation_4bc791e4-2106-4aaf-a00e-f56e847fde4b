#![allow(unknown_lints)]
#![allow(clippy::all)]
#![allow(clippy::pedantic)]

#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unsafe_code)]
#![allow(unused_imports)]
#![allow(unused_results)]

use crate::midi_renditions::ActiveChannelsMode;

pub mod client_message;
pub mod midi_renditions;
pub mod pianorhythm_actions;
pub mod pianorhythm_effects;
pub mod pianorhythm_events;
pub mod pianorhythm_models_audio;
pub mod pianorhythm_app_renditions;
pub mod room_renditions;
pub mod server_message;
pub mod user_renditions;
pub mod world_renditions;
