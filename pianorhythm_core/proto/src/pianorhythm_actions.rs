// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `pianorhythm-actions.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>ult)]
pub struct SocketIdWithInt32 {
    // message fields
    pub socketId: ::std::string::String,
    pub int32Value: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SocketIdWithInt32 {
    fn default() -> &'a SocketIdWithInt32 {
        <SocketIdWithInt32 as ::protobuf::Message>::default_instance()
    }
}

impl SocketIdWithInt32 {
    pub fn new() -> SocketIdWithInt32 {
        ::std::default::Default::default()
    }

    // string socketId = 1;


    pub fn get_socketId(&self) -> &str {
        &self.socketId
    }
    pub fn clear_socketId(&mut self) {
        self.socketId.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketId(&mut self, v: ::std::string::String) {
        self.socketId = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketId(&mut self) -> &mut ::std::string::String {
        &mut self.socketId
    }

    // Take field
    pub fn take_socketId(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketId, ::std::string::String::new())
    }

    // uint32 int32Value = 2;


    pub fn get_int32Value(&self) -> u32 {
        self.int32Value
    }
    pub fn clear_int32Value(&mut self) {
        self.int32Value = 0;
    }

    // Param is passed by value, moved
    pub fn set_int32Value(&mut self, v: u32) {
        self.int32Value = v;
    }
}

impl ::protobuf::Message for SocketIdWithInt32 {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketId)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.int32Value = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.socketId.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.socketId);
        }
        if self.int32Value != 0 {
            my_size += ::protobuf::rt::value_size(2, self.int32Value, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.socketId.is_empty() {
            os.write_string(1, &self.socketId)?;
        }
        if self.int32Value != 0 {
            os.write_uint32(2, self.int32Value)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SocketIdWithInt32 {
        SocketIdWithInt32::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketId",
                |m: &SocketIdWithInt32| { &m.socketId },
                |m: &mut SocketIdWithInt32| { &mut m.socketId },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "int32Value",
                |m: &SocketIdWithInt32| { &m.int32Value },
                |m: &mut SocketIdWithInt32| { &mut m.int32Value },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<SocketIdWithInt32>(
                "SocketIdWithInt32",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static SocketIdWithInt32 {
        static instance: ::protobuf::rt::LazyV2<SocketIdWithInt32> = ::protobuf::rt::LazyV2::INIT;
        instance.get(SocketIdWithInt32::new)
    }
}

impl ::protobuf::Clear for SocketIdWithInt32 {
    fn clear(&mut self) {
        self.socketId.clear();
        self.int32Value = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for SocketIdWithInt32 {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for SocketIdWithInt32 {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ChannelWithBool {
    // message fields
    pub channel: u32,
    pub boolValue: bool,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ChannelWithBool {
    fn default() -> &'a ChannelWithBool {
        <ChannelWithBool as ::protobuf::Message>::default_instance()
    }
}

impl ChannelWithBool {
    pub fn new() -> ChannelWithBool {
        ::std::default::Default::default()
    }

    // uint32 channel = 1;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }

    // bool boolValue = 2;


    pub fn get_boolValue(&self) -> bool {
        self.boolValue
    }
    pub fn clear_boolValue(&mut self) {
        self.boolValue = false;
    }

    // Param is passed by value, moved
    pub fn set_boolValue(&mut self, v: bool) {
        self.boolValue = v;
    }
}

impl ::protobuf::Message for ChannelWithBool {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.boolValue = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.boolValue != false {
            my_size += 2;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_uint32(1, self.channel)?;
        }
        if self.boolValue != false {
            os.write_bool(2, self.boolValue)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ChannelWithBool {
        ChannelWithBool::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &ChannelWithBool| { &m.channel },
                |m: &mut ChannelWithBool| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "boolValue",
                |m: &ChannelWithBool| { &m.boolValue },
                |m: &mut ChannelWithBool| { &mut m.boolValue },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ChannelWithBool>(
                "ChannelWithBool",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ChannelWithBool {
        static instance: ::protobuf::rt::LazyV2<ChannelWithBool> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ChannelWithBool::new)
    }
}

impl ::protobuf::Clear for ChannelWithBool {
    fn clear(&mut self) {
        self.channel = 0;
        self.boolValue = false;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ChannelWithBool {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ChannelWithBool {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ChannelWithUint32 {
    // message fields
    pub channel: u32,
    pub uint32Value: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ChannelWithUint32 {
    fn default() -> &'a ChannelWithUint32 {
        <ChannelWithUint32 as ::protobuf::Message>::default_instance()
    }
}

impl ChannelWithUint32 {
    pub fn new() -> ChannelWithUint32 {
        ::std::default::Default::default()
    }

    // uint32 channel = 1;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }

    // uint32 uint32Value = 2;


    pub fn get_uint32Value(&self) -> u32 {
        self.uint32Value
    }
    pub fn clear_uint32Value(&mut self) {
        self.uint32Value = 0;
    }

    // Param is passed by value, moved
    pub fn set_uint32Value(&mut self, v: u32) {
        self.uint32Value = v;
    }
}

impl ::protobuf::Message for ChannelWithUint32 {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.uint32Value = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.uint32Value != 0 {
            my_size += ::protobuf::rt::value_size(2, self.uint32Value, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_uint32(1, self.channel)?;
        }
        if self.uint32Value != 0 {
            os.write_uint32(2, self.uint32Value)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ChannelWithUint32 {
        ChannelWithUint32::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &ChannelWithUint32| { &m.channel },
                |m: &mut ChannelWithUint32| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "uint32Value",
                |m: &ChannelWithUint32| { &m.uint32Value },
                |m: &mut ChannelWithUint32| { &mut m.uint32Value },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ChannelWithUint32>(
                "ChannelWithUint32",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ChannelWithUint32 {
        static instance: ::protobuf::rt::LazyV2<ChannelWithUint32> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ChannelWithUint32::new)
    }
}

impl ::protobuf::Clear for ChannelWithUint32 {
    fn clear(&mut self) {
        self.channel = 0;
        self.uint32Value = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ChannelWithUint32 {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ChannelWithUint32 {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AudioSynthActionData {
    // message fields
    pub channel: u32,
    pub note: u32,
    pub velocity: u32,
    pub noteSource: super::midi_renditions::MidiNoteSource,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AudioSynthActionData {
    fn default() -> &'a AudioSynthActionData {
        <AudioSynthActionData as ::protobuf::Message>::default_instance()
    }
}

impl AudioSynthActionData {
    pub fn new() -> AudioSynthActionData {
        ::std::default::Default::default()
    }

    // uint32 channel = 1;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }

    // uint32 note = 2;


    pub fn get_note(&self) -> u32 {
        self.note
    }
    pub fn clear_note(&mut self) {
        self.note = 0;
    }

    // Param is passed by value, moved
    pub fn set_note(&mut self, v: u32) {
        self.note = v;
    }

    // uint32 velocity = 3;


    pub fn get_velocity(&self) -> u32 {
        self.velocity
    }
    pub fn clear_velocity(&mut self) {
        self.velocity = 0;
    }

    // Param is passed by value, moved
    pub fn set_velocity(&mut self, v: u32) {
        self.velocity = v;
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiNoteSource noteSource = 4;


    pub fn get_noteSource(&self) -> super::midi_renditions::MidiNoteSource {
        self.noteSource
    }
    pub fn clear_noteSource(&mut self) {
        self.noteSource = super::midi_renditions::MidiNoteSource::IGNORED;
    }

    // Param is passed by value, moved
    pub fn set_noteSource(&mut self, v: super::midi_renditions::MidiNoteSource) {
        self.noteSource = v;
    }
}

impl ::protobuf::Message for AudioSynthActionData {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.note = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.velocity = tmp;
                },
                4 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.noteSource, 4, &mut self.unknown_fields)?
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.note != 0 {
            my_size += ::protobuf::rt::value_size(2, self.note, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.velocity != 0 {
            my_size += ::protobuf::rt::value_size(3, self.velocity, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.noteSource != super::midi_renditions::MidiNoteSource::IGNORED {
            my_size += ::protobuf::rt::enum_size(4, self.noteSource);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_uint32(1, self.channel)?;
        }
        if self.note != 0 {
            os.write_uint32(2, self.note)?;
        }
        if self.velocity != 0 {
            os.write_uint32(3, self.velocity)?;
        }
        if self.noteSource != super::midi_renditions::MidiNoteSource::IGNORED {
            os.write_enum(4, ::protobuf::ProtobufEnum::value(&self.noteSource))?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AudioSynthActionData {
        AudioSynthActionData::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &AudioSynthActionData| { &m.channel },
                |m: &mut AudioSynthActionData| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "note",
                |m: &AudioSynthActionData| { &m.note },
                |m: &mut AudioSynthActionData| { &mut m.note },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "velocity",
                |m: &AudioSynthActionData| { &m.velocity },
                |m: &mut AudioSynthActionData| { &mut m.velocity },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<super::midi_renditions::MidiNoteSource>>(
                "noteSource",
                |m: &AudioSynthActionData| { &m.noteSource },
                |m: &mut AudioSynthActionData| { &mut m.noteSource },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AudioSynthActionData>(
                "AudioSynthActionData",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AudioSynthActionData {
        static instance: ::protobuf::rt::LazyV2<AudioSynthActionData> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AudioSynthActionData::new)
    }
}

impl ::protobuf::Clear for AudioSynthActionData {
    fn clear(&mut self) {
        self.channel = 0;
        self.note = 0;
        self.velocity = 0;
        self.noteSource = super::midi_renditions::MidiNoteSource::IGNORED;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AudioSynthActionData {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AudioSynthActionData {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AudioSynthActions {
    // message fields
    pub action: AudioSynthActions_Action,
    // message oneof groups
    pub data: ::std::option::Option<AudioSynthActions_oneof_data>,
    pub _sourceSocketID: ::std::option::Option<AudioSynthActions_oneof__sourceSocketID>,
    pub _sourceHashedSocketID: ::std::option::Option<AudioSynthActions_oneof__sourceHashedSocketID>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AudioSynthActions {
    fn default() -> &'a AudioSynthActions {
        <AudioSynthActions as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AudioSynthActions_oneof_data {
    socketId(::std::string::String),
    channel(u32),
    channelWithBool(ChannelWithBool),
    instrument(super::midi_renditions::Instrument),
    uint32Value(u32),
    instrumentsList(super::midi_renditions::InstrumentsList),
    boolValue(bool),
    channelWithUint32(ChannelWithUint32),
    synthData(AudioSynthActionData),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AudioSynthActions_oneof__sourceSocketID {
    sourceSocketID(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AudioSynthActions_oneof__sourceHashedSocketID {
    sourceHashedSocketID(u64),
}

impl AudioSynthActions {
    pub fn new() -> AudioSynthActions {
        ::std::default::Default::default()
    }

    // .PianoRhythm.AppStateActions.AudioSynthActions.Action action = 1;


    pub fn get_action(&self) -> AudioSynthActions_Action {
        self.action
    }
    pub fn clear_action(&mut self) {
        self.action = AudioSynthActions_Action::NoteOn;
    }

    // Param is passed by value, moved
    pub fn set_action(&mut self, v: AudioSynthActions_Action) {
        self.action = v;
    }

    // string sourceSocketID = 999;


    pub fn get_sourceSocketID(&self) -> &str {
        match self._sourceSocketID {
            ::std::option::Option::Some(AudioSynthActions_oneof__sourceSocketID::sourceSocketID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_sourceSocketID(&mut self) {
        self._sourceSocketID = ::std::option::Option::None;
    }

    pub fn has_sourceSocketID(&self) -> bool {
        match self._sourceSocketID {
            ::std::option::Option::Some(AudioSynthActions_oneof__sourceSocketID::sourceSocketID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_sourceSocketID(&mut self, v: ::std::string::String) {
        self._sourceSocketID = ::std::option::Option::Some(AudioSynthActions_oneof__sourceSocketID::sourceSocketID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_sourceSocketID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AudioSynthActions_oneof__sourceSocketID::sourceSocketID(_)) = self._sourceSocketID {
        } else {
            self._sourceSocketID = ::std::option::Option::Some(AudioSynthActions_oneof__sourceSocketID::sourceSocketID(::std::string::String::new()));
        }
        match self._sourceSocketID {
            ::std::option::Option::Some(AudioSynthActions_oneof__sourceSocketID::sourceSocketID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_sourceSocketID(&mut self) -> ::std::string::String {
        if self.has_sourceSocketID() {
            match self._sourceSocketID.take() {
                ::std::option::Option::Some(AudioSynthActions_oneof__sourceSocketID::sourceSocketID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // uint64 sourceHashedSocketID = 998;


    pub fn get_sourceHashedSocketID(&self) -> u64 {
        match self._sourceHashedSocketID {
            ::std::option::Option::Some(AudioSynthActions_oneof__sourceHashedSocketID::sourceHashedSocketID(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_sourceHashedSocketID(&mut self) {
        self._sourceHashedSocketID = ::std::option::Option::None;
    }

    pub fn has_sourceHashedSocketID(&self) -> bool {
        match self._sourceHashedSocketID {
            ::std::option::Option::Some(AudioSynthActions_oneof__sourceHashedSocketID::sourceHashedSocketID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_sourceHashedSocketID(&mut self, v: u64) {
        self._sourceHashedSocketID = ::std::option::Option::Some(AudioSynthActions_oneof__sourceHashedSocketID::sourceHashedSocketID(v))
    }

    // string socketId = 2;


    pub fn get_socketId(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::socketId(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_socketId(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_socketId(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::socketId(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketId(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::socketId(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketId(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AudioSynthActions_oneof_data::socketId(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::socketId(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::socketId(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketId(&mut self) -> ::std::string::String {
        if self.has_socketId() {
            match self.data.take() {
                ::std::option::Option::Some(AudioSynthActions_oneof_data::socketId(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // uint32 channel = 3;


    pub fn get_channel(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::channel(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_channel(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_channel(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::channel(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::channel(v))
    }

    // .PianoRhythm.AppStateActions.ChannelWithBool channelWithBool = 4;


    pub fn get_channelWithBool(&self) -> &ChannelWithBool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithBool(ref v)) => v,
            _ => <ChannelWithBool as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_channelWithBool(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_channelWithBool(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithBool(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_channelWithBool(&mut self, v: ChannelWithBool) {
        self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithBool(v))
    }

    // Mutable pointer to the field.
    pub fn mut_channelWithBool(&mut self) -> &mut ChannelWithBool {
        if let ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithBool(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithBool(ChannelWithBool::new()));
        }
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithBool(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_channelWithBool(&mut self) -> ChannelWithBool {
        if self.has_channelWithBool() {
            match self.data.take() {
                ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithBool(v)) => v,
                _ => panic!(),
            }
        } else {
            ChannelWithBool::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.Instrument instrument = 5;


    pub fn get_instrument(&self) -> &super::midi_renditions::Instrument {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::instrument(ref v)) => v,
            _ => <super::midi_renditions::Instrument as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_instrument(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_instrument(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::instrument(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_instrument(&mut self, v: super::midi_renditions::Instrument) {
        self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::instrument(v))
    }

    // Mutable pointer to the field.
    pub fn mut_instrument(&mut self) -> &mut super::midi_renditions::Instrument {
        if let ::std::option::Option::Some(AudioSynthActions_oneof_data::instrument(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::instrument(super::midi_renditions::Instrument::new()));
        }
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::instrument(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_instrument(&mut self) -> super::midi_renditions::Instrument {
        if self.has_instrument() {
            match self.data.take() {
                ::std::option::Option::Some(AudioSynthActions_oneof_data::instrument(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::Instrument::new()
        }
    }

    // uint32 uint32Value = 6;


    pub fn get_uint32Value(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::uint32Value(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_uint32Value(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_uint32Value(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::uint32Value(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_uint32Value(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::uint32Value(v))
    }

    // .PianoRhythm.Serialization.Midi.Msgs.InstrumentsList instrumentsList = 7;


    pub fn get_instrumentsList(&self) -> &super::midi_renditions::InstrumentsList {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::instrumentsList(ref v)) => v,
            _ => <super::midi_renditions::InstrumentsList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_instrumentsList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_instrumentsList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::instrumentsList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_instrumentsList(&mut self, v: super::midi_renditions::InstrumentsList) {
        self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::instrumentsList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_instrumentsList(&mut self) -> &mut super::midi_renditions::InstrumentsList {
        if let ::std::option::Option::Some(AudioSynthActions_oneof_data::instrumentsList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::instrumentsList(super::midi_renditions::InstrumentsList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::instrumentsList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_instrumentsList(&mut self) -> super::midi_renditions::InstrumentsList {
        if self.has_instrumentsList() {
            match self.data.take() {
                ::std::option::Option::Some(AudioSynthActions_oneof_data::instrumentsList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::InstrumentsList::new()
        }
    }

    // bool boolValue = 8;


    pub fn get_boolValue(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::boolValue(v)) => v,
            _ => false,
        }
    }
    pub fn clear_boolValue(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_boolValue(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::boolValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_boolValue(&mut self, v: bool) {
        self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::boolValue(v))
    }

    // .PianoRhythm.AppStateActions.ChannelWithUint32 channelWithUint32 = 9;


    pub fn get_channelWithUint32(&self) -> &ChannelWithUint32 {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithUint32(ref v)) => v,
            _ => <ChannelWithUint32 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_channelWithUint32(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_channelWithUint32(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithUint32(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_channelWithUint32(&mut self, v: ChannelWithUint32) {
        self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithUint32(v))
    }

    // Mutable pointer to the field.
    pub fn mut_channelWithUint32(&mut self) -> &mut ChannelWithUint32 {
        if let ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithUint32(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithUint32(ChannelWithUint32::new()));
        }
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithUint32(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_channelWithUint32(&mut self) -> ChannelWithUint32 {
        if self.has_channelWithUint32() {
            match self.data.take() {
                ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithUint32(v)) => v,
                _ => panic!(),
            }
        } else {
            ChannelWithUint32::new()
        }
    }

    // .PianoRhythm.AppStateActions.AudioSynthActionData synthData = 10;


    pub fn get_synthData(&self) -> &AudioSynthActionData {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::synthData(ref v)) => v,
            _ => <AudioSynthActionData as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_synthData(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_synthData(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::synthData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_synthData(&mut self, v: AudioSynthActionData) {
        self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::synthData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_synthData(&mut self) -> &mut AudioSynthActionData {
        if let ::std::option::Option::Some(AudioSynthActions_oneof_data::synthData(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::synthData(AudioSynthActionData::new()));
        }
        match self.data {
            ::std::option::Option::Some(AudioSynthActions_oneof_data::synthData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_synthData(&mut self) -> AudioSynthActionData {
        if self.has_synthData() {
            match self.data.take() {
                ::std::option::Option::Some(AudioSynthActions_oneof_data::synthData(v)) => v,
                _ => panic!(),
            }
        } else {
            AudioSynthActionData::new()
        }
    }
}

impl ::protobuf::Message for AudioSynthActions {
    fn is_initialized(&self) -> bool {
        if let Some(AudioSynthActions_oneof_data::channelWithBool(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AudioSynthActions_oneof_data::instrument(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AudioSynthActions_oneof_data::instrumentsList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AudioSynthActions_oneof_data::channelWithUint32(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AudioSynthActions_oneof_data::synthData(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.action, 1, &mut self.unknown_fields)?
                },
                999 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._sourceSocketID = ::std::option::Option::Some(AudioSynthActions_oneof__sourceSocketID::sourceSocketID(is.read_string()?));
                },
                998 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._sourceHashedSocketID = ::std::option::Option::Some(AudioSynthActions_oneof__sourceHashedSocketID::sourceHashedSocketID(is.read_uint64()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::socketId(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::channel(is.read_uint32()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithBool(is.read_message()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::instrument(is.read_message()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::uint32Value(is.read_uint32()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::instrumentsList(is.read_message()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::boolValue(is.read_bool()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::channelWithUint32(is.read_message()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AudioSynthActions_oneof_data::synthData(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.action != AudioSynthActions_Action::NoteOn {
            my_size += ::protobuf::rt::enum_size(1, self.action);
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &AudioSynthActions_oneof_data::socketId(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
                &AudioSynthActions_oneof_data::channel(v) => {
                    my_size += ::protobuf::rt::value_size(3, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &AudioSynthActions_oneof_data::channelWithBool(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AudioSynthActions_oneof_data::instrument(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AudioSynthActions_oneof_data::uint32Value(v) => {
                    my_size += ::protobuf::rt::value_size(6, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &AudioSynthActions_oneof_data::instrumentsList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AudioSynthActions_oneof_data::boolValue(v) => {
                    my_size += 2;
                },
                &AudioSynthActions_oneof_data::channelWithUint32(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AudioSynthActions_oneof_data::synthData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._sourceSocketID {
            match v {
                &AudioSynthActions_oneof__sourceSocketID::sourceSocketID(ref v) => {
                    my_size += ::protobuf::rt::string_size(999, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._sourceHashedSocketID {
            match v {
                &AudioSynthActions_oneof__sourceHashedSocketID::sourceHashedSocketID(v) => {
                    my_size += ::protobuf::rt::value_size(998, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.action != AudioSynthActions_Action::NoteOn {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.action))?;
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &AudioSynthActions_oneof_data::socketId(ref v) => {
                    os.write_string(2, v)?;
                },
                &AudioSynthActions_oneof_data::channel(v) => {
                    os.write_uint32(3, v)?;
                },
                &AudioSynthActions_oneof_data::channelWithBool(ref v) => {
                    os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AudioSynthActions_oneof_data::instrument(ref v) => {
                    os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AudioSynthActions_oneof_data::uint32Value(v) => {
                    os.write_uint32(6, v)?;
                },
                &AudioSynthActions_oneof_data::instrumentsList(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AudioSynthActions_oneof_data::boolValue(v) => {
                    os.write_bool(8, v)?;
                },
                &AudioSynthActions_oneof_data::channelWithUint32(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AudioSynthActions_oneof_data::synthData(ref v) => {
                    os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._sourceSocketID {
            match v {
                &AudioSynthActions_oneof__sourceSocketID::sourceSocketID(ref v) => {
                    os.write_string(999, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._sourceHashedSocketID {
            match v {
                &AudioSynthActions_oneof__sourceHashedSocketID::sourceHashedSocketID(v) => {
                    os.write_uint64(998, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AudioSynthActions {
        AudioSynthActions::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<AudioSynthActions_Action>>(
                "action",
                |m: &AudioSynthActions| { &m.action },
                |m: &mut AudioSynthActions| { &mut m.action },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "sourceSocketID",
                AudioSynthActions::has_sourceSocketID,
                AudioSynthActions::get_sourceSocketID,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u64_accessor::<_>(
                "sourceHashedSocketID",
                AudioSynthActions::has_sourceHashedSocketID,
                AudioSynthActions::get_sourceHashedSocketID,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "socketId",
                AudioSynthActions::has_socketId,
                AudioSynthActions::get_socketId,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "channel",
                AudioSynthActions::has_channel,
                AudioSynthActions::get_channel,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ChannelWithBool>(
                "channelWithBool",
                AudioSynthActions::has_channelWithBool,
                AudioSynthActions::get_channelWithBool,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::Instrument>(
                "instrument",
                AudioSynthActions::has_instrument,
                AudioSynthActions::get_instrument,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "uint32Value",
                AudioSynthActions::has_uint32Value,
                AudioSynthActions::get_uint32Value,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::InstrumentsList>(
                "instrumentsList",
                AudioSynthActions::has_instrumentsList,
                AudioSynthActions::get_instrumentsList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "boolValue",
                AudioSynthActions::has_boolValue,
                AudioSynthActions::get_boolValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ChannelWithUint32>(
                "channelWithUint32",
                AudioSynthActions::has_channelWithUint32,
                AudioSynthActions::get_channelWithUint32,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AudioSynthActionData>(
                "synthData",
                AudioSynthActions::has_synthData,
                AudioSynthActions::get_synthData,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AudioSynthActions>(
                "AudioSynthActions",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AudioSynthActions {
        static instance: ::protobuf::rt::LazyV2<AudioSynthActions> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AudioSynthActions::new)
    }
}

impl ::protobuf::Clear for AudioSynthActions {
    fn clear(&mut self) {
        self.action = AudioSynthActions_Action::NoteOn;
        self._sourceSocketID = ::std::option::Option::None;
        self._sourceHashedSocketID = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AudioSynthActions {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AudioSynthActions {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum AudioSynthActions_Action {
    NoteOn = 0,
    NoteOff = 1,
    AddUser = 2,
    RemoveUser = 3,
    AddClient = 4,
    SetChannelVolume = 5,
    SetChannelPan = 6,
    SetChannelExpression = 7,
}

impl ::protobuf::ProtobufEnum for AudioSynthActions_Action {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<AudioSynthActions_Action> {
        match value {
            0 => ::std::option::Option::Some(AudioSynthActions_Action::NoteOn),
            1 => ::std::option::Option::Some(AudioSynthActions_Action::NoteOff),
            2 => ::std::option::Option::Some(AudioSynthActions_Action::AddUser),
            3 => ::std::option::Option::Some(AudioSynthActions_Action::RemoveUser),
            4 => ::std::option::Option::Some(AudioSynthActions_Action::AddClient),
            5 => ::std::option::Option::Some(AudioSynthActions_Action::SetChannelVolume),
            6 => ::std::option::Option::Some(AudioSynthActions_Action::SetChannelPan),
            7 => ::std::option::Option::Some(AudioSynthActions_Action::SetChannelExpression),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [AudioSynthActions_Action] = &[
            AudioSynthActions_Action::NoteOn,
            AudioSynthActions_Action::NoteOff,
            AudioSynthActions_Action::AddUser,
            AudioSynthActions_Action::RemoveUser,
            AudioSynthActions_Action::AddClient,
            AudioSynthActions_Action::SetChannelVolume,
            AudioSynthActions_Action::SetChannelPan,
            AudioSynthActions_Action::SetChannelExpression,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<AudioSynthActions_Action>("AudioSynthActions.Action", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for AudioSynthActions_Action {
}

impl ::std::default::Default for AudioSynthActions_Action {
    fn default() -> Self {
        AudioSynthActions_Action::NoteOn
    }
}

impl ::protobuf::reflect::ProtobufValue for AudioSynthActions_Action {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AppStateActions {
    // message fields
    pub action: AppStateActions_Action,
    // message oneof groups
    pub data: ::std::option::Option<AppStateActions_oneof_data>,
    pub _sourceSocketID: ::std::option::Option<AppStateActions_oneof__sourceSocketID>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AppStateActions {
    fn default() -> &'a AppStateActions {
        <AppStateActions as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AppStateActions_oneof_data {
    stringValue(::std::string::String),
    boolValue(bool),
    userDto(super::user_renditions::UserDto),
    userClientDto(super::user_renditions::UserClientDto),
    basicRoomDto(super::room_renditions::BasicRoomDto),
    userUpdateCommand(super::user_renditions::UserUpdateCommand),
    userDtoList(super::client_message::UserDtoList),
    socketIdList(super::client_message::SocketIdList),
    friendDtoList(super::client_message::FriendDtoList),
    socketId(::std::string::String),
    usertag(::std::string::String),
    userid(::std::string::String),
    joinedRoomData(super::client_message::JoinedRoomData),
    roomChatHistory(super::client_message::RoomChatHistory),
    chatMessageDto(super::client_message::ChatMessageDto),
    roomSettings(super::room_renditions::RoomSettings),
    roomFullDetails(super::room_renditions::RoomFullDetails),
    joinRoomFailResponse(super::client_message::CommandResponse_JoinRoomFailResponse),
    roomsList(super::client_message::CommandResponse_RoomsList),
    joinRoomByNameRequest(super::server_message::ServerMessage_JoinRoomByNameRequest),
    serverCommand(super::server_message::ServerCommandDU),
    roomOwnerCommand(super::server_message::RoomOwnerCommandDU),
    roomIdWithPassword(super::server_message::RoomIDWithPassword),
    roomId(::std::string::String),
    midiMessageOutputDto(super::client_message::MidiMessageOutputDto),
    welcomeDto(super::client_message::WelcomeDto),
    pendingFriendRequestList(super::client_message::PendingFriendRequestList),
    kickedUsersList(super::client_message::KickedUsersList),
    clientSideUserDtoList(super::client_message::ClientSideUserDtoList),
    pendingFriendRequest(super::user_renditions::PendingFriendRequest),
    clientMsg(super::client_message::ClientMessage),
    commandResponse(super::client_message::CommandResponse),
    audioSynthAction(AudioSynthActions),
    int32Value(i32),
    uint32Value(u32),
    socketIdWithIn32(SocketIdWithInt32),
    instrument(super::midi_renditions::Instrument),
    instrumentsList(super::midi_renditions::InstrumentsList),
    setChannelInstrumentPayload(super::midi_renditions::SetChannelInstrumentPayload),
    setChannelDetailsPayload(super::midi_renditions::SetChannelDetailsPayload),
    slotMode(super::midi_renditions::ActiveChannelsMode),
    updateChannelPayload(super::midi_renditions::UpdateChannelPayload),
    synthEventProgramChangePayload(super::midi_renditions::SynthEventProgramChangePayload),
    audioChannel(super::midi_renditions::AudioChannel),
    appPianoKey(super::pianorhythm_app_renditions::AppPianoKey),
    appPianoPedal(super::pianorhythm_app_renditions::AppPianoPedal),
    appRenderableEntity(super::pianorhythm_app_renditions::AppRenderableEntity),
    loadMeshDetails(AppStateActions_RendererLoadMeshDetails),
    appNotificationConfig(super::pianorhythm_app_renditions::AppNotificationConfig),
    appSettings(super::pianorhythm_app_renditions::AppSettings),
    channelWithBool(ChannelWithBool),
    doubleValue(f64),
    vpFileLoad(super::pianorhythm_app_renditions::AppVPSequencerFileLoad),
    appMidiTrack(super::pianorhythm_app_renditions::AppMidiTrack),
    appCommonEnvironment(super::pianorhythm_app_renditions::AppCommonEnvironment),
    keyboardVisualizeMappings(super::pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec),
    avatarWorldPosition(super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition),
    avatarPianoBench(i32),
    avatarCustomizationData(super::world_renditions::AvatarCustomizationData),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AppStateActions_oneof__sourceSocketID {
    sourceSocketID(::std::string::String),
}

impl AppStateActions {
    pub fn new() -> AppStateActions {
        ::std::default::Default::default()
    }

    // .PianoRhythm.AppStateActions.AppStateActions.Action action = 1;


    pub fn get_action(&self) -> AppStateActions_Action {
        self.action
    }
    pub fn clear_action(&mut self) {
        self.action = AppStateActions_Action::Unknown;
    }

    // Param is passed by value, moved
    pub fn set_action(&mut self, v: AppStateActions_Action) {
        self.action = v;
    }

    // string sourceSocketID = 999;


    pub fn get_sourceSocketID(&self) -> &str {
        match self._sourceSocketID {
            ::std::option::Option::Some(AppStateActions_oneof__sourceSocketID::sourceSocketID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_sourceSocketID(&mut self) {
        self._sourceSocketID = ::std::option::Option::None;
    }

    pub fn has_sourceSocketID(&self) -> bool {
        match self._sourceSocketID {
            ::std::option::Option::Some(AppStateActions_oneof__sourceSocketID::sourceSocketID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_sourceSocketID(&mut self, v: ::std::string::String) {
        self._sourceSocketID = ::std::option::Option::Some(AppStateActions_oneof__sourceSocketID::sourceSocketID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_sourceSocketID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateActions_oneof__sourceSocketID::sourceSocketID(_)) = self._sourceSocketID {
        } else {
            self._sourceSocketID = ::std::option::Option::Some(AppStateActions_oneof__sourceSocketID::sourceSocketID(::std::string::String::new()));
        }
        match self._sourceSocketID {
            ::std::option::Option::Some(AppStateActions_oneof__sourceSocketID::sourceSocketID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_sourceSocketID(&mut self) -> ::std::string::String {
        if self.has_sourceSocketID() {
            match self._sourceSocketID.take() {
                ::std::option::Option::Some(AppStateActions_oneof__sourceSocketID::sourceSocketID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string stringValue = 2;


    pub fn get_stringValue(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::stringValue(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_stringValue(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_stringValue(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::stringValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_stringValue(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::stringValue(v))
    }

    // Mutable pointer to the field.
    pub fn mut_stringValue(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::stringValue(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::stringValue(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::stringValue(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_stringValue(&mut self) -> ::std::string::String {
        if self.has_stringValue() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::stringValue(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // bool boolValue = 3;


    pub fn get_boolValue(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::boolValue(v)) => v,
            _ => false,
        }
    }
    pub fn clear_boolValue(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_boolValue(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::boolValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_boolValue(&mut self, v: bool) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::boolValue(v))
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserDto userDto = 4;


    pub fn get_userDto(&self) -> &super::user_renditions::UserDto {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userDto(ref v)) => v,
            _ => <super::user_renditions::UserDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userDto(&mut self, v: super::user_renditions::UserDto) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userDto(&mut self) -> &mut super::user_renditions::UserDto {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::userDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userDto(super::user_renditions::UserDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userDto(&mut self) -> super::user_renditions::UserDto {
        if self.has_userDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::userDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::UserDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserClientDto userClientDto = 5;


    pub fn get_userClientDto(&self) -> &super::user_renditions::UserClientDto {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userClientDto(ref v)) => v,
            _ => <super::user_renditions::UserClientDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userClientDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userClientDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userClientDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userClientDto(&mut self, v: super::user_renditions::UserClientDto) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userClientDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userClientDto(&mut self) -> &mut super::user_renditions::UserClientDto {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::userClientDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userClientDto(super::user_renditions::UserClientDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userClientDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userClientDto(&mut self) -> super::user_renditions::UserClientDto {
        if self.has_userClientDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::userClientDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::UserClientDto::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.BasicRoomDto basicRoomDto = 6;


    pub fn get_basicRoomDto(&self) -> &super::room_renditions::BasicRoomDto {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::basicRoomDto(ref v)) => v,
            _ => <super::room_renditions::BasicRoomDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_basicRoomDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_basicRoomDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::basicRoomDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_basicRoomDto(&mut self, v: super::room_renditions::BasicRoomDto) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::basicRoomDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_basicRoomDto(&mut self) -> &mut super::room_renditions::BasicRoomDto {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::basicRoomDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::basicRoomDto(super::room_renditions::BasicRoomDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::basicRoomDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_basicRoomDto(&mut self) -> super::room_renditions::BasicRoomDto {
        if self.has_basicRoomDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::basicRoomDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::BasicRoomDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserUpdateCommand userUpdateCommand = 7;


    pub fn get_userUpdateCommand(&self) -> &super::user_renditions::UserUpdateCommand {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userUpdateCommand(ref v)) => v,
            _ => <super::user_renditions::UserUpdateCommand as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userUpdateCommand(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userUpdateCommand(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userUpdateCommand(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userUpdateCommand(&mut self, v: super::user_renditions::UserUpdateCommand) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userUpdateCommand(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userUpdateCommand(&mut self) -> &mut super::user_renditions::UserUpdateCommand {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::userUpdateCommand(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userUpdateCommand(super::user_renditions::UserUpdateCommand::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userUpdateCommand(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userUpdateCommand(&mut self) -> super::user_renditions::UserUpdateCommand {
        if self.has_userUpdateCommand() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::userUpdateCommand(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::UserUpdateCommand::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.UserDtoList userDtoList = 8;


    pub fn get_userDtoList(&self) -> &super::client_message::UserDtoList {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userDtoList(ref v)) => v,
            _ => <super::client_message::UserDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userDtoList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userDtoList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userDtoList(&mut self, v: super::client_message::UserDtoList) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userDtoList(&mut self) -> &mut super::client_message::UserDtoList {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::userDtoList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userDtoList(super::client_message::UserDtoList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userDtoList(&mut self) -> super::client_message::UserDtoList {
        if self.has_userDtoList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::userDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::UserDtoList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.SocketIdList socketIdList = 9;


    pub fn get_socketIdList(&self) -> &super::client_message::SocketIdList {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::socketIdList(ref v)) => v,
            _ => <super::client_message::SocketIdList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_socketIdList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_socketIdList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::socketIdList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketIdList(&mut self, v: super::client_message::SocketIdList) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::socketIdList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketIdList(&mut self) -> &mut super::client_message::SocketIdList {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::socketIdList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::socketIdList(super::client_message::SocketIdList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::socketIdList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketIdList(&mut self) -> super::client_message::SocketIdList {
        if self.has_socketIdList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::socketIdList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::SocketIdList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.FriendDtoList friendDtoList = 10;


    pub fn get_friendDtoList(&self) -> &super::client_message::FriendDtoList {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::friendDtoList(ref v)) => v,
            _ => <super::client_message::FriendDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_friendDtoList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_friendDtoList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::friendDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_friendDtoList(&mut self, v: super::client_message::FriendDtoList) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::friendDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_friendDtoList(&mut self) -> &mut super::client_message::FriendDtoList {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::friendDtoList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::friendDtoList(super::client_message::FriendDtoList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::friendDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_friendDtoList(&mut self) -> super::client_message::FriendDtoList {
        if self.has_friendDtoList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::friendDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::FriendDtoList::new()
        }
    }

    // string socketId = 11;


    pub fn get_socketId(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::socketId(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_socketId(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_socketId(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::socketId(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketId(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::socketId(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketId(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::socketId(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::socketId(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::socketId(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketId(&mut self) -> ::std::string::String {
        if self.has_socketId() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::socketId(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string usertag = 12;


    pub fn get_usertag(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::usertag(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_usertag(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_usertag(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::usertag(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::usertag(v))
    }

    // Mutable pointer to the field.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::usertag(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::usertag(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::usertag(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        if self.has_usertag() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::usertag(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string userid = 13;


    pub fn get_userid(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userid(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_userid(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userid(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userid(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userid(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userid(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userid(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::userid(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userid(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::userid(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userid(&mut self) -> ::std::string::String {
        if self.has_userid() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::userid(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.JoinedRoomData joinedRoomData = 14;


    pub fn get_joinedRoomData(&self) -> &super::client_message::JoinedRoomData {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::joinedRoomData(ref v)) => v,
            _ => <super::client_message::JoinedRoomData as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_joinedRoomData(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_joinedRoomData(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::joinedRoomData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_joinedRoomData(&mut self, v: super::client_message::JoinedRoomData) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::joinedRoomData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_joinedRoomData(&mut self) -> &mut super::client_message::JoinedRoomData {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::joinedRoomData(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::joinedRoomData(super::client_message::JoinedRoomData::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::joinedRoomData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_joinedRoomData(&mut self) -> super::client_message::JoinedRoomData {
        if self.has_joinedRoomData() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::joinedRoomData(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::JoinedRoomData::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.RoomChatHistory roomChatHistory = 15;


    pub fn get_roomChatHistory(&self) -> &super::client_message::RoomChatHistory {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomChatHistory(ref v)) => v,
            _ => <super::client_message::RoomChatHistory as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomChatHistory(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomChatHistory(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomChatHistory(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomChatHistory(&mut self, v: super::client_message::RoomChatHistory) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomChatHistory(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomChatHistory(&mut self) -> &mut super::client_message::RoomChatHistory {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::roomChatHistory(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomChatHistory(super::client_message::RoomChatHistory::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomChatHistory(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomChatHistory(&mut self) -> super::client_message::RoomChatHistory {
        if self.has_roomChatHistory() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::roomChatHistory(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::RoomChatHistory::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDto chatMessageDto = 16;


    pub fn get_chatMessageDto(&self) -> &super::client_message::ChatMessageDto {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::chatMessageDto(ref v)) => v,
            _ => <super::client_message::ChatMessageDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_chatMessageDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_chatMessageDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::chatMessageDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_chatMessageDto(&mut self, v: super::client_message::ChatMessageDto) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::chatMessageDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_chatMessageDto(&mut self) -> &mut super::client_message::ChatMessageDto {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::chatMessageDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::chatMessageDto(super::client_message::ChatMessageDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::chatMessageDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_chatMessageDto(&mut self) -> super::client_message::ChatMessageDto {
        if self.has_chatMessageDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::chatMessageDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::ChatMessageDto::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomSettings roomSettings = 17;


    pub fn get_roomSettings(&self) -> &super::room_renditions::RoomSettings {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomSettings(ref v)) => v,
            _ => <super::room_renditions::RoomSettings as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomSettings(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomSettings(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomSettings(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomSettings(&mut self, v: super::room_renditions::RoomSettings) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomSettings(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomSettings(&mut self) -> &mut super::room_renditions::RoomSettings {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::roomSettings(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomSettings(super::room_renditions::RoomSettings::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomSettings(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomSettings(&mut self) -> super::room_renditions::RoomSettings {
        if self.has_roomSettings() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::roomSettings(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::RoomSettings::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomFullDetails roomFullDetails = 18;


    pub fn get_roomFullDetails(&self) -> &super::room_renditions::RoomFullDetails {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomFullDetails(ref v)) => v,
            _ => <super::room_renditions::RoomFullDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomFullDetails(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomFullDetails(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomFullDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomFullDetails(&mut self, v: super::room_renditions::RoomFullDetails) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomFullDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomFullDetails(&mut self) -> &mut super::room_renditions::RoomFullDetails {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::roomFullDetails(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomFullDetails(super::room_renditions::RoomFullDetails::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomFullDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomFullDetails(&mut self) -> super::room_renditions::RoomFullDetails {
        if self.has_roomFullDetails() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::roomFullDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::RoomFullDetails::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.JoinRoomFailResponse joinRoomFailResponse = 19;


    pub fn get_joinRoomFailResponse(&self) -> &super::client_message::CommandResponse_JoinRoomFailResponse {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomFailResponse(ref v)) => v,
            _ => <super::client_message::CommandResponse_JoinRoomFailResponse as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_joinRoomFailResponse(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_joinRoomFailResponse(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomFailResponse(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_joinRoomFailResponse(&mut self, v: super::client_message::CommandResponse_JoinRoomFailResponse) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomFailResponse(v))
    }

    // Mutable pointer to the field.
    pub fn mut_joinRoomFailResponse(&mut self) -> &mut super::client_message::CommandResponse_JoinRoomFailResponse {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomFailResponse(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomFailResponse(super::client_message::CommandResponse_JoinRoomFailResponse::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomFailResponse(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_joinRoomFailResponse(&mut self) -> super::client_message::CommandResponse_JoinRoomFailResponse {
        if self.has_joinRoomFailResponse() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomFailResponse(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::CommandResponse_JoinRoomFailResponse::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.RoomsList roomsList = 20;


    pub fn get_roomsList(&self) -> &super::client_message::CommandResponse_RoomsList {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomsList(ref v)) => v,
            _ => <super::client_message::CommandResponse_RoomsList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomsList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomsList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomsList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomsList(&mut self, v: super::client_message::CommandResponse_RoomsList) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomsList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomsList(&mut self) -> &mut super::client_message::CommandResponse_RoomsList {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::roomsList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomsList(super::client_message::CommandResponse_RoomsList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomsList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomsList(&mut self) -> super::client_message::CommandResponse_RoomsList {
        if self.has_roomsList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::roomsList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::CommandResponse_RoomsList::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ServerMessage.JoinRoomByNameRequest joinRoomByNameRequest = 21;


    pub fn get_joinRoomByNameRequest(&self) -> &super::server_message::ServerMessage_JoinRoomByNameRequest {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomByNameRequest(ref v)) => v,
            _ => <super::server_message::ServerMessage_JoinRoomByNameRequest as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_joinRoomByNameRequest(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_joinRoomByNameRequest(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomByNameRequest(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_joinRoomByNameRequest(&mut self, v: super::server_message::ServerMessage_JoinRoomByNameRequest) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomByNameRequest(v))
    }

    // Mutable pointer to the field.
    pub fn mut_joinRoomByNameRequest(&mut self) -> &mut super::server_message::ServerMessage_JoinRoomByNameRequest {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomByNameRequest(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomByNameRequest(super::server_message::ServerMessage_JoinRoomByNameRequest::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomByNameRequest(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_joinRoomByNameRequest(&mut self) -> super::server_message::ServerMessage_JoinRoomByNameRequest {
        if self.has_joinRoomByNameRequest() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomByNameRequest(v)) => v,
                _ => panic!(),
            }
        } else {
            super::server_message::ServerMessage_JoinRoomByNameRequest::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ServerCommandDU serverCommand = 22;


    pub fn get_serverCommand(&self) -> &super::server_message::ServerCommandDU {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::serverCommand(ref v)) => v,
            _ => <super::server_message::ServerCommandDU as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_serverCommand(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_serverCommand(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::serverCommand(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_serverCommand(&mut self, v: super::server_message::ServerCommandDU) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::serverCommand(v))
    }

    // Mutable pointer to the field.
    pub fn mut_serverCommand(&mut self) -> &mut super::server_message::ServerCommandDU {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::serverCommand(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::serverCommand(super::server_message::ServerCommandDU::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::serverCommand(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_serverCommand(&mut self) -> super::server_message::ServerCommandDU {
        if self.has_serverCommand() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::serverCommand(v)) => v,
                _ => panic!(),
            }
        } else {
            super::server_message::ServerCommandDU::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.RoomOwnerCommandDU roomOwnerCommand = 23;


    pub fn get_roomOwnerCommand(&self) -> &super::server_message::RoomOwnerCommandDU {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomOwnerCommand(ref v)) => v,
            _ => <super::server_message::RoomOwnerCommandDU as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomOwnerCommand(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomOwnerCommand(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomOwnerCommand(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomOwnerCommand(&mut self, v: super::server_message::RoomOwnerCommandDU) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomOwnerCommand(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomOwnerCommand(&mut self) -> &mut super::server_message::RoomOwnerCommandDU {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::roomOwnerCommand(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomOwnerCommand(super::server_message::RoomOwnerCommandDU::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomOwnerCommand(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomOwnerCommand(&mut self) -> super::server_message::RoomOwnerCommandDU {
        if self.has_roomOwnerCommand() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::roomOwnerCommand(v)) => v,
                _ => panic!(),
            }
        } else {
            super::server_message::RoomOwnerCommandDU::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.RoomIDWithPassword roomIdWithPassword = 24;


    pub fn get_roomIdWithPassword(&self) -> &super::server_message::RoomIDWithPassword {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomIdWithPassword(ref v)) => v,
            _ => <super::server_message::RoomIDWithPassword as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomIdWithPassword(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomIdWithPassword(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomIdWithPassword(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomIdWithPassword(&mut self, v: super::server_message::RoomIDWithPassword) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomIdWithPassword(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomIdWithPassword(&mut self) -> &mut super::server_message::RoomIDWithPassword {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::roomIdWithPassword(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomIdWithPassword(super::server_message::RoomIDWithPassword::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomIdWithPassword(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomIdWithPassword(&mut self) -> super::server_message::RoomIDWithPassword {
        if self.has_roomIdWithPassword() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::roomIdWithPassword(v)) => v,
                _ => panic!(),
            }
        } else {
            super::server_message::RoomIDWithPassword::new()
        }
    }

    // string roomId = 25;


    pub fn get_roomId(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomId(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_roomId(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomId(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomId(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomId(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomId(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomId(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::roomId(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomId(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::roomId(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomId(&mut self) -> ::std::string::String {
        if self.has_roomId() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::roomId(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.MidiMessageOutputDto midiMessageOutputDto = 26;


    pub fn get_midiMessageOutputDto(&self) -> &super::client_message::MidiMessageOutputDto {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::midiMessageOutputDto(ref v)) => v,
            _ => <super::client_message::MidiMessageOutputDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_midiMessageOutputDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_midiMessageOutputDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::midiMessageOutputDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_midiMessageOutputDto(&mut self, v: super::client_message::MidiMessageOutputDto) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::midiMessageOutputDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_midiMessageOutputDto(&mut self) -> &mut super::client_message::MidiMessageOutputDto {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::midiMessageOutputDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::midiMessageOutputDto(super::client_message::MidiMessageOutputDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::midiMessageOutputDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_midiMessageOutputDto(&mut self) -> super::client_message::MidiMessageOutputDto {
        if self.has_midiMessageOutputDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::midiMessageOutputDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::MidiMessageOutputDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.WelcomeDto welcomeDto = 27;


    pub fn get_welcomeDto(&self) -> &super::client_message::WelcomeDto {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::welcomeDto(ref v)) => v,
            _ => <super::client_message::WelcomeDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_welcomeDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_welcomeDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::welcomeDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_welcomeDto(&mut self, v: super::client_message::WelcomeDto) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::welcomeDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_welcomeDto(&mut self) -> &mut super::client_message::WelcomeDto {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::welcomeDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::welcomeDto(super::client_message::WelcomeDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::welcomeDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_welcomeDto(&mut self) -> super::client_message::WelcomeDto {
        if self.has_welcomeDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::welcomeDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::WelcomeDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.PendingFriendRequestList pendingFriendRequestList = 28;


    pub fn get_pendingFriendRequestList(&self) -> &super::client_message::PendingFriendRequestList {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequestList(ref v)) => v,
            _ => <super::client_message::PendingFriendRequestList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_pendingFriendRequestList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_pendingFriendRequestList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequestList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pendingFriendRequestList(&mut self, v: super::client_message::PendingFriendRequestList) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequestList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_pendingFriendRequestList(&mut self) -> &mut super::client_message::PendingFriendRequestList {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequestList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequestList(super::client_message::PendingFriendRequestList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequestList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_pendingFriendRequestList(&mut self) -> super::client_message::PendingFriendRequestList {
        if self.has_pendingFriendRequestList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequestList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::PendingFriendRequestList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.KickedUsersList kickedUsersList = 29;


    pub fn get_kickedUsersList(&self) -> &super::client_message::KickedUsersList {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::kickedUsersList(ref v)) => v,
            _ => <super::client_message::KickedUsersList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_kickedUsersList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_kickedUsersList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::kickedUsersList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_kickedUsersList(&mut self, v: super::client_message::KickedUsersList) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::kickedUsersList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_kickedUsersList(&mut self) -> &mut super::client_message::KickedUsersList {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::kickedUsersList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::kickedUsersList(super::client_message::KickedUsersList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::kickedUsersList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_kickedUsersList(&mut self) -> super::client_message::KickedUsersList {
        if self.has_kickedUsersList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::kickedUsersList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::KickedUsersList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ClientSideUserDtoList clientSideUserDtoList = 30;


    pub fn get_clientSideUserDtoList(&self) -> &super::client_message::ClientSideUserDtoList {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::clientSideUserDtoList(ref v)) => v,
            _ => <super::client_message::ClientSideUserDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_clientSideUserDtoList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_clientSideUserDtoList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::clientSideUserDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clientSideUserDtoList(&mut self, v: super::client_message::ClientSideUserDtoList) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::clientSideUserDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clientSideUserDtoList(&mut self) -> &mut super::client_message::ClientSideUserDtoList {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::clientSideUserDtoList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::clientSideUserDtoList(super::client_message::ClientSideUserDtoList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::clientSideUserDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clientSideUserDtoList(&mut self) -> super::client_message::ClientSideUserDtoList {
        if self.has_clientSideUserDtoList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::clientSideUserDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::ClientSideUserDtoList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.PendingFriendRequest pendingFriendRequest = 31;


    pub fn get_pendingFriendRequest(&self) -> &super::user_renditions::PendingFriendRequest {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequest(ref v)) => v,
            _ => <super::user_renditions::PendingFriendRequest as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_pendingFriendRequest(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_pendingFriendRequest(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequest(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pendingFriendRequest(&mut self, v: super::user_renditions::PendingFriendRequest) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequest(v))
    }

    // Mutable pointer to the field.
    pub fn mut_pendingFriendRequest(&mut self) -> &mut super::user_renditions::PendingFriendRequest {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequest(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequest(super::user_renditions::PendingFriendRequest::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequest(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_pendingFriendRequest(&mut self) -> super::user_renditions::PendingFriendRequest {
        if self.has_pendingFriendRequest() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequest(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::PendingFriendRequest::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ClientMessage clientMsg = 32;


    pub fn get_clientMsg(&self) -> &super::client_message::ClientMessage {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::clientMsg(ref v)) => v,
            _ => <super::client_message::ClientMessage as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_clientMsg(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_clientMsg(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::clientMsg(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clientMsg(&mut self, v: super::client_message::ClientMessage) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::clientMsg(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clientMsg(&mut self) -> &mut super::client_message::ClientMessage {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::clientMsg(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::clientMsg(super::client_message::ClientMessage::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::clientMsg(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clientMsg(&mut self) -> super::client_message::ClientMessage {
        if self.has_clientMsg() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::clientMsg(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::ClientMessage::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse commandResponse = 33;


    pub fn get_commandResponse(&self) -> &super::client_message::CommandResponse {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::commandResponse(ref v)) => v,
            _ => <super::client_message::CommandResponse as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_commandResponse(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_commandResponse(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::commandResponse(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_commandResponse(&mut self, v: super::client_message::CommandResponse) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::commandResponse(v))
    }

    // Mutable pointer to the field.
    pub fn mut_commandResponse(&mut self) -> &mut super::client_message::CommandResponse {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::commandResponse(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::commandResponse(super::client_message::CommandResponse::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::commandResponse(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_commandResponse(&mut self) -> super::client_message::CommandResponse {
        if self.has_commandResponse() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::commandResponse(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::CommandResponse::new()
        }
    }

    // .PianoRhythm.AppStateActions.AudioSynthActions audioSynthAction = 34;


    pub fn get_audioSynthAction(&self) -> &AudioSynthActions {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::audioSynthAction(ref v)) => v,
            _ => <AudioSynthActions as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_audioSynthAction(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_audioSynthAction(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::audioSynthAction(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_audioSynthAction(&mut self, v: AudioSynthActions) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::audioSynthAction(v))
    }

    // Mutable pointer to the field.
    pub fn mut_audioSynthAction(&mut self) -> &mut AudioSynthActions {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::audioSynthAction(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::audioSynthAction(AudioSynthActions::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::audioSynthAction(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_audioSynthAction(&mut self) -> AudioSynthActions {
        if self.has_audioSynthAction() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::audioSynthAction(v)) => v,
                _ => panic!(),
            }
        } else {
            AudioSynthActions::new()
        }
    }

    // int32 int32Value = 35;


    pub fn get_int32Value(&self) -> i32 {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::int32Value(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_int32Value(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_int32Value(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::int32Value(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_int32Value(&mut self, v: i32) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::int32Value(v))
    }

    // uint32 uint32Value = 36;


    pub fn get_uint32Value(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::uint32Value(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_uint32Value(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_uint32Value(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::uint32Value(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_uint32Value(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::uint32Value(v))
    }

    // .PianoRhythm.AppStateActions.SocketIdWithInt32 socketIdWithIn32 = 37;


    pub fn get_socketIdWithIn32(&self) -> &SocketIdWithInt32 {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::socketIdWithIn32(ref v)) => v,
            _ => <SocketIdWithInt32 as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_socketIdWithIn32(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_socketIdWithIn32(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::socketIdWithIn32(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketIdWithIn32(&mut self, v: SocketIdWithInt32) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::socketIdWithIn32(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketIdWithIn32(&mut self) -> &mut SocketIdWithInt32 {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::socketIdWithIn32(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::socketIdWithIn32(SocketIdWithInt32::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::socketIdWithIn32(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketIdWithIn32(&mut self) -> SocketIdWithInt32 {
        if self.has_socketIdWithIn32() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::socketIdWithIn32(v)) => v,
                _ => panic!(),
            }
        } else {
            SocketIdWithInt32::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.Instrument instrument = 38;


    pub fn get_instrument(&self) -> &super::midi_renditions::Instrument {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::instrument(ref v)) => v,
            _ => <super::midi_renditions::Instrument as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_instrument(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_instrument(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::instrument(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_instrument(&mut self, v: super::midi_renditions::Instrument) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::instrument(v))
    }

    // Mutable pointer to the field.
    pub fn mut_instrument(&mut self) -> &mut super::midi_renditions::Instrument {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::instrument(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::instrument(super::midi_renditions::Instrument::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::instrument(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_instrument(&mut self) -> super::midi_renditions::Instrument {
        if self.has_instrument() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::instrument(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::Instrument::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.InstrumentsList instrumentsList = 39;


    pub fn get_instrumentsList(&self) -> &super::midi_renditions::InstrumentsList {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::instrumentsList(ref v)) => v,
            _ => <super::midi_renditions::InstrumentsList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_instrumentsList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_instrumentsList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::instrumentsList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_instrumentsList(&mut self, v: super::midi_renditions::InstrumentsList) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::instrumentsList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_instrumentsList(&mut self) -> &mut super::midi_renditions::InstrumentsList {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::instrumentsList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::instrumentsList(super::midi_renditions::InstrumentsList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::instrumentsList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_instrumentsList(&mut self) -> super::midi_renditions::InstrumentsList {
        if self.has_instrumentsList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::instrumentsList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::InstrumentsList::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.SetChannelInstrumentPayload setChannelInstrumentPayload = 40;


    pub fn get_setChannelInstrumentPayload(&self) -> &super::midi_renditions::SetChannelInstrumentPayload {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::setChannelInstrumentPayload(ref v)) => v,
            _ => <super::midi_renditions::SetChannelInstrumentPayload as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_setChannelInstrumentPayload(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_setChannelInstrumentPayload(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::setChannelInstrumentPayload(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_setChannelInstrumentPayload(&mut self, v: super::midi_renditions::SetChannelInstrumentPayload) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::setChannelInstrumentPayload(v))
    }

    // Mutable pointer to the field.
    pub fn mut_setChannelInstrumentPayload(&mut self) -> &mut super::midi_renditions::SetChannelInstrumentPayload {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::setChannelInstrumentPayload(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::setChannelInstrumentPayload(super::midi_renditions::SetChannelInstrumentPayload::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::setChannelInstrumentPayload(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_setChannelInstrumentPayload(&mut self) -> super::midi_renditions::SetChannelInstrumentPayload {
        if self.has_setChannelInstrumentPayload() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::setChannelInstrumentPayload(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::SetChannelInstrumentPayload::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.SetChannelDetailsPayload setChannelDetailsPayload = 41;


    pub fn get_setChannelDetailsPayload(&self) -> &super::midi_renditions::SetChannelDetailsPayload {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::setChannelDetailsPayload(ref v)) => v,
            _ => <super::midi_renditions::SetChannelDetailsPayload as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_setChannelDetailsPayload(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_setChannelDetailsPayload(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::setChannelDetailsPayload(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_setChannelDetailsPayload(&mut self, v: super::midi_renditions::SetChannelDetailsPayload) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::setChannelDetailsPayload(v))
    }

    // Mutable pointer to the field.
    pub fn mut_setChannelDetailsPayload(&mut self) -> &mut super::midi_renditions::SetChannelDetailsPayload {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::setChannelDetailsPayload(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::setChannelDetailsPayload(super::midi_renditions::SetChannelDetailsPayload::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::setChannelDetailsPayload(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_setChannelDetailsPayload(&mut self) -> super::midi_renditions::SetChannelDetailsPayload {
        if self.has_setChannelDetailsPayload() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::setChannelDetailsPayload(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::SetChannelDetailsPayload::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.ActiveChannelsMode slotMode = 42;


    pub fn get_slotMode(&self) -> super::midi_renditions::ActiveChannelsMode {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::slotMode(v)) => v,
            _ => super::midi_renditions::ActiveChannelsMode::SINGLE,
        }
    }
    pub fn clear_slotMode(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_slotMode(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::slotMode(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_slotMode(&mut self, v: super::midi_renditions::ActiveChannelsMode) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::slotMode(v))
    }

    // .PianoRhythm.Serialization.Midi.Msgs.UpdateChannelPayload updateChannelPayload = 43;


    pub fn get_updateChannelPayload(&self) -> &super::midi_renditions::UpdateChannelPayload {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::updateChannelPayload(ref v)) => v,
            _ => <super::midi_renditions::UpdateChannelPayload as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_updateChannelPayload(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_updateChannelPayload(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::updateChannelPayload(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_updateChannelPayload(&mut self, v: super::midi_renditions::UpdateChannelPayload) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::updateChannelPayload(v))
    }

    // Mutable pointer to the field.
    pub fn mut_updateChannelPayload(&mut self) -> &mut super::midi_renditions::UpdateChannelPayload {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::updateChannelPayload(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::updateChannelPayload(super::midi_renditions::UpdateChannelPayload::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::updateChannelPayload(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_updateChannelPayload(&mut self) -> super::midi_renditions::UpdateChannelPayload {
        if self.has_updateChannelPayload() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::updateChannelPayload(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::UpdateChannelPayload::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.SynthEventProgramChangePayload synthEventProgramChangePayload = 44;


    pub fn get_synthEventProgramChangePayload(&self) -> &super::midi_renditions::SynthEventProgramChangePayload {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::synthEventProgramChangePayload(ref v)) => v,
            _ => <super::midi_renditions::SynthEventProgramChangePayload as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_synthEventProgramChangePayload(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_synthEventProgramChangePayload(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::synthEventProgramChangePayload(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_synthEventProgramChangePayload(&mut self, v: super::midi_renditions::SynthEventProgramChangePayload) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::synthEventProgramChangePayload(v))
    }

    // Mutable pointer to the field.
    pub fn mut_synthEventProgramChangePayload(&mut self) -> &mut super::midi_renditions::SynthEventProgramChangePayload {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::synthEventProgramChangePayload(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::synthEventProgramChangePayload(super::midi_renditions::SynthEventProgramChangePayload::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::synthEventProgramChangePayload(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_synthEventProgramChangePayload(&mut self) -> super::midi_renditions::SynthEventProgramChangePayload {
        if self.has_synthEventProgramChangePayload() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::synthEventProgramChangePayload(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::SynthEventProgramChangePayload::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.AudioChannel audioChannel = 45;


    pub fn get_audioChannel(&self) -> &super::midi_renditions::AudioChannel {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::audioChannel(ref v)) => v,
            _ => <super::midi_renditions::AudioChannel as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_audioChannel(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_audioChannel(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::audioChannel(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_audioChannel(&mut self, v: super::midi_renditions::AudioChannel) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::audioChannel(v))
    }

    // Mutable pointer to the field.
    pub fn mut_audioChannel(&mut self) -> &mut super::midi_renditions::AudioChannel {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::audioChannel(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::audioChannel(super::midi_renditions::AudioChannel::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::audioChannel(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_audioChannel(&mut self) -> super::midi_renditions::AudioChannel {
        if self.has_audioChannel() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::audioChannel(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::AudioChannel::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppPianoKey appPianoKey = 46;


    pub fn get_appPianoKey(&self) -> &super::pianorhythm_app_renditions::AppPianoKey {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appPianoKey(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppPianoKey as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appPianoKey(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appPianoKey(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appPianoKey(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appPianoKey(&mut self, v: super::pianorhythm_app_renditions::AppPianoKey) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appPianoKey(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appPianoKey(&mut self) -> &mut super::pianorhythm_app_renditions::AppPianoKey {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::appPianoKey(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appPianoKey(super::pianorhythm_app_renditions::AppPianoKey::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appPianoKey(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appPianoKey(&mut self) -> super::pianorhythm_app_renditions::AppPianoKey {
        if self.has_appPianoKey() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::appPianoKey(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppPianoKey::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppPianoPedal appPianoPedal = 47;


    pub fn get_appPianoPedal(&self) -> &super::pianorhythm_app_renditions::AppPianoPedal {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appPianoPedal(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppPianoPedal as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appPianoPedal(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appPianoPedal(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appPianoPedal(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appPianoPedal(&mut self, v: super::pianorhythm_app_renditions::AppPianoPedal) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appPianoPedal(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appPianoPedal(&mut self) -> &mut super::pianorhythm_app_renditions::AppPianoPedal {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::appPianoPedal(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appPianoPedal(super::pianorhythm_app_renditions::AppPianoPedal::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appPianoPedal(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appPianoPedal(&mut self) -> super::pianorhythm_app_renditions::AppPianoPedal {
        if self.has_appPianoPedal() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::appPianoPedal(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppPianoPedal::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppRenderableEntity appRenderableEntity = 48;


    pub fn get_appRenderableEntity(&self) -> &super::pianorhythm_app_renditions::AppRenderableEntity {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appRenderableEntity(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppRenderableEntity as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appRenderableEntity(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appRenderableEntity(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appRenderableEntity(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appRenderableEntity(&mut self, v: super::pianorhythm_app_renditions::AppRenderableEntity) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appRenderableEntity(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appRenderableEntity(&mut self) -> &mut super::pianorhythm_app_renditions::AppRenderableEntity {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::appRenderableEntity(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appRenderableEntity(super::pianorhythm_app_renditions::AppRenderableEntity::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appRenderableEntity(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appRenderableEntity(&mut self) -> super::pianorhythm_app_renditions::AppRenderableEntity {
        if self.has_appRenderableEntity() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::appRenderableEntity(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppRenderableEntity::new()
        }
    }

    // .PianoRhythm.AppStateActions.AppStateActions.RendererLoadMeshDetails loadMeshDetails = 49;


    pub fn get_loadMeshDetails(&self) -> &AppStateActions_RendererLoadMeshDetails {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::loadMeshDetails(ref v)) => v,
            _ => <AppStateActions_RendererLoadMeshDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_loadMeshDetails(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_loadMeshDetails(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::loadMeshDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_loadMeshDetails(&mut self, v: AppStateActions_RendererLoadMeshDetails) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::loadMeshDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_loadMeshDetails(&mut self) -> &mut AppStateActions_RendererLoadMeshDetails {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::loadMeshDetails(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::loadMeshDetails(AppStateActions_RendererLoadMeshDetails::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::loadMeshDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_loadMeshDetails(&mut self) -> AppStateActions_RendererLoadMeshDetails {
        if self.has_loadMeshDetails() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::loadMeshDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            AppStateActions_RendererLoadMeshDetails::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppNotificationConfig appNotificationConfig = 50;


    pub fn get_appNotificationConfig(&self) -> &super::pianorhythm_app_renditions::AppNotificationConfig {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appNotificationConfig(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppNotificationConfig as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appNotificationConfig(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appNotificationConfig(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appNotificationConfig(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appNotificationConfig(&mut self, v: super::pianorhythm_app_renditions::AppNotificationConfig) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appNotificationConfig(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appNotificationConfig(&mut self) -> &mut super::pianorhythm_app_renditions::AppNotificationConfig {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::appNotificationConfig(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appNotificationConfig(super::pianorhythm_app_renditions::AppNotificationConfig::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appNotificationConfig(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appNotificationConfig(&mut self) -> super::pianorhythm_app_renditions::AppNotificationConfig {
        if self.has_appNotificationConfig() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::appNotificationConfig(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppNotificationConfig::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppSettings appSettings = 51;


    pub fn get_appSettings(&self) -> &super::pianorhythm_app_renditions::AppSettings {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appSettings(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppSettings as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appSettings(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appSettings(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appSettings(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appSettings(&mut self, v: super::pianorhythm_app_renditions::AppSettings) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appSettings(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appSettings(&mut self) -> &mut super::pianorhythm_app_renditions::AppSettings {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::appSettings(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appSettings(super::pianorhythm_app_renditions::AppSettings::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appSettings(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appSettings(&mut self) -> super::pianorhythm_app_renditions::AppSettings {
        if self.has_appSettings() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::appSettings(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppSettings::new()
        }
    }

    // .PianoRhythm.AppStateActions.ChannelWithBool channelWithBool = 52;


    pub fn get_channelWithBool(&self) -> &ChannelWithBool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::channelWithBool(ref v)) => v,
            _ => <ChannelWithBool as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_channelWithBool(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_channelWithBool(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::channelWithBool(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_channelWithBool(&mut self, v: ChannelWithBool) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::channelWithBool(v))
    }

    // Mutable pointer to the field.
    pub fn mut_channelWithBool(&mut self) -> &mut ChannelWithBool {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::channelWithBool(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::channelWithBool(ChannelWithBool::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::channelWithBool(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_channelWithBool(&mut self) -> ChannelWithBool {
        if self.has_channelWithBool() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::channelWithBool(v)) => v,
                _ => panic!(),
            }
        } else {
            ChannelWithBool::new()
        }
    }

    // double doubleValue = 53;


    pub fn get_doubleValue(&self) -> f64 {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::doubleValue(v)) => v,
            _ => 0.,
        }
    }
    pub fn clear_doubleValue(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_doubleValue(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::doubleValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_doubleValue(&mut self, v: f64) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::doubleValue(v))
    }

    // .PianoRhythm.AppRenditions.AppVPSequencerFileLoad vpFileLoad = 54;


    pub fn get_vpFileLoad(&self) -> &super::pianorhythm_app_renditions::AppVPSequencerFileLoad {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::vpFileLoad(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppVPSequencerFileLoad as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_vpFileLoad(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_vpFileLoad(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::vpFileLoad(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_vpFileLoad(&mut self, v: super::pianorhythm_app_renditions::AppVPSequencerFileLoad) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::vpFileLoad(v))
    }

    // Mutable pointer to the field.
    pub fn mut_vpFileLoad(&mut self) -> &mut super::pianorhythm_app_renditions::AppVPSequencerFileLoad {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::vpFileLoad(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::vpFileLoad(super::pianorhythm_app_renditions::AppVPSequencerFileLoad::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::vpFileLoad(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_vpFileLoad(&mut self) -> super::pianorhythm_app_renditions::AppVPSequencerFileLoad {
        if self.has_vpFileLoad() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::vpFileLoad(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppVPSequencerFileLoad::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppMidiTrack appMidiTrack = 55;


    pub fn get_appMidiTrack(&self) -> &super::pianorhythm_app_renditions::AppMidiTrack {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appMidiTrack(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppMidiTrack as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appMidiTrack(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appMidiTrack(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appMidiTrack(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appMidiTrack(&mut self, v: super::pianorhythm_app_renditions::AppMidiTrack) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appMidiTrack(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appMidiTrack(&mut self) -> &mut super::pianorhythm_app_renditions::AppMidiTrack {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::appMidiTrack(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appMidiTrack(super::pianorhythm_app_renditions::AppMidiTrack::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appMidiTrack(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appMidiTrack(&mut self) -> super::pianorhythm_app_renditions::AppMidiTrack {
        if self.has_appMidiTrack() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::appMidiTrack(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppMidiTrack::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppCommonEnvironment appCommonEnvironment = 56;


    pub fn get_appCommonEnvironment(&self) -> &super::pianorhythm_app_renditions::AppCommonEnvironment {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appCommonEnvironment(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppCommonEnvironment as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appCommonEnvironment(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appCommonEnvironment(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appCommonEnvironment(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appCommonEnvironment(&mut self, v: super::pianorhythm_app_renditions::AppCommonEnvironment) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appCommonEnvironment(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appCommonEnvironment(&mut self) -> &mut super::pianorhythm_app_renditions::AppCommonEnvironment {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::appCommonEnvironment(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appCommonEnvironment(super::pianorhythm_app_renditions::AppCommonEnvironment::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::appCommonEnvironment(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appCommonEnvironment(&mut self) -> super::pianorhythm_app_renditions::AppCommonEnvironment {
        if self.has_appCommonEnvironment() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::appCommonEnvironment(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppCommonEnvironment::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppKeyboardMappingVisualizeVec keyboardVisualizeMappings = 57;


    pub fn get_keyboardVisualizeMappings(&self) -> &super::pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::keyboardVisualizeMappings(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_keyboardVisualizeMappings(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_keyboardVisualizeMappings(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::keyboardVisualizeMappings(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_keyboardVisualizeMappings(&mut self, v: super::pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::keyboardVisualizeMappings(v))
    }

    // Mutable pointer to the field.
    pub fn mut_keyboardVisualizeMappings(&mut self) -> &mut super::pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::keyboardVisualizeMappings(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::keyboardVisualizeMappings(super::pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::keyboardVisualizeMappings(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_keyboardVisualizeMappings(&mut self) -> super::pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec {
        if self.has_keyboardVisualizeMappings() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::keyboardVisualizeMappings(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.AvatarWorldDataDto.AvatarMessageWorldPosition avatarWorldPosition = 58;


    pub fn get_avatarWorldPosition(&self) -> &super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::avatarWorldPosition(ref v)) => v,
            _ => <super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_avatarWorldPosition(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_avatarWorldPosition(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::avatarWorldPosition(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_avatarWorldPosition(&mut self, v: super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::avatarWorldPosition(v))
    }

    // Mutable pointer to the field.
    pub fn mut_avatarWorldPosition(&mut self) -> &mut super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::avatarWorldPosition(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::avatarWorldPosition(super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::avatarWorldPosition(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_avatarWorldPosition(&mut self) -> super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition {
        if self.has_avatarWorldPosition() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::avatarWorldPosition(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition::new()
        }
    }

    // int32 avatarPianoBench = 59;


    pub fn get_avatarPianoBench(&self) -> i32 {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::avatarPianoBench(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_avatarPianoBench(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_avatarPianoBench(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::avatarPianoBench(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_avatarPianoBench(&mut self, v: i32) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::avatarPianoBench(v))
    }

    // .PianoRhythm.Serialization.WorldRenditions.AvatarCustomizationData avatarCustomizationData = 60;


    pub fn get_avatarCustomizationData(&self) -> &super::world_renditions::AvatarCustomizationData {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::avatarCustomizationData(ref v)) => v,
            _ => <super::world_renditions::AvatarCustomizationData as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_avatarCustomizationData(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_avatarCustomizationData(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::avatarCustomizationData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_avatarCustomizationData(&mut self, v: super::world_renditions::AvatarCustomizationData) {
        self.data = ::std::option::Option::Some(AppStateActions_oneof_data::avatarCustomizationData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_avatarCustomizationData(&mut self) -> &mut super::world_renditions::AvatarCustomizationData {
        if let ::std::option::Option::Some(AppStateActions_oneof_data::avatarCustomizationData(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateActions_oneof_data::avatarCustomizationData(super::world_renditions::AvatarCustomizationData::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateActions_oneof_data::avatarCustomizationData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_avatarCustomizationData(&mut self) -> super::world_renditions::AvatarCustomizationData {
        if self.has_avatarCustomizationData() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateActions_oneof_data::avatarCustomizationData(v)) => v,
                _ => panic!(),
            }
        } else {
            super::world_renditions::AvatarCustomizationData::new()
        }
    }
}

impl ::protobuf::Message for AppStateActions {
    fn is_initialized(&self) -> bool {
        if let Some(AppStateActions_oneof_data::userDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::userClientDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::basicRoomDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::userUpdateCommand(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::userDtoList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::socketIdList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::friendDtoList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::joinedRoomData(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::roomChatHistory(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::chatMessageDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::roomSettings(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::roomFullDetails(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::joinRoomFailResponse(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::roomsList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::joinRoomByNameRequest(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::serverCommand(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::roomOwnerCommand(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::roomIdWithPassword(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::midiMessageOutputDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::welcomeDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::pendingFriendRequestList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::kickedUsersList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::clientSideUserDtoList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::pendingFriendRequest(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::clientMsg(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::commandResponse(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::audioSynthAction(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::socketIdWithIn32(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::instrument(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::instrumentsList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::setChannelInstrumentPayload(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::setChannelDetailsPayload(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::updateChannelPayload(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::synthEventProgramChangePayload(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::audioChannel(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::appPianoKey(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::appPianoPedal(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::appRenderableEntity(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::loadMeshDetails(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::appNotificationConfig(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::appSettings(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::channelWithBool(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::vpFileLoad(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::appMidiTrack(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::appCommonEnvironment(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::keyboardVisualizeMappings(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::avatarWorldPosition(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateActions_oneof_data::avatarCustomizationData(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.action, 1, &mut self.unknown_fields)?
                },
                999 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._sourceSocketID = ::std::option::Option::Some(AppStateActions_oneof__sourceSocketID::sourceSocketID(is.read_string()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::stringValue(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::boolValue(is.read_bool()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userDto(is.read_message()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userClientDto(is.read_message()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::basicRoomDto(is.read_message()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userUpdateCommand(is.read_message()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userDtoList(is.read_message()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::socketIdList(is.read_message()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::friendDtoList(is.read_message()?));
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::socketId(is.read_string()?));
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::usertag(is.read_string()?));
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::userid(is.read_string()?));
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::joinedRoomData(is.read_message()?));
                },
                15 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomChatHistory(is.read_message()?));
                },
                16 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::chatMessageDto(is.read_message()?));
                },
                17 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomSettings(is.read_message()?));
                },
                18 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomFullDetails(is.read_message()?));
                },
                19 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomFailResponse(is.read_message()?));
                },
                20 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomsList(is.read_message()?));
                },
                21 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::joinRoomByNameRequest(is.read_message()?));
                },
                22 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::serverCommand(is.read_message()?));
                },
                23 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomOwnerCommand(is.read_message()?));
                },
                24 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomIdWithPassword(is.read_message()?));
                },
                25 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::roomId(is.read_string()?));
                },
                26 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::midiMessageOutputDto(is.read_message()?));
                },
                27 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::welcomeDto(is.read_message()?));
                },
                28 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequestList(is.read_message()?));
                },
                29 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::kickedUsersList(is.read_message()?));
                },
                30 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::clientSideUserDtoList(is.read_message()?));
                },
                31 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::pendingFriendRequest(is.read_message()?));
                },
                32 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::clientMsg(is.read_message()?));
                },
                33 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::commandResponse(is.read_message()?));
                },
                34 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::audioSynthAction(is.read_message()?));
                },
                35 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::int32Value(is.read_int32()?));
                },
                36 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::uint32Value(is.read_uint32()?));
                },
                37 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::socketIdWithIn32(is.read_message()?));
                },
                38 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::instrument(is.read_message()?));
                },
                39 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::instrumentsList(is.read_message()?));
                },
                40 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::setChannelInstrumentPayload(is.read_message()?));
                },
                41 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::setChannelDetailsPayload(is.read_message()?));
                },
                42 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::slotMode(is.read_enum()?));
                },
                43 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::updateChannelPayload(is.read_message()?));
                },
                44 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::synthEventProgramChangePayload(is.read_message()?));
                },
                45 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::audioChannel(is.read_message()?));
                },
                46 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appPianoKey(is.read_message()?));
                },
                47 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appPianoPedal(is.read_message()?));
                },
                48 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appRenderableEntity(is.read_message()?));
                },
                49 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::loadMeshDetails(is.read_message()?));
                },
                50 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appNotificationConfig(is.read_message()?));
                },
                51 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appSettings(is.read_message()?));
                },
                52 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::channelWithBool(is.read_message()?));
                },
                53 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed64 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::doubleValue(is.read_double()?));
                },
                54 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::vpFileLoad(is.read_message()?));
                },
                55 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appMidiTrack(is.read_message()?));
                },
                56 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::appCommonEnvironment(is.read_message()?));
                },
                57 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::keyboardVisualizeMappings(is.read_message()?));
                },
                58 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::avatarWorldPosition(is.read_message()?));
                },
                59 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::avatarPianoBench(is.read_int32()?));
                },
                60 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateActions_oneof_data::avatarCustomizationData(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.action != AppStateActions_Action::Unknown {
            my_size += ::protobuf::rt::enum_size(1, self.action);
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &AppStateActions_oneof_data::stringValue(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
                &AppStateActions_oneof_data::boolValue(v) => {
                    my_size += 2;
                },
                &AppStateActions_oneof_data::userDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::userClientDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::basicRoomDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::userUpdateCommand(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::userDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::socketIdList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::friendDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::socketId(ref v) => {
                    my_size += ::protobuf::rt::string_size(11, &v);
                },
                &AppStateActions_oneof_data::usertag(ref v) => {
                    my_size += ::protobuf::rt::string_size(12, &v);
                },
                &AppStateActions_oneof_data::userid(ref v) => {
                    my_size += ::protobuf::rt::string_size(13, &v);
                },
                &AppStateActions_oneof_data::joinedRoomData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::roomChatHistory(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::chatMessageDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::roomSettings(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::roomFullDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::joinRoomFailResponse(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::roomsList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::joinRoomByNameRequest(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::serverCommand(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::roomOwnerCommand(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::roomIdWithPassword(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::roomId(ref v) => {
                    my_size += ::protobuf::rt::string_size(25, &v);
                },
                &AppStateActions_oneof_data::midiMessageOutputDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::welcomeDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::pendingFriendRequestList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::kickedUsersList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::clientSideUserDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::pendingFriendRequest(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::clientMsg(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::commandResponse(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::audioSynthAction(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::int32Value(v) => {
                    my_size += ::protobuf::rt::value_size(35, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &AppStateActions_oneof_data::uint32Value(v) => {
                    my_size += ::protobuf::rt::value_size(36, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &AppStateActions_oneof_data::socketIdWithIn32(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::instrument(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::instrumentsList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::setChannelInstrumentPayload(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::setChannelDetailsPayload(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::slotMode(v) => {
                    my_size += ::protobuf::rt::enum_size(42, v);
                },
                &AppStateActions_oneof_data::updateChannelPayload(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::synthEventProgramChangePayload(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::audioChannel(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::appPianoKey(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::appPianoPedal(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::appRenderableEntity(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::loadMeshDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::appNotificationConfig(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::appSettings(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::channelWithBool(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::doubleValue(v) => {
                    my_size += 10;
                },
                &AppStateActions_oneof_data::vpFileLoad(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::appMidiTrack(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::appCommonEnvironment(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::keyboardVisualizeMappings(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::avatarWorldPosition(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateActions_oneof_data::avatarPianoBench(v) => {
                    my_size += ::protobuf::rt::value_size(59, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &AppStateActions_oneof_data::avatarCustomizationData(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._sourceSocketID {
            match v {
                &AppStateActions_oneof__sourceSocketID::sourceSocketID(ref v) => {
                    my_size += ::protobuf::rt::string_size(999, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.action != AppStateActions_Action::Unknown {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.action))?;
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &AppStateActions_oneof_data::stringValue(ref v) => {
                    os.write_string(2, v)?;
                },
                &AppStateActions_oneof_data::boolValue(v) => {
                    os.write_bool(3, v)?;
                },
                &AppStateActions_oneof_data::userDto(ref v) => {
                    os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::userClientDto(ref v) => {
                    os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::basicRoomDto(ref v) => {
                    os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::userUpdateCommand(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::userDtoList(ref v) => {
                    os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::socketIdList(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::friendDtoList(ref v) => {
                    os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::socketId(ref v) => {
                    os.write_string(11, v)?;
                },
                &AppStateActions_oneof_data::usertag(ref v) => {
                    os.write_string(12, v)?;
                },
                &AppStateActions_oneof_data::userid(ref v) => {
                    os.write_string(13, v)?;
                },
                &AppStateActions_oneof_data::joinedRoomData(ref v) => {
                    os.write_tag(14, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::roomChatHistory(ref v) => {
                    os.write_tag(15, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::chatMessageDto(ref v) => {
                    os.write_tag(16, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::roomSettings(ref v) => {
                    os.write_tag(17, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::roomFullDetails(ref v) => {
                    os.write_tag(18, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::joinRoomFailResponse(ref v) => {
                    os.write_tag(19, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::roomsList(ref v) => {
                    os.write_tag(20, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::joinRoomByNameRequest(ref v) => {
                    os.write_tag(21, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::serverCommand(ref v) => {
                    os.write_tag(22, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::roomOwnerCommand(ref v) => {
                    os.write_tag(23, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::roomIdWithPassword(ref v) => {
                    os.write_tag(24, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::roomId(ref v) => {
                    os.write_string(25, v)?;
                },
                &AppStateActions_oneof_data::midiMessageOutputDto(ref v) => {
                    os.write_tag(26, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::welcomeDto(ref v) => {
                    os.write_tag(27, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::pendingFriendRequestList(ref v) => {
                    os.write_tag(28, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::kickedUsersList(ref v) => {
                    os.write_tag(29, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::clientSideUserDtoList(ref v) => {
                    os.write_tag(30, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::pendingFriendRequest(ref v) => {
                    os.write_tag(31, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::clientMsg(ref v) => {
                    os.write_tag(32, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::commandResponse(ref v) => {
                    os.write_tag(33, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::audioSynthAction(ref v) => {
                    os.write_tag(34, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::int32Value(v) => {
                    os.write_int32(35, v)?;
                },
                &AppStateActions_oneof_data::uint32Value(v) => {
                    os.write_uint32(36, v)?;
                },
                &AppStateActions_oneof_data::socketIdWithIn32(ref v) => {
                    os.write_tag(37, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::instrument(ref v) => {
                    os.write_tag(38, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::instrumentsList(ref v) => {
                    os.write_tag(39, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::setChannelInstrumentPayload(ref v) => {
                    os.write_tag(40, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::setChannelDetailsPayload(ref v) => {
                    os.write_tag(41, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::slotMode(v) => {
                    os.write_enum(42, ::protobuf::ProtobufEnum::value(&v))?;
                },
                &AppStateActions_oneof_data::updateChannelPayload(ref v) => {
                    os.write_tag(43, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::synthEventProgramChangePayload(ref v) => {
                    os.write_tag(44, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::audioChannel(ref v) => {
                    os.write_tag(45, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::appPianoKey(ref v) => {
                    os.write_tag(46, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::appPianoPedal(ref v) => {
                    os.write_tag(47, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::appRenderableEntity(ref v) => {
                    os.write_tag(48, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::loadMeshDetails(ref v) => {
                    os.write_tag(49, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::appNotificationConfig(ref v) => {
                    os.write_tag(50, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::appSettings(ref v) => {
                    os.write_tag(51, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::channelWithBool(ref v) => {
                    os.write_tag(52, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::doubleValue(v) => {
                    os.write_double(53, v)?;
                },
                &AppStateActions_oneof_data::vpFileLoad(ref v) => {
                    os.write_tag(54, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::appMidiTrack(ref v) => {
                    os.write_tag(55, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::appCommonEnvironment(ref v) => {
                    os.write_tag(56, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::keyboardVisualizeMappings(ref v) => {
                    os.write_tag(57, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::avatarWorldPosition(ref v) => {
                    os.write_tag(58, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateActions_oneof_data::avatarPianoBench(v) => {
                    os.write_int32(59, v)?;
                },
                &AppStateActions_oneof_data::avatarCustomizationData(ref v) => {
                    os.write_tag(60, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._sourceSocketID {
            match v {
                &AppStateActions_oneof__sourceSocketID::sourceSocketID(ref v) => {
                    os.write_string(999, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AppStateActions {
        AppStateActions::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<AppStateActions_Action>>(
                "action",
                |m: &AppStateActions| { &m.action },
                |m: &mut AppStateActions| { &mut m.action },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "sourceSocketID",
                AppStateActions::has_sourceSocketID,
                AppStateActions::get_sourceSocketID,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "stringValue",
                AppStateActions::has_stringValue,
                AppStateActions::get_stringValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "boolValue",
                AppStateActions::has_boolValue,
                AppStateActions::get_boolValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::UserDto>(
                "userDto",
                AppStateActions::has_userDto,
                AppStateActions::get_userDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::UserClientDto>(
                "userClientDto",
                AppStateActions::has_userClientDto,
                AppStateActions::get_userClientDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::BasicRoomDto>(
                "basicRoomDto",
                AppStateActions::has_basicRoomDto,
                AppStateActions::get_basicRoomDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::UserUpdateCommand>(
                "userUpdateCommand",
                AppStateActions::has_userUpdateCommand,
                AppStateActions::get_userUpdateCommand,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::UserDtoList>(
                "userDtoList",
                AppStateActions::has_userDtoList,
                AppStateActions::get_userDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::SocketIdList>(
                "socketIdList",
                AppStateActions::has_socketIdList,
                AppStateActions::get_socketIdList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::FriendDtoList>(
                "friendDtoList",
                AppStateActions::has_friendDtoList,
                AppStateActions::get_friendDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "socketId",
                AppStateActions::has_socketId,
                AppStateActions::get_socketId,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "usertag",
                AppStateActions::has_usertag,
                AppStateActions::get_usertag,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "userid",
                AppStateActions::has_userid,
                AppStateActions::get_userid,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::JoinedRoomData>(
                "joinedRoomData",
                AppStateActions::has_joinedRoomData,
                AppStateActions::get_joinedRoomData,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::RoomChatHistory>(
                "roomChatHistory",
                AppStateActions::has_roomChatHistory,
                AppStateActions::get_roomChatHistory,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::ChatMessageDto>(
                "chatMessageDto",
                AppStateActions::has_chatMessageDto,
                AppStateActions::get_chatMessageDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::RoomSettings>(
                "roomSettings",
                AppStateActions::has_roomSettings,
                AppStateActions::get_roomSettings,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::RoomFullDetails>(
                "roomFullDetails",
                AppStateActions::has_roomFullDetails,
                AppStateActions::get_roomFullDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::CommandResponse_JoinRoomFailResponse>(
                "joinRoomFailResponse",
                AppStateActions::has_joinRoomFailResponse,
                AppStateActions::get_joinRoomFailResponse,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::CommandResponse_RoomsList>(
                "roomsList",
                AppStateActions::has_roomsList,
                AppStateActions::get_roomsList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::server_message::ServerMessage_JoinRoomByNameRequest>(
                "joinRoomByNameRequest",
                AppStateActions::has_joinRoomByNameRequest,
                AppStateActions::get_joinRoomByNameRequest,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::server_message::ServerCommandDU>(
                "serverCommand",
                AppStateActions::has_serverCommand,
                AppStateActions::get_serverCommand,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::server_message::RoomOwnerCommandDU>(
                "roomOwnerCommand",
                AppStateActions::has_roomOwnerCommand,
                AppStateActions::get_roomOwnerCommand,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::server_message::RoomIDWithPassword>(
                "roomIdWithPassword",
                AppStateActions::has_roomIdWithPassword,
                AppStateActions::get_roomIdWithPassword,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "roomId",
                AppStateActions::has_roomId,
                AppStateActions::get_roomId,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::MidiMessageOutputDto>(
                "midiMessageOutputDto",
                AppStateActions::has_midiMessageOutputDto,
                AppStateActions::get_midiMessageOutputDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::WelcomeDto>(
                "welcomeDto",
                AppStateActions::has_welcomeDto,
                AppStateActions::get_welcomeDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::PendingFriendRequestList>(
                "pendingFriendRequestList",
                AppStateActions::has_pendingFriendRequestList,
                AppStateActions::get_pendingFriendRequestList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::KickedUsersList>(
                "kickedUsersList",
                AppStateActions::has_kickedUsersList,
                AppStateActions::get_kickedUsersList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::ClientSideUserDtoList>(
                "clientSideUserDtoList",
                AppStateActions::has_clientSideUserDtoList,
                AppStateActions::get_clientSideUserDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::PendingFriendRequest>(
                "pendingFriendRequest",
                AppStateActions::has_pendingFriendRequest,
                AppStateActions::get_pendingFriendRequest,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::ClientMessage>(
                "clientMsg",
                AppStateActions::has_clientMsg,
                AppStateActions::get_clientMsg,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::CommandResponse>(
                "commandResponse",
                AppStateActions::has_commandResponse,
                AppStateActions::get_commandResponse,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AudioSynthActions>(
                "audioSynthAction",
                AppStateActions::has_audioSynthAction,
                AppStateActions::get_audioSynthAction,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "int32Value",
                AppStateActions::has_int32Value,
                AppStateActions::get_int32Value,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "uint32Value",
                AppStateActions::has_uint32Value,
                AppStateActions::get_uint32Value,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, SocketIdWithInt32>(
                "socketIdWithIn32",
                AppStateActions::has_socketIdWithIn32,
                AppStateActions::get_socketIdWithIn32,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::Instrument>(
                "instrument",
                AppStateActions::has_instrument,
                AppStateActions::get_instrument,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::InstrumentsList>(
                "instrumentsList",
                AppStateActions::has_instrumentsList,
                AppStateActions::get_instrumentsList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::SetChannelInstrumentPayload>(
                "setChannelInstrumentPayload",
                AppStateActions::has_setChannelInstrumentPayload,
                AppStateActions::get_setChannelInstrumentPayload,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::SetChannelDetailsPayload>(
                "setChannelDetailsPayload",
                AppStateActions::has_setChannelDetailsPayload,
                AppStateActions::get_setChannelDetailsPayload,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_enum_accessor::<_, super::midi_renditions::ActiveChannelsMode>(
                "slotMode",
                AppStateActions::has_slotMode,
                AppStateActions::get_slotMode,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::UpdateChannelPayload>(
                "updateChannelPayload",
                AppStateActions::has_updateChannelPayload,
                AppStateActions::get_updateChannelPayload,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::SynthEventProgramChangePayload>(
                "synthEventProgramChangePayload",
                AppStateActions::has_synthEventProgramChangePayload,
                AppStateActions::get_synthEventProgramChangePayload,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::AudioChannel>(
                "audioChannel",
                AppStateActions::has_audioChannel,
                AppStateActions::get_audioChannel,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppPianoKey>(
                "appPianoKey",
                AppStateActions::has_appPianoKey,
                AppStateActions::get_appPianoKey,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppPianoPedal>(
                "appPianoPedal",
                AppStateActions::has_appPianoPedal,
                AppStateActions::get_appPianoPedal,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppRenderableEntity>(
                "appRenderableEntity",
                AppStateActions::has_appRenderableEntity,
                AppStateActions::get_appRenderableEntity,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AppStateActions_RendererLoadMeshDetails>(
                "loadMeshDetails",
                AppStateActions::has_loadMeshDetails,
                AppStateActions::get_loadMeshDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppNotificationConfig>(
                "appNotificationConfig",
                AppStateActions::has_appNotificationConfig,
                AppStateActions::get_appNotificationConfig,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppSettings>(
                "appSettings",
                AppStateActions::has_appSettings,
                AppStateActions::get_appSettings,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ChannelWithBool>(
                "channelWithBool",
                AppStateActions::has_channelWithBool,
                AppStateActions::get_channelWithBool,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_f64_accessor::<_>(
                "doubleValue",
                AppStateActions::has_doubleValue,
                AppStateActions::get_doubleValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppVPSequencerFileLoad>(
                "vpFileLoad",
                AppStateActions::has_vpFileLoad,
                AppStateActions::get_vpFileLoad,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppMidiTrack>(
                "appMidiTrack",
                AppStateActions::has_appMidiTrack,
                AppStateActions::get_appMidiTrack,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppCommonEnvironment>(
                "appCommonEnvironment",
                AppStateActions::has_appCommonEnvironment,
                AppStateActions::get_appCommonEnvironment,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppKeyboardMappingVisualizeVec>(
                "keyboardVisualizeMappings",
                AppStateActions::has_keyboardVisualizeMappings,
                AppStateActions::get_keyboardVisualizeMappings,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition>(
                "avatarWorldPosition",
                AppStateActions::has_avatarWorldPosition,
                AppStateActions::get_avatarWorldPosition,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "avatarPianoBench",
                AppStateActions::has_avatarPianoBench,
                AppStateActions::get_avatarPianoBench,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::world_renditions::AvatarCustomizationData>(
                "avatarCustomizationData",
                AppStateActions::has_avatarCustomizationData,
                AppStateActions::get_avatarCustomizationData,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AppStateActions>(
                "AppStateActions",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AppStateActions {
        static instance: ::protobuf::rt::LazyV2<AppStateActions> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AppStateActions::new)
    }
}

impl ::protobuf::Clear for AppStateActions {
    fn clear(&mut self) {
        self.action = AppStateActions_Action::Unknown;
        self._sourceSocketID = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AppStateActions {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AppStateActions {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AppStateActions_RendererLoadMeshDetails {
    // message fields
    pub filePath: ::std::string::String,
    pub meshName: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AppStateActions_RendererLoadMeshDetails {
    fn default() -> &'a AppStateActions_RendererLoadMeshDetails {
        <AppStateActions_RendererLoadMeshDetails as ::protobuf::Message>::default_instance()
    }
}

impl AppStateActions_RendererLoadMeshDetails {
    pub fn new() -> AppStateActions_RendererLoadMeshDetails {
        ::std::default::Default::default()
    }

    // string filePath = 1;


    pub fn get_filePath(&self) -> &str {
        &self.filePath
    }
    pub fn clear_filePath(&mut self) {
        self.filePath.clear();
    }

    // Param is passed by value, moved
    pub fn set_filePath(&mut self, v: ::std::string::String) {
        self.filePath = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_filePath(&mut self) -> &mut ::std::string::String {
        &mut self.filePath
    }

    // Take field
    pub fn take_filePath(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.filePath, ::std::string::String::new())
    }

    // string meshName = 2;


    pub fn get_meshName(&self) -> &str {
        &self.meshName
    }
    pub fn clear_meshName(&mut self) {
        self.meshName.clear();
    }

    // Param is passed by value, moved
    pub fn set_meshName(&mut self, v: ::std::string::String) {
        self.meshName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_meshName(&mut self) -> &mut ::std::string::String {
        &mut self.meshName
    }

    // Take field
    pub fn take_meshName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.meshName, ::std::string::String::new())
    }
}

impl ::protobuf::Message for AppStateActions_RendererLoadMeshDetails {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.filePath)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.meshName)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.filePath.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.filePath);
        }
        if !self.meshName.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.meshName);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.filePath.is_empty() {
            os.write_string(1, &self.filePath)?;
        }
        if !self.meshName.is_empty() {
            os.write_string(2, &self.meshName)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AppStateActions_RendererLoadMeshDetails {
        AppStateActions_RendererLoadMeshDetails::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "filePath",
                |m: &AppStateActions_RendererLoadMeshDetails| { &m.filePath },
                |m: &mut AppStateActions_RendererLoadMeshDetails| { &mut m.filePath },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "meshName",
                |m: &AppStateActions_RendererLoadMeshDetails| { &m.meshName },
                |m: &mut AppStateActions_RendererLoadMeshDetails| { &mut m.meshName },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AppStateActions_RendererLoadMeshDetails>(
                "AppStateActions.RendererLoadMeshDetails",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AppStateActions_RendererLoadMeshDetails {
        static instance: ::protobuf::rt::LazyV2<AppStateActions_RendererLoadMeshDetails> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AppStateActions_RendererLoadMeshDetails::new)
    }
}

impl ::protobuf::Clear for AppStateActions_RendererLoadMeshDetails {
    fn clear(&mut self) {
        self.filePath.clear();
        self.meshName.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AppStateActions_RendererLoadMeshDetails {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AppStateActions_RendererLoadMeshDetails {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum AppStateActions_Action {
    Unknown = 0,
    DisableUI = 87,
    ResetState = 998,
    EnableUI = 1,
    SetCanvasLoaded = 2,
    SetCurrentRoomName = 3,
    SetCurrentRoomOwner = 4,
    SetLoggedIn = 5,
    SetMaintenanceModeActive = 6,
    SetClientLoaded = 7,
    UpdateClient = 8,
    UpdateClientByCommand = 9,
    UpdateUser = 10,
    AddUser = 11,
    RemoveUser = 12,
    SetUsers = 13,
    JoinedRoom = 14,
    FailToJoinRoom = 15,
    AddRoom = 16,
    UpdateRoom = 17,
    DeleteRoom = 18,
    SetRooms = 19,
    SetUsersTyping = 20,
    JoinRoomByName = 21,
    JoinRoomById = 22,
    JoinNextAvailableLobby = 23,
    EmitToast = 24,
    EnterRoomPassword = 25,
    WebsocketDisconnected = 26,
    ServerToClientMessage = 27,
    AddRoomChatMessage = 28,
    EditRoomChatMessage = 29,
    DeleteRoomChatMessage = 30,
    SetMaintenanceMode = 31,
    SetRoomChatMessages = 32,
    SetRoomWelcomeMessage = 33,
    SetRoomSettings = 34,
    ClearChat = 35,
    ClearChatByMessageID = 36,
    ClearChatByUsername = 37,
    ClearChatByAmount = 38,
    ClearChatBySocketID = 39,
    ServerCommandResponse = 40,
    GetRoomFullDetails = 41,
    HandleMidiMessage = 42,
    MuteEveryoneElse = 43,
    SetServerTimeOffset = 44,
    SynthAction = 45,
    AddHashedSynthUser = 46,
    RemoveHashedSynthUser = 47,
    InitializeAudioState = 48,
    SetLoadedInstruments = 49,
    SetRoomIsSelfHosted = 50,
    SetChannelActive = 51,
    SetInstrumentOnChannel = 52,
    SetPrimaryChannel = 53,
    SetMaxMultiModeChannels = 54,
    SetIsDrumChannelMuted = 55,
    SetSlotMode = 56,
    SetUseSeparateDrumKit = 57,
    SetOutputOwnNotesToOutput = 58,
    SetUseDefaultBankWhenMissing = 59,
    RemoveInstrumentFromChannel = 60,
    ClearAllAudioChannels = 61,
    IncrementSlotMode = 62,
    ResetAudioChannelsToDefault = 63,
    ToggleChannelActive = 64,
    SetClientIsMuted = 65,
    SetListenToProgramChanges = 66,
    UpdateChannelParameter = 67,
    SetIsPlayingDrumsMode = 68,
    SetMousePositionSetsVelocity = 69,
    SetIsMobile = 70,
    SetCanPlayKeys = 71,
    UpdateChannelFromSynthProgramChangeEvent = 72,
    SetUserVolume = 73,
    SetUserVelocityPercentage = 74,
    SetEqualizerEnabled = 76,
    SetReverbEnabled = 77,
    SetAudioChannel = 78,
    SetRoomStageLoading = 79,
    SetRoomStageLoaded = 80,
    PersistSettings = 82,
    InitializeAppState = 83,
    SetAppSettings = 84,
    Logout = 85,
    TriggerOfflineMode = 86,
    SetConnectionState = 88,
    SetUserMuted = 89,
    SetUserChatMuted = 90,
    SetCommonEnvironment = 91,
    AudioSetApplyVelocityCurve = 92,
    SynthEngineCreated = 93,
    RendererPianoKeyModelLoaded = 1000,
    RendererPianoPedalModelLoaded = 1001,
    RendererMainCameraLoaded = 1002,
    RendererLoadMesh = 1003,
    RendererDrumSetMeshLoaded = 1004,
    RendererResetCamera = 1005,
    RendererToggleLockCamera = 1006,
    RendererEnableRenderLoop = 1007,
    RendererDisableRenderLoop = 1008,
    RendererSetKeyboardMappings = 1009,
    RendererToggleDisplayKeyboardMappings = 1010,
    RendererSetCameraTopPosition = 1011,
    MidiSequencerResume = 2000,
    MidiSequencerPause = 2001,
    MidiSequencerStop = 2002,
    MidiSequencerEnableLoop = 2003,
    MidiSequencerDisableLoop = 2004,
    MidiSequencerSeekPosition = 2005,
    MidiSequencerMuteTrack = 2006,
    MidiSequencerSoloTrack = 2007,
    MidiSequencerSetSpeed = 2008,
    MidiSequencerSetBPM = 2009,
    MidiSequencerRewind = 2010,
    MidiSequencerForward = 2011,
    MidiSequencerEnablePreviewOnly = 2012,
    MidiSequencerDisablePreviewOnly = 2013,
    VPSequencerLoadData = 3000,
    VPSequencerResume = 3001,
    VPSequencerPause = 3002,
    VPSequencerStop = 3003,
    VPSequencerEnableLoop = 3004,
    VPSequencerDisableLoop = 3005,
    VPSequencerDownloadAsMidi = 3006,
    VPSequencerEnableSustain = 3007,
    VPSequencerDisableSustain = 3008,
    VPSequencerSetPlaybackSpeed = 3009,
    VPSequencerSetBPM = 3010,
    AppMidiLooperRecord = 4000,
    AppMidiLooperPlay = 4001,
    AppMidiLooperStop = 4002,
    AppMidiLooperSetBPM = 4003,
    AppMidiLooperDispose = 4004,
    AppMidiLooperStopRecord = 4005,
    AppMidiLooperEnableTrim = 4006,
    AppMidiLooperDisableTrim = 4007,
    AppMidiLooperSetTrack = 4008,
    AppMidiLooperGetTracks = 4009,
    AppMidiLooperClearTrack = 4010,
    AppMidiLooperToggleTrackPlaying = 4011,
    SetAvatarPosition = 5000,
    SetAvatarPianoBench = 5001,
    UpdateAvatarCustomization = 5002,
    ShowAvatarCustomizationScreen = 6000,
    HideAvatarCustomizationScreen = 6001,
}

impl ::protobuf::ProtobufEnum for AppStateActions_Action {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<AppStateActions_Action> {
        match value {
            0 => ::std::option::Option::Some(AppStateActions_Action::Unknown),
            87 => ::std::option::Option::Some(AppStateActions_Action::DisableUI),
            998 => ::std::option::Option::Some(AppStateActions_Action::ResetState),
            1 => ::std::option::Option::Some(AppStateActions_Action::EnableUI),
            2 => ::std::option::Option::Some(AppStateActions_Action::SetCanvasLoaded),
            3 => ::std::option::Option::Some(AppStateActions_Action::SetCurrentRoomName),
            4 => ::std::option::Option::Some(AppStateActions_Action::SetCurrentRoomOwner),
            5 => ::std::option::Option::Some(AppStateActions_Action::SetLoggedIn),
            6 => ::std::option::Option::Some(AppStateActions_Action::SetMaintenanceModeActive),
            7 => ::std::option::Option::Some(AppStateActions_Action::SetClientLoaded),
            8 => ::std::option::Option::Some(AppStateActions_Action::UpdateClient),
            9 => ::std::option::Option::Some(AppStateActions_Action::UpdateClientByCommand),
            10 => ::std::option::Option::Some(AppStateActions_Action::UpdateUser),
            11 => ::std::option::Option::Some(AppStateActions_Action::AddUser),
            12 => ::std::option::Option::Some(AppStateActions_Action::RemoveUser),
            13 => ::std::option::Option::Some(AppStateActions_Action::SetUsers),
            14 => ::std::option::Option::Some(AppStateActions_Action::JoinedRoom),
            15 => ::std::option::Option::Some(AppStateActions_Action::FailToJoinRoom),
            16 => ::std::option::Option::Some(AppStateActions_Action::AddRoom),
            17 => ::std::option::Option::Some(AppStateActions_Action::UpdateRoom),
            18 => ::std::option::Option::Some(AppStateActions_Action::DeleteRoom),
            19 => ::std::option::Option::Some(AppStateActions_Action::SetRooms),
            20 => ::std::option::Option::Some(AppStateActions_Action::SetUsersTyping),
            21 => ::std::option::Option::Some(AppStateActions_Action::JoinRoomByName),
            22 => ::std::option::Option::Some(AppStateActions_Action::JoinRoomById),
            23 => ::std::option::Option::Some(AppStateActions_Action::JoinNextAvailableLobby),
            24 => ::std::option::Option::Some(AppStateActions_Action::EmitToast),
            25 => ::std::option::Option::Some(AppStateActions_Action::EnterRoomPassword),
            26 => ::std::option::Option::Some(AppStateActions_Action::WebsocketDisconnected),
            27 => ::std::option::Option::Some(AppStateActions_Action::ServerToClientMessage),
            28 => ::std::option::Option::Some(AppStateActions_Action::AddRoomChatMessage),
            29 => ::std::option::Option::Some(AppStateActions_Action::EditRoomChatMessage),
            30 => ::std::option::Option::Some(AppStateActions_Action::DeleteRoomChatMessage),
            31 => ::std::option::Option::Some(AppStateActions_Action::SetMaintenanceMode),
            32 => ::std::option::Option::Some(AppStateActions_Action::SetRoomChatMessages),
            33 => ::std::option::Option::Some(AppStateActions_Action::SetRoomWelcomeMessage),
            34 => ::std::option::Option::Some(AppStateActions_Action::SetRoomSettings),
            35 => ::std::option::Option::Some(AppStateActions_Action::ClearChat),
            36 => ::std::option::Option::Some(AppStateActions_Action::ClearChatByMessageID),
            37 => ::std::option::Option::Some(AppStateActions_Action::ClearChatByUsername),
            38 => ::std::option::Option::Some(AppStateActions_Action::ClearChatByAmount),
            39 => ::std::option::Option::Some(AppStateActions_Action::ClearChatBySocketID),
            40 => ::std::option::Option::Some(AppStateActions_Action::ServerCommandResponse),
            41 => ::std::option::Option::Some(AppStateActions_Action::GetRoomFullDetails),
            42 => ::std::option::Option::Some(AppStateActions_Action::HandleMidiMessage),
            43 => ::std::option::Option::Some(AppStateActions_Action::MuteEveryoneElse),
            44 => ::std::option::Option::Some(AppStateActions_Action::SetServerTimeOffset),
            45 => ::std::option::Option::Some(AppStateActions_Action::SynthAction),
            46 => ::std::option::Option::Some(AppStateActions_Action::AddHashedSynthUser),
            47 => ::std::option::Option::Some(AppStateActions_Action::RemoveHashedSynthUser),
            48 => ::std::option::Option::Some(AppStateActions_Action::InitializeAudioState),
            49 => ::std::option::Option::Some(AppStateActions_Action::SetLoadedInstruments),
            50 => ::std::option::Option::Some(AppStateActions_Action::SetRoomIsSelfHosted),
            51 => ::std::option::Option::Some(AppStateActions_Action::SetChannelActive),
            52 => ::std::option::Option::Some(AppStateActions_Action::SetInstrumentOnChannel),
            53 => ::std::option::Option::Some(AppStateActions_Action::SetPrimaryChannel),
            54 => ::std::option::Option::Some(AppStateActions_Action::SetMaxMultiModeChannels),
            55 => ::std::option::Option::Some(AppStateActions_Action::SetIsDrumChannelMuted),
            56 => ::std::option::Option::Some(AppStateActions_Action::SetSlotMode),
            57 => ::std::option::Option::Some(AppStateActions_Action::SetUseSeparateDrumKit),
            58 => ::std::option::Option::Some(AppStateActions_Action::SetOutputOwnNotesToOutput),
            59 => ::std::option::Option::Some(AppStateActions_Action::SetUseDefaultBankWhenMissing),
            60 => ::std::option::Option::Some(AppStateActions_Action::RemoveInstrumentFromChannel),
            61 => ::std::option::Option::Some(AppStateActions_Action::ClearAllAudioChannels),
            62 => ::std::option::Option::Some(AppStateActions_Action::IncrementSlotMode),
            63 => ::std::option::Option::Some(AppStateActions_Action::ResetAudioChannelsToDefault),
            64 => ::std::option::Option::Some(AppStateActions_Action::ToggleChannelActive),
            65 => ::std::option::Option::Some(AppStateActions_Action::SetClientIsMuted),
            66 => ::std::option::Option::Some(AppStateActions_Action::SetListenToProgramChanges),
            67 => ::std::option::Option::Some(AppStateActions_Action::UpdateChannelParameter),
            68 => ::std::option::Option::Some(AppStateActions_Action::SetIsPlayingDrumsMode),
            69 => ::std::option::Option::Some(AppStateActions_Action::SetMousePositionSetsVelocity),
            70 => ::std::option::Option::Some(AppStateActions_Action::SetIsMobile),
            71 => ::std::option::Option::Some(AppStateActions_Action::SetCanPlayKeys),
            72 => ::std::option::Option::Some(AppStateActions_Action::UpdateChannelFromSynthProgramChangeEvent),
            73 => ::std::option::Option::Some(AppStateActions_Action::SetUserVolume),
            74 => ::std::option::Option::Some(AppStateActions_Action::SetUserVelocityPercentage),
            76 => ::std::option::Option::Some(AppStateActions_Action::SetEqualizerEnabled),
            77 => ::std::option::Option::Some(AppStateActions_Action::SetReverbEnabled),
            78 => ::std::option::Option::Some(AppStateActions_Action::SetAudioChannel),
            79 => ::std::option::Option::Some(AppStateActions_Action::SetRoomStageLoading),
            80 => ::std::option::Option::Some(AppStateActions_Action::SetRoomStageLoaded),
            82 => ::std::option::Option::Some(AppStateActions_Action::PersistSettings),
            83 => ::std::option::Option::Some(AppStateActions_Action::InitializeAppState),
            84 => ::std::option::Option::Some(AppStateActions_Action::SetAppSettings),
            85 => ::std::option::Option::Some(AppStateActions_Action::Logout),
            86 => ::std::option::Option::Some(AppStateActions_Action::TriggerOfflineMode),
            88 => ::std::option::Option::Some(AppStateActions_Action::SetConnectionState),
            89 => ::std::option::Option::Some(AppStateActions_Action::SetUserMuted),
            90 => ::std::option::Option::Some(AppStateActions_Action::SetUserChatMuted),
            91 => ::std::option::Option::Some(AppStateActions_Action::SetCommonEnvironment),
            92 => ::std::option::Option::Some(AppStateActions_Action::AudioSetApplyVelocityCurve),
            93 => ::std::option::Option::Some(AppStateActions_Action::SynthEngineCreated),
            1000 => ::std::option::Option::Some(AppStateActions_Action::RendererPianoKeyModelLoaded),
            1001 => ::std::option::Option::Some(AppStateActions_Action::RendererPianoPedalModelLoaded),
            1002 => ::std::option::Option::Some(AppStateActions_Action::RendererMainCameraLoaded),
            1003 => ::std::option::Option::Some(AppStateActions_Action::RendererLoadMesh),
            1004 => ::std::option::Option::Some(AppStateActions_Action::RendererDrumSetMeshLoaded),
            1005 => ::std::option::Option::Some(AppStateActions_Action::RendererResetCamera),
            1006 => ::std::option::Option::Some(AppStateActions_Action::RendererToggleLockCamera),
            1007 => ::std::option::Option::Some(AppStateActions_Action::RendererEnableRenderLoop),
            1008 => ::std::option::Option::Some(AppStateActions_Action::RendererDisableRenderLoop),
            1009 => ::std::option::Option::Some(AppStateActions_Action::RendererSetKeyboardMappings),
            1010 => ::std::option::Option::Some(AppStateActions_Action::RendererToggleDisplayKeyboardMappings),
            1011 => ::std::option::Option::Some(AppStateActions_Action::RendererSetCameraTopPosition),
            2000 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerResume),
            2001 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerPause),
            2002 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerStop),
            2003 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerEnableLoop),
            2004 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerDisableLoop),
            2005 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerSeekPosition),
            2006 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerMuteTrack),
            2007 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerSoloTrack),
            2008 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerSetSpeed),
            2009 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerSetBPM),
            2010 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerRewind),
            2011 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerForward),
            2012 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerEnablePreviewOnly),
            2013 => ::std::option::Option::Some(AppStateActions_Action::MidiSequencerDisablePreviewOnly),
            3000 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerLoadData),
            3001 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerResume),
            3002 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerPause),
            3003 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerStop),
            3004 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerEnableLoop),
            3005 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerDisableLoop),
            3006 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerDownloadAsMidi),
            3007 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerEnableSustain),
            3008 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerDisableSustain),
            3009 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerSetPlaybackSpeed),
            3010 => ::std::option::Option::Some(AppStateActions_Action::VPSequencerSetBPM),
            4000 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperRecord),
            4001 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperPlay),
            4002 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperStop),
            4003 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperSetBPM),
            4004 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperDispose),
            4005 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperStopRecord),
            4006 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperEnableTrim),
            4007 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperDisableTrim),
            4008 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperSetTrack),
            4009 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperGetTracks),
            4010 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperClearTrack),
            4011 => ::std::option::Option::Some(AppStateActions_Action::AppMidiLooperToggleTrackPlaying),
            5000 => ::std::option::Option::Some(AppStateActions_Action::SetAvatarPosition),
            5001 => ::std::option::Option::Some(AppStateActions_Action::SetAvatarPianoBench),
            5002 => ::std::option::Option::Some(AppStateActions_Action::UpdateAvatarCustomization),
            6000 => ::std::option::Option::Some(AppStateActions_Action::ShowAvatarCustomizationScreen),
            6001 => ::std::option::Option::Some(AppStateActions_Action::HideAvatarCustomizationScreen),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [AppStateActions_Action] = &[
            AppStateActions_Action::Unknown,
            AppStateActions_Action::DisableUI,
            AppStateActions_Action::ResetState,
            AppStateActions_Action::EnableUI,
            AppStateActions_Action::SetCanvasLoaded,
            AppStateActions_Action::SetCurrentRoomName,
            AppStateActions_Action::SetCurrentRoomOwner,
            AppStateActions_Action::SetLoggedIn,
            AppStateActions_Action::SetMaintenanceModeActive,
            AppStateActions_Action::SetClientLoaded,
            AppStateActions_Action::UpdateClient,
            AppStateActions_Action::UpdateClientByCommand,
            AppStateActions_Action::UpdateUser,
            AppStateActions_Action::AddUser,
            AppStateActions_Action::RemoveUser,
            AppStateActions_Action::SetUsers,
            AppStateActions_Action::JoinedRoom,
            AppStateActions_Action::FailToJoinRoom,
            AppStateActions_Action::AddRoom,
            AppStateActions_Action::UpdateRoom,
            AppStateActions_Action::DeleteRoom,
            AppStateActions_Action::SetRooms,
            AppStateActions_Action::SetUsersTyping,
            AppStateActions_Action::JoinRoomByName,
            AppStateActions_Action::JoinRoomById,
            AppStateActions_Action::JoinNextAvailableLobby,
            AppStateActions_Action::EmitToast,
            AppStateActions_Action::EnterRoomPassword,
            AppStateActions_Action::WebsocketDisconnected,
            AppStateActions_Action::ServerToClientMessage,
            AppStateActions_Action::AddRoomChatMessage,
            AppStateActions_Action::EditRoomChatMessage,
            AppStateActions_Action::DeleteRoomChatMessage,
            AppStateActions_Action::SetMaintenanceMode,
            AppStateActions_Action::SetRoomChatMessages,
            AppStateActions_Action::SetRoomWelcomeMessage,
            AppStateActions_Action::SetRoomSettings,
            AppStateActions_Action::ClearChat,
            AppStateActions_Action::ClearChatByMessageID,
            AppStateActions_Action::ClearChatByUsername,
            AppStateActions_Action::ClearChatByAmount,
            AppStateActions_Action::ClearChatBySocketID,
            AppStateActions_Action::ServerCommandResponse,
            AppStateActions_Action::GetRoomFullDetails,
            AppStateActions_Action::HandleMidiMessage,
            AppStateActions_Action::MuteEveryoneElse,
            AppStateActions_Action::SetServerTimeOffset,
            AppStateActions_Action::SynthAction,
            AppStateActions_Action::AddHashedSynthUser,
            AppStateActions_Action::RemoveHashedSynthUser,
            AppStateActions_Action::InitializeAudioState,
            AppStateActions_Action::SetLoadedInstruments,
            AppStateActions_Action::SetRoomIsSelfHosted,
            AppStateActions_Action::SetChannelActive,
            AppStateActions_Action::SetInstrumentOnChannel,
            AppStateActions_Action::SetPrimaryChannel,
            AppStateActions_Action::SetMaxMultiModeChannels,
            AppStateActions_Action::SetIsDrumChannelMuted,
            AppStateActions_Action::SetSlotMode,
            AppStateActions_Action::SetUseSeparateDrumKit,
            AppStateActions_Action::SetOutputOwnNotesToOutput,
            AppStateActions_Action::SetUseDefaultBankWhenMissing,
            AppStateActions_Action::RemoveInstrumentFromChannel,
            AppStateActions_Action::ClearAllAudioChannels,
            AppStateActions_Action::IncrementSlotMode,
            AppStateActions_Action::ResetAudioChannelsToDefault,
            AppStateActions_Action::ToggleChannelActive,
            AppStateActions_Action::SetClientIsMuted,
            AppStateActions_Action::SetListenToProgramChanges,
            AppStateActions_Action::UpdateChannelParameter,
            AppStateActions_Action::SetIsPlayingDrumsMode,
            AppStateActions_Action::SetMousePositionSetsVelocity,
            AppStateActions_Action::SetIsMobile,
            AppStateActions_Action::SetCanPlayKeys,
            AppStateActions_Action::UpdateChannelFromSynthProgramChangeEvent,
            AppStateActions_Action::SetUserVolume,
            AppStateActions_Action::SetUserVelocityPercentage,
            AppStateActions_Action::SetEqualizerEnabled,
            AppStateActions_Action::SetReverbEnabled,
            AppStateActions_Action::SetAudioChannel,
            AppStateActions_Action::SetRoomStageLoading,
            AppStateActions_Action::SetRoomStageLoaded,
            AppStateActions_Action::PersistSettings,
            AppStateActions_Action::InitializeAppState,
            AppStateActions_Action::SetAppSettings,
            AppStateActions_Action::Logout,
            AppStateActions_Action::TriggerOfflineMode,
            AppStateActions_Action::SetConnectionState,
            AppStateActions_Action::SetUserMuted,
            AppStateActions_Action::SetUserChatMuted,
            AppStateActions_Action::SetCommonEnvironment,
            AppStateActions_Action::AudioSetApplyVelocityCurve,
            AppStateActions_Action::SynthEngineCreated,
            AppStateActions_Action::RendererPianoKeyModelLoaded,
            AppStateActions_Action::RendererPianoPedalModelLoaded,
            AppStateActions_Action::RendererMainCameraLoaded,
            AppStateActions_Action::RendererLoadMesh,
            AppStateActions_Action::RendererDrumSetMeshLoaded,
            AppStateActions_Action::RendererResetCamera,
            AppStateActions_Action::RendererToggleLockCamera,
            AppStateActions_Action::RendererEnableRenderLoop,
            AppStateActions_Action::RendererDisableRenderLoop,
            AppStateActions_Action::RendererSetKeyboardMappings,
            AppStateActions_Action::RendererToggleDisplayKeyboardMappings,
            AppStateActions_Action::RendererSetCameraTopPosition,
            AppStateActions_Action::MidiSequencerResume,
            AppStateActions_Action::MidiSequencerPause,
            AppStateActions_Action::MidiSequencerStop,
            AppStateActions_Action::MidiSequencerEnableLoop,
            AppStateActions_Action::MidiSequencerDisableLoop,
            AppStateActions_Action::MidiSequencerSeekPosition,
            AppStateActions_Action::MidiSequencerMuteTrack,
            AppStateActions_Action::MidiSequencerSoloTrack,
            AppStateActions_Action::MidiSequencerSetSpeed,
            AppStateActions_Action::MidiSequencerSetBPM,
            AppStateActions_Action::MidiSequencerRewind,
            AppStateActions_Action::MidiSequencerForward,
            AppStateActions_Action::MidiSequencerEnablePreviewOnly,
            AppStateActions_Action::MidiSequencerDisablePreviewOnly,
            AppStateActions_Action::VPSequencerLoadData,
            AppStateActions_Action::VPSequencerResume,
            AppStateActions_Action::VPSequencerPause,
            AppStateActions_Action::VPSequencerStop,
            AppStateActions_Action::VPSequencerEnableLoop,
            AppStateActions_Action::VPSequencerDisableLoop,
            AppStateActions_Action::VPSequencerDownloadAsMidi,
            AppStateActions_Action::VPSequencerEnableSustain,
            AppStateActions_Action::VPSequencerDisableSustain,
            AppStateActions_Action::VPSequencerSetPlaybackSpeed,
            AppStateActions_Action::VPSequencerSetBPM,
            AppStateActions_Action::AppMidiLooperRecord,
            AppStateActions_Action::AppMidiLooperPlay,
            AppStateActions_Action::AppMidiLooperStop,
            AppStateActions_Action::AppMidiLooperSetBPM,
            AppStateActions_Action::AppMidiLooperDispose,
            AppStateActions_Action::AppMidiLooperStopRecord,
            AppStateActions_Action::AppMidiLooperEnableTrim,
            AppStateActions_Action::AppMidiLooperDisableTrim,
            AppStateActions_Action::AppMidiLooperSetTrack,
            AppStateActions_Action::AppMidiLooperGetTracks,
            AppStateActions_Action::AppMidiLooperClearTrack,
            AppStateActions_Action::AppMidiLooperToggleTrackPlaying,
            AppStateActions_Action::SetAvatarPosition,
            AppStateActions_Action::SetAvatarPianoBench,
            AppStateActions_Action::UpdateAvatarCustomization,
            AppStateActions_Action::ShowAvatarCustomizationScreen,
            AppStateActions_Action::HideAvatarCustomizationScreen,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<AppStateActions_Action>("AppStateActions.Action", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for AppStateActions_Action {
}

impl ::std::default::Default for AppStateActions_Action {
    fn default() -> Self {
        AppStateActions_Action::Unknown
    }
}

impl ::protobuf::reflect::ProtobufValue for AppStateActions_Action {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x19pianorhythm-actions.proto\x12\x1bPianoRhythm.AppStateActions\x1a\
    \x15room-renditions.proto\x1a\x15user-renditions.proto\x1a\x14client-mes\
    sage.proto\x1a\x14server-message.proto\x1a\x15midi-renditions.proto\x1a\
    \x20pianorhythm-app-renditions.proto\x1a\x16world-renditions.proto\"O\n\
    \x11SocketIdWithInt32\x12\x1a\n\x08socketId\x18\x01\x20\x01(\tR\x08socke\
    tId\x12\x1e\n\nint32Value\x18\x02\x20\x01(\rR\nint32Value\"I\n\x0fChanne\
    lWithBool\x12\x18\n\x07channel\x18\x01\x20\x01(\rR\x07channel\x12\x1c\n\
    \tboolValue\x18\x02\x20\x01(\x08R\tboolValue\"O\n\x11ChannelWithUint32\
    \x12\x18\n\x07channel\x18\x01\x20\x01(\rR\x07channel\x12\x20\n\x0buint32\
    Value\x18\x02\x20\x01(\rR\x0buint32Value\"\xb5\x01\n\x14AudioSynthAction\
    Data\x12\x18\n\x07channel\x18\x01\x20\x01(\rR\x07channel\x12\x12\n\x04no\
    te\x18\x02\x20\x01(\rR\x04note\x12\x1a\n\x08velocity\x18\x03\x20\x01(\rR\
    \x08velocity\x12S\n\nnoteSource\x18\x04\x20\x01(\x0e23.PianoRhythm.Seria\
    lization.Midi.Msgs.MidiNoteSourceR\nnoteSource\"\xd1\x07\n\x11AudioSynth\
    Actions\x12M\n\x06action\x18\x01\x20\x01(\x0e25.PianoRhythm.AppStateActi\
    ons.AudioSynthActions.ActionR\x06action\x12,\n\x0esourceSocketID\x18\xe7\
    \x07\x20\x01(\tH\x01R\x0esourceSocketID\x88\x01\x01\x128\n\x14sourceHash\
    edSocketID\x18\xe6\x07\x20\x01(\x04H\x02R\x14sourceHashedSocketID\x88\
    \x01\x01\x12\x1c\n\x08socketId\x18\x02\x20\x01(\tH\0R\x08socketId\x12\
    \x1a\n\x07channel\x18\x03\x20\x01(\rH\0R\x07channel\x12X\n\x0fchannelWit\
    hBool\x18\x04\x20\x01(\x0b2,.PianoRhythm.AppStateActions.ChannelWithBool\
    H\0R\x0fchannelWithBool\x12Q\n\ninstrument\x18\x05\x20\x01(\x0b2/.PianoR\
    hythm.Serialization.Midi.Msgs.InstrumentH\0R\ninstrument\x12\"\n\x0buint\
    32Value\x18\x06\x20\x01(\rH\0R\x0buint32Value\x12`\n\x0finstrumentsList\
    \x18\x07\x20\x01(\x0b24.PianoRhythm.Serialization.Midi.Msgs.InstrumentsL\
    istH\0R\x0finstrumentsList\x12\x1e\n\tboolValue\x18\x08\x20\x01(\x08H\0R\
    \tboolValue\x12^\n\x11channelWithUint32\x18\t\x20\x01(\x0b2..PianoRhythm\
    .AppStateActions.ChannelWithUint32H\0R\x11channelWithUint32\x12Q\n\tsynt\
    hData\x18\n\x20\x01(\x0b21.PianoRhythm.AppStateActions.AudioSynthActionD\
    ataH\0R\tsynthData\"\x90\x01\n\x06Action\x12\n\n\x06NoteOn\x10\0\x12\x0b\
    \n\x07NoteOff\x10\x01\x12\x0b\n\x07AddUser\x10\x02\x12\x0e\n\nRemoveUser\
    \x10\x03\x12\r\n\tAddClient\x10\x04\x12\x14\n\x10SetChannelVolume\x10\
    \x05\x12\x11\n\rSetChannelPan\x10\x06\x12\x18\n\x14SetChannelExpression\
    \x10\x07B\x06\n\x04dataB\x11\n\x0f_sourceSocketIDB\x17\n\x15_sourceHashe\
    dSocketID\"\xfbJ\n\x0fAppStateActions\x12K\n\x06action\x18\x01\x20\x01(\
    \x0e23.PianoRhythm.AppStateActions.AppStateActions.ActionR\x06action\x12\
    ,\n\x0esourceSocketID\x18\xe7\x07\x20\x01(\tH\x01R\x0esourceSocketID\x88\
    \x01\x01\x12\"\n\x0bstringValue\x18\x02\x20\x01(\tH\0R\x0bstringValue\
    \x12\x1e\n\tboolValue\x18\x03\x20\x01(\x08H\0R\tboolValue\x12\\\n\x07use\
    rDto\x18\x04\x20\x01(\<EMAIL>\
    rRenditions.UserDtoH\0R\x07userDto\x12n\n\ruserClientDto\x18\x05\x20\x01\
    (\x0b2F.PianoRhythm.Serialization.ServerToClient.UserRenditions.UserClie\
    ntDtoH\0R\ruserClientDto\x12\\\n\x0cbasicRoomDto\x18\x06\x20\x01(\x0b26.\
    PianoRhythm.Serialization.RoomRenditions.BasicRoomDtoH\0R\x0cbasicRoomDt\
    o\x12z\n\x11userUpdateCommand\x18\x07\x20\x01(\x0b2J.PianoRhythm.Seriali\
    zation.ServerToClient.UserRenditions.UserUpdateCommandH\0R\x11userUpdate\
    Command\x12^\n\x0buserDtoList\x18\x08\x20\x01(\x0b2:.PianoRhythm.Seriali\
    zation.ServerToClient.Msgs.UserDtoListH\0R\x0buserDtoList\x12a\n\x0csock\
    etIdList\x18\t\x20\x01(\x0b2;.PianoRhythm.Serialization.ServerToClient.M\
    sgs.SocketIdListH\0R\x0csocketIdList\x12d\n\rfriendDtoList\x18\n\x20\x01\
    (\x0b2<.PianoRhythm.Serialization.ServerToClient.Msgs.FriendDtoListH\0R\
    \rfriendDtoList\x12\x1c\n\x08socketId\x18\x0b\x20\x01(\tH\0R\x08socketId\
    \x12\x1a\n\x07usertag\x18\x0c\x20\x01(\tH\0R\x07usertag\x12\x18\n\x06use\
    rid\x18\r\x20\x01(\tH\0R\x06userid\x12g\n\x0ejoinedRoomData\x18\x0e\x20\
    \x01(\x0b2=.PianoRhythm.Serialization.ServerToClient.Msgs.JoinedRoomData\
    H\0R\x0ejoinedRoomData\x12j\n\x0froomChatHistory\x18\x0f\x20\x01(\x0b2>.\
    PianoRhythm.Serialization.ServerToClient.Msgs.RoomChatHistoryH\0R\x0froo\
    mChatHistory\x12g\n\x0echatMessageDto\x18\x10\x20\x01(\x0b2=.PianoRhythm\
    .Serialization.ServerToClient.Msgs.ChatMessageDtoH\0R\x0echatMessageDto\
    \x12\\\n\x0croomSettings\x18\x11\x20\x01(\x0b26.PianoRhythm.Serializatio\
    n.RoomRenditions.RoomSettingsH\0R\x0croomSettings\x12e\n\x0froomFullDeta\
    ils\x18\x12\x20\x01(\x0b29.PianoRhythm.Serialization.RoomRenditions.Room\
    FullDetailsH\0R\x0froomFullDetails\x12\x89\x01\n\x14joinRoomFailResponse\
    \x18\x13\x20\x01(\x0b2S.PianoRhythm.Serialization.ServerToClient.Msgs.Co\
    mmandResponse.JoinRoomFailResponseH\0R\x14joinRoomFailResponse\x12h\n\tr\
    oomsList\x18\x14\x20\x01(\x0b2H.PianoRhythm.Serialization.ServerToClient\
    .Msgs.CommandResponse.RoomsListH\0R\troomsList\x12\x8a\x01\n\x15joinRoom\
    ByNameRequest\x18\x15\x20\x01(\x0b2R.PianoRhythm.Serialization.ClientToS\
    erver.Msgs.ServerMessage.JoinRoomByNameRequestH\0R\x15joinRoomByNameRequ\
    est\x12f\n\rserverCommand\x18\x16\x20\x01(\x0b2>.PianoRhythm.Serializati\
    on.ClientToServer.Msgs.ServerCommandDUH\0R\rserverCommand\x12o\n\x10room\
    OwnerCommand\x18\x17\x20\x01(\x0b2A.PianoRhythm.Serialization.ClientToSe\
    rver.Msgs.RoomOwnerCommandDUH\0R\x10roomOwnerCommand\x12s\n\x12roomIdWit\
    hPassword\x18\x18\x20\x01(\x0b2A.PianoRhythm.Serialization.ClientToServe\
    r.Msgs.RoomIDWithPasswordH\0R\x12roomIdWithPassword\x12\x18\n\x06roomId\
    \x18\x19\x20\x01(\tH\0R\x06roomId\x12y\n\x14midiMessageOutputDto\x18\x1a\
    \x20\x01(\x0b2C.PianoRhythm.Serialization.ServerToClient.Msgs.MidiMessag\
    eOutputDtoH\0R\x14midiMessageOutputDto\x12[\n\nwelcomeDto\x18\x1b\x20\
    \x01(\x0b29.PianoRhythm.Serialization.ServerToClient.Msgs.WelcomeDtoH\0R\
    \nwelcomeDto\x12\x85\x01\n\x18pendingFriendRequestList\x18\x1c\x20\x01(\
    \x0b2G.PianoRhythm.Serialization.ServerToClient.Msgs.PendingFriendReques\
    tListH\0R\x18pendingFriendRequestList\x12j\n\x0fkickedUsersList\x18\x1d\
    \x20\x01(\x0b2>.PianoRhythm.Serialization.ServerToClient.Msgs.KickedUser\
    sListH\0R\x0fkickedUsersList\x12|\n\x15clientSideUserDtoList\x18\x1e\x20\
    \x01(\x0b2D.PianoRhythm.Serialization.ServerToClient.Msgs.ClientSideUser\
    DtoListH\0R\x15clientSideUserDtoList\x12\x83\x01\n\x14pendingFriendReque\
    st\x18\x1f\x20\x01(\x0b2M.PianoRhythm.Serialization.ServerToClient.UserR\
    enditions.PendingFriendRequestH\0R\x14pendingFriendRequest\x12\\\n\tclie\
    ntMsg\x18\x20\x20\x01(\x0b2<.PianoRhythm.Serialization.ServerToClient.Ms\
    gs.ClientMessageH\0R\tclientMsg\x12j\n\x0fcommandResponse\x18!\x20\x01(\
    \x0b2>.PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponseH\0R\
    \x0fcommandResponse\x12\\\n\x10audioSynthAction\x18\"\x20\x01(\x0b2..Pia\
    noRhythm.AppStateActions.AudioSynthActionsH\0R\x10audioSynthAction\x12\
    \x20\n\nint32Value\x18#\x20\x01(\x05H\0R\nint32Value\x12\"\n\x0buint32Va\
    lue\x18$\x20\x01(\rH\0R\x0buint32Value\x12\\\n\x10socketIdWithIn32\x18%\
    \x20\x01(\x0b2..PianoRhythm.AppStateActions.SocketIdWithInt32H\0R\x10soc\
    ketIdWithIn32\x12Q\n\ninstrument\x18&\x20\x01(\x0b2/.PianoRhythm.Seriali\
    zation.Midi.Msgs.InstrumentH\0R\ninstrument\x12`\n\x0finstrumentsList\
    \x18'\x20\x01(\x0b24.PianoRhythm.Serialization.Midi.Msgs.InstrumentsList\
    H\0R\x0finstrumentsList\x12\x84\x01\n\x1bsetChannelInstrumentPayload\x18\
    (\x20\x01(\<EMAIL>\
    tPayloadH\0R\x1bsetChannelInstrumentPayload\x12{\n\x18setChannelDetailsP\
    ayload\x18)\x20\x01(\x0b2=.PianoRhythm.Serialization.Midi.Msgs.SetChanne\
    lDetailsPayloadH\0R\x18setChannelDetailsPayload\x12U\n\x08slotMode\x18*\
    \x20\x01(\x0e27.PianoRhythm.Serialization.Midi.Msgs.ActiveChannelsModeH\
    \0R\x08slotMode\x12o\n\x14updateChannelPayload\x18+\x20\x01(\x0b29.Piano\
    Rhythm.Serialization.Midi.Msgs.UpdateChannelPayloadH\0R\x14updateChannel\
    Payload\x12\x8d\x01\n\x1esynthEventProgramChangePayload\x18,\x20\x01(\
    \x0b2C.PianoRhythm.Serialization.Midi.Msgs.SynthEventProgramChangePayloa\
    dH\0R\x1esynthEventProgramChangePayload\x12W\n\x0caudioChannel\x18-\x20\
    \x01(\x0b21.PianoRhythm.Serialization.Midi.Msgs.AudioChannelH\0R\x0caudi\
    oChannel\x12J\n\x0bappPianoKey\x18.\x20\x01(\x0b2&.PianoRhythm.AppRendit\
    ions.AppPianoKeyH\0R\x0bappPianoKey\x12P\n\rappPianoPedal\x18/\x20\x01(\
    \x0b2(.PianoRhythm.AppRenditions.AppPianoPedalH\0R\rappPianoPedal\x12b\n\
    \x13appRenderableEntity\x180\x20\x01(\x0b2..PianoRhythm.AppRenditions.Ap\
    pRenderableEntityH\0R\x13appRenderableEntity\x12p\n\x0floadMeshDetails\
    \x181\x20\x01(\x0b2D.PianoRhythm.AppStateActions.AppStateActions.Rendere\
    rLoadMeshDetailsH\0R\x0floadMeshDetails\x12h\n\x15appNotificationConfig\
    \x182\x20\x01(\x0b20.PianoRhythm.AppRenditions.AppNotificationConfigH\0R\
    \x15appNotificationConfig\x12J\n\x0bappSettings\x183\x20\x01(\x0b2&.Pian\
    oRhythm.AppRenditions.AppSettingsH\0R\x0bappSettings\x12X\n\x0fchannelWi\
    thBool\x184\x20\x01(\x0b2,.PianoRhythm.AppStateActions.ChannelWithBoolH\
    \0R\x0fchannelWithBool\x12\"\n\x0bdoubleValue\x185\x20\x01(\x01H\0R\x0bd\
    oubleValue\x12S\n\nvpFileLoad\x186\x20\x01(\x0b21.PianoRhythm.AppRenditi\
    ons.AppVPSequencerFileLoadH\0R\nvpFileLoad\x12M\n\x0cappMidiTrack\x187\
    \x20\x01(\x0b2'.PianoRhythm.AppRenditions.AppMidiTrackH\0R\x0cappMidiTra\
    ck\x12e\n\x14appCommonEnvironment\x188\x20\x01(\x0b2/.PianoRhythm.AppRen\
    ditions.AppCommonEnvironmentH\0R\x14appCommonEnvironment\x12y\n\x19keybo\
    ardVisualizeMappings\x189\x20\x01(\x0b29.PianoRhythm.AppRenditions.AppKe\
    yboardMappingVisualizeVecH\0R\x19keyboardVisualizeMappings\x12\x9a\x01\n\
    \x13avatarWorldPosition\x18:\x20\x01(\x0b2f.PianoRhythm.Serialization.Se\
    rverToClient.UserRenditions.AvatarWorldDataDto.AvatarMessageWorldPositio\
    nH\0R\x13avatarWorldPosition\x12,\n\x10avatarPianoBench\x18;\x20\x01(\
    \x05H\0R\x10avatarPianoBench\x12~\n\x17avatarCustomizationData\x18<\x20\
    \x01(\x0b2B.PianoRhythm.Serialization.WorldRenditions.AvatarCustomizatio\
    nDataH\0R\x17avatarCustomizationData\x1aQ\n\x17RendererLoadMeshDetails\
    \x12\x1a\n\x08filePath\x18\x01\x20\x01(\tR\x08filePath\x12\x1a\n\x08mesh\
    Name\x18\x02\x20\x01(\tR\x08meshName\"\xa8\x1d\n\x06Action\x12\x0b\n\x07\
    Unknown\x10\0\x12\r\n\tDisableUI\x10W\x12\x0f\n\nResetState\x10\xe6\x07\
    \x12\x0c\n\x08EnableUI\x10\x01\x12\x13\n\x0fSetCanvasLoaded\x10\x02\x12\
    \x16\n\x12SetCurrentRoomName\x10\x03\x12\x17\n\x13SetCurrentRoomOwner\
    \x10\x04\x12\x0f\n\x0bSetLoggedIn\x10\x05\x12\x1c\n\x18SetMaintenanceMod\
    eActive\x10\x06\x12\x13\n\x0fSetClientLoaded\x10\x07\x12\x10\n\x0cUpdate\
    Client\x10\x08\x12\x19\n\x15UpdateClientByCommand\x10\t\x12\x0e\n\nUpdat\
    eUser\x10\n\x12\x0b\n\x07AddUser\x10\x0b\x12\x0e\n\nRemoveUser\x10\x0c\
    \x12\x0c\n\x08SetUsers\x10\r\x12\x0e\n\nJoinedRoom\x10\x0e\x12\x12\n\x0e\
    FailToJoinRoom\x10\x0f\x12\x0b\n\x07AddRoom\x10\x10\x12\x0e\n\nUpdateRoo\
    m\x10\x11\x12\x0e\n\nDeleteRoom\x10\x12\x12\x0c\n\x08SetRooms\x10\x13\
    \x12\x12\n\x0eSetUsersTyping\x10\x14\x12\x12\n\x0eJoinRoomByName\x10\x15\
    \x12\x10\n\x0cJoinRoomById\x10\x16\x12\x1a\n\x16JoinNextAvailableLobby\
    \x10\x17\x12\r\n\tEmitToast\x10\x18\x12\x15\n\x11EnterRoomPassword\x10\
    \x19\x12\x19\n\x15WebsocketDisconnected\x10\x1a\x12\x19\n\x15ServerToCli\
    entMessage\x10\x1b\x12\x16\n\x12AddRoomChatMessage\x10\x1c\x12\x17\n\x13\
    EditRoomChatMessage\x10\x1d\x12\x19\n\x15DeleteRoomChatMessage\x10\x1e\
    \x12\x16\n\x12SetMaintenanceMode\x10\x1f\x12\x17\n\x13SetRoomChatMessage\
    s\x10\x20\x12\x19\n\x15SetRoomWelcomeMessage\x10!\x12\x13\n\x0fSetRoomSe\
    ttings\x10\"\x12\r\n\tClearChat\x10#\x12\x18\n\x14ClearChatByMessageID\
    \x10$\x12\x17\n\x13ClearChatByUsername\x10%\x12\x15\n\x11ClearChatByAmou\
    nt\x10&\x12\x17\n\x13ClearChatBySocketID\x10'\x12\x19\n\x15ServerCommand\
    Response\x10(\x12\x16\n\x12GetRoomFullDetails\x10)\x12\x15\n\x11HandleMi\
    diMessage\x10*\x12\x14\n\x10MuteEveryoneElse\x10+\x12\x17\n\x13SetServer\
    TimeOffset\x10,\x12\x0f\n\x0bSynthAction\x10-\x12\x16\n\x12AddHashedSynt\
    hUser\x10.\x12\x19\n\x15RemoveHashedSynthUser\x10/\x12\x18\n\x14Initiali\
    zeAudioState\x100\x12\x18\n\x14SetLoadedInstruments\x101\x12\x17\n\x13Se\
    tRoomIsSelfHosted\x102\x12\x14\n\x10SetChannelActive\x103\x12\x1a\n\x16S\
    etInstrumentOnChannel\x104\x12\x15\n\x11SetPrimaryChannel\x105\x12\x1b\n\
    \x17SetMaxMultiModeChannels\x106\x12\x19\n\x15SetIsDrumChannelMuted\x107\
    \x12\x0f\n\x0bSetSlotMode\x108\x12\x19\n\x15SetUseSeparateDrumKit\x109\
    \x12\x1d\n\x19SetOutputOwnNotesToOutput\x10:\x12\x20\n\x1cSetUseDefaultB\
    ankWhenMissing\x10;\x12\x1f\n\x1bRemoveInstrumentFromChannel\x10<\x12\
    \x19\n\x15ClearAllAudioChannels\x10=\x12\x15\n\x11IncrementSlotMode\x10>\
    \x12\x1f\n\x1bResetAudioChannelsToDefault\x10?\x12\x17\n\x13ToggleChanne\
    lActive\x10@\x12\x14\n\x10SetClientIsMuted\x10A\x12\x1d\n\x19SetListenTo\
    ProgramChanges\x10B\x12\x1a\n\x16UpdateChannelParameter\x10C\x12\x19\n\
    \x15SetIsPlayingDrumsMode\x10D\x12\x20\n\x1cSetMousePositionSetsVelocity\
    \x10E\x12\x0f\n\x0bSetIsMobile\x10F\x12\x12\n\x0eSetCanPlayKeys\x10G\x12\
    ,\n(UpdateChannelFromSynthProgramChangeEvent\x10H\x12\x11\n\rSetUserVolu\
    me\x10I\x12\x1d\n\x19SetUserVelocityPercentage\x10J\x12\x17\n\x13SetEqua\
    lizerEnabled\x10L\x12\x14\n\x10SetReverbEnabled\x10M\x12\x13\n\x0fSetAud\
    ioChannel\x10N\x12\x17\n\x13SetRoomStageLoading\x10O\x12\x16\n\x12SetRoo\
    mStageLoaded\x10P\x12\x13\n\x0fPersistSettings\x10R\x12\x16\n\x12Initial\
    izeAppState\x10S\x12\x12\n\x0eSetAppSettings\x10T\x12\n\n\x06Logout\x10U\
    \x12\x16\n\x12TriggerOfflineMode\x10V\x12\x16\n\x12SetConnectionState\
    \x10X\x12\x10\n\x0cSetUserMuted\x10Y\x12\x14\n\x10SetUserChatMuted\x10Z\
    \x12\x18\n\x14SetCommonEnvironment\x10[\x12\x1e\n\x1aAudioSetApplyVeloci\
    tyCurve\x10\\\x12\x16\n\x12SynthEngineCreated\x10]\x12\x20\n\x1bRenderer\
    PianoKeyModelLoaded\x10\xe8\x07\x12\"\n\x1dRendererPianoPedalModelLoaded\
    \x10\xe9\x07\x12\x1d\n\x18RendererMainCameraLoaded\x10\xea\x07\x12\x15\n\
    \x10RendererLoadMesh\x10\xeb\x07\x12\x1e\n\x19RendererDrumSetMeshLoaded\
    \x10\xec\x07\x12\x18\n\x13RendererResetCamera\x10\xed\x07\x12\x1d\n\x18R\
    endererToggleLockCamera\x10\xee\x07\x12\x1d\n\x18RendererEnableRenderLoo\
    p\x10\xef\x07\x12\x1e\n\x19RendererDisableRenderLoop\x10\xf0\x07\x12\x20\
    \n\x1bRendererSetKeyboardMappings\x10\xf1\x07\x12*\n%RendererToggleDispl\
    ayKeyboardMappings\x10\xf2\x07\x12!\n\x1cRendererSetCameraTopPosition\
    \x10\xf3\x07\x12\x18\n\x13MidiSequencerResume\x10\xd0\x0f\x12\x17\n\x12M\
    idiSequencerPause\x10\xd1\x0f\x12\x16\n\x11MidiSequencerStop\x10\xd2\x0f\
    \x12\x1c\n\x17MidiSequencerEnableLoop\x10\xd3\x0f\x12\x1d\n\x18MidiSeque\
    ncerDisableLoop\x10\xd4\x0f\x12\x1e\n\x19MidiSequencerSeekPosition\x10\
    \xd5\x0f\x12\x1b\n\x16MidiSequencerMuteTrack\x10\xd6\x0f\x12\x1b\n\x16Mi\
    diSequencerSoloTrack\x10\xd7\x0f\x12\x1a\n\x15MidiSequencerSetSpeed\x10\
    \xd8\x0f\x12\x18\n\x13MidiSequencerSetBPM\x10\xd9\x0f\x12\x18\n\x13MidiS\
    equencerRewind\x10\xda\x0f\x12\x19\n\x14MidiSequencerForward\x10\xdb\x0f\
    \x12#\n\x1eMidiSequencerEnablePreviewOnly\x10\xdc\x0f\x12$\n\x1fMidiSequ\
    encerDisablePreviewOnly\x10\xdd\x0f\x12\x18\n\x13VPSequencerLoadData\x10\
    \xb8\x17\x12\x16\n\x11VPSequencerResume\x10\xb9\x17\x12\x15\n\x10VPSeque\
    ncerPause\x10\xba\x17\x12\x14\n\x0fVPSequencerStop\x10\xbb\x17\x12\x1a\n\
    \x15VPSequencerEnableLoop\x10\xbc\x17\x12\x1b\n\x16VPSequencerDisableLoo\
    p\x10\xbd\x17\x12\x1e\n\x19VPSequencerDownloadAsMidi\x10\xbe\x17\x12\x1d\
    \n\x18VPSequencerEnableSustain\x10\xbf\x17\x12\x1e\n\x19VPSequencerDisab\
    leSustain\x10\xc0\x17\x12\x20\n\x1bVPSequencerSetPlaybackSpeed\x10\xc1\
    \x17\x12\x16\n\x11VPSequencerSetBPM\x10\xc2\x17\x12\x18\n\x13AppMidiLoop\
    erRecord\x10\xa0\x1f\x12\x16\n\x11AppMidiLooperPlay\x10\xa1\x1f\x12\x16\
    \n\x11AppMidiLooperStop\x10\xa2\x1f\x12\x18\n\x13AppMidiLooperSetBPM\x10\
    \xa3\x1f\x12\x19\n\x14AppMidiLooperDispose\x10\xa4\x1f\x12\x1c\n\x17AppM\
    idiLooperStopRecord\x10\xa5\x1f\x12\x1c\n\x17AppMidiLooperEnableTrim\x10\
    \xa6\x1f\x12\x1d\n\x18AppMidiLooperDisableTrim\x10\xa7\x1f\x12\x1a\n\x15\
    AppMidiLooperSetTrack\x10\xa8\x1f\x12\x1b\n\x16AppMidiLooperGetTracks\
    \x10\xa9\x1f\x12\x1c\n\x17AppMidiLooperClearTrack\x10\xaa\x1f\x12$\n\x1f\
    AppMidiLooperToggleTrackPlaying\x10\xab\x1f\x12\x16\n\x11SetAvatarPositi\
    on\x10\x88'\x12\x18\n\x13SetAvatarPianoBench\x10\x89'\x12\x1e\n\x19Updat\
    eAvatarCustomization\x10\x8a'\x12\"\n\x1dShowAvatarCustomizationScreen\
    \x10\xf0.\x12\"\n\x1dHideAvatarCustomizationScreen\x10\xf1.B\x06\n\x04da\
    taB\x11\n\x0f_sourceSocketIDb\x06proto3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
