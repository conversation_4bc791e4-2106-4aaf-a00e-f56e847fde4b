// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `midi-renditions.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(<PERSON>ialEq,<PERSON><PERSON>,Default)]
pub struct MidiDto {
    // message fields
    pub messageType: MidiDtoType,
    pub noteSource: MidiNoteSource,
    // message oneof groups
    pub data: ::std::option::Option<MidiDto_oneof_data>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiDto {
    fn default() -> &'a MidiDto {
        <MidiDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum MidiDto_oneof_data {
    noteOn(MidiDto_MidiNoteOn),
    noteOff(MidiDto_MidiNoteOff),
    sustain(MidiDto_MidiNoteSustain),
    allSoundOff(MidiDto_MidiAllSoundOff),
    pitchBend(MidiDto_MidiPitchBend),
}

impl MidiDto {
    pub fn new() -> MidiDto {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDtoType messageType = 1;


    pub fn get_messageType(&self) -> MidiDtoType {
        self.messageType
    }
    pub fn clear_messageType(&mut self) {
        self.messageType = MidiDtoType::Invalid;
    }

    // Param is passed by value, moved
    pub fn set_messageType(&mut self, v: MidiDtoType) {
        self.messageType = v;
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiNoteSource noteSource = 999;


    pub fn get_noteSource(&self) -> MidiNoteSource {
        self.noteSource
    }
    pub fn clear_noteSource(&mut self) {
        self.noteSource = MidiNoteSource::IGNORED;
    }

    // Param is passed by value, moved
    pub fn set_noteSource(&mut self, v: MidiNoteSource) {
        self.noteSource = v;
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiNoteOn noteOn = 2;


    pub fn get_noteOn(&self) -> &MidiDto_MidiNoteOn {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOn(ref v)) => v,
            _ => <MidiDto_MidiNoteOn as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_noteOn(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_noteOn(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOn(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_noteOn(&mut self, v: MidiDto_MidiNoteOn) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOn(v))
    }

    // Mutable pointer to the field.
    pub fn mut_noteOn(&mut self) -> &mut MidiDto_MidiNoteOn {
        if let ::std::option::Option::Some(MidiDto_oneof_data::noteOn(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOn(MidiDto_MidiNoteOn::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOn(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_noteOn(&mut self) -> MidiDto_MidiNoteOn {
        if self.has_noteOn() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::noteOn(v)) => v,
                _ => panic!(),
            }
        } else {
            MidiDto_MidiNoteOn::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiNoteOff noteOff = 3;


    pub fn get_noteOff(&self) -> &MidiDto_MidiNoteOff {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOff(ref v)) => v,
            _ => <MidiDto_MidiNoteOff as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_noteOff(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_noteOff(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOff(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_noteOff(&mut self, v: MidiDto_MidiNoteOff) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOff(v))
    }

    // Mutable pointer to the field.
    pub fn mut_noteOff(&mut self) -> &mut MidiDto_MidiNoteOff {
        if let ::std::option::Option::Some(MidiDto_oneof_data::noteOff(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOff(MidiDto_MidiNoteOff::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOff(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_noteOff(&mut self) -> MidiDto_MidiNoteOff {
        if self.has_noteOff() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::noteOff(v)) => v,
                _ => panic!(),
            }
        } else {
            MidiDto_MidiNoteOff::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiNoteSustain sustain = 4;


    pub fn get_sustain(&self) -> &MidiDto_MidiNoteSustain {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::sustain(ref v)) => v,
            _ => <MidiDto_MidiNoteSustain as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_sustain(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_sustain(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::sustain(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_sustain(&mut self, v: MidiDto_MidiNoteSustain) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::sustain(v))
    }

    // Mutable pointer to the field.
    pub fn mut_sustain(&mut self) -> &mut MidiDto_MidiNoteSustain {
        if let ::std::option::Option::Some(MidiDto_oneof_data::sustain(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::sustain(MidiDto_MidiNoteSustain::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::sustain(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_sustain(&mut self) -> MidiDto_MidiNoteSustain {
        if self.has_sustain() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::sustain(v)) => v,
                _ => panic!(),
            }
        } else {
            MidiDto_MidiNoteSustain::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiAllSoundOff allSoundOff = 5;


    pub fn get_allSoundOff(&self) -> &MidiDto_MidiAllSoundOff {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(ref v)) => v,
            _ => <MidiDto_MidiAllSoundOff as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_allSoundOff(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_allSoundOff(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_allSoundOff(&mut self, v: MidiDto_MidiAllSoundOff) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(v))
    }

    // Mutable pointer to the field.
    pub fn mut_allSoundOff(&mut self) -> &mut MidiDto_MidiAllSoundOff {
        if let ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(MidiDto_MidiAllSoundOff::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_allSoundOff(&mut self) -> MidiDto_MidiAllSoundOff {
        if self.has_allSoundOff() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(v)) => v,
                _ => panic!(),
            }
        } else {
            MidiDto_MidiAllSoundOff::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiPitchBend pitchBend = 6;


    pub fn get_pitchBend(&self) -> &MidiDto_MidiPitchBend {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(ref v)) => v,
            _ => <MidiDto_MidiPitchBend as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_pitchBend(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_pitchBend(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pitchBend(&mut self, v: MidiDto_MidiPitchBend) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(v))
    }

    // Mutable pointer to the field.
    pub fn mut_pitchBend(&mut self) -> &mut MidiDto_MidiPitchBend {
        if let ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(MidiDto_MidiPitchBend::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_pitchBend(&mut self) -> MidiDto_MidiPitchBend {
        if self.has_pitchBend() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(v)) => v,
                _ => panic!(),
            }
        } else {
            MidiDto_MidiPitchBend::new()
        }
    }
}

impl ::protobuf::Message for MidiDto {
    fn is_initialized(&self) -> bool {
        if let Some(MidiDto_oneof_data::noteOn(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(MidiDto_oneof_data::noteOff(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(MidiDto_oneof_data::sustain(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(MidiDto_oneof_data::allSoundOff(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(MidiDto_oneof_data::pitchBend(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.messageType, 1, &mut self.unknown_fields)?
                },
                999 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.noteSource, 999, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOn(is.read_message()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOff(is.read_message()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::sustain(is.read_message()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(is.read_message()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.messageType != MidiDtoType::Invalid {
            my_size += ::protobuf::rt::enum_size(1, self.messageType);
        }
        if self.noteSource != MidiNoteSource::IGNORED {
            my_size += ::protobuf::rt::enum_size(999, self.noteSource);
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &MidiDto_oneof_data::noteOn(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &MidiDto_oneof_data::noteOff(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &MidiDto_oneof_data::sustain(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &MidiDto_oneof_data::allSoundOff(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &MidiDto_oneof_data::pitchBend(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.messageType != MidiDtoType::Invalid {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.messageType))?;
        }
        if self.noteSource != MidiNoteSource::IGNORED {
            os.write_enum(999, ::protobuf::ProtobufEnum::value(&self.noteSource))?;
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &MidiDto_oneof_data::noteOn(ref v) => {
                    os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &MidiDto_oneof_data::noteOff(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &MidiDto_oneof_data::sustain(ref v) => {
                    os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &MidiDto_oneof_data::allSoundOff(ref v) => {
                    os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &MidiDto_oneof_data::pitchBend(ref v) => {
                    os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiDto {
        MidiDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<MidiDtoType>>(
                "messageType",
                |m: &MidiDto| { &m.messageType },
                |m: &mut MidiDto| { &mut m.messageType },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<MidiNoteSource>>(
                "noteSource",
                |m: &MidiDto| { &m.noteSource },
                |m: &mut MidiDto| { &mut m.noteSource },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, MidiDto_MidiNoteOn>(
                "noteOn",
                MidiDto::has_noteOn,
                MidiDto::get_noteOn,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, MidiDto_MidiNoteOff>(
                "noteOff",
                MidiDto::has_noteOff,
                MidiDto::get_noteOff,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, MidiDto_MidiNoteSustain>(
                "sustain",
                MidiDto::has_sustain,
                MidiDto::get_sustain,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, MidiDto_MidiAllSoundOff>(
                "allSoundOff",
                MidiDto::has_allSoundOff,
                MidiDto::get_allSoundOff,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, MidiDto_MidiPitchBend>(
                "pitchBend",
                MidiDto::has_pitchBend,
                MidiDto::get_pitchBend,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiDto>(
                "MidiDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiDto {
        static instance: ::protobuf::rt::LazyV2<MidiDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiDto::new)
    }
}

impl ::protobuf::Clear for MidiDto {
    fn clear(&mut self) {
        self.messageType = MidiDtoType::Invalid;
        self.noteSource = MidiNoteSource::IGNORED;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiDto_MidiNoteOn {
    // message fields
    pub channel: i32,
    pub note: i32,
    pub velocity: i32,
    // message oneof groups
    pub _program: ::std::option::Option<MidiDto_MidiNoteOn_oneof__program>,
    pub _volume: ::std::option::Option<MidiDto_MidiNoteOn_oneof__volume>,
    pub _bank: ::std::option::Option<MidiDto_MidiNoteOn_oneof__bank>,
    pub _pan: ::std::option::Option<MidiDto_MidiNoteOn_oneof__pan>,
    pub _expression: ::std::option::Option<MidiDto_MidiNoteOn_oneof__expression>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiDto_MidiNoteOn {
    fn default() -> &'a MidiDto_MidiNoteOn {
        <MidiDto_MidiNoteOn as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum MidiDto_MidiNoteOn_oneof__program {
    program(i32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum MidiDto_MidiNoteOn_oneof__volume {
    volume(i32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum MidiDto_MidiNoteOn_oneof__bank {
    bank(i32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum MidiDto_MidiNoteOn_oneof__pan {
    pan(i32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum MidiDto_MidiNoteOn_oneof__expression {
    expression(i32),
}

impl MidiDto_MidiNoteOn {
    pub fn new() -> MidiDto_MidiNoteOn {
        ::std::default::Default::default()
    }

    // int32 channel = 1;


    pub fn get_channel(&self) -> i32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: i32) {
        self.channel = v;
    }

    // int32 note = 2;


    pub fn get_note(&self) -> i32 {
        self.note
    }
    pub fn clear_note(&mut self) {
        self.note = 0;
    }

    // Param is passed by value, moved
    pub fn set_note(&mut self, v: i32) {
        self.note = v;
    }

    // int32 velocity = 3;


    pub fn get_velocity(&self) -> i32 {
        self.velocity
    }
    pub fn clear_velocity(&mut self) {
        self.velocity = 0;
    }

    // Param is passed by value, moved
    pub fn set_velocity(&mut self, v: i32) {
        self.velocity = v;
    }

    // int32 program = 4;


    pub fn get_program(&self) -> i32 {
        match self._program {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__program::program(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_program(&mut self) {
        self._program = ::std::option::Option::None;
    }

    pub fn has_program(&self) -> bool {
        match self._program {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__program::program(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_program(&mut self, v: i32) {
        self._program = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__program::program(v))
    }

    // int32 volume = 5;


    pub fn get_volume(&self) -> i32 {
        match self._volume {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__volume::volume(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_volume(&mut self) {
        self._volume = ::std::option::Option::None;
    }

    pub fn has_volume(&self) -> bool {
        match self._volume {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__volume::volume(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_volume(&mut self, v: i32) {
        self._volume = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__volume::volume(v))
    }

    // int32 bank = 6;


    pub fn get_bank(&self) -> i32 {
        match self._bank {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__bank::bank(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_bank(&mut self) {
        self._bank = ::std::option::Option::None;
    }

    pub fn has_bank(&self) -> bool {
        match self._bank {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__bank::bank(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: i32) {
        self._bank = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__bank::bank(v))
    }

    // int32 pan = 7;


    pub fn get_pan(&self) -> i32 {
        match self._pan {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__pan::pan(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_pan(&mut self) {
        self._pan = ::std::option::Option::None;
    }

    pub fn has_pan(&self) -> bool {
        match self._pan {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__pan::pan(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pan(&mut self, v: i32) {
        self._pan = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__pan::pan(v))
    }

    // int32 expression = 8;


    pub fn get_expression(&self) -> i32 {
        match self._expression {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__expression::expression(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_expression(&mut self) {
        self._expression = ::std::option::Option::None;
    }

    pub fn has_expression(&self) -> bool {
        match self._expression {
            ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__expression::expression(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_expression(&mut self, v: i32) {
        self._expression = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__expression::expression(v))
    }
}

impl ::protobuf::Message for MidiDto_MidiNoteOn {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.note = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.velocity = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._program = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__program::program(is.read_int32()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._volume = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__volume::volume(is.read_int32()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._bank = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__bank::bank(is.read_int32()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._pan = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__pan::pan(is.read_int32()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._expression = ::std::option::Option::Some(MidiDto_MidiNoteOn_oneof__expression::expression(is.read_int32()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.note != 0 {
            my_size += ::protobuf::rt::value_size(2, self.note, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.velocity != 0 {
            my_size += ::protobuf::rt::value_size(3, self.velocity, ::protobuf::wire_format::WireTypeVarint);
        }
        if let ::std::option::Option::Some(ref v) = self._program {
            match v {
                &MidiDto_MidiNoteOn_oneof__program::program(v) => {
                    my_size += ::protobuf::rt::value_size(4, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._volume {
            match v {
                &MidiDto_MidiNoteOn_oneof__volume::volume(v) => {
                    my_size += ::protobuf::rt::value_size(5, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._bank {
            match v {
                &MidiDto_MidiNoteOn_oneof__bank::bank(v) => {
                    my_size += ::protobuf::rt::value_size(6, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pan {
            match v {
                &MidiDto_MidiNoteOn_oneof__pan::pan(v) => {
                    my_size += ::protobuf::rt::value_size(7, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._expression {
            match v {
                &MidiDto_MidiNoteOn_oneof__expression::expression(v) => {
                    my_size += ::protobuf::rt::value_size(8, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_int32(1, self.channel)?;
        }
        if self.note != 0 {
            os.write_int32(2, self.note)?;
        }
        if self.velocity != 0 {
            os.write_int32(3, self.velocity)?;
        }
        if let ::std::option::Option::Some(ref v) = self._program {
            match v {
                &MidiDto_MidiNoteOn_oneof__program::program(v) => {
                    os.write_int32(4, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._volume {
            match v {
                &MidiDto_MidiNoteOn_oneof__volume::volume(v) => {
                    os.write_int32(5, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._bank {
            match v {
                &MidiDto_MidiNoteOn_oneof__bank::bank(v) => {
                    os.write_int32(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pan {
            match v {
                &MidiDto_MidiNoteOn_oneof__pan::pan(v) => {
                    os.write_int32(7, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._expression {
            match v {
                &MidiDto_MidiNoteOn_oneof__expression::expression(v) => {
                    os.write_int32(8, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiDto_MidiNoteOn {
        MidiDto_MidiNoteOn::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "channel",
                |m: &MidiDto_MidiNoteOn| { &m.channel },
                |m: &mut MidiDto_MidiNoteOn| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "note",
                |m: &MidiDto_MidiNoteOn| { &m.note },
                |m: &mut MidiDto_MidiNoteOn| { &mut m.note },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "velocity",
                |m: &MidiDto_MidiNoteOn| { &m.velocity },
                |m: &mut MidiDto_MidiNoteOn| { &mut m.velocity },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "program",
                MidiDto_MidiNoteOn::has_program,
                MidiDto_MidiNoteOn::get_program,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "volume",
                MidiDto_MidiNoteOn::has_volume,
                MidiDto_MidiNoteOn::get_volume,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "bank",
                MidiDto_MidiNoteOn::has_bank,
                MidiDto_MidiNoteOn::get_bank,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "pan",
                MidiDto_MidiNoteOn::has_pan,
                MidiDto_MidiNoteOn::get_pan,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "expression",
                MidiDto_MidiNoteOn::has_expression,
                MidiDto_MidiNoteOn::get_expression,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiDto_MidiNoteOn>(
                "MidiDto.MidiNoteOn",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiDto_MidiNoteOn {
        static instance: ::protobuf::rt::LazyV2<MidiDto_MidiNoteOn> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiDto_MidiNoteOn::new)
    }
}

impl ::protobuf::Clear for MidiDto_MidiNoteOn {
    fn clear(&mut self) {
        self.channel = 0;
        self.note = 0;
        self.velocity = 0;
        self._program = ::std::option::Option::None;
        self._volume = ::std::option::Option::None;
        self._bank = ::std::option::Option::None;
        self._pan = ::std::option::Option::None;
        self._expression = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiDto_MidiNoteOn {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiDto_MidiNoteOn {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiDto_MidiNoteOff {
    // message fields
    pub channel: i32,
    pub note: i32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiDto_MidiNoteOff {
    fn default() -> &'a MidiDto_MidiNoteOff {
        <MidiDto_MidiNoteOff as ::protobuf::Message>::default_instance()
    }
}

impl MidiDto_MidiNoteOff {
    pub fn new() -> MidiDto_MidiNoteOff {
        ::std::default::Default::default()
    }

    // int32 channel = 1;


    pub fn get_channel(&self) -> i32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: i32) {
        self.channel = v;
    }

    // int32 note = 2;


    pub fn get_note(&self) -> i32 {
        self.note
    }
    pub fn clear_note(&mut self) {
        self.note = 0;
    }

    // Param is passed by value, moved
    pub fn set_note(&mut self, v: i32) {
        self.note = v;
    }
}

impl ::protobuf::Message for MidiDto_MidiNoteOff {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.note = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.note != 0 {
            my_size += ::protobuf::rt::value_size(2, self.note, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_int32(1, self.channel)?;
        }
        if self.note != 0 {
            os.write_int32(2, self.note)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiDto_MidiNoteOff {
        MidiDto_MidiNoteOff::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "channel",
                |m: &MidiDto_MidiNoteOff| { &m.channel },
                |m: &mut MidiDto_MidiNoteOff| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "note",
                |m: &MidiDto_MidiNoteOff| { &m.note },
                |m: &mut MidiDto_MidiNoteOff| { &mut m.note },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiDto_MidiNoteOff>(
                "MidiDto.MidiNoteOff",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiDto_MidiNoteOff {
        static instance: ::protobuf::rt::LazyV2<MidiDto_MidiNoteOff> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiDto_MidiNoteOff::new)
    }
}

impl ::protobuf::Clear for MidiDto_MidiNoteOff {
    fn clear(&mut self) {
        self.channel = 0;
        self.note = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiDto_MidiNoteOff {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiDto_MidiNoteOff {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiDto_MidiNoteSustain {
    // message fields
    pub channel: i32,
    pub value: bool,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiDto_MidiNoteSustain {
    fn default() -> &'a MidiDto_MidiNoteSustain {
        <MidiDto_MidiNoteSustain as ::protobuf::Message>::default_instance()
    }
}

impl MidiDto_MidiNoteSustain {
    pub fn new() -> MidiDto_MidiNoteSustain {
        ::std::default::Default::default()
    }

    // int32 channel = 1;


    pub fn get_channel(&self) -> i32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: i32) {
        self.channel = v;
    }

    // bool value = 2;


    pub fn get_value(&self) -> bool {
        self.value
    }
    pub fn clear_value(&mut self) {
        self.value = false;
    }

    // Param is passed by value, moved
    pub fn set_value(&mut self, v: bool) {
        self.value = v;
    }
}

impl ::protobuf::Message for MidiDto_MidiNoteSustain {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.value = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.value != false {
            my_size += 2;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_int32(1, self.channel)?;
        }
        if self.value != false {
            os.write_bool(2, self.value)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiDto_MidiNoteSustain {
        MidiDto_MidiNoteSustain::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "channel",
                |m: &MidiDto_MidiNoteSustain| { &m.channel },
                |m: &mut MidiDto_MidiNoteSustain| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "value",
                |m: &MidiDto_MidiNoteSustain| { &m.value },
                |m: &mut MidiDto_MidiNoteSustain| { &mut m.value },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiDto_MidiNoteSustain>(
                "MidiDto.MidiNoteSustain",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiDto_MidiNoteSustain {
        static instance: ::protobuf::rt::LazyV2<MidiDto_MidiNoteSustain> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiDto_MidiNoteSustain::new)
    }
}

impl ::protobuf::Clear for MidiDto_MidiNoteSustain {
    fn clear(&mut self) {
        self.channel = 0;
        self.value = false;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiDto_MidiNoteSustain {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiDto_MidiNoteSustain {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiDto_MidiAllSoundOff {
    // message fields
    pub channel: i32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiDto_MidiAllSoundOff {
    fn default() -> &'a MidiDto_MidiAllSoundOff {
        <MidiDto_MidiAllSoundOff as ::protobuf::Message>::default_instance()
    }
}

impl MidiDto_MidiAllSoundOff {
    pub fn new() -> MidiDto_MidiAllSoundOff {
        ::std::default::Default::default()
    }

    // int32 channel = 1;


    pub fn get_channel(&self) -> i32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: i32) {
        self.channel = v;
    }
}

impl ::protobuf::Message for MidiDto_MidiAllSoundOff {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.channel = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_int32(1, self.channel)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiDto_MidiAllSoundOff {
        MidiDto_MidiAllSoundOff::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "channel",
                |m: &MidiDto_MidiAllSoundOff| { &m.channel },
                |m: &mut MidiDto_MidiAllSoundOff| { &mut m.channel },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiDto_MidiAllSoundOff>(
                "MidiDto.MidiAllSoundOff",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiDto_MidiAllSoundOff {
        static instance: ::protobuf::rt::LazyV2<MidiDto_MidiAllSoundOff> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiDto_MidiAllSoundOff::new)
    }
}

impl ::protobuf::Clear for MidiDto_MidiAllSoundOff {
    fn clear(&mut self) {
        self.channel = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiDto_MidiAllSoundOff {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiDto_MidiAllSoundOff {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiDto_MidiPitchBend {
    // message fields
    pub channel: u32,
    pub value: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiDto_MidiPitchBend {
    fn default() -> &'a MidiDto_MidiPitchBend {
        <MidiDto_MidiPitchBend as ::protobuf::Message>::default_instance()
    }
}

impl MidiDto_MidiPitchBend {
    pub fn new() -> MidiDto_MidiPitchBend {
        ::std::default::Default::default()
    }

    // uint32 channel = 1;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }

    // uint32 value = 2;


    pub fn get_value(&self) -> u32 {
        self.value
    }
    pub fn clear_value(&mut self) {
        self.value = 0;
    }

    // Param is passed by value, moved
    pub fn set_value(&mut self, v: u32) {
        self.value = v;
    }
}

impl ::protobuf::Message for MidiDto_MidiPitchBend {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.value = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.value != 0 {
            my_size += ::protobuf::rt::value_size(2, self.value, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_uint32(1, self.channel)?;
        }
        if self.value != 0 {
            os.write_uint32(2, self.value)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiDto_MidiPitchBend {
        MidiDto_MidiPitchBend::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &MidiDto_MidiPitchBend| { &m.channel },
                |m: &mut MidiDto_MidiPitchBend| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "value",
                |m: &MidiDto_MidiPitchBend| { &m.value },
                |m: &mut MidiDto_MidiPitchBend| { &mut m.value },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiDto_MidiPitchBend>(
                "MidiDto.MidiPitchBend",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiDto_MidiPitchBend {
        static instance: ::protobuf::rt::LazyV2<MidiDto_MidiPitchBend> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiDto_MidiPitchBend::new)
    }
}

impl ::protobuf::Clear for MidiDto_MidiPitchBend {
    fn clear(&mut self) {
        self.channel = 0;
        self.value = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiDto_MidiPitchBend {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiDto_MidiPitchBend {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiMessageInputBuffer {
    // message fields
    pub field_type: MidiDtoType,
    pub channel: u32,
    pub note: u32,
    pub velocity: u32,
    pub delay: f32,
    pub program: u32,
    pub volume: u32,
    pub bank: u32,
    pub pan: u32,
    pub pitch: u32,
    pub expression: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiMessageInputBuffer {
    fn default() -> &'a MidiMessageInputBuffer {
        <MidiMessageInputBuffer as ::protobuf::Message>::default_instance()
    }
}

impl MidiMessageInputBuffer {
    pub fn new() -> MidiMessageInputBuffer {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDtoType type = 1;


    pub fn get_field_type(&self) -> MidiDtoType {
        self.field_type
    }
    pub fn clear_field_type(&mut self) {
        self.field_type = MidiDtoType::Invalid;
    }

    // Param is passed by value, moved
    pub fn set_field_type(&mut self, v: MidiDtoType) {
        self.field_type = v;
    }

    // uint32 channel = 2;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }

    // uint32 note = 3;


    pub fn get_note(&self) -> u32 {
        self.note
    }
    pub fn clear_note(&mut self) {
        self.note = 0;
    }

    // Param is passed by value, moved
    pub fn set_note(&mut self, v: u32) {
        self.note = v;
    }

    // uint32 velocity = 4;


    pub fn get_velocity(&self) -> u32 {
        self.velocity
    }
    pub fn clear_velocity(&mut self) {
        self.velocity = 0;
    }

    // Param is passed by value, moved
    pub fn set_velocity(&mut self, v: u32) {
        self.velocity = v;
    }

    // float delay = 5;


    pub fn get_delay(&self) -> f32 {
        self.delay
    }
    pub fn clear_delay(&mut self) {
        self.delay = 0.;
    }

    // Param is passed by value, moved
    pub fn set_delay(&mut self, v: f32) {
        self.delay = v;
    }

    // uint32 program = 6;


    pub fn get_program(&self) -> u32 {
        self.program
    }
    pub fn clear_program(&mut self) {
        self.program = 0;
    }

    // Param is passed by value, moved
    pub fn set_program(&mut self, v: u32) {
        self.program = v;
    }

    // uint32 volume = 7;


    pub fn get_volume(&self) -> u32 {
        self.volume
    }
    pub fn clear_volume(&mut self) {
        self.volume = 0;
    }

    // Param is passed by value, moved
    pub fn set_volume(&mut self, v: u32) {
        self.volume = v;
    }

    // uint32 bank = 8;


    pub fn get_bank(&self) -> u32 {
        self.bank
    }
    pub fn clear_bank(&mut self) {
        self.bank = 0;
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: u32) {
        self.bank = v;
    }

    // uint32 pan = 9;


    pub fn get_pan(&self) -> u32 {
        self.pan
    }
    pub fn clear_pan(&mut self) {
        self.pan = 0;
    }

    // Param is passed by value, moved
    pub fn set_pan(&mut self, v: u32) {
        self.pan = v;
    }

    // uint32 pitch = 10;


    pub fn get_pitch(&self) -> u32 {
        self.pitch
    }
    pub fn clear_pitch(&mut self) {
        self.pitch = 0;
    }

    // Param is passed by value, moved
    pub fn set_pitch(&mut self, v: u32) {
        self.pitch = v;
    }

    // uint32 expression = 11;


    pub fn get_expression(&self) -> u32 {
        self.expression
    }
    pub fn clear_expression(&mut self) {
        self.expression = 0;
    }

    // Param is passed by value, moved
    pub fn set_expression(&mut self, v: u32) {
        self.expression = v;
    }
}

impl ::protobuf::Message for MidiMessageInputBuffer {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.field_type, 1, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.note = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.velocity = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed32 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_float()?;
                    self.delay = tmp;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.program = tmp;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.volume = tmp;
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.bank = tmp;
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.pan = tmp;
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.pitch = tmp;
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.expression = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.field_type != MidiDtoType::Invalid {
            my_size += ::protobuf::rt::enum_size(1, self.field_type);
        }
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(2, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.note != 0 {
            my_size += ::protobuf::rt::value_size(3, self.note, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.velocity != 0 {
            my_size += ::protobuf::rt::value_size(4, self.velocity, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.delay != 0. {
            my_size += 5;
        }
        if self.program != 0 {
            my_size += ::protobuf::rt::value_size(6, self.program, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.volume != 0 {
            my_size += ::protobuf::rt::value_size(7, self.volume, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.bank != 0 {
            my_size += ::protobuf::rt::value_size(8, self.bank, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.pan != 0 {
            my_size += ::protobuf::rt::value_size(9, self.pan, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.pitch != 0 {
            my_size += ::protobuf::rt::value_size(10, self.pitch, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.expression != 0 {
            my_size += ::protobuf::rt::value_size(11, self.expression, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.field_type != MidiDtoType::Invalid {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.field_type))?;
        }
        if self.channel != 0 {
            os.write_uint32(2, self.channel)?;
        }
        if self.note != 0 {
            os.write_uint32(3, self.note)?;
        }
        if self.velocity != 0 {
            os.write_uint32(4, self.velocity)?;
        }
        if self.delay != 0. {
            os.write_float(5, self.delay)?;
        }
        if self.program != 0 {
            os.write_uint32(6, self.program)?;
        }
        if self.volume != 0 {
            os.write_uint32(7, self.volume)?;
        }
        if self.bank != 0 {
            os.write_uint32(8, self.bank)?;
        }
        if self.pan != 0 {
            os.write_uint32(9, self.pan)?;
        }
        if self.pitch != 0 {
            os.write_uint32(10, self.pitch)?;
        }
        if self.expression != 0 {
            os.write_uint32(11, self.expression)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiMessageInputBuffer {
        MidiMessageInputBuffer::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<MidiDtoType>>(
                "type",
                |m: &MidiMessageInputBuffer| { &m.field_type },
                |m: &mut MidiMessageInputBuffer| { &mut m.field_type },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &MidiMessageInputBuffer| { &m.channel },
                |m: &mut MidiMessageInputBuffer| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "note",
                |m: &MidiMessageInputBuffer| { &m.note },
                |m: &mut MidiMessageInputBuffer| { &mut m.note },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "velocity",
                |m: &MidiMessageInputBuffer| { &m.velocity },
                |m: &mut MidiMessageInputBuffer| { &mut m.velocity },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeFloat>(
                "delay",
                |m: &MidiMessageInputBuffer| { &m.delay },
                |m: &mut MidiMessageInputBuffer| { &mut m.delay },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "program",
                |m: &MidiMessageInputBuffer| { &m.program },
                |m: &mut MidiMessageInputBuffer| { &mut m.program },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "volume",
                |m: &MidiMessageInputBuffer| { &m.volume },
                |m: &mut MidiMessageInputBuffer| { &mut m.volume },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "bank",
                |m: &MidiMessageInputBuffer| { &m.bank },
                |m: &mut MidiMessageInputBuffer| { &mut m.bank },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "pan",
                |m: &MidiMessageInputBuffer| { &m.pan },
                |m: &mut MidiMessageInputBuffer| { &mut m.pan },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "pitch",
                |m: &MidiMessageInputBuffer| { &m.pitch },
                |m: &mut MidiMessageInputBuffer| { &mut m.pitch },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "expression",
                |m: &MidiMessageInputBuffer| { &m.expression },
                |m: &mut MidiMessageInputBuffer| { &mut m.expression },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiMessageInputBuffer>(
                "MidiMessageInputBuffer",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiMessageInputBuffer {
        static instance: ::protobuf::rt::LazyV2<MidiMessageInputBuffer> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiMessageInputBuffer::new)
    }
}

impl ::protobuf::Clear for MidiMessageInputBuffer {
    fn clear(&mut self) {
        self.field_type = MidiDtoType::Invalid;
        self.channel = 0;
        self.note = 0;
        self.velocity = 0;
        self.delay = 0.;
        self.program = 0;
        self.volume = 0;
        self.bank = 0;
        self.pan = 0;
        self.pitch = 0;
        self.expression = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiMessageInputBuffer {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiMessageInputBuffer {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiMessageInputDto {
    // message fields
    pub time: ::std::string::String,
    pub data: ::protobuf::RepeatedField<MidiMessageInputBuffer>,
    // message oneof groups
    pub _source: ::std::option::Option<MidiMessageInputDto_oneof__source>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiMessageInputDto {
    fn default() -> &'a MidiMessageInputDto {
        <MidiMessageInputDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum MidiMessageInputDto_oneof__source {
    source(MidiNoteSource),
}

impl MidiMessageInputDto {
    pub fn new() -> MidiMessageInputDto {
        ::std::default::Default::default()
    }

    // string time = 1;


    pub fn get_time(&self) -> &str {
        &self.time
    }
    pub fn clear_time(&mut self) {
        self.time.clear();
    }

    // Param is passed by value, moved
    pub fn set_time(&mut self, v: ::std::string::String) {
        self.time = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_time(&mut self) -> &mut ::std::string::String {
        &mut self.time
    }

    // Take field
    pub fn take_time(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.time, ::std::string::String::new())
    }

    // repeated .PianoRhythm.Serialization.Midi.Msgs.MidiMessageInputBuffer data = 2;


    pub fn get_data(&self) -> &[MidiMessageInputBuffer] {
        &self.data
    }
    pub fn clear_data(&mut self) {
        self.data.clear();
    }

    // Param is passed by value, moved
    pub fn set_data(&mut self, v: ::protobuf::RepeatedField<MidiMessageInputBuffer>) {
        self.data = v;
    }

    // Mutable pointer to the field.
    pub fn mut_data(&mut self) -> &mut ::protobuf::RepeatedField<MidiMessageInputBuffer> {
        &mut self.data
    }

    // Take field
    pub fn take_data(&mut self) -> ::protobuf::RepeatedField<MidiMessageInputBuffer> {
        ::std::mem::replace(&mut self.data, ::protobuf::RepeatedField::new())
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiNoteSource source = 3;


    pub fn get_source(&self) -> MidiNoteSource {
        match self._source {
            ::std::option::Option::Some(MidiMessageInputDto_oneof__source::source(v)) => v,
            _ => MidiNoteSource::IGNORED,
        }
    }
    pub fn clear_source(&mut self) {
        self._source = ::std::option::Option::None;
    }

    pub fn has_source(&self) -> bool {
        match self._source {
            ::std::option::Option::Some(MidiMessageInputDto_oneof__source::source(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_source(&mut self, v: MidiNoteSource) {
        self._source = ::std::option::Option::Some(MidiMessageInputDto_oneof__source::source(v))
    }
}

impl ::protobuf::Message for MidiMessageInputDto {
    fn is_initialized(&self) -> bool {
        for v in &self.data {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.time)?;
                },
                2 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.data)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._source = ::std::option::Option::Some(MidiMessageInputDto_oneof__source::source(is.read_enum()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.time.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.time);
        }
        for value in &self.data {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if let ::std::option::Option::Some(ref v) = self._source {
            match v {
                &MidiMessageInputDto_oneof__source::source(v) => {
                    my_size += ::protobuf::rt::enum_size(3, v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.time.is_empty() {
            os.write_string(1, &self.time)?;
        }
        for v in &self.data {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if let ::std::option::Option::Some(ref v) = self._source {
            match v {
                &MidiMessageInputDto_oneof__source::source(v) => {
                    os.write_enum(3, ::protobuf::ProtobufEnum::value(&v))?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiMessageInputDto {
        MidiMessageInputDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "time",
                |m: &MidiMessageInputDto| { &m.time },
                |m: &mut MidiMessageInputDto| { &mut m.time },
            ));
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<MidiMessageInputBuffer>>(
                "data",
                |m: &MidiMessageInputDto| { &m.data },
                |m: &mut MidiMessageInputDto| { &mut m.data },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_enum_accessor::<_, MidiNoteSource>(
                "source",
                MidiMessageInputDto::has_source,
                MidiMessageInputDto::get_source,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiMessageInputDto>(
                "MidiMessageInputDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiMessageInputDto {
        static instance: ::protobuf::rt::LazyV2<MidiMessageInputDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiMessageInputDto::new)
    }
}

impl ::protobuf::Clear for MidiMessageInputDto {
    fn clear(&mut self) {
        self.time.clear();
        self.data.clear();
        self._source = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiMessageInputDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiMessageInputDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct SF2Program {
    // message fields
    pub id: u32,
    pub name: ::std::string::String,
    pub bank: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SF2Program {
    fn default() -> &'a SF2Program {
        <SF2Program as ::protobuf::Message>::default_instance()
    }
}

impl SF2Program {
    pub fn new() -> SF2Program {
        ::std::default::Default::default()
    }

    // uint32 id = 1;


    pub fn get_id(&self) -> u32 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: u32) {
        self.id = v;
    }

    // string name = 2;


    pub fn get_name(&self) -> &str {
        &self.name
    }
    pub fn clear_name(&mut self) {
        self.name.clear();
    }

    // Param is passed by value, moved
    pub fn set_name(&mut self, v: ::std::string::String) {
        self.name = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_name(&mut self) -> &mut ::std::string::String {
        &mut self.name
    }

    // Take field
    pub fn take_name(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.name, ::std::string::String::new())
    }

    // uint32 bank = 3;


    pub fn get_bank(&self) -> u32 {
        self.bank
    }
    pub fn clear_bank(&mut self) {
        self.bank = 0;
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: u32) {
        self.bank = v;
    }
}

impl ::protobuf::Message for SF2Program {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.name)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.bank = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.name.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.name);
        }
        if self.bank != 0 {
            my_size += ::protobuf::rt::value_size(3, self.bank, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_uint32(1, self.id)?;
        }
        if !self.name.is_empty() {
            os.write_string(2, &self.name)?;
        }
        if self.bank != 0 {
            os.write_uint32(3, self.bank)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SF2Program {
        SF2Program::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "id",
                |m: &SF2Program| { &m.id },
                |m: &mut SF2Program| { &mut m.id },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "name",
                |m: &SF2Program| { &m.name },
                |m: &mut SF2Program| { &mut m.name },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "bank",
                |m: &SF2Program| { &m.bank },
                |m: &mut SF2Program| { &mut m.bank },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<SF2Program>(
                "SF2Program",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static SF2Program {
        static instance: ::protobuf::rt::LazyV2<SF2Program> = ::protobuf::rt::LazyV2::INIT;
        instance.get(SF2Program::new)
    }
}

impl ::protobuf::Clear for SF2Program {
    fn clear(&mut self) {
        self.id = 0;
        self.name.clear();
        self.bank = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for SF2Program {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for SF2Program {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct SoundfontPreset {
    // message fields
    pub name: ::std::string::String,
    pub bank: u32,
    pub preset: u32,
    pub key_low: u32,
    pub key_high: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SoundfontPreset {
    fn default() -> &'a SoundfontPreset {
        <SoundfontPreset as ::protobuf::Message>::default_instance()
    }
}

impl SoundfontPreset {
    pub fn new() -> SoundfontPreset {
        ::std::default::Default::default()
    }

    // string name = 1;


    pub fn get_name(&self) -> &str {
        &self.name
    }
    pub fn clear_name(&mut self) {
        self.name.clear();
    }

    // Param is passed by value, moved
    pub fn set_name(&mut self, v: ::std::string::String) {
        self.name = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_name(&mut self) -> &mut ::std::string::String {
        &mut self.name
    }

    // Take field
    pub fn take_name(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.name, ::std::string::String::new())
    }

    // uint32 bank = 2;


    pub fn get_bank(&self) -> u32 {
        self.bank
    }
    pub fn clear_bank(&mut self) {
        self.bank = 0;
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: u32) {
        self.bank = v;
    }

    // uint32 preset = 3;


    pub fn get_preset(&self) -> u32 {
        self.preset
    }
    pub fn clear_preset(&mut self) {
        self.preset = 0;
    }

    // Param is passed by value, moved
    pub fn set_preset(&mut self, v: u32) {
        self.preset = v;
    }

    // uint32 key_low = 4;


    pub fn get_key_low(&self) -> u32 {
        self.key_low
    }
    pub fn clear_key_low(&mut self) {
        self.key_low = 0;
    }

    // Param is passed by value, moved
    pub fn set_key_low(&mut self, v: u32) {
        self.key_low = v;
    }

    // uint32 key_high = 5;


    pub fn get_key_high(&self) -> u32 {
        self.key_high
    }
    pub fn clear_key_high(&mut self) {
        self.key_high = 0;
    }

    // Param is passed by value, moved
    pub fn set_key_high(&mut self, v: u32) {
        self.key_high = v;
    }
}

impl ::protobuf::Message for SoundfontPreset {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.name)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.bank = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.preset = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.key_low = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.key_high = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.name.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.name);
        }
        if self.bank != 0 {
            my_size += ::protobuf::rt::value_size(2, self.bank, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.preset != 0 {
            my_size += ::protobuf::rt::value_size(3, self.preset, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.key_low != 0 {
            my_size += ::protobuf::rt::value_size(4, self.key_low, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.key_high != 0 {
            my_size += ::protobuf::rt::value_size(5, self.key_high, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.name.is_empty() {
            os.write_string(1, &self.name)?;
        }
        if self.bank != 0 {
            os.write_uint32(2, self.bank)?;
        }
        if self.preset != 0 {
            os.write_uint32(3, self.preset)?;
        }
        if self.key_low != 0 {
            os.write_uint32(4, self.key_low)?;
        }
        if self.key_high != 0 {
            os.write_uint32(5, self.key_high)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SoundfontPreset {
        SoundfontPreset::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "name",
                |m: &SoundfontPreset| { &m.name },
                |m: &mut SoundfontPreset| { &mut m.name },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "bank",
                |m: &SoundfontPreset| { &m.bank },
                |m: &mut SoundfontPreset| { &mut m.bank },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "preset",
                |m: &SoundfontPreset| { &m.preset },
                |m: &mut SoundfontPreset| { &mut m.preset },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "key_low",
                |m: &SoundfontPreset| { &m.key_low },
                |m: &mut SoundfontPreset| { &mut m.key_low },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "key_high",
                |m: &SoundfontPreset| { &m.key_high },
                |m: &mut SoundfontPreset| { &mut m.key_high },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<SoundfontPreset>(
                "SoundfontPreset",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static SoundfontPreset {
        static instance: ::protobuf::rt::LazyV2<SoundfontPreset> = ::protobuf::rt::LazyV2::INIT;
        instance.get(SoundfontPreset::new)
    }
}

impl ::protobuf::Clear for SoundfontPreset {
    fn clear(&mut self) {
        self.name.clear();
        self.bank = 0;
        self.preset = 0;
        self.key_low = 0;
        self.key_high = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for SoundfontPreset {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for SoundfontPreset {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct Instrument {
    // message fields
    pub name: ::std::string::String,
    pub display_name: ::std::string::String,
    pub bank: u32,
    pub preset: u32,
    pub is_drum_kit: bool,
    pub key_low: u32,
    pub key_high: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Instrument {
    fn default() -> &'a Instrument {
        <Instrument as ::protobuf::Message>::default_instance()
    }
}

impl Instrument {
    pub fn new() -> Instrument {
        ::std::default::Default::default()
    }

    // string name = 1;


    pub fn get_name(&self) -> &str {
        &self.name
    }
    pub fn clear_name(&mut self) {
        self.name.clear();
    }

    // Param is passed by value, moved
    pub fn set_name(&mut self, v: ::std::string::String) {
        self.name = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_name(&mut self) -> &mut ::std::string::String {
        &mut self.name
    }

    // Take field
    pub fn take_name(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.name, ::std::string::String::new())
    }

    // string display_name = 2;


    pub fn get_display_name(&self) -> &str {
        &self.display_name
    }
    pub fn clear_display_name(&mut self) {
        self.display_name.clear();
    }

    // Param is passed by value, moved
    pub fn set_display_name(&mut self, v: ::std::string::String) {
        self.display_name = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_display_name(&mut self) -> &mut ::std::string::String {
        &mut self.display_name
    }

    // Take field
    pub fn take_display_name(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.display_name, ::std::string::String::new())
    }

    // uint32 bank = 3;


    pub fn get_bank(&self) -> u32 {
        self.bank
    }
    pub fn clear_bank(&mut self) {
        self.bank = 0;
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: u32) {
        self.bank = v;
    }

    // uint32 preset = 4;


    pub fn get_preset(&self) -> u32 {
        self.preset
    }
    pub fn clear_preset(&mut self) {
        self.preset = 0;
    }

    // Param is passed by value, moved
    pub fn set_preset(&mut self, v: u32) {
        self.preset = v;
    }

    // bool is_drum_kit = 5;


    pub fn get_is_drum_kit(&self) -> bool {
        self.is_drum_kit
    }
    pub fn clear_is_drum_kit(&mut self) {
        self.is_drum_kit = false;
    }

    // Param is passed by value, moved
    pub fn set_is_drum_kit(&mut self, v: bool) {
        self.is_drum_kit = v;
    }

    // uint32 key_low = 6;


    pub fn get_key_low(&self) -> u32 {
        self.key_low
    }
    pub fn clear_key_low(&mut self) {
        self.key_low = 0;
    }

    // Param is passed by value, moved
    pub fn set_key_low(&mut self, v: u32) {
        self.key_low = v;
    }

    // uint32 key_high = 7;


    pub fn get_key_high(&self) -> u32 {
        self.key_high
    }
    pub fn clear_key_high(&mut self) {
        self.key_high = 0;
    }

    // Param is passed by value, moved
    pub fn set_key_high(&mut self, v: u32) {
        self.key_high = v;
    }
}

impl ::protobuf::Message for Instrument {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.name)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.display_name)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.bank = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.preset = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.is_drum_kit = tmp;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.key_low = tmp;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.key_high = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.name.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.name);
        }
        if !self.display_name.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.display_name);
        }
        if self.bank != 0 {
            my_size += ::protobuf::rt::value_size(3, self.bank, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.preset != 0 {
            my_size += ::protobuf::rt::value_size(4, self.preset, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.is_drum_kit != false {
            my_size += 2;
        }
        if self.key_low != 0 {
            my_size += ::protobuf::rt::value_size(6, self.key_low, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.key_high != 0 {
            my_size += ::protobuf::rt::value_size(7, self.key_high, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.name.is_empty() {
            os.write_string(1, &self.name)?;
        }
        if !self.display_name.is_empty() {
            os.write_string(2, &self.display_name)?;
        }
        if self.bank != 0 {
            os.write_uint32(3, self.bank)?;
        }
        if self.preset != 0 {
            os.write_uint32(4, self.preset)?;
        }
        if self.is_drum_kit != false {
            os.write_bool(5, self.is_drum_kit)?;
        }
        if self.key_low != 0 {
            os.write_uint32(6, self.key_low)?;
        }
        if self.key_high != 0 {
            os.write_uint32(7, self.key_high)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Instrument {
        Instrument::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "name",
                |m: &Instrument| { &m.name },
                |m: &mut Instrument| { &mut m.name },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "display_name",
                |m: &Instrument| { &m.display_name },
                |m: &mut Instrument| { &mut m.display_name },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "bank",
                |m: &Instrument| { &m.bank },
                |m: &mut Instrument| { &mut m.bank },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "preset",
                |m: &Instrument| { &m.preset },
                |m: &mut Instrument| { &mut m.preset },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "is_drum_kit",
                |m: &Instrument| { &m.is_drum_kit },
                |m: &mut Instrument| { &mut m.is_drum_kit },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "key_low",
                |m: &Instrument| { &m.key_low },
                |m: &mut Instrument| { &mut m.key_low },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "key_high",
                |m: &Instrument| { &m.key_high },
                |m: &mut Instrument| { &mut m.key_high },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<Instrument>(
                "Instrument",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static Instrument {
        static instance: ::protobuf::rt::LazyV2<Instrument> = ::protobuf::rt::LazyV2::INIT;
        instance.get(Instrument::new)
    }
}

impl ::protobuf::Clear for Instrument {
    fn clear(&mut self) {
        self.name.clear();
        self.display_name.clear();
        self.bank = 0;
        self.preset = 0;
        self.is_drum_kit = false;
        self.key_low = 0;
        self.key_high = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for Instrument {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for Instrument {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct InstrumentsList {
    // message fields
    pub instruments: ::protobuf::RepeatedField<Instrument>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a InstrumentsList {
    fn default() -> &'a InstrumentsList {
        <InstrumentsList as ::protobuf::Message>::default_instance()
    }
}

impl InstrumentsList {
    pub fn new() -> InstrumentsList {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.Midi.Msgs.Instrument instruments = 1;


    pub fn get_instruments(&self) -> &[Instrument] {
        &self.instruments
    }
    pub fn clear_instruments(&mut self) {
        self.instruments.clear();
    }

    // Param is passed by value, moved
    pub fn set_instruments(&mut self, v: ::protobuf::RepeatedField<Instrument>) {
        self.instruments = v;
    }

    // Mutable pointer to the field.
    pub fn mut_instruments(&mut self) -> &mut ::protobuf::RepeatedField<Instrument> {
        &mut self.instruments
    }

    // Take field
    pub fn take_instruments(&mut self) -> ::protobuf::RepeatedField<Instrument> {
        ::std::mem::replace(&mut self.instruments, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for InstrumentsList {
    fn is_initialized(&self) -> bool {
        for v in &self.instruments {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.instruments)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.instruments {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.instruments {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> InstrumentsList {
        InstrumentsList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Instrument>>(
                "instruments",
                |m: &InstrumentsList| { &m.instruments },
                |m: &mut InstrumentsList| { &mut m.instruments },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<InstrumentsList>(
                "InstrumentsList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static InstrumentsList {
        static instance: ::protobuf::rt::LazyV2<InstrumentsList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(InstrumentsList::new)
    }
}

impl ::protobuf::Clear for InstrumentsList {
    fn clear(&mut self) {
        self.instruments.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for InstrumentsList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for InstrumentsList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AudioChannel {
    // message fields
    pub channel: u32,
    pub active: bool,
    pub volume: u32,
    pub pan: u32,
    pub bank: u32,
    pub bank_msb: u32,
    pub preset: u32,
    pub expression: u32,
    pub channel_pressure: u32,
    pub pitch_bend: u32,
    pub pitch_wheel_sensitivity: u32,
    // message oneof groups
    pub _instrument: ::std::option::Option<AudioChannel_oneof__instrument>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AudioChannel {
    fn default() -> &'a AudioChannel {
        <AudioChannel as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AudioChannel_oneof__instrument {
    instrument(Instrument),
}

impl AudioChannel {
    pub fn new() -> AudioChannel {
        ::std::default::Default::default()
    }

    // uint32 channel = 1;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }

    // bool active = 2;


    pub fn get_active(&self) -> bool {
        self.active
    }
    pub fn clear_active(&mut self) {
        self.active = false;
    }

    // Param is passed by value, moved
    pub fn set_active(&mut self, v: bool) {
        self.active = v;
    }

    // .PianoRhythm.Serialization.Midi.Msgs.Instrument instrument = 3;


    pub fn get_instrument(&self) -> &Instrument {
        match self._instrument {
            ::std::option::Option::Some(AudioChannel_oneof__instrument::instrument(ref v)) => v,
            _ => <Instrument as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_instrument(&mut self) {
        self._instrument = ::std::option::Option::None;
    }

    pub fn has_instrument(&self) -> bool {
        match self._instrument {
            ::std::option::Option::Some(AudioChannel_oneof__instrument::instrument(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_instrument(&mut self, v: Instrument) {
        self._instrument = ::std::option::Option::Some(AudioChannel_oneof__instrument::instrument(v))
    }

    // Mutable pointer to the field.
    pub fn mut_instrument(&mut self) -> &mut Instrument {
        if let ::std::option::Option::Some(AudioChannel_oneof__instrument::instrument(_)) = self._instrument {
        } else {
            self._instrument = ::std::option::Option::Some(AudioChannel_oneof__instrument::instrument(Instrument::new()));
        }
        match self._instrument {
            ::std::option::Option::Some(AudioChannel_oneof__instrument::instrument(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_instrument(&mut self) -> Instrument {
        if self.has_instrument() {
            match self._instrument.take() {
                ::std::option::Option::Some(AudioChannel_oneof__instrument::instrument(v)) => v,
                _ => panic!(),
            }
        } else {
            Instrument::new()
        }
    }

    // uint32 volume = 4;


    pub fn get_volume(&self) -> u32 {
        self.volume
    }
    pub fn clear_volume(&mut self) {
        self.volume = 0;
    }

    // Param is passed by value, moved
    pub fn set_volume(&mut self, v: u32) {
        self.volume = v;
    }

    // uint32 pan = 5;


    pub fn get_pan(&self) -> u32 {
        self.pan
    }
    pub fn clear_pan(&mut self) {
        self.pan = 0;
    }

    // Param is passed by value, moved
    pub fn set_pan(&mut self, v: u32) {
        self.pan = v;
    }

    // uint32 bank = 6;


    pub fn get_bank(&self) -> u32 {
        self.bank
    }
    pub fn clear_bank(&mut self) {
        self.bank = 0;
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: u32) {
        self.bank = v;
    }

    // uint32 bank_msb = 7;


    pub fn get_bank_msb(&self) -> u32 {
        self.bank_msb
    }
    pub fn clear_bank_msb(&mut self) {
        self.bank_msb = 0;
    }

    // Param is passed by value, moved
    pub fn set_bank_msb(&mut self, v: u32) {
        self.bank_msb = v;
    }

    // uint32 preset = 8;


    pub fn get_preset(&self) -> u32 {
        self.preset
    }
    pub fn clear_preset(&mut self) {
        self.preset = 0;
    }

    // Param is passed by value, moved
    pub fn set_preset(&mut self, v: u32) {
        self.preset = v;
    }

    // uint32 expression = 9;


    pub fn get_expression(&self) -> u32 {
        self.expression
    }
    pub fn clear_expression(&mut self) {
        self.expression = 0;
    }

    // Param is passed by value, moved
    pub fn set_expression(&mut self, v: u32) {
        self.expression = v;
    }

    // uint32 channel_pressure = 10;


    pub fn get_channel_pressure(&self) -> u32 {
        self.channel_pressure
    }
    pub fn clear_channel_pressure(&mut self) {
        self.channel_pressure = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel_pressure(&mut self, v: u32) {
        self.channel_pressure = v;
    }

    // uint32 pitch_bend = 11;


    pub fn get_pitch_bend(&self) -> u32 {
        self.pitch_bend
    }
    pub fn clear_pitch_bend(&mut self) {
        self.pitch_bend = 0;
    }

    // Param is passed by value, moved
    pub fn set_pitch_bend(&mut self, v: u32) {
        self.pitch_bend = v;
    }

    // uint32 pitch_wheel_sensitivity = 12;


    pub fn get_pitch_wheel_sensitivity(&self) -> u32 {
        self.pitch_wheel_sensitivity
    }
    pub fn clear_pitch_wheel_sensitivity(&mut self) {
        self.pitch_wheel_sensitivity = 0;
    }

    // Param is passed by value, moved
    pub fn set_pitch_wheel_sensitivity(&mut self, v: u32) {
        self.pitch_wheel_sensitivity = v;
    }
}

impl ::protobuf::Message for AudioChannel {
    fn is_initialized(&self) -> bool {
        if let Some(AudioChannel_oneof__instrument::instrument(ref v)) = self._instrument {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.active = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._instrument = ::std::option::Option::Some(AudioChannel_oneof__instrument::instrument(is.read_message()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.volume = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.pan = tmp;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.bank = tmp;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.bank_msb = tmp;
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.preset = tmp;
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.expression = tmp;
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel_pressure = tmp;
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.pitch_bend = tmp;
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.pitch_wheel_sensitivity = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.active != false {
            my_size += 2;
        }
        if self.volume != 0 {
            my_size += ::protobuf::rt::value_size(4, self.volume, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.pan != 0 {
            my_size += ::protobuf::rt::value_size(5, self.pan, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.bank != 0 {
            my_size += ::protobuf::rt::value_size(6, self.bank, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.bank_msb != 0 {
            my_size += ::protobuf::rt::value_size(7, self.bank_msb, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.preset != 0 {
            my_size += ::protobuf::rt::value_size(8, self.preset, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.expression != 0 {
            my_size += ::protobuf::rt::value_size(9, self.expression, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.channel_pressure != 0 {
            my_size += ::protobuf::rt::value_size(10, self.channel_pressure, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.pitch_bend != 0 {
            my_size += ::protobuf::rt::value_size(11, self.pitch_bend, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.pitch_wheel_sensitivity != 0 {
            my_size += ::protobuf::rt::value_size(12, self.pitch_wheel_sensitivity, ::protobuf::wire_format::WireTypeVarint);
        }
        if let ::std::option::Option::Some(ref v) = self._instrument {
            match v {
                &AudioChannel_oneof__instrument::instrument(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_uint32(1, self.channel)?;
        }
        if self.active != false {
            os.write_bool(2, self.active)?;
        }
        if self.volume != 0 {
            os.write_uint32(4, self.volume)?;
        }
        if self.pan != 0 {
            os.write_uint32(5, self.pan)?;
        }
        if self.bank != 0 {
            os.write_uint32(6, self.bank)?;
        }
        if self.bank_msb != 0 {
            os.write_uint32(7, self.bank_msb)?;
        }
        if self.preset != 0 {
            os.write_uint32(8, self.preset)?;
        }
        if self.expression != 0 {
            os.write_uint32(9, self.expression)?;
        }
        if self.channel_pressure != 0 {
            os.write_uint32(10, self.channel_pressure)?;
        }
        if self.pitch_bend != 0 {
            os.write_uint32(11, self.pitch_bend)?;
        }
        if self.pitch_wheel_sensitivity != 0 {
            os.write_uint32(12, self.pitch_wheel_sensitivity)?;
        }
        if let ::std::option::Option::Some(ref v) = self._instrument {
            match v {
                &AudioChannel_oneof__instrument::instrument(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AudioChannel {
        AudioChannel::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &AudioChannel| { &m.channel },
                |m: &mut AudioChannel| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "active",
                |m: &AudioChannel| { &m.active },
                |m: &mut AudioChannel| { &mut m.active },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, Instrument>(
                "instrument",
                AudioChannel::has_instrument,
                AudioChannel::get_instrument,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "volume",
                |m: &AudioChannel| { &m.volume },
                |m: &mut AudioChannel| { &mut m.volume },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "pan",
                |m: &AudioChannel| { &m.pan },
                |m: &mut AudioChannel| { &mut m.pan },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "bank",
                |m: &AudioChannel| { &m.bank },
                |m: &mut AudioChannel| { &mut m.bank },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "bank_msb",
                |m: &AudioChannel| { &m.bank_msb },
                |m: &mut AudioChannel| { &mut m.bank_msb },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "preset",
                |m: &AudioChannel| { &m.preset },
                |m: &mut AudioChannel| { &mut m.preset },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "expression",
                |m: &AudioChannel| { &m.expression },
                |m: &mut AudioChannel| { &mut m.expression },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel_pressure",
                |m: &AudioChannel| { &m.channel_pressure },
                |m: &mut AudioChannel| { &mut m.channel_pressure },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "pitch_bend",
                |m: &AudioChannel| { &m.pitch_bend },
                |m: &mut AudioChannel| { &mut m.pitch_bend },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "pitch_wheel_sensitivity",
                |m: &AudioChannel| { &m.pitch_wheel_sensitivity },
                |m: &mut AudioChannel| { &mut m.pitch_wheel_sensitivity },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AudioChannel>(
                "AudioChannel",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AudioChannel {
        static instance: ::protobuf::rt::LazyV2<AudioChannel> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AudioChannel::new)
    }
}

impl ::protobuf::Clear for AudioChannel {
    fn clear(&mut self) {
        self.channel = 0;
        self.active = false;
        self._instrument = ::std::option::Option::None;
        self.volume = 0;
        self.pan = 0;
        self.bank = 0;
        self.bank_msb = 0;
        self.preset = 0;
        self.expression = 0;
        self.channel_pressure = 0;
        self.pitch_bend = 0;
        self.pitch_wheel_sensitivity = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AudioChannel {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AudioChannel {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct SoundfontSetting {
    // message fields
    pub name: ::std::string::String,
    pub is_default: bool,
    pub path: ::std::string::String,
    pub custom: bool,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SoundfontSetting {
    fn default() -> &'a SoundfontSetting {
        <SoundfontSetting as ::protobuf::Message>::default_instance()
    }
}

impl SoundfontSetting {
    pub fn new() -> SoundfontSetting {
        ::std::default::Default::default()
    }

    // string name = 1;


    pub fn get_name(&self) -> &str {
        &self.name
    }
    pub fn clear_name(&mut self) {
        self.name.clear();
    }

    // Param is passed by value, moved
    pub fn set_name(&mut self, v: ::std::string::String) {
        self.name = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_name(&mut self) -> &mut ::std::string::String {
        &mut self.name
    }

    // Take field
    pub fn take_name(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.name, ::std::string::String::new())
    }

    // bool is_default = 2;


    pub fn get_is_default(&self) -> bool {
        self.is_default
    }
    pub fn clear_is_default(&mut self) {
        self.is_default = false;
    }

    // Param is passed by value, moved
    pub fn set_is_default(&mut self, v: bool) {
        self.is_default = v;
    }

    // string path = 3;


    pub fn get_path(&self) -> &str {
        &self.path
    }
    pub fn clear_path(&mut self) {
        self.path.clear();
    }

    // Param is passed by value, moved
    pub fn set_path(&mut self, v: ::std::string::String) {
        self.path = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_path(&mut self) -> &mut ::std::string::String {
        &mut self.path
    }

    // Take field
    pub fn take_path(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.path, ::std::string::String::new())
    }

    // bool custom = 4;


    pub fn get_custom(&self) -> bool {
        self.custom
    }
    pub fn clear_custom(&mut self) {
        self.custom = false;
    }

    // Param is passed by value, moved
    pub fn set_custom(&mut self, v: bool) {
        self.custom = v;
    }
}

impl ::protobuf::Message for SoundfontSetting {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.name)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.is_default = tmp;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.path)?;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.custom = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.name.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.name);
        }
        if self.is_default != false {
            my_size += 2;
        }
        if !self.path.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.path);
        }
        if self.custom != false {
            my_size += 2;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.name.is_empty() {
            os.write_string(1, &self.name)?;
        }
        if self.is_default != false {
            os.write_bool(2, self.is_default)?;
        }
        if !self.path.is_empty() {
            os.write_string(3, &self.path)?;
        }
        if self.custom != false {
            os.write_bool(4, self.custom)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SoundfontSetting {
        SoundfontSetting::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "name",
                |m: &SoundfontSetting| { &m.name },
                |m: &mut SoundfontSetting| { &mut m.name },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "is_default",
                |m: &SoundfontSetting| { &m.is_default },
                |m: &mut SoundfontSetting| { &mut m.is_default },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "path",
                |m: &SoundfontSetting| { &m.path },
                |m: &mut SoundfontSetting| { &mut m.path },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "custom",
                |m: &SoundfontSetting| { &m.custom },
                |m: &mut SoundfontSetting| { &mut m.custom },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<SoundfontSetting>(
                "SoundfontSetting",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static SoundfontSetting {
        static instance: ::protobuf::rt::LazyV2<SoundfontSetting> = ::protobuf::rt::LazyV2::INIT;
        instance.get(SoundfontSetting::new)
    }
}

impl ::protobuf::Clear for SoundfontSetting {
    fn clear(&mut self) {
        self.name.clear();
        self.is_default = false;
        self.path.clear();
        self.custom = false;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for SoundfontSetting {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for SoundfontSetting {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AudioServiceNoteActivity {
    // message fields
    pub channel: i32,
    pub field_type: AudioServiceNoteActivityNoteType,
    pub socketID: ::std::string::String,
    pub isClient: bool,
    pub velocity: i32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AudioServiceNoteActivity {
    fn default() -> &'a AudioServiceNoteActivity {
        <AudioServiceNoteActivity as ::protobuf::Message>::default_instance()
    }
}

impl AudioServiceNoteActivity {
    pub fn new() -> AudioServiceNoteActivity {
        ::std::default::Default::default()
    }

    // int32 channel = 1;


    pub fn get_channel(&self) -> i32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: i32) {
        self.channel = v;
    }

    // .PianoRhythm.Serialization.Midi.Msgs.AudioServiceNoteActivityNoteType type = 2;


    pub fn get_field_type(&self) -> AudioServiceNoteActivityNoteType {
        self.field_type
    }
    pub fn clear_field_type(&mut self) {
        self.field_type = AudioServiceNoteActivityNoteType::ON;
    }

    // Param is passed by value, moved
    pub fn set_field_type(&mut self, v: AudioServiceNoteActivityNoteType) {
        self.field_type = v;
    }

    // string socketID = 3;


    pub fn get_socketID(&self) -> &str {
        &self.socketID
    }
    pub fn clear_socketID(&mut self) {
        self.socketID.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.socketID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        &mut self.socketID
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketID, ::std::string::String::new())
    }

    // bool isClient = 4;


    pub fn get_isClient(&self) -> bool {
        self.isClient
    }
    pub fn clear_isClient(&mut self) {
        self.isClient = false;
    }

    // Param is passed by value, moved
    pub fn set_isClient(&mut self, v: bool) {
        self.isClient = v;
    }

    // int32 velocity = 5;


    pub fn get_velocity(&self) -> i32 {
        self.velocity
    }
    pub fn clear_velocity(&mut self) {
        self.velocity = 0;
    }

    // Param is passed by value, moved
    pub fn set_velocity(&mut self, v: i32) {
        self.velocity = v;
    }
}

impl ::protobuf::Message for AudioServiceNoteActivity {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.channel = tmp;
                },
                2 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.field_type, 2, &mut self.unknown_fields)?
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketID)?;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isClient = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.velocity = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.field_type != AudioServiceNoteActivityNoteType::ON {
            my_size += ::protobuf::rt::enum_size(2, self.field_type);
        }
        if !self.socketID.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.socketID);
        }
        if self.isClient != false {
            my_size += 2;
        }
        if self.velocity != 0 {
            my_size += ::protobuf::rt::value_size(5, self.velocity, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_int32(1, self.channel)?;
        }
        if self.field_type != AudioServiceNoteActivityNoteType::ON {
            os.write_enum(2, ::protobuf::ProtobufEnum::value(&self.field_type))?;
        }
        if !self.socketID.is_empty() {
            os.write_string(3, &self.socketID)?;
        }
        if self.isClient != false {
            os.write_bool(4, self.isClient)?;
        }
        if self.velocity != 0 {
            os.write_int32(5, self.velocity)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AudioServiceNoteActivity {
        AudioServiceNoteActivity::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "channel",
                |m: &AudioServiceNoteActivity| { &m.channel },
                |m: &mut AudioServiceNoteActivity| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<AudioServiceNoteActivityNoteType>>(
                "type",
                |m: &AudioServiceNoteActivity| { &m.field_type },
                |m: &mut AudioServiceNoteActivity| { &mut m.field_type },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketID",
                |m: &AudioServiceNoteActivity| { &m.socketID },
                |m: &mut AudioServiceNoteActivity| { &mut m.socketID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isClient",
                |m: &AudioServiceNoteActivity| { &m.isClient },
                |m: &mut AudioServiceNoteActivity| { &mut m.isClient },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "velocity",
                |m: &AudioServiceNoteActivity| { &m.velocity },
                |m: &mut AudioServiceNoteActivity| { &mut m.velocity },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AudioServiceNoteActivity>(
                "AudioServiceNoteActivity",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AudioServiceNoteActivity {
        static instance: ::protobuf::rt::LazyV2<AudioServiceNoteActivity> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AudioServiceNoteActivity::new)
    }
}

impl ::protobuf::Clear for AudioServiceNoteActivity {
    fn clear(&mut self) {
        self.channel = 0;
        self.field_type = AudioServiceNoteActivityNoteType::ON;
        self.socketID.clear();
        self.isClient = false;
        self.velocity = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AudioServiceNoteActivity {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AudioServiceNoteActivity {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct SetChannelInstrumentPayload {
    // message fields
    pub channel: u32,
    pub bank: u32,
    pub preset: u32,
    pub field_type: SetChannelInstrumentType,
    pub setActive: bool,
    // message oneof groups
    pub _socket_id: ::std::option::Option<SetChannelInstrumentPayload_oneof__socket_id>,
    pub _volume: ::std::option::Option<SetChannelInstrumentPayload_oneof__volume>,
    pub _pan: ::std::option::Option<SetChannelInstrumentPayload_oneof__pan>,
    pub _expression: ::std::option::Option<SetChannelInstrumentPayload_oneof__expression>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SetChannelInstrumentPayload {
    fn default() -> &'a SetChannelInstrumentPayload {
        <SetChannelInstrumentPayload as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum SetChannelInstrumentPayload_oneof__socket_id {
    socket_id(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum SetChannelInstrumentPayload_oneof__volume {
    volume(u32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum SetChannelInstrumentPayload_oneof__pan {
    pan(u32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum SetChannelInstrumentPayload_oneof__expression {
    expression(u32),
}

impl SetChannelInstrumentPayload {
    pub fn new() -> SetChannelInstrumentPayload {
        ::std::default::Default::default()
    }

    // uint32 channel = 1;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }

    // uint32 bank = 2;


    pub fn get_bank(&self) -> u32 {
        self.bank
    }
    pub fn clear_bank(&mut self) {
        self.bank = 0;
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: u32) {
        self.bank = v;
    }

    // uint32 preset = 3;


    pub fn get_preset(&self) -> u32 {
        self.preset
    }
    pub fn clear_preset(&mut self) {
        self.preset = 0;
    }

    // Param is passed by value, moved
    pub fn set_preset(&mut self, v: u32) {
        self.preset = v;
    }

    // .PianoRhythm.Serialization.Midi.Msgs.SetChannelInstrumentType type = 4;


    pub fn get_field_type(&self) -> SetChannelInstrumentType {
        self.field_type
    }
    pub fn clear_field_type(&mut self) {
        self.field_type = SetChannelInstrumentType::Add;
    }

    // Param is passed by value, moved
    pub fn set_field_type(&mut self, v: SetChannelInstrumentType) {
        self.field_type = v;
    }

    // bool setActive = 5;


    pub fn get_setActive(&self) -> bool {
        self.setActive
    }
    pub fn clear_setActive(&mut self) {
        self.setActive = false;
    }

    // Param is passed by value, moved
    pub fn set_setActive(&mut self, v: bool) {
        self.setActive = v;
    }

    // string socket_id = 6;


    pub fn get_socket_id(&self) -> &str {
        match self._socket_id {
            ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__socket_id::socket_id(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_socket_id(&mut self) {
        self._socket_id = ::std::option::Option::None;
    }

    pub fn has_socket_id(&self) -> bool {
        match self._socket_id {
            ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__socket_id::socket_id(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socket_id(&mut self, v: ::std::string::String) {
        self._socket_id = ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__socket_id::socket_id(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socket_id(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__socket_id::socket_id(_)) = self._socket_id {
        } else {
            self._socket_id = ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__socket_id::socket_id(::std::string::String::new()));
        }
        match self._socket_id {
            ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__socket_id::socket_id(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socket_id(&mut self) -> ::std::string::String {
        if self.has_socket_id() {
            match self._socket_id.take() {
                ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__socket_id::socket_id(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // uint32 volume = 7;


    pub fn get_volume(&self) -> u32 {
        match self._volume {
            ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__volume::volume(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_volume(&mut self) {
        self._volume = ::std::option::Option::None;
    }

    pub fn has_volume(&self) -> bool {
        match self._volume {
            ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__volume::volume(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_volume(&mut self, v: u32) {
        self._volume = ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__volume::volume(v))
    }

    // uint32 pan = 8;


    pub fn get_pan(&self) -> u32 {
        match self._pan {
            ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__pan::pan(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_pan(&mut self) {
        self._pan = ::std::option::Option::None;
    }

    pub fn has_pan(&self) -> bool {
        match self._pan {
            ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__pan::pan(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pan(&mut self, v: u32) {
        self._pan = ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__pan::pan(v))
    }

    // uint32 expression = 9;


    pub fn get_expression(&self) -> u32 {
        match self._expression {
            ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__expression::expression(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_expression(&mut self) {
        self._expression = ::std::option::Option::None;
    }

    pub fn has_expression(&self) -> bool {
        match self._expression {
            ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__expression::expression(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_expression(&mut self, v: u32) {
        self._expression = ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__expression::expression(v))
    }
}

impl ::protobuf::Message for SetChannelInstrumentPayload {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.bank = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.preset = tmp;
                },
                4 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.field_type, 4, &mut self.unknown_fields)?
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.setActive = tmp;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._socket_id = ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__socket_id::socket_id(is.read_string()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._volume = ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__volume::volume(is.read_uint32()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._pan = ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__pan::pan(is.read_uint32()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._expression = ::std::option::Option::Some(SetChannelInstrumentPayload_oneof__expression::expression(is.read_uint32()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.bank != 0 {
            my_size += ::protobuf::rt::value_size(2, self.bank, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.preset != 0 {
            my_size += ::protobuf::rt::value_size(3, self.preset, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.field_type != SetChannelInstrumentType::Add {
            my_size += ::protobuf::rt::enum_size(4, self.field_type);
        }
        if self.setActive != false {
            my_size += 2;
        }
        if let ::std::option::Option::Some(ref v) = self._socket_id {
            match v {
                &SetChannelInstrumentPayload_oneof__socket_id::socket_id(ref v) => {
                    my_size += ::protobuf::rt::string_size(6, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._volume {
            match v {
                &SetChannelInstrumentPayload_oneof__volume::volume(v) => {
                    my_size += ::protobuf::rt::value_size(7, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pan {
            match v {
                &SetChannelInstrumentPayload_oneof__pan::pan(v) => {
                    my_size += ::protobuf::rt::value_size(8, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._expression {
            match v {
                &SetChannelInstrumentPayload_oneof__expression::expression(v) => {
                    my_size += ::protobuf::rt::value_size(9, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_uint32(1, self.channel)?;
        }
        if self.bank != 0 {
            os.write_uint32(2, self.bank)?;
        }
        if self.preset != 0 {
            os.write_uint32(3, self.preset)?;
        }
        if self.field_type != SetChannelInstrumentType::Add {
            os.write_enum(4, ::protobuf::ProtobufEnum::value(&self.field_type))?;
        }
        if self.setActive != false {
            os.write_bool(5, self.setActive)?;
        }
        if let ::std::option::Option::Some(ref v) = self._socket_id {
            match v {
                &SetChannelInstrumentPayload_oneof__socket_id::socket_id(ref v) => {
                    os.write_string(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._volume {
            match v {
                &SetChannelInstrumentPayload_oneof__volume::volume(v) => {
                    os.write_uint32(7, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pan {
            match v {
                &SetChannelInstrumentPayload_oneof__pan::pan(v) => {
                    os.write_uint32(8, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._expression {
            match v {
                &SetChannelInstrumentPayload_oneof__expression::expression(v) => {
                    os.write_uint32(9, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SetChannelInstrumentPayload {
        SetChannelInstrumentPayload::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &SetChannelInstrumentPayload| { &m.channel },
                |m: &mut SetChannelInstrumentPayload| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "bank",
                |m: &SetChannelInstrumentPayload| { &m.bank },
                |m: &mut SetChannelInstrumentPayload| { &mut m.bank },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "preset",
                |m: &SetChannelInstrumentPayload| { &m.preset },
                |m: &mut SetChannelInstrumentPayload| { &mut m.preset },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<SetChannelInstrumentType>>(
                "type",
                |m: &SetChannelInstrumentPayload| { &m.field_type },
                |m: &mut SetChannelInstrumentPayload| { &mut m.field_type },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "setActive",
                |m: &SetChannelInstrumentPayload| { &m.setActive },
                |m: &mut SetChannelInstrumentPayload| { &mut m.setActive },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "socket_id",
                SetChannelInstrumentPayload::has_socket_id,
                SetChannelInstrumentPayload::get_socket_id,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "volume",
                SetChannelInstrumentPayload::has_volume,
                SetChannelInstrumentPayload::get_volume,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "pan",
                SetChannelInstrumentPayload::has_pan,
                SetChannelInstrumentPayload::get_pan,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "expression",
                SetChannelInstrumentPayload::has_expression,
                SetChannelInstrumentPayload::get_expression,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<SetChannelInstrumentPayload>(
                "SetChannelInstrumentPayload",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static SetChannelInstrumentPayload {
        static instance: ::protobuf::rt::LazyV2<SetChannelInstrumentPayload> = ::protobuf::rt::LazyV2::INIT;
        instance.get(SetChannelInstrumentPayload::new)
    }
}

impl ::protobuf::Clear for SetChannelInstrumentPayload {
    fn clear(&mut self) {
        self.channel = 0;
        self.bank = 0;
        self.preset = 0;
        self.field_type = SetChannelInstrumentType::Add;
        self.setActive = false;
        self._socket_id = ::std::option::Option::None;
        self._volume = ::std::option::Option::None;
        self._pan = ::std::option::Option::None;
        self._expression = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for SetChannelInstrumentPayload {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for SetChannelInstrumentPayload {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct SetChannelDetailsPayload {
    // message fields
    pub channel: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SetChannelDetailsPayload {
    fn default() -> &'a SetChannelDetailsPayload {
        <SetChannelDetailsPayload as ::protobuf::Message>::default_instance()
    }
}

impl SetChannelDetailsPayload {
    pub fn new() -> SetChannelDetailsPayload {
        ::std::default::Default::default()
    }

    // uint32 channel = 1;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }
}

impl ::protobuf::Message for SetChannelDetailsPayload {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_uint32(1, self.channel)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SetChannelDetailsPayload {
        SetChannelDetailsPayload::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &SetChannelDetailsPayload| { &m.channel },
                |m: &mut SetChannelDetailsPayload| { &mut m.channel },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<SetChannelDetailsPayload>(
                "SetChannelDetailsPayload",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static SetChannelDetailsPayload {
        static instance: ::protobuf::rt::LazyV2<SetChannelDetailsPayload> = ::protobuf::rt::LazyV2::INIT;
        instance.get(SetChannelDetailsPayload::new)
    }
}

impl ::protobuf::Clear for SetChannelDetailsPayload {
    fn clear(&mut self) {
        self.channel = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for SetChannelDetailsPayload {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for SetChannelDetailsPayload {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct UpdateChannelPayload {
    // message fields
    pub channel: u32,
    // message oneof groups
    pub data: ::std::option::Option<UpdateChannelPayload_oneof_data>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UpdateChannelPayload {
    fn default() -> &'a UpdateChannelPayload {
        <UpdateChannelPayload as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum UpdateChannelPayload_oneof_data {
    expression(u32),
    pan(u32),
    volume(u32),
    bank(u32),
    preset(u32),
    pitch(u32),
}

impl UpdateChannelPayload {
    pub fn new() -> UpdateChannelPayload {
        ::std::default::Default::default()
    }

    // uint32 channel = 1;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }

    // uint32 expression = 2;


    pub fn get_expression(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::expression(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_expression(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_expression(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::expression(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_expression(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::expression(v))
    }

    // uint32 pan = 3;


    pub fn get_pan(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::pan(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_pan(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_pan(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::pan(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pan(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::pan(v))
    }

    // uint32 volume = 4;


    pub fn get_volume(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::volume(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_volume(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_volume(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::volume(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_volume(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::volume(v))
    }

    // uint32 bank = 5;


    pub fn get_bank(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::bank(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_bank(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_bank(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::bank(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::bank(v))
    }

    // uint32 preset = 6;


    pub fn get_preset(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::preset(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_preset(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_preset(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::preset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_preset(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::preset(v))
    }

    // uint32 pitch = 7;


    pub fn get_pitch(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::pitch(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_pitch(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_pitch(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UpdateChannelPayload_oneof_data::pitch(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pitch(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::pitch(v))
    }
}

impl ::protobuf::Message for UpdateChannelPayload {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::expression(is.read_uint32()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::pan(is.read_uint32()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::volume(is.read_uint32()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::bank(is.read_uint32()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::preset(is.read_uint32()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UpdateChannelPayload_oneof_data::pitch(is.read_uint32()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &UpdateChannelPayload_oneof_data::expression(v) => {
                    my_size += ::protobuf::rt::value_size(2, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &UpdateChannelPayload_oneof_data::pan(v) => {
                    my_size += ::protobuf::rt::value_size(3, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &UpdateChannelPayload_oneof_data::volume(v) => {
                    my_size += ::protobuf::rt::value_size(4, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &UpdateChannelPayload_oneof_data::bank(v) => {
                    my_size += ::protobuf::rt::value_size(5, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &UpdateChannelPayload_oneof_data::preset(v) => {
                    my_size += ::protobuf::rt::value_size(6, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &UpdateChannelPayload_oneof_data::pitch(v) => {
                    my_size += ::protobuf::rt::value_size(7, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_uint32(1, self.channel)?;
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &UpdateChannelPayload_oneof_data::expression(v) => {
                    os.write_uint32(2, v)?;
                },
                &UpdateChannelPayload_oneof_data::pan(v) => {
                    os.write_uint32(3, v)?;
                },
                &UpdateChannelPayload_oneof_data::volume(v) => {
                    os.write_uint32(4, v)?;
                },
                &UpdateChannelPayload_oneof_data::bank(v) => {
                    os.write_uint32(5, v)?;
                },
                &UpdateChannelPayload_oneof_data::preset(v) => {
                    os.write_uint32(6, v)?;
                },
                &UpdateChannelPayload_oneof_data::pitch(v) => {
                    os.write_uint32(7, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UpdateChannelPayload {
        UpdateChannelPayload::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &UpdateChannelPayload| { &m.channel },
                |m: &mut UpdateChannelPayload| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "expression",
                UpdateChannelPayload::has_expression,
                UpdateChannelPayload::get_expression,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "pan",
                UpdateChannelPayload::has_pan,
                UpdateChannelPayload::get_pan,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "volume",
                UpdateChannelPayload::has_volume,
                UpdateChannelPayload::get_volume,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "bank",
                UpdateChannelPayload::has_bank,
                UpdateChannelPayload::get_bank,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "preset",
                UpdateChannelPayload::has_preset,
                UpdateChannelPayload::get_preset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "pitch",
                UpdateChannelPayload::has_pitch,
                UpdateChannelPayload::get_pitch,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<UpdateChannelPayload>(
                "UpdateChannelPayload",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static UpdateChannelPayload {
        static instance: ::protobuf::rt::LazyV2<UpdateChannelPayload> = ::protobuf::rt::LazyV2::INIT;
        instance.get(UpdateChannelPayload::new)
    }
}

impl ::protobuf::Clear for UpdateChannelPayload {
    fn clear(&mut self) {
        self.channel = 0;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for UpdateChannelPayload {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for UpdateChannelPayload {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct SynthEventProgramChangePayload {
    // message fields
    pub channel: u32,
    // message oneof groups
    pub _expression: ::std::option::Option<SynthEventProgramChangePayload_oneof__expression>,
    pub _pan: ::std::option::Option<SynthEventProgramChangePayload_oneof__pan>,
    pub _volume: ::std::option::Option<SynthEventProgramChangePayload_oneof__volume>,
    pub _bank: ::std::option::Option<SynthEventProgramChangePayload_oneof__bank>,
    pub _preset: ::std::option::Option<SynthEventProgramChangePayload_oneof__preset>,
    pub _pitch: ::std::option::Option<SynthEventProgramChangePayload_oneof__pitch>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SynthEventProgramChangePayload {
    fn default() -> &'a SynthEventProgramChangePayload {
        <SynthEventProgramChangePayload as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum SynthEventProgramChangePayload_oneof__expression {
    expression(u32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum SynthEventProgramChangePayload_oneof__pan {
    pan(u32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum SynthEventProgramChangePayload_oneof__volume {
    volume(u32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum SynthEventProgramChangePayload_oneof__bank {
    bank(u32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum SynthEventProgramChangePayload_oneof__preset {
    preset(u32),
}

#[derive(Clone,PartialEq,Debug)]
pub enum SynthEventProgramChangePayload_oneof__pitch {
    pitch(u32),
}

impl SynthEventProgramChangePayload {
    pub fn new() -> SynthEventProgramChangePayload {
        ::std::default::Default::default()
    }

    // uint32 channel = 1;


    pub fn get_channel(&self) -> u32 {
        self.channel
    }
    pub fn clear_channel(&mut self) {
        self.channel = 0;
    }

    // Param is passed by value, moved
    pub fn set_channel(&mut self, v: u32) {
        self.channel = v;
    }

    // uint32 expression = 2;


    pub fn get_expression(&self) -> u32 {
        match self._expression {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__expression::expression(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_expression(&mut self) {
        self._expression = ::std::option::Option::None;
    }

    pub fn has_expression(&self) -> bool {
        match self._expression {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__expression::expression(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_expression(&mut self, v: u32) {
        self._expression = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__expression::expression(v))
    }

    // uint32 pan = 3;


    pub fn get_pan(&self) -> u32 {
        match self._pan {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__pan::pan(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_pan(&mut self) {
        self._pan = ::std::option::Option::None;
    }

    pub fn has_pan(&self) -> bool {
        match self._pan {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__pan::pan(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pan(&mut self, v: u32) {
        self._pan = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__pan::pan(v))
    }

    // uint32 volume = 4;


    pub fn get_volume(&self) -> u32 {
        match self._volume {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__volume::volume(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_volume(&mut self) {
        self._volume = ::std::option::Option::None;
    }

    pub fn has_volume(&self) -> bool {
        match self._volume {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__volume::volume(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_volume(&mut self, v: u32) {
        self._volume = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__volume::volume(v))
    }

    // uint32 bank = 5;


    pub fn get_bank(&self) -> u32 {
        match self._bank {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__bank::bank(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_bank(&mut self) {
        self._bank = ::std::option::Option::None;
    }

    pub fn has_bank(&self) -> bool {
        match self._bank {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__bank::bank(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: u32) {
        self._bank = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__bank::bank(v))
    }

    // uint32 preset = 6;


    pub fn get_preset(&self) -> u32 {
        match self._preset {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__preset::preset(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_preset(&mut self) {
        self._preset = ::std::option::Option::None;
    }

    pub fn has_preset(&self) -> bool {
        match self._preset {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__preset::preset(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_preset(&mut self, v: u32) {
        self._preset = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__preset::preset(v))
    }

    // uint32 pitch = 7;


    pub fn get_pitch(&self) -> u32 {
        match self._pitch {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__pitch::pitch(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_pitch(&mut self) {
        self._pitch = ::std::option::Option::None;
    }

    pub fn has_pitch(&self) -> bool {
        match self._pitch {
            ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__pitch::pitch(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pitch(&mut self, v: u32) {
        self._pitch = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__pitch::pitch(v))
    }
}

impl ::protobuf::Message for SynthEventProgramChangePayload {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.channel = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._expression = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__expression::expression(is.read_uint32()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._pan = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__pan::pan(is.read_uint32()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._volume = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__volume::volume(is.read_uint32()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._bank = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__bank::bank(is.read_uint32()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._preset = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__preset::preset(is.read_uint32()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._pitch = ::std::option::Option::Some(SynthEventProgramChangePayload_oneof__pitch::pitch(is.read_uint32()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.channel != 0 {
            my_size += ::protobuf::rt::value_size(1, self.channel, ::protobuf::wire_format::WireTypeVarint);
        }
        if let ::std::option::Option::Some(ref v) = self._expression {
            match v {
                &SynthEventProgramChangePayload_oneof__expression::expression(v) => {
                    my_size += ::protobuf::rt::value_size(2, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pan {
            match v {
                &SynthEventProgramChangePayload_oneof__pan::pan(v) => {
                    my_size += ::protobuf::rt::value_size(3, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._volume {
            match v {
                &SynthEventProgramChangePayload_oneof__volume::volume(v) => {
                    my_size += ::protobuf::rt::value_size(4, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._bank {
            match v {
                &SynthEventProgramChangePayload_oneof__bank::bank(v) => {
                    my_size += ::protobuf::rt::value_size(5, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._preset {
            match v {
                &SynthEventProgramChangePayload_oneof__preset::preset(v) => {
                    my_size += ::protobuf::rt::value_size(6, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pitch {
            match v {
                &SynthEventProgramChangePayload_oneof__pitch::pitch(v) => {
                    my_size += ::protobuf::rt::value_size(7, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.channel != 0 {
            os.write_uint32(1, self.channel)?;
        }
        if let ::std::option::Option::Some(ref v) = self._expression {
            match v {
                &SynthEventProgramChangePayload_oneof__expression::expression(v) => {
                    os.write_uint32(2, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pan {
            match v {
                &SynthEventProgramChangePayload_oneof__pan::pan(v) => {
                    os.write_uint32(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._volume {
            match v {
                &SynthEventProgramChangePayload_oneof__volume::volume(v) => {
                    os.write_uint32(4, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._bank {
            match v {
                &SynthEventProgramChangePayload_oneof__bank::bank(v) => {
                    os.write_uint32(5, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._preset {
            match v {
                &SynthEventProgramChangePayload_oneof__preset::preset(v) => {
                    os.write_uint32(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pitch {
            match v {
                &SynthEventProgramChangePayload_oneof__pitch::pitch(v) => {
                    os.write_uint32(7, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SynthEventProgramChangePayload {
        SynthEventProgramChangePayload::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "channel",
                |m: &SynthEventProgramChangePayload| { &m.channel },
                |m: &mut SynthEventProgramChangePayload| { &mut m.channel },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "expression",
                SynthEventProgramChangePayload::has_expression,
                SynthEventProgramChangePayload::get_expression,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "pan",
                SynthEventProgramChangePayload::has_pan,
                SynthEventProgramChangePayload::get_pan,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "volume",
                SynthEventProgramChangePayload::has_volume,
                SynthEventProgramChangePayload::get_volume,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "bank",
                SynthEventProgramChangePayload::has_bank,
                SynthEventProgramChangePayload::get_bank,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "preset",
                SynthEventProgramChangePayload::has_preset,
                SynthEventProgramChangePayload::get_preset,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "pitch",
                SynthEventProgramChangePayload::has_pitch,
                SynthEventProgramChangePayload::get_pitch,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<SynthEventProgramChangePayload>(
                "SynthEventProgramChangePayload",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static SynthEventProgramChangePayload {
        static instance: ::protobuf::rt::LazyV2<SynthEventProgramChangePayload> = ::protobuf::rt::LazyV2::INIT;
        instance.get(SynthEventProgramChangePayload::new)
    }
}

impl ::protobuf::Clear for SynthEventProgramChangePayload {
    fn clear(&mut self) {
        self.channel = 0;
        self._expression = ::std::option::Option::None;
        self._pan = ::std::option::Option::None;
        self._volume = ::std::option::Option::None;
        self._bank = ::std::option::Option::None;
        self._preset = ::std::option::Option::None;
        self._pitch = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for SynthEventProgramChangePayload {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for SynthEventProgramChangePayload {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum MidiDtoType {
    Invalid = 0,
    NoteOn = 1,
    NoteOff = 2,
    Sustain = 3,
    AllSoundOff = 4,
    PitchBend = 5,
}

impl ::protobuf::ProtobufEnum for MidiDtoType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<MidiDtoType> {
        match value {
            0 => ::std::option::Option::Some(MidiDtoType::Invalid),
            1 => ::std::option::Option::Some(MidiDtoType::NoteOn),
            2 => ::std::option::Option::Some(MidiDtoType::NoteOff),
            3 => ::std::option::Option::Some(MidiDtoType::Sustain),
            4 => ::std::option::Option::Some(MidiDtoType::AllSoundOff),
            5 => ::std::option::Option::Some(MidiDtoType::PitchBend),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [MidiDtoType] = &[
            MidiDtoType::Invalid,
            MidiDtoType::NoteOn,
            MidiDtoType::NoteOff,
            MidiDtoType::Sustain,
            MidiDtoType::AllSoundOff,
            MidiDtoType::PitchBend,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<MidiDtoType>("MidiDtoType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for MidiDtoType {
}

impl ::std::default::Default for MidiDtoType {
    fn default() -> Self {
        MidiDtoType::Invalid
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiDtoType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum MidiNoteSource {
    IGNORED = 0,
    KEYBOARD = 1,
    MOUSE = 2,
    MIDI = 3,
    MIDI_PLAYER_PREVIEW = 4,
    MIDI_PLAYER = 5,
}

impl ::protobuf::ProtobufEnum for MidiNoteSource {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<MidiNoteSource> {
        match value {
            0 => ::std::option::Option::Some(MidiNoteSource::IGNORED),
            1 => ::std::option::Option::Some(MidiNoteSource::KEYBOARD),
            2 => ::std::option::Option::Some(MidiNoteSource::MOUSE),
            3 => ::std::option::Option::Some(MidiNoteSource::MIDI),
            4 => ::std::option::Option::Some(MidiNoteSource::MIDI_PLAYER_PREVIEW),
            5 => ::std::option::Option::Some(MidiNoteSource::MIDI_PLAYER),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [MidiNoteSource] = &[
            MidiNoteSource::IGNORED,
            MidiNoteSource::KEYBOARD,
            MidiNoteSource::MOUSE,
            MidiNoteSource::MIDI,
            MidiNoteSource::MIDI_PLAYER_PREVIEW,
            MidiNoteSource::MIDI_PLAYER,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<MidiNoteSource>("MidiNoteSource", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for MidiNoteSource {
}

impl ::std::default::Default for MidiNoteSource {
    fn default() -> Self {
        MidiNoteSource::IGNORED
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiNoteSource {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum ActiveChannelsMode {
    SINGLE = 0,
    MULTI = 1,
    ALL = 2,
    SPLIT2 = 3,
    SPLIT4 = 4,
    SPLIT8 = 5,
}

impl ::protobuf::ProtobufEnum for ActiveChannelsMode {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<ActiveChannelsMode> {
        match value {
            0 => ::std::option::Option::Some(ActiveChannelsMode::SINGLE),
            1 => ::std::option::Option::Some(ActiveChannelsMode::MULTI),
            2 => ::std::option::Option::Some(ActiveChannelsMode::ALL),
            3 => ::std::option::Option::Some(ActiveChannelsMode::SPLIT2),
            4 => ::std::option::Option::Some(ActiveChannelsMode::SPLIT4),
            5 => ::std::option::Option::Some(ActiveChannelsMode::SPLIT8),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [ActiveChannelsMode] = &[
            ActiveChannelsMode::SINGLE,
            ActiveChannelsMode::MULTI,
            ActiveChannelsMode::ALL,
            ActiveChannelsMode::SPLIT2,
            ActiveChannelsMode::SPLIT4,
            ActiveChannelsMode::SPLIT8,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<ActiveChannelsMode>("ActiveChannelsMode", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for ActiveChannelsMode {
}

impl ::std::default::Default for ActiveChannelsMode {
    fn default() -> Self {
        ActiveChannelsMode::SINGLE
    }
}

impl ::protobuf::reflect::ProtobufValue for ActiveChannelsMode {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum AudioServiceNoteActivityNoteType {
    ON = 0,
    OFF = 1,
}

impl ::protobuf::ProtobufEnum for AudioServiceNoteActivityNoteType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<AudioServiceNoteActivityNoteType> {
        match value {
            0 => ::std::option::Option::Some(AudioServiceNoteActivityNoteType::ON),
            1 => ::std::option::Option::Some(AudioServiceNoteActivityNoteType::OFF),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [AudioServiceNoteActivityNoteType] = &[
            AudioServiceNoteActivityNoteType::ON,
            AudioServiceNoteActivityNoteType::OFF,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<AudioServiceNoteActivityNoteType>("AudioServiceNoteActivityNoteType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for AudioServiceNoteActivityNoteType {
}

impl ::std::default::Default for AudioServiceNoteActivityNoteType {
    fn default() -> Self {
        AudioServiceNoteActivityNoteType::ON
    }
}

impl ::protobuf::reflect::ProtobufValue for AudioServiceNoteActivityNoteType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum SetChannelInstrumentType {
    Add = 0,
    NextInactive = 1,
    NextEmpty = 2,
}

impl ::protobuf::ProtobufEnum for SetChannelInstrumentType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<SetChannelInstrumentType> {
        match value {
            0 => ::std::option::Option::Some(SetChannelInstrumentType::Add),
            1 => ::std::option::Option::Some(SetChannelInstrumentType::NextInactive),
            2 => ::std::option::Option::Some(SetChannelInstrumentType::NextEmpty),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [SetChannelInstrumentType] = &[
            SetChannelInstrumentType::Add,
            SetChannelInstrumentType::NextInactive,
            SetChannelInstrumentType::NextEmpty,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<SetChannelInstrumentType>("SetChannelInstrumentType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for SetChannelInstrumentType {
}

impl ::std::default::Default for SetChannelInstrumentType {
    fn default() -> Self {
        SetChannelInstrumentType::Add
    }
}

impl ::protobuf::reflect::ProtobufValue for SetChannelInstrumentType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum PianoRhythmSynthEventName {
    NOTE_ON = 0,
    NOTE_OFF = 1,
    PROGRAM_CHANGE = 2,
    CHANNEL_PRESSURE = 3,
    SYSTEM_RESET = 4,
    POLYPHONIC_KEY_PRESSURE = 5,
    ALL_NOTES_OFF = 6,
    ALL_SOUND_OFF = 7,
    OMNI_MODE_OFF = 8,
    MONO_MODE_ON = 9,
    POLY_MODE_ON = 10,
    OMNI_MODE_ON = 11,
    MAIN_VOLUME_MSB = 12,
    MAIN_VOLUME_LSB = 13,
    FOOT_CONTROLLER_MSB = 14,
    MODULATION_MSB = 15,
    MODULATION_LSB = 16,
    BANK_SELECT_MSB = 17,
    BANK_SELECT_LSB = 18,
    FOOT_CONTROLLER_LSB = 19,
    RESET_ALL_CONTROLLERS = 20,
    PAN_MSB = 21,
    PAN_LSB = 22,
    DAMPER_PEDAL = 23,
    PORTAMENTO = 24,
    SUSTENUTO = 25,
    SOFT_PEDAL = 26,
    LEGATO_FOOTSWITCH = 27,
    HOLD_2 = 28,
    EFFECTS_1_DEPTH = 29,
    TREMELO_EFFECT = 30,
    CHORUS_EFFECT = 31,
    CELESTE_EFFECT = 32,
    PHASER_EFFECT = 33,
    PITCH_BEND = 34,
    CONTROL_CHANGE = 35,
    SOCKET_USER_GAIN_CHANGE = 36,
    UNKNOWN = 37,
}

impl ::protobuf::ProtobufEnum for PianoRhythmSynthEventName {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<PianoRhythmSynthEventName> {
        match value {
            0 => ::std::option::Option::Some(PianoRhythmSynthEventName::NOTE_ON),
            1 => ::std::option::Option::Some(PianoRhythmSynthEventName::NOTE_OFF),
            2 => ::std::option::Option::Some(PianoRhythmSynthEventName::PROGRAM_CHANGE),
            3 => ::std::option::Option::Some(PianoRhythmSynthEventName::CHANNEL_PRESSURE),
            4 => ::std::option::Option::Some(PianoRhythmSynthEventName::SYSTEM_RESET),
            5 => ::std::option::Option::Some(PianoRhythmSynthEventName::POLYPHONIC_KEY_PRESSURE),
            6 => ::std::option::Option::Some(PianoRhythmSynthEventName::ALL_NOTES_OFF),
            7 => ::std::option::Option::Some(PianoRhythmSynthEventName::ALL_SOUND_OFF),
            8 => ::std::option::Option::Some(PianoRhythmSynthEventName::OMNI_MODE_OFF),
            9 => ::std::option::Option::Some(PianoRhythmSynthEventName::MONO_MODE_ON),
            10 => ::std::option::Option::Some(PianoRhythmSynthEventName::POLY_MODE_ON),
            11 => ::std::option::Option::Some(PianoRhythmSynthEventName::OMNI_MODE_ON),
            12 => ::std::option::Option::Some(PianoRhythmSynthEventName::MAIN_VOLUME_MSB),
            13 => ::std::option::Option::Some(PianoRhythmSynthEventName::MAIN_VOLUME_LSB),
            14 => ::std::option::Option::Some(PianoRhythmSynthEventName::FOOT_CONTROLLER_MSB),
            15 => ::std::option::Option::Some(PianoRhythmSynthEventName::MODULATION_MSB),
            16 => ::std::option::Option::Some(PianoRhythmSynthEventName::MODULATION_LSB),
            17 => ::std::option::Option::Some(PianoRhythmSynthEventName::BANK_SELECT_MSB),
            18 => ::std::option::Option::Some(PianoRhythmSynthEventName::BANK_SELECT_LSB),
            19 => ::std::option::Option::Some(PianoRhythmSynthEventName::FOOT_CONTROLLER_LSB),
            20 => ::std::option::Option::Some(PianoRhythmSynthEventName::RESET_ALL_CONTROLLERS),
            21 => ::std::option::Option::Some(PianoRhythmSynthEventName::PAN_MSB),
            22 => ::std::option::Option::Some(PianoRhythmSynthEventName::PAN_LSB),
            23 => ::std::option::Option::Some(PianoRhythmSynthEventName::DAMPER_PEDAL),
            24 => ::std::option::Option::Some(PianoRhythmSynthEventName::PORTAMENTO),
            25 => ::std::option::Option::Some(PianoRhythmSynthEventName::SUSTENUTO),
            26 => ::std::option::Option::Some(PianoRhythmSynthEventName::SOFT_PEDAL),
            27 => ::std::option::Option::Some(PianoRhythmSynthEventName::LEGATO_FOOTSWITCH),
            28 => ::std::option::Option::Some(PianoRhythmSynthEventName::HOLD_2),
            29 => ::std::option::Option::Some(PianoRhythmSynthEventName::EFFECTS_1_DEPTH),
            30 => ::std::option::Option::Some(PianoRhythmSynthEventName::TREMELO_EFFECT),
            31 => ::std::option::Option::Some(PianoRhythmSynthEventName::CHORUS_EFFECT),
            32 => ::std::option::Option::Some(PianoRhythmSynthEventName::CELESTE_EFFECT),
            33 => ::std::option::Option::Some(PianoRhythmSynthEventName::PHASER_EFFECT),
            34 => ::std::option::Option::Some(PianoRhythmSynthEventName::PITCH_BEND),
            35 => ::std::option::Option::Some(PianoRhythmSynthEventName::CONTROL_CHANGE),
            36 => ::std::option::Option::Some(PianoRhythmSynthEventName::SOCKET_USER_GAIN_CHANGE),
            37 => ::std::option::Option::Some(PianoRhythmSynthEventName::UNKNOWN),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [PianoRhythmSynthEventName] = &[
            PianoRhythmSynthEventName::NOTE_ON,
            PianoRhythmSynthEventName::NOTE_OFF,
            PianoRhythmSynthEventName::PROGRAM_CHANGE,
            PianoRhythmSynthEventName::CHANNEL_PRESSURE,
            PianoRhythmSynthEventName::SYSTEM_RESET,
            PianoRhythmSynthEventName::POLYPHONIC_KEY_PRESSURE,
            PianoRhythmSynthEventName::ALL_NOTES_OFF,
            PianoRhythmSynthEventName::ALL_SOUND_OFF,
            PianoRhythmSynthEventName::OMNI_MODE_OFF,
            PianoRhythmSynthEventName::MONO_MODE_ON,
            PianoRhythmSynthEventName::POLY_MODE_ON,
            PianoRhythmSynthEventName::OMNI_MODE_ON,
            PianoRhythmSynthEventName::MAIN_VOLUME_MSB,
            PianoRhythmSynthEventName::MAIN_VOLUME_LSB,
            PianoRhythmSynthEventName::FOOT_CONTROLLER_MSB,
            PianoRhythmSynthEventName::MODULATION_MSB,
            PianoRhythmSynthEventName::MODULATION_LSB,
            PianoRhythmSynthEventName::BANK_SELECT_MSB,
            PianoRhythmSynthEventName::BANK_SELECT_LSB,
            PianoRhythmSynthEventName::FOOT_CONTROLLER_LSB,
            PianoRhythmSynthEventName::RESET_ALL_CONTROLLERS,
            PianoRhythmSynthEventName::PAN_MSB,
            PianoRhythmSynthEventName::PAN_LSB,
            PianoRhythmSynthEventName::DAMPER_PEDAL,
            PianoRhythmSynthEventName::PORTAMENTO,
            PianoRhythmSynthEventName::SUSTENUTO,
            PianoRhythmSynthEventName::SOFT_PEDAL,
            PianoRhythmSynthEventName::LEGATO_FOOTSWITCH,
            PianoRhythmSynthEventName::HOLD_2,
            PianoRhythmSynthEventName::EFFECTS_1_DEPTH,
            PianoRhythmSynthEventName::TREMELO_EFFECT,
            PianoRhythmSynthEventName::CHORUS_EFFECT,
            PianoRhythmSynthEventName::CELESTE_EFFECT,
            PianoRhythmSynthEventName::PHASER_EFFECT,
            PianoRhythmSynthEventName::PITCH_BEND,
            PianoRhythmSynthEventName::CONTROL_CHANGE,
            PianoRhythmSynthEventName::SOCKET_USER_GAIN_CHANGE,
            PianoRhythmSynthEventName::UNKNOWN,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<PianoRhythmSynthEventName>("PianoRhythmSynthEventName", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for PianoRhythmSynthEventName {
}

impl ::std::default::Default for PianoRhythmSynthEventName {
    fn default() -> Self {
        PianoRhythmSynthEventName::NOTE_ON
    }
}

impl ::protobuf::reflect::ProtobufValue for PianoRhythmSynthEventName {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x15midi-renditions.proto\x12#PianoRhythm.Serialization.Midi.Msgs\"\
    \x8b\t\n\x07MidiDto\x12R\n\x0bmessageType\x18\x01\x20\x01(\x0e20.PianoRh\
    ythm.Serialization.Midi.Msgs.MidiDtoTypeR\x0bmessageType\x12T\n\nnoteSou\
    rce\x18\xe7\x07\x20\x01(\x0e23.PianoRhythm.Serialization.Midi.Msgs.MidiN\
    oteSourceR\nnoteSource\x12Q\n\x06noteOn\x18\x02\x20\x01(\x0b27.PianoRhyt\
    hm.Serialization.Midi.Msgs.MidiDto.MidiNoteOnH\0R\x06noteOn\x12T\n\x07no\
    teOff\x18\x03\x20\x01(\x0b28.PianoRhythm.Serialization.Midi.Msgs.MidiDto\
    .MidiNoteOffH\0R\x07noteOff\x12X\n\x07sustain\x18\x04\x20\x01(\x0b2<.Pia\
    noRhythm.Serialization.Midi.Msgs.MidiDto.MidiNoteSustainH\0R\x07sustain\
    \x12`\n\x0ballSoundOff\x18\x05\x20\x01(\x0b2<.PianoRhythm.Serialization.\
    Midi.Msgs.MidiDto.MidiAllSoundOffH\0R\x0ballSoundOff\x12Z\n\tpitchBend\
    \x18\x06\x20\x01(\x0b2:.PianoRhythm.Serialization.Midi.Msgs.MidiDto.Midi\
    PitchBendH\0R\tpitchBend\x1a\x9e\x02\n\nMidiNoteOn\x12\x18\n\x07channel\
    \x18\x01\x20\x01(\x05R\x07channel\x12\x12\n\x04note\x18\x02\x20\x01(\x05\
    R\x04note\x12\x1a\n\x08velocity\x18\x03\x20\x01(\x05R\x08velocity\x12\
    \x1d\n\x07program\x18\x04\x20\x01(\x05H\0R\x07program\x88\x01\x01\x12\
    \x1b\n\x06volume\x18\x05\x20\x01(\x05H\x01R\x06volume\x88\x01\x01\x12\
    \x17\n\x04bank\x18\x06\x20\x01(\x05H\x02R\x04bank\x88\x01\x01\x12\x15\n\
    \x03pan\x18\x07\x20\x01(\x05H\x03R\x03pan\x88\x01\x01\x12#\n\nexpression\
    \x18\x08\x20\x01(\x05H\x04R\nexpression\x88\x01\x01B\n\n\x08_programB\t\
    \n\x07_volumeB\x07\n\x05_bankB\x06\n\x04_panB\r\n\x0b_expression\x1a;\n\
    \x0bMidiNoteOff\x12\x18\n\x07channel\x18\x01\x20\x01(\x05R\x07channel\
    \x12\x12\n\x04note\x18\x02\x20\x01(\x05R\x04note\x1aA\n\x0fMidiNoteSusta\
    in\x12\x18\n\x07channel\x18\x01\x20\x01(\x05R\x07channel\x12\x14\n\x05va\
    lue\x18\x02\x20\x01(\x08R\x05value\x1a+\n\x0fMidiAllSoundOff\x12\x18\n\
    \x07channel\x18\x01\x20\x01(\x05R\x07channel\x1a?\n\rMidiPitchBend\x12\
    \x18\n\x07channel\x18\x01\x20\x01(\rR\x07channel\x12\x14\n\x05value\x18\
    \x02\x20\x01(\rR\x05valueB\x06\n\x04data\"\xcc\x02\n\x16MidiMessageInput\
    Buffer\x12D\n\x04type\x18\x01\x20\x01(\x0e20.PianoRhythm.Serialization.M\
    idi.Msgs.MidiDtoTypeR\x04type\x12\x18\n\x07channel\x18\x02\x20\x01(\rR\
    \x07channel\x12\x12\n\x04note\x18\x03\x20\x01(\rR\x04note\x12\x1a\n\x08v\
    elocity\x18\x04\x20\x01(\rR\x08velocity\x12\x14\n\x05delay\x18\x05\x20\
    \x01(\x02R\x05delay\x12\x18\n\x07program\x18\x06\x20\x01(\rR\x07program\
    \x12\x16\n\x06volume\x18\x07\x20\x01(\rR\x06volume\x12\x12\n\x04bank\x18\
    \x08\x20\x01(\rR\x04bank\x12\x10\n\x03pan\x18\t\x20\x01(\rR\x03pan\x12\
    \x14\n\x05pitch\x18\n\x20\x01(\rR\x05pitch\x12\x1e\n\nexpression\x18\x0b\
    \x20\x01(\rR\nexpression\"\xd7\x01\n\x13MidiMessageInputDto\x12\x12\n\
    \x04time\x18\x01\x20\x01(\tR\x04time\x12O\n\x04data\x18\x02\x20\x03(\x0b\
    2;.PianoRhythm.Serialization.Midi.Msgs.MidiMessageInputBufferR\x04data\
    \x12P\n\x06source\x18\x03\x20\x01(\x0e23.PianoRhythm.Serialization.Midi.\
    Msgs.MidiNoteSourceH\0R\x06source\x88\x01\x01B\t\n\x07_source\"D\n\nSF2P\
    rogram\x12\x0e\n\x02id\x18\x01\x20\x01(\rR\x02id\x12\x12\n\x04name\x18\
    \x02\x20\x01(\tR\x04name\x12\x12\n\x04bank\x18\x03\x20\x01(\rR\x04bank\"\
    \x85\x01\n\x0fSoundfontPreset\x12\x12\n\x04name\x18\x01\x20\x01(\tR\x04n\
    ame\x12\x12\n\x04bank\x18\x02\x20\x01(\rR\x04bank\x12\x16\n\x06preset\
    \x18\x03\x20\x01(\rR\x06preset\x12\x17\n\x07key_low\x18\x04\x20\x01(\rR\
    \x06keyLow\x12\x19\n\x08key_high\x18\x05\x20\x01(\rR\x07keyHigh\"\xc3\
    \x01\n\nInstrument\x12\x12\n\x04name\x18\x01\x20\x01(\tR\x04name\x12!\n\
    \x0cdisplay_name\x18\x02\x20\x01(\tR\x0bdisplayName\x12\x12\n\x04bank\
    \x18\x03\x20\x01(\rR\x04bank\x12\x16\n\x06preset\x18\x04\x20\x01(\rR\x06\
    preset\x12\x1e\n\x0bis_drum_kit\x18\x05\x20\x01(\x08R\tisDrumKit\x12\x17\
    \n\x07key_low\x18\x06\x20\x01(\rR\x06keyLow\x12\x19\n\x08key_high\x18\
    \x07\x20\x01(\rR\x07keyHigh\"d\n\x0fInstrumentsList\x12Q\n\x0binstrument\
    s\x18\x01\x20\x03(\x0b2/.PianoRhythm.Serialization.Midi.Msgs.InstrumentR\
    \x0binstruments\"\xb8\x03\n\x0cAudioChannel\x12\x18\n\x07channel\x18\x01\
    \x20\x01(\rR\x07channel\x12\x16\n\x06active\x18\x02\x20\x01(\x08R\x06act\
    ive\x12T\n\ninstrument\x18\x03\x20\x01(\x0b2/.PianoRhythm.Serialization.\
    Midi.Msgs.InstrumentH\0R\ninstrument\x88\x01\x01\x12\x16\n\x06volume\x18\
    \x04\x20\x01(\rR\x06volume\x12\x10\n\x03pan\x18\x05\x20\x01(\rR\x03pan\
    \x12\x12\n\x04bank\x18\x06\x20\x01(\rR\x04bank\x12\x19\n\x08bank_msb\x18\
    \x07\x20\x01(\rR\x07bankMsb\x12\x16\n\x06preset\x18\x08\x20\x01(\rR\x06p\
    reset\x12\x1e\n\nexpression\x18\t\x20\x01(\rR\nexpression\x12)\n\x10chan\
    nel_pressure\x18\n\x20\x01(\rR\x0fchannelPressure\x12\x1d\n\npitch_bend\
    \x18\x0b\x20\x01(\rR\tpitchBend\x126\n\x17pitch_wheel_sensitivity\x18\
    \x0c\x20\x01(\rR\x15pitchWheelSensitivityB\r\n\x0b_instrument\"q\n\x10So\
    undfontSetting\x12\x12\n\x04name\x18\x01\x20\x01(\tR\x04name\x12\x1d\n\n\
    is_default\x18\x02\x20\x01(\x08R\tisDefault\x12\x12\n\x04path\x18\x03\
    \x20\x01(\tR\x04path\x12\x16\n\x06custom\x18\x04\x20\x01(\x08R\x06custom\
    \"\xe3\x01\n\x18AudioServiceNoteActivity\x12\x18\n\x07channel\x18\x01\
    \x20\x01(\x05R\x07channel\x12Y\n\x04type\x18\x02\x20\x01(\x0e2E.PianoRhy\
    thm.Serialization.Midi.Msgs.AudioServiceNoteActivityNoteTypeR\x04type\
    \x12\x1a\n\x08socketID\x18\x03\x20\x01(\tR\x08socketID\x12\x1a\n\x08isCl\
    ient\x18\x04\x20\x01(\x08R\x08isClient\x12\x1a\n\x08velocity\x18\x05\x20\
    \x01(\x05R\x08velocity\"\xff\x02\n\x1bSetChannelInstrumentPayload\x12\
    \x18\n\x07channel\x18\x01\x20\x01(\rR\x07channel\x12\x12\n\x04bank\x18\
    \x02\x20\x01(\rR\x04bank\x12\x16\n\x06preset\x18\x03\x20\x01(\rR\x06pres\
    et\x12Q\n\x04type\x18\x04\x20\x01(\x0e2=.PianoRhythm.Serialization.Midi.\
    Msgs.SetChannelInstrumentTypeR\x04type\x12\x1c\n\tsetActive\x18\x05\x20\
    \x01(\x08R\tsetActive\x12\x20\n\tsocket_id\x18\x06\x20\x01(\tH\0R\x08soc\
    ketId\x88\x01\x01\x12\x1b\n\x06volume\x18\x07\x20\x01(\rH\x01R\x06volume\
    \x88\x01\x01\x12\x15\n\x03pan\x18\x08\x20\x01(\rH\x02R\x03pan\x88\x01\
    \x01\x12#\n\nexpression\x18\t\x20\x01(\rH\x03R\nexpression\x88\x01\x01B\
    \x0c\n\n_socket_idB\t\n\x07_volumeB\x06\n\x04_panB\r\n\x0b_expression\"4\
    \n\x18SetChannelDetailsPayload\x12\x18\n\x07channel\x18\x01\x20\x01(\rR\
    \x07channel\"\xd0\x01\n\x14UpdateChannelPayload\x12\x18\n\x07channel\x18\
    \x01\x20\x01(\rR\x07channel\x12\x20\n\nexpression\x18\x02\x20\x01(\rH\0R\
    \nexpression\x12\x12\n\x03pan\x18\x03\x20\x01(\rH\0R\x03pan\x12\x18\n\
    \x06volume\x18\x04\x20\x01(\rH\0R\x06volume\x12\x14\n\x04bank\x18\x05\
    \x20\x01(\rH\0R\x04bank\x12\x18\n\x06preset\x18\x06\x20\x01(\rH\0R\x06pr\
    eset\x12\x16\n\x05pitch\x18\x07\x20\x01(\rH\0R\x05pitchB\x06\n\x04data\"\
    \xa4\x02\n\x1eSynthEventProgramChangePayload\x12\x18\n\x07channel\x18\
    \x01\x20\x01(\rR\x07channel\x12#\n\nexpression\x18\x02\x20\x01(\rH\0R\ne\
    xpression\x88\x01\x01\x12\x15\n\x03pan\x18\x03\x20\x01(\rH\x01R\x03pan\
    \x88\x01\x01\x12\x1b\n\x06volume\x18\x04\x20\x01(\rH\x02R\x06volume\x88\
    \x01\x01\x12\x17\n\x04bank\x18\x05\x20\x01(\rH\x03R\x04bank\x88\x01\x01\
    \x12\x1b\n\x06preset\x18\x06\x20\x01(\rH\x04R\x06preset\x88\x01\x01\x12\
    \x19\n\x05pitch\x18\x07\x20\x01(\rH\x05R\x05pitch\x88\x01\x01B\r\n\x0b_e\
    xpressionB\x06\n\x04_panB\t\n\x07_volumeB\x07\n\x05_bankB\t\n\x07_preset\
    B\x08\n\x06_pitch*`\n\x0bMidiDtoType\x12\x0b\n\x07Invalid\x10\0\x12\n\n\
    \x06NoteOn\x10\x01\x12\x0b\n\x07NoteOff\x10\x02\x12\x0b\n\x07Sustain\x10\
    \x03\x12\x0f\n\x0bAllSoundOff\x10\x04\x12\r\n\tPitchBend\x10\x05*j\n\x0e\
    MidiNoteSource\x12\x0b\n\x07IGNORED\x10\0\x12\x0c\n\x08KEYBOARD\x10\x01\
    \x12\t\n\x05MOUSE\x10\x02\x12\x08\n\x04MIDI\x10\x03\x12\x17\n\x13MIDI_PL\
    AYER_PREVIEW\x10\x04\x12\x0f\n\x0bMIDI_PLAYER\x10\x05*X\n\x12ActiveChann\
    elsMode\x12\n\n\x06SINGLE\x10\0\x12\t\n\x05MULTI\x10\x01\x12\x07\n\x03AL\
    L\x10\x02\x12\n\n\x06SPLIT2\x10\x03\x12\n\n\x06SPLIT4\x10\x04\x12\n\n\
    \x06SPLIT8\x10\x05*3\n\x20AudioServiceNoteActivityNoteType\x12\x06\n\x02\
    ON\x10\0\x12\x07\n\x03OFF\x10\x01*D\n\x18SetChannelInstrumentType\x12\
    \x07\n\x03Add\x10\0\x12\x10\n\x0cNextInactive\x10\x01\x12\r\n\tNextEmpty\
    \x10\x02*\xf6\x05\n\x19PianoRhythmSynthEventName\x12\x0b\n\x07NOTE_ON\
    \x10\0\x12\x0c\n\x08NOTE_OFF\x10\x01\x12\x12\n\x0ePROGRAM_CHANGE\x10\x02\
    \x12\x14\n\x10CHANNEL_PRESSURE\x10\x03\x12\x10\n\x0cSYSTEM_RESET\x10\x04\
    \x12\x1b\n\x17POLYPHONIC_KEY_PRESSURE\x10\x05\x12\x11\n\rALL_NOTES_OFF\
    \x10\x06\x12\x11\n\rALL_SOUND_OFF\x10\x07\x12\x11\n\rOMNI_MODE_OFF\x10\
    \x08\x12\x10\n\x0cMONO_MODE_ON\x10\t\x12\x10\n\x0cPOLY_MODE_ON\x10\n\x12\
    \x10\n\x0cOMNI_MODE_ON\x10\x0b\x12\x13\n\x0fMAIN_VOLUME_MSB\x10\x0c\x12\
    \x13\n\x0fMAIN_VOLUME_LSB\x10\r\x12\x17\n\x13FOOT_CONTROLLER_MSB\x10\x0e\
    \x12\x12\n\x0eMODULATION_MSB\x10\x0f\x12\x12\n\x0eMODULATION_LSB\x10\x10\
    \x12\x13\n\x0fBANK_SELECT_MSB\x10\x11\x12\x13\n\x0fBANK_SELECT_LSB\x10\
    \x12\x12\x17\n\x13FOOT_CONTROLLER_LSB\x10\x13\x12\x19\n\x15RESET_ALL_CON\
    TROLLERS\x10\x14\x12\x0b\n\x07PAN_MSB\x10\x15\x12\x0b\n\x07PAN_LSB\x10\
    \x16\x12\x10\n\x0cDAMPER_PEDAL\x10\x17\x12\x0e\n\nPORTAMENTO\x10\x18\x12\
    \r\n\tSUSTENUTO\x10\x19\x12\x0e\n\nSOFT_PEDAL\x10\x1a\x12\x15\n\x11LEGAT\
    O_FOOTSWITCH\x10\x1b\x12\n\n\x06HOLD_2\x10\x1c\x12\x13\n\x0fEFFECTS_1_DE\
    PTH\x10\x1d\x12\x12\n\x0eTREMELO_EFFECT\x10\x1e\x12\x11\n\rCHORUS_EFFECT\
    \x10\x1f\x12\x12\n\x0eCELESTE_EFFECT\x10\x20\x12\x11\n\rPHASER_EFFECT\
    \x10!\x12\x0e\n\nPITCH_BEND\x10\"\x12\x12\n\x0eCONTROL_CHANGE\x10#\x12\
    \x1b\n\x17SOCKET_USER_GAIN_CHANGE\x10$\x12\x0b\n\x07UNKNOWN\x10%b\x06pro\
    to3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
