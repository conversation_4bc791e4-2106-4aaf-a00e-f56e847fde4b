// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]


#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `pianorhythm-models-audio.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>ult)]
pub struct Instrument {
    // message fields
    pub name: ::std::string::String,
    pub displayName: ::std::string::String,
    pub bank: i32,
    pub preset: i32,
    pub isDrumKit: bool,
    pub keyLow: i32,
    pub keyHigh: i32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Instrument {
    fn default() -> &'a Instrument {
        <Instrument as ::protobuf::Message>::default_instance()
    }
}

impl Instrument {
    pub fn new() -> Instrument {
        ::std::default::Default::default()
    }

    // string name = 1;


    pub fn get_name(&self) -> &str {
        &self.name
    }
    pub fn clear_name(&mut self) {
        self.name.clear();
    }

    // Param is passed by value, moved
    pub fn set_name(&mut self, v: ::std::string::String) {
        self.name = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_name(&mut self) -> &mut ::std::string::String {
        &mut self.name
    }

    // Take field
    pub fn take_name(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.name, ::std::string::String::new())
    }

    // string displayName = 2;


    pub fn get_displayName(&self) -> &str {
        &self.displayName
    }
    pub fn clear_displayName(&mut self) {
        self.displayName.clear();
    }

    // Param is passed by value, moved
    pub fn set_displayName(&mut self, v: ::std::string::String) {
        self.displayName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_displayName(&mut self) -> &mut ::std::string::String {
        &mut self.displayName
    }

    // Take field
    pub fn take_displayName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.displayName, ::std::string::String::new())
    }

    // int32 bank = 3;


    pub fn get_bank(&self) -> i32 {
        self.bank
    }
    pub fn clear_bank(&mut self) {
        self.bank = 0;
    }

    // Param is passed by value, moved
    pub fn set_bank(&mut self, v: i32) {
        self.bank = v;
    }

    // int32 preset = 4;


    pub fn get_preset(&self) -> i32 {
        self.preset
    }
    pub fn clear_preset(&mut self) {
        self.preset = 0;
    }

    // Param is passed by value, moved
    pub fn set_preset(&mut self, v: i32) {
        self.preset = v;
    }

    // bool isDrumKit = 5;


    pub fn get_isDrumKit(&self) -> bool {
        self.isDrumKit
    }
    pub fn clear_isDrumKit(&mut self) {
        self.isDrumKit = false;
    }

    // Param is passed by value, moved
    pub fn set_isDrumKit(&mut self, v: bool) {
        self.isDrumKit = v;
    }

    // int32 keyLow = 6;


    pub fn get_keyLow(&self) -> i32 {
        self.keyLow
    }
    pub fn clear_keyLow(&mut self) {
        self.keyLow = 0;
    }

    // Param is passed by value, moved
    pub fn set_keyLow(&mut self, v: i32) {
        self.keyLow = v;
    }

    // int32 keyHigh = 7;


    pub fn get_keyHigh(&self) -> i32 {
        self.keyHigh
    }
    pub fn clear_keyHigh(&mut self) {
        self.keyHigh = 0;
    }

    // Param is passed by value, moved
    pub fn set_keyHigh(&mut self, v: i32) {
        self.keyHigh = v;
    }
}

impl ::protobuf::Message for Instrument {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.name)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.displayName)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.bank = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.preset = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isDrumKit = tmp;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.keyLow = tmp;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.keyHigh = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.name.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.name);
        }
        if !self.displayName.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.displayName);
        }
        if self.bank != 0 {
            my_size += ::protobuf::rt::value_size(3, self.bank, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.preset != 0 {
            my_size += ::protobuf::rt::value_size(4, self.preset, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.isDrumKit != false {
            my_size += 2;
        }
        if self.keyLow != 0 {
            my_size += ::protobuf::rt::value_size(6, self.keyLow, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.keyHigh != 0 {
            my_size += ::protobuf::rt::value_size(7, self.keyHigh, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.name.is_empty() {
            os.write_string(1, &self.name)?;
        }
        if !self.displayName.is_empty() {
            os.write_string(2, &self.displayName)?;
        }
        if self.bank != 0 {
            os.write_int32(3, self.bank)?;
        }
        if self.preset != 0 {
            os.write_int32(4, self.preset)?;
        }
        if self.isDrumKit != false {
            os.write_bool(5, self.isDrumKit)?;
        }
        if self.keyLow != 0 {
            os.write_int32(6, self.keyLow)?;
        }
        if self.keyHigh != 0 {
            os.write_int32(7, self.keyHigh)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Instrument {
        Instrument::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "name",
                |m: &Instrument| { &m.name },
                |m: &mut Instrument| { &mut m.name },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "displayName",
                |m: &Instrument| { &m.displayName },
                |m: &mut Instrument| { &mut m.displayName },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "bank",
                |m: &Instrument| { &m.bank },
                |m: &mut Instrument| { &mut m.bank },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "preset",
                |m: &Instrument| { &m.preset },
                |m: &mut Instrument| { &mut m.preset },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isDrumKit",
                |m: &Instrument| { &m.isDrumKit },
                |m: &mut Instrument| { &mut m.isDrumKit },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "keyLow",
                |m: &Instrument| { &m.keyLow },
                |m: &mut Instrument| { &mut m.keyLow },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "keyHigh",
                |m: &Instrument| { &m.keyHigh },
                |m: &mut Instrument| { &mut m.keyHigh },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<Instrument>(
                "Instrument",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static Instrument {
        static instance: ::protobuf::rt::LazyV2<Instrument> = ::protobuf::rt::LazyV2::INIT;
        instance.get(Instrument::new)
    }
}

impl ::protobuf::Clear for Instrument {
    fn clear(&mut self) {
        self.name.clear();
        self.displayName.clear();
        self.bank = 0;
        self.preset = 0;
        self.isDrumKit = false;
        self.keyLow = 0;
        self.keyHigh = 0;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for Instrument {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for Instrument {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x1epianorhythm-models-audio.proto\"\xbe\x01\n\nInstrument\x12\x12\n\
    \x04name\x18\x01\x20\x01(\tR\x04name\x12\x20\n\x0bdisplayName\x18\x02\
    \x20\x01(\tR\x0bdisplayName\x12\x12\n\x04bank\x18\x03\x20\x01(\x05R\x04b\
    ank\x12\x16\n\x06preset\x18\x04\x20\x01(\x05R\x06preset\x12\x1c\n\tisDru\
    mKit\x18\x05\x20\x01(\x08R\tisDrumKit\x12\x16\n\x06keyLow\x18\x06\x20\
    \x01(\x05R\x06keyLow\x12\x18\n\x07keyHigh\x18\x07\x20\x01(\x05R\x07keyHi\
    ghb\x06proto3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
