// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `client-message.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>fault)]
pub struct ClientValidationError {
    // message fields
    pub FieldName: ::std::string::String,
    pub Reason: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ClientValidationError {
    fn default() -> &'a ClientValidationError {
        <ClientValidationError as ::protobuf::Message>::default_instance()
    }
}

impl ClientValidationError {
    pub fn new() -> ClientValidationError {
        ::std::default::Default::default()
    }

    // string FieldName = 1;


    pub fn get_FieldName(&self) -> &str {
        &self.FieldName
    }
    pub fn clear_FieldName(&mut self) {
        self.FieldName.clear();
    }

    // Param is passed by value, moved
    pub fn set_FieldName(&mut self, v: ::std::string::String) {
        self.FieldName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_FieldName(&mut self) -> &mut ::std::string::String {
        &mut self.FieldName
    }

    // Take field
    pub fn take_FieldName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.FieldName, ::std::string::String::new())
    }

    // string Reason = 2;


    pub fn get_Reason(&self) -> &str {
        &self.Reason
    }
    pub fn clear_Reason(&mut self) {
        self.Reason.clear();
    }

    // Param is passed by value, moved
    pub fn set_Reason(&mut self, v: ::std::string::String) {
        self.Reason = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_Reason(&mut self) -> &mut ::std::string::String {
        &mut self.Reason
    }

    // Take field
    pub fn take_Reason(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.Reason, ::std::string::String::new())
    }
}

impl ::protobuf::Message for ClientValidationError {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.FieldName)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.Reason)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.FieldName.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.FieldName);
        }
        if !self.Reason.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.Reason);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.FieldName.is_empty() {
            os.write_string(1, &self.FieldName)?;
        }
        if !self.Reason.is_empty() {
            os.write_string(2, &self.Reason)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ClientValidationError {
        ClientValidationError::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "FieldName",
                |m: &ClientValidationError| { &m.FieldName },
                |m: &mut ClientValidationError| { &mut m.FieldName },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "Reason",
                |m: &ClientValidationError| { &m.Reason },
                |m: &mut ClientValidationError| { &mut m.Reason },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ClientValidationError>(
                "ClientValidationError",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ClientValidationError {
        static instance: ::protobuf::rt::LazyV2<ClientValidationError> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ClientValidationError::new)
    }
}

impl ::protobuf::Clear for ClientValidationError {
    fn clear(&mut self) {
        self.FieldName.clear();
        self.Reason.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ClientValidationError {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ClientValidationError {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiDto {
    // message fields
    pub messageType: super::midi_renditions::MidiDtoType,
    pub noteSource: super::midi_renditions::MidiNoteSource,
    // message oneof groups
    pub data: ::std::option::Option<MidiDto_oneof_data>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiDto {
    fn default() -> &'a MidiDto {
        <MidiDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum MidiDto_oneof_data {
    noteOn(super::midi_renditions::MidiDto_MidiNoteOn),
    noteOff(super::midi_renditions::MidiDto_MidiNoteOff),
    sustain(super::midi_renditions::MidiDto_MidiNoteSustain),
    allSoundOff(super::midi_renditions::MidiDto_MidiAllSoundOff),
    pitchBend(super::midi_renditions::MidiDto_MidiPitchBend),
}

impl MidiDto {
    pub fn new() -> MidiDto {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDtoType messageType = 1;


    pub fn get_messageType(&self) -> super::midi_renditions::MidiDtoType {
        self.messageType
    }
    pub fn clear_messageType(&mut self) {
        self.messageType = super::midi_renditions::MidiDtoType::Invalid;
    }

    // Param is passed by value, moved
    pub fn set_messageType(&mut self, v: super::midi_renditions::MidiDtoType) {
        self.messageType = v;
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiNoteSource noteSource = 999;


    pub fn get_noteSource(&self) -> super::midi_renditions::MidiNoteSource {
        self.noteSource
    }
    pub fn clear_noteSource(&mut self) {
        self.noteSource = super::midi_renditions::MidiNoteSource::IGNORED;
    }

    // Param is passed by value, moved
    pub fn set_noteSource(&mut self, v: super::midi_renditions::MidiNoteSource) {
        self.noteSource = v;
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiNoteOn noteOn = 2;


    pub fn get_noteOn(&self) -> &super::midi_renditions::MidiDto_MidiNoteOn {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOn(ref v)) => v,
            _ => <super::midi_renditions::MidiDto_MidiNoteOn as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_noteOn(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_noteOn(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOn(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_noteOn(&mut self, v: super::midi_renditions::MidiDto_MidiNoteOn) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOn(v))
    }

    // Mutable pointer to the field.
    pub fn mut_noteOn(&mut self) -> &mut super::midi_renditions::MidiDto_MidiNoteOn {
        if let ::std::option::Option::Some(MidiDto_oneof_data::noteOn(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOn(super::midi_renditions::MidiDto_MidiNoteOn::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOn(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_noteOn(&mut self) -> super::midi_renditions::MidiDto_MidiNoteOn {
        if self.has_noteOn() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::noteOn(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::MidiDto_MidiNoteOn::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiNoteOff noteOff = 3;


    pub fn get_noteOff(&self) -> &super::midi_renditions::MidiDto_MidiNoteOff {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOff(ref v)) => v,
            _ => <super::midi_renditions::MidiDto_MidiNoteOff as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_noteOff(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_noteOff(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOff(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_noteOff(&mut self, v: super::midi_renditions::MidiDto_MidiNoteOff) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOff(v))
    }

    // Mutable pointer to the field.
    pub fn mut_noteOff(&mut self) -> &mut super::midi_renditions::MidiDto_MidiNoteOff {
        if let ::std::option::Option::Some(MidiDto_oneof_data::noteOff(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOff(super::midi_renditions::MidiDto_MidiNoteOff::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::noteOff(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_noteOff(&mut self) -> super::midi_renditions::MidiDto_MidiNoteOff {
        if self.has_noteOff() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::noteOff(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::MidiDto_MidiNoteOff::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiNoteSustain sustain = 4;


    pub fn get_sustain(&self) -> &super::midi_renditions::MidiDto_MidiNoteSustain {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::sustain(ref v)) => v,
            _ => <super::midi_renditions::MidiDto_MidiNoteSustain as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_sustain(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_sustain(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::sustain(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_sustain(&mut self, v: super::midi_renditions::MidiDto_MidiNoteSustain) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::sustain(v))
    }

    // Mutable pointer to the field.
    pub fn mut_sustain(&mut self) -> &mut super::midi_renditions::MidiDto_MidiNoteSustain {
        if let ::std::option::Option::Some(MidiDto_oneof_data::sustain(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::sustain(super::midi_renditions::MidiDto_MidiNoteSustain::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::sustain(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_sustain(&mut self) -> super::midi_renditions::MidiDto_MidiNoteSustain {
        if self.has_sustain() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::sustain(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::MidiDto_MidiNoteSustain::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiAllSoundOff allSoundOff = 5;


    pub fn get_allSoundOff(&self) -> &super::midi_renditions::MidiDto_MidiAllSoundOff {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(ref v)) => v,
            _ => <super::midi_renditions::MidiDto_MidiAllSoundOff as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_allSoundOff(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_allSoundOff(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_allSoundOff(&mut self, v: super::midi_renditions::MidiDto_MidiAllSoundOff) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(v))
    }

    // Mutable pointer to the field.
    pub fn mut_allSoundOff(&mut self) -> &mut super::midi_renditions::MidiDto_MidiAllSoundOff {
        if let ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(super::midi_renditions::MidiDto_MidiAllSoundOff::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_allSoundOff(&mut self) -> super::midi_renditions::MidiDto_MidiAllSoundOff {
        if self.has_allSoundOff() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::MidiDto_MidiAllSoundOff::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiPitchBend pitchBend = 6;


    pub fn get_pitchBend(&self) -> &super::midi_renditions::MidiDto_MidiPitchBend {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(ref v)) => v,
            _ => <super::midi_renditions::MidiDto_MidiPitchBend as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_pitchBend(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_pitchBend(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pitchBend(&mut self, v: super::midi_renditions::MidiDto_MidiPitchBend) {
        self.data = ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(v))
    }

    // Mutable pointer to the field.
    pub fn mut_pitchBend(&mut self) -> &mut super::midi_renditions::MidiDto_MidiPitchBend {
        if let ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(super::midi_renditions::MidiDto_MidiPitchBend::new()));
        }
        match self.data {
            ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_pitchBend(&mut self) -> super::midi_renditions::MidiDto_MidiPitchBend {
        if self.has_pitchBend() {
            match self.data.take() {
                ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::MidiDto_MidiPitchBend::new()
        }
    }
}

impl ::protobuf::Message for MidiDto {
    fn is_initialized(&self) -> bool {
        if let Some(MidiDto_oneof_data::noteOn(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(MidiDto_oneof_data::noteOff(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(MidiDto_oneof_data::sustain(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(MidiDto_oneof_data::allSoundOff(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(MidiDto_oneof_data::pitchBend(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.messageType, 1, &mut self.unknown_fields)?
                },
                999 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.noteSource, 999, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOn(is.read_message()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::noteOff(is.read_message()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::sustain(is.read_message()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::allSoundOff(is.read_message()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(MidiDto_oneof_data::pitchBend(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.messageType != super::midi_renditions::MidiDtoType::Invalid {
            my_size += ::protobuf::rt::enum_size(1, self.messageType);
        }
        if self.noteSource != super::midi_renditions::MidiNoteSource::IGNORED {
            my_size += ::protobuf::rt::enum_size(999, self.noteSource);
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &MidiDto_oneof_data::noteOn(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &MidiDto_oneof_data::noteOff(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &MidiDto_oneof_data::sustain(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &MidiDto_oneof_data::allSoundOff(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &MidiDto_oneof_data::pitchBend(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.messageType != super::midi_renditions::MidiDtoType::Invalid {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.messageType))?;
        }
        if self.noteSource != super::midi_renditions::MidiNoteSource::IGNORED {
            os.write_enum(999, ::protobuf::ProtobufEnum::value(&self.noteSource))?;
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &MidiDto_oneof_data::noteOn(ref v) => {
                    os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &MidiDto_oneof_data::noteOff(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &MidiDto_oneof_data::sustain(ref v) => {
                    os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &MidiDto_oneof_data::allSoundOff(ref v) => {
                    os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &MidiDto_oneof_data::pitchBend(ref v) => {
                    os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiDto {
        MidiDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<super::midi_renditions::MidiDtoType>>(
                "messageType",
                |m: &MidiDto| { &m.messageType },
                |m: &mut MidiDto| { &mut m.messageType },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<super::midi_renditions::MidiNoteSource>>(
                "noteSource",
                |m: &MidiDto| { &m.noteSource },
                |m: &mut MidiDto| { &mut m.noteSource },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::MidiDto_MidiNoteOn>(
                "noteOn",
                MidiDto::has_noteOn,
                MidiDto::get_noteOn,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::MidiDto_MidiNoteOff>(
                "noteOff",
                MidiDto::has_noteOff,
                MidiDto::get_noteOff,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::MidiDto_MidiNoteSustain>(
                "sustain",
                MidiDto::has_sustain,
                MidiDto::get_sustain,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::MidiDto_MidiAllSoundOff>(
                "allSoundOff",
                MidiDto::has_allSoundOff,
                MidiDto::get_allSoundOff,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::MidiDto_MidiPitchBend>(
                "pitchBend",
                MidiDto::has_pitchBend,
                MidiDto::get_pitchBend,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiDto>(
                "MidiDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiDto {
        static instance: ::protobuf::rt::LazyV2<MidiDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiDto::new)
    }
}

impl ::protobuf::Clear for MidiDto {
    fn clear(&mut self) {
        self.messageType = super::midi_renditions::MidiDtoType::Invalid;
        self.noteSource = super::midi_renditions::MidiNoteSource::IGNORED;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiMessageOutputDto {
    // message fields
    pub socketID: ::std::string::String,
    pub time: ::std::string::String,
    pub data: ::protobuf::RepeatedField<MidiMessageOutputDto_MidiMessageBuffer>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiMessageOutputDto {
    fn default() -> &'a MidiMessageOutputDto {
        <MidiMessageOutputDto as ::protobuf::Message>::default_instance()
    }
}

impl MidiMessageOutputDto {
    pub fn new() -> MidiMessageOutputDto {
        ::std::default::Default::default()
    }

    // string socketID = 1;


    pub fn get_socketID(&self) -> &str {
        &self.socketID
    }
    pub fn clear_socketID(&mut self) {
        self.socketID.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.socketID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        &mut self.socketID
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketID, ::std::string::String::new())
    }

    // string time = 2;


    pub fn get_time(&self) -> &str {
        &self.time
    }
    pub fn clear_time(&mut self) {
        self.time.clear();
    }

    // Param is passed by value, moved
    pub fn set_time(&mut self, v: ::std::string::String) {
        self.time = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_time(&mut self) -> &mut ::std::string::String {
        &mut self.time
    }

    // Take field
    pub fn take_time(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.time, ::std::string::String::new())
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.Msgs.MidiMessageOutputDto.MidiMessageBuffer data = 3;


    pub fn get_data(&self) -> &[MidiMessageOutputDto_MidiMessageBuffer] {
        &self.data
    }
    pub fn clear_data(&mut self) {
        self.data.clear();
    }

    // Param is passed by value, moved
    pub fn set_data(&mut self, v: ::protobuf::RepeatedField<MidiMessageOutputDto_MidiMessageBuffer>) {
        self.data = v;
    }

    // Mutable pointer to the field.
    pub fn mut_data(&mut self) -> &mut ::protobuf::RepeatedField<MidiMessageOutputDto_MidiMessageBuffer> {
        &mut self.data
    }

    // Take field
    pub fn take_data(&mut self) -> ::protobuf::RepeatedField<MidiMessageOutputDto_MidiMessageBuffer> {
        ::std::mem::replace(&mut self.data, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for MidiMessageOutputDto {
    fn is_initialized(&self) -> bool {
        for v in &self.data {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketID)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.time)?;
                },
                3 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.data)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.socketID.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.socketID);
        }
        if !self.time.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.time);
        }
        for value in &self.data {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.socketID.is_empty() {
            os.write_string(1, &self.socketID)?;
        }
        if !self.time.is_empty() {
            os.write_string(2, &self.time)?;
        }
        for v in &self.data {
            os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiMessageOutputDto {
        MidiMessageOutputDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketID",
                |m: &MidiMessageOutputDto| { &m.socketID },
                |m: &mut MidiMessageOutputDto| { &mut m.socketID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "time",
                |m: &MidiMessageOutputDto| { &m.time },
                |m: &mut MidiMessageOutputDto| { &mut m.time },
            ));
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<MidiMessageOutputDto_MidiMessageBuffer>>(
                "data",
                |m: &MidiMessageOutputDto| { &m.data },
                |m: &mut MidiMessageOutputDto| { &mut m.data },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiMessageOutputDto>(
                "MidiMessageOutputDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiMessageOutputDto {
        static instance: ::protobuf::rt::LazyV2<MidiMessageOutputDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiMessageOutputDto::new)
    }
}

impl ::protobuf::Clear for MidiMessageOutputDto {
    fn clear(&mut self) {
        self.socketID.clear();
        self.time.clear();
        self.data.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiMessageOutputDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiMessageOutputDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiMessageOutputDto_MidiMessageBuffer {
    // message fields
    pub data: ::protobuf::SingularPtrField<MidiDto>,
    pub delay: f64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiMessageOutputDto_MidiMessageBuffer {
    fn default() -> &'a MidiMessageOutputDto_MidiMessageBuffer {
        <MidiMessageOutputDto_MidiMessageBuffer as ::protobuf::Message>::default_instance()
    }
}

impl MidiMessageOutputDto_MidiMessageBuffer {
    pub fn new() -> MidiMessageOutputDto_MidiMessageBuffer {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.MidiDto data = 1;


    pub fn get_data(&self) -> &MidiDto {
        self.data.as_ref().unwrap_or_else(|| <MidiDto as ::protobuf::Message>::default_instance())
    }
    pub fn clear_data(&mut self) {
        self.data.clear();
    }

    pub fn has_data(&self) -> bool {
        self.data.is_some()
    }

    // Param is passed by value, moved
    pub fn set_data(&mut self, v: MidiDto) {
        self.data = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_data(&mut self) -> &mut MidiDto {
        if self.data.is_none() {
            self.data.set_default();
        }
        self.data.as_mut().unwrap()
    }

    // Take field
    pub fn take_data(&mut self) -> MidiDto {
        self.data.take().unwrap_or_else(|| MidiDto::new())
    }

    // double delay = 2;


    pub fn get_delay(&self) -> f64 {
        self.delay
    }
    pub fn clear_delay(&mut self) {
        self.delay = 0.;
    }

    // Param is passed by value, moved
    pub fn set_delay(&mut self, v: f64) {
        self.delay = v;
    }
}

impl ::protobuf::Message for MidiMessageOutputDto_MidiMessageBuffer {
    fn is_initialized(&self) -> bool {
        for v in &self.data {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.data)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed64 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_double()?;
                    self.delay = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.data.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.delay != 0. {
            my_size += 9;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.data.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.delay != 0. {
            os.write_double(2, self.delay)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiMessageOutputDto_MidiMessageBuffer {
        MidiMessageOutputDto_MidiMessageBuffer::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<MidiDto>>(
                "data",
                |m: &MidiMessageOutputDto_MidiMessageBuffer| { &m.data },
                |m: &mut MidiMessageOutputDto_MidiMessageBuffer| { &mut m.data },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeDouble>(
                "delay",
                |m: &MidiMessageOutputDto_MidiMessageBuffer| { &m.delay },
                |m: &mut MidiMessageOutputDto_MidiMessageBuffer| { &mut m.delay },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiMessageOutputDto_MidiMessageBuffer>(
                "MidiMessageOutputDto.MidiMessageBuffer",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiMessageOutputDto_MidiMessageBuffer {
        static instance: ::protobuf::rt::LazyV2<MidiMessageOutputDto_MidiMessageBuffer> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiMessageOutputDto_MidiMessageBuffer::new)
    }
}

impl ::protobuf::Clear for MidiMessageOutputDto_MidiMessageBuffer {
    fn clear(&mut self) {
        self.data.clear();
        self.delay = 0.;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiMessageOutputDto_MidiMessageBuffer {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiMessageOutputDto_MidiMessageBuffer {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ChatMessageDto {
    // message fields
    pub id: i32,
    pub messageID: ::std::string::String,
    pub messageReplyID: ::std::string::String,
    pub socketID: ::std::string::String,
    pub message: ::std::string::String,
    pub ts: ::std::string::String,
    pub roomID: ::std::string::String,
    pub usertag: ::std::string::String,
    pub username: ::std::string::String,
    pub nickname: ::std::string::String,
    pub userColor: ::std::string::String,
    pub isBot: bool,
    pub isPlugin: bool,
    pub isMod: bool,
    pub isSystem: bool,
    pub isAdmin: bool,
    pub isDev: bool,
    pub isRoomOwner: bool,
    pub isGuest: bool,
    pub isProMember: bool,
    pub whisperer: ::std::string::String,
    pub autoDelete: bool,
    pub modifiedDate: ::std::string::String,
    pub user_uuid: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ChatMessageDto {
    fn default() -> &'a ChatMessageDto {
        <ChatMessageDto as ::protobuf::Message>::default_instance()
    }
}

impl ChatMessageDto {
    pub fn new() -> ChatMessageDto {
        ::std::default::Default::default()
    }

    // int32 id = 1;


    pub fn get_id(&self) -> i32 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: i32) {
        self.id = v;
    }

    // string messageID = 2;


    pub fn get_messageID(&self) -> &str {
        &self.messageID
    }
    pub fn clear_messageID(&mut self) {
        self.messageID.clear();
    }

    // Param is passed by value, moved
    pub fn set_messageID(&mut self, v: ::std::string::String) {
        self.messageID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_messageID(&mut self) -> &mut ::std::string::String {
        &mut self.messageID
    }

    // Take field
    pub fn take_messageID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.messageID, ::std::string::String::new())
    }

    // string messageReplyID = 3;


    pub fn get_messageReplyID(&self) -> &str {
        &self.messageReplyID
    }
    pub fn clear_messageReplyID(&mut self) {
        self.messageReplyID.clear();
    }

    // Param is passed by value, moved
    pub fn set_messageReplyID(&mut self, v: ::std::string::String) {
        self.messageReplyID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_messageReplyID(&mut self) -> &mut ::std::string::String {
        &mut self.messageReplyID
    }

    // Take field
    pub fn take_messageReplyID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.messageReplyID, ::std::string::String::new())
    }

    // string socketID = 4;


    pub fn get_socketID(&self) -> &str {
        &self.socketID
    }
    pub fn clear_socketID(&mut self) {
        self.socketID.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.socketID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        &mut self.socketID
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketID, ::std::string::String::new())
    }

    // string message = 5;


    pub fn get_message(&self) -> &str {
        &self.message
    }
    pub fn clear_message(&mut self) {
        self.message.clear();
    }

    // Param is passed by value, moved
    pub fn set_message(&mut self, v: ::std::string::String) {
        self.message = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_message(&mut self) -> &mut ::std::string::String {
        &mut self.message
    }

    // Take field
    pub fn take_message(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.message, ::std::string::String::new())
    }

    // string ts = 6;


    pub fn get_ts(&self) -> &str {
        &self.ts
    }
    pub fn clear_ts(&mut self) {
        self.ts.clear();
    }

    // Param is passed by value, moved
    pub fn set_ts(&mut self, v: ::std::string::String) {
        self.ts = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_ts(&mut self) -> &mut ::std::string::String {
        &mut self.ts
    }

    // Take field
    pub fn take_ts(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.ts, ::std::string::String::new())
    }

    // string roomID = 7;


    pub fn get_roomID(&self) -> &str {
        &self.roomID
    }
    pub fn clear_roomID(&mut self) {
        self.roomID.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.roomID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        &mut self.roomID
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomID, ::std::string::String::new())
    }

    // string usertag = 8;


    pub fn get_usertag(&self) -> &str {
        &self.usertag
    }
    pub fn clear_usertag(&mut self) {
        self.usertag.clear();
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.usertag = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        &mut self.usertag
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.usertag, ::std::string::String::new())
    }

    // string username = 9;


    pub fn get_username(&self) -> &str {
        &self.username
    }
    pub fn clear_username(&mut self) {
        self.username.clear();
    }

    // Param is passed by value, moved
    pub fn set_username(&mut self, v: ::std::string::String) {
        self.username = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_username(&mut self) -> &mut ::std::string::String {
        &mut self.username
    }

    // Take field
    pub fn take_username(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.username, ::std::string::String::new())
    }

    // string nickname = 10;


    pub fn get_nickname(&self) -> &str {
        &self.nickname
    }
    pub fn clear_nickname(&mut self) {
        self.nickname.clear();
    }

    // Param is passed by value, moved
    pub fn set_nickname(&mut self, v: ::std::string::String) {
        self.nickname = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_nickname(&mut self) -> &mut ::std::string::String {
        &mut self.nickname
    }

    // Take field
    pub fn take_nickname(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.nickname, ::std::string::String::new())
    }

    // string userColor = 11;


    pub fn get_userColor(&self) -> &str {
        &self.userColor
    }
    pub fn clear_userColor(&mut self) {
        self.userColor.clear();
    }

    // Param is passed by value, moved
    pub fn set_userColor(&mut self, v: ::std::string::String) {
        self.userColor = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_userColor(&mut self) -> &mut ::std::string::String {
        &mut self.userColor
    }

    // Take field
    pub fn take_userColor(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.userColor, ::std::string::String::new())
    }

    // bool isBot = 12;


    pub fn get_isBot(&self) -> bool {
        self.isBot
    }
    pub fn clear_isBot(&mut self) {
        self.isBot = false;
    }

    // Param is passed by value, moved
    pub fn set_isBot(&mut self, v: bool) {
        self.isBot = v;
    }

    // bool isPlugin = 13;


    pub fn get_isPlugin(&self) -> bool {
        self.isPlugin
    }
    pub fn clear_isPlugin(&mut self) {
        self.isPlugin = false;
    }

    // Param is passed by value, moved
    pub fn set_isPlugin(&mut self, v: bool) {
        self.isPlugin = v;
    }

    // bool isMod = 14;


    pub fn get_isMod(&self) -> bool {
        self.isMod
    }
    pub fn clear_isMod(&mut self) {
        self.isMod = false;
    }

    // Param is passed by value, moved
    pub fn set_isMod(&mut self, v: bool) {
        self.isMod = v;
    }

    // bool isSystem = 15;


    pub fn get_isSystem(&self) -> bool {
        self.isSystem
    }
    pub fn clear_isSystem(&mut self) {
        self.isSystem = false;
    }

    // Param is passed by value, moved
    pub fn set_isSystem(&mut self, v: bool) {
        self.isSystem = v;
    }

    // bool isAdmin = 16;


    pub fn get_isAdmin(&self) -> bool {
        self.isAdmin
    }
    pub fn clear_isAdmin(&mut self) {
        self.isAdmin = false;
    }

    // Param is passed by value, moved
    pub fn set_isAdmin(&mut self, v: bool) {
        self.isAdmin = v;
    }

    // bool isDev = 17;


    pub fn get_isDev(&self) -> bool {
        self.isDev
    }
    pub fn clear_isDev(&mut self) {
        self.isDev = false;
    }

    // Param is passed by value, moved
    pub fn set_isDev(&mut self, v: bool) {
        self.isDev = v;
    }

    // bool isRoomOwner = 18;


    pub fn get_isRoomOwner(&self) -> bool {
        self.isRoomOwner
    }
    pub fn clear_isRoomOwner(&mut self) {
        self.isRoomOwner = false;
    }

    // Param is passed by value, moved
    pub fn set_isRoomOwner(&mut self, v: bool) {
        self.isRoomOwner = v;
    }

    // bool isGuest = 19;


    pub fn get_isGuest(&self) -> bool {
        self.isGuest
    }
    pub fn clear_isGuest(&mut self) {
        self.isGuest = false;
    }

    // Param is passed by value, moved
    pub fn set_isGuest(&mut self, v: bool) {
        self.isGuest = v;
    }

    // bool isProMember = 20;


    pub fn get_isProMember(&self) -> bool {
        self.isProMember
    }
    pub fn clear_isProMember(&mut self) {
        self.isProMember = false;
    }

    // Param is passed by value, moved
    pub fn set_isProMember(&mut self, v: bool) {
        self.isProMember = v;
    }

    // string whisperer = 21;


    pub fn get_whisperer(&self) -> &str {
        &self.whisperer
    }
    pub fn clear_whisperer(&mut self) {
        self.whisperer.clear();
    }

    // Param is passed by value, moved
    pub fn set_whisperer(&mut self, v: ::std::string::String) {
        self.whisperer = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_whisperer(&mut self) -> &mut ::std::string::String {
        &mut self.whisperer
    }

    // Take field
    pub fn take_whisperer(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.whisperer, ::std::string::String::new())
    }

    // bool autoDelete = 22;


    pub fn get_autoDelete(&self) -> bool {
        self.autoDelete
    }
    pub fn clear_autoDelete(&mut self) {
        self.autoDelete = false;
    }

    // Param is passed by value, moved
    pub fn set_autoDelete(&mut self, v: bool) {
        self.autoDelete = v;
    }

    // string modifiedDate = 23;


    pub fn get_modifiedDate(&self) -> &str {
        &self.modifiedDate
    }
    pub fn clear_modifiedDate(&mut self) {
        self.modifiedDate.clear();
    }

    // Param is passed by value, moved
    pub fn set_modifiedDate(&mut self, v: ::std::string::String) {
        self.modifiedDate = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_modifiedDate(&mut self) -> &mut ::std::string::String {
        &mut self.modifiedDate
    }

    // Take field
    pub fn take_modifiedDate(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.modifiedDate, ::std::string::String::new())
    }

    // string user_uuid = 24;


    pub fn get_user_uuid(&self) -> &str {
        &self.user_uuid
    }
    pub fn clear_user_uuid(&mut self) {
        self.user_uuid.clear();
    }

    // Param is passed by value, moved
    pub fn set_user_uuid(&mut self, v: ::std::string::String) {
        self.user_uuid = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_user_uuid(&mut self) -> &mut ::std::string::String {
        &mut self.user_uuid
    }

    // Take field
    pub fn take_user_uuid(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.user_uuid, ::std::string::String::new())
    }
}

impl ::protobuf::Message for ChatMessageDto {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.messageID)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.messageReplyID)?;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketID)?;
                },
                5 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.message)?;
                },
                6 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.ts)?;
                },
                7 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomID)?;
                },
                8 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.usertag)?;
                },
                9 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.username)?;
                },
                10 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.nickname)?;
                },
                11 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.userColor)?;
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isBot = tmp;
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isPlugin = tmp;
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isMod = tmp;
                },
                15 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isSystem = tmp;
                },
                16 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isAdmin = tmp;
                },
                17 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isDev = tmp;
                },
                18 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isRoomOwner = tmp;
                },
                19 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isGuest = tmp;
                },
                20 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isProMember = tmp;
                },
                21 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.whisperer)?;
                },
                22 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.autoDelete = tmp;
                },
                23 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.modifiedDate)?;
                },
                24 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.user_uuid)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.messageID.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.messageID);
        }
        if !self.messageReplyID.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.messageReplyID);
        }
        if !self.socketID.is_empty() {
            my_size += ::protobuf::rt::string_size(4, &self.socketID);
        }
        if !self.message.is_empty() {
            my_size += ::protobuf::rt::string_size(5, &self.message);
        }
        if !self.ts.is_empty() {
            my_size += ::protobuf::rt::string_size(6, &self.ts);
        }
        if !self.roomID.is_empty() {
            my_size += ::protobuf::rt::string_size(7, &self.roomID);
        }
        if !self.usertag.is_empty() {
            my_size += ::protobuf::rt::string_size(8, &self.usertag);
        }
        if !self.username.is_empty() {
            my_size += ::protobuf::rt::string_size(9, &self.username);
        }
        if !self.nickname.is_empty() {
            my_size += ::protobuf::rt::string_size(10, &self.nickname);
        }
        if !self.userColor.is_empty() {
            my_size += ::protobuf::rt::string_size(11, &self.userColor);
        }
        if self.isBot != false {
            my_size += 2;
        }
        if self.isPlugin != false {
            my_size += 2;
        }
        if self.isMod != false {
            my_size += 2;
        }
        if self.isSystem != false {
            my_size += 2;
        }
        if self.isAdmin != false {
            my_size += 3;
        }
        if self.isDev != false {
            my_size += 3;
        }
        if self.isRoomOwner != false {
            my_size += 3;
        }
        if self.isGuest != false {
            my_size += 3;
        }
        if self.isProMember != false {
            my_size += 3;
        }
        if !self.whisperer.is_empty() {
            my_size += ::protobuf::rt::string_size(21, &self.whisperer);
        }
        if self.autoDelete != false {
            my_size += 3;
        }
        if !self.modifiedDate.is_empty() {
            my_size += ::protobuf::rt::string_size(23, &self.modifiedDate);
        }
        if !self.user_uuid.is_empty() {
            my_size += ::protobuf::rt::string_size(24, &self.user_uuid);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_int32(1, self.id)?;
        }
        if !self.messageID.is_empty() {
            os.write_string(2, &self.messageID)?;
        }
        if !self.messageReplyID.is_empty() {
            os.write_string(3, &self.messageReplyID)?;
        }
        if !self.socketID.is_empty() {
            os.write_string(4, &self.socketID)?;
        }
        if !self.message.is_empty() {
            os.write_string(5, &self.message)?;
        }
        if !self.ts.is_empty() {
            os.write_string(6, &self.ts)?;
        }
        if !self.roomID.is_empty() {
            os.write_string(7, &self.roomID)?;
        }
        if !self.usertag.is_empty() {
            os.write_string(8, &self.usertag)?;
        }
        if !self.username.is_empty() {
            os.write_string(9, &self.username)?;
        }
        if !self.nickname.is_empty() {
            os.write_string(10, &self.nickname)?;
        }
        if !self.userColor.is_empty() {
            os.write_string(11, &self.userColor)?;
        }
        if self.isBot != false {
            os.write_bool(12, self.isBot)?;
        }
        if self.isPlugin != false {
            os.write_bool(13, self.isPlugin)?;
        }
        if self.isMod != false {
            os.write_bool(14, self.isMod)?;
        }
        if self.isSystem != false {
            os.write_bool(15, self.isSystem)?;
        }
        if self.isAdmin != false {
            os.write_bool(16, self.isAdmin)?;
        }
        if self.isDev != false {
            os.write_bool(17, self.isDev)?;
        }
        if self.isRoomOwner != false {
            os.write_bool(18, self.isRoomOwner)?;
        }
        if self.isGuest != false {
            os.write_bool(19, self.isGuest)?;
        }
        if self.isProMember != false {
            os.write_bool(20, self.isProMember)?;
        }
        if !self.whisperer.is_empty() {
            os.write_string(21, &self.whisperer)?;
        }
        if self.autoDelete != false {
            os.write_bool(22, self.autoDelete)?;
        }
        if !self.modifiedDate.is_empty() {
            os.write_string(23, &self.modifiedDate)?;
        }
        if !self.user_uuid.is_empty() {
            os.write_string(24, &self.user_uuid)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ChatMessageDto {
        ChatMessageDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "id",
                |m: &ChatMessageDto| { &m.id },
                |m: &mut ChatMessageDto| { &mut m.id },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "messageID",
                |m: &ChatMessageDto| { &m.messageID },
                |m: &mut ChatMessageDto| { &mut m.messageID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "messageReplyID",
                |m: &ChatMessageDto| { &m.messageReplyID },
                |m: &mut ChatMessageDto| { &mut m.messageReplyID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketID",
                |m: &ChatMessageDto| { &m.socketID },
                |m: &mut ChatMessageDto| { &mut m.socketID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "message",
                |m: &ChatMessageDto| { &m.message },
                |m: &mut ChatMessageDto| { &mut m.message },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "ts",
                |m: &ChatMessageDto| { &m.ts },
                |m: &mut ChatMessageDto| { &mut m.ts },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomID",
                |m: &ChatMessageDto| { &m.roomID },
                |m: &mut ChatMessageDto| { &mut m.roomID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "usertag",
                |m: &ChatMessageDto| { &m.usertag },
                |m: &mut ChatMessageDto| { &mut m.usertag },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "username",
                |m: &ChatMessageDto| { &m.username },
                |m: &mut ChatMessageDto| { &mut m.username },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "nickname",
                |m: &ChatMessageDto| { &m.nickname },
                |m: &mut ChatMessageDto| { &mut m.nickname },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "userColor",
                |m: &ChatMessageDto| { &m.userColor },
                |m: &mut ChatMessageDto| { &mut m.userColor },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isBot",
                |m: &ChatMessageDto| { &m.isBot },
                |m: &mut ChatMessageDto| { &mut m.isBot },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isPlugin",
                |m: &ChatMessageDto| { &m.isPlugin },
                |m: &mut ChatMessageDto| { &mut m.isPlugin },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isMod",
                |m: &ChatMessageDto| { &m.isMod },
                |m: &mut ChatMessageDto| { &mut m.isMod },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isSystem",
                |m: &ChatMessageDto| { &m.isSystem },
                |m: &mut ChatMessageDto| { &mut m.isSystem },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isAdmin",
                |m: &ChatMessageDto| { &m.isAdmin },
                |m: &mut ChatMessageDto| { &mut m.isAdmin },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isDev",
                |m: &ChatMessageDto| { &m.isDev },
                |m: &mut ChatMessageDto| { &mut m.isDev },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isRoomOwner",
                |m: &ChatMessageDto| { &m.isRoomOwner },
                |m: &mut ChatMessageDto| { &mut m.isRoomOwner },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isGuest",
                |m: &ChatMessageDto| { &m.isGuest },
                |m: &mut ChatMessageDto| { &mut m.isGuest },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isProMember",
                |m: &ChatMessageDto| { &m.isProMember },
                |m: &mut ChatMessageDto| { &mut m.isProMember },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "whisperer",
                |m: &ChatMessageDto| { &m.whisperer },
                |m: &mut ChatMessageDto| { &mut m.whisperer },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "autoDelete",
                |m: &ChatMessageDto| { &m.autoDelete },
                |m: &mut ChatMessageDto| { &mut m.autoDelete },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "modifiedDate",
                |m: &ChatMessageDto| { &m.modifiedDate },
                |m: &mut ChatMessageDto| { &mut m.modifiedDate },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "user_uuid",
                |m: &ChatMessageDto| { &m.user_uuid },
                |m: &mut ChatMessageDto| { &mut m.user_uuid },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ChatMessageDto>(
                "ChatMessageDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ChatMessageDto {
        static instance: ::protobuf::rt::LazyV2<ChatMessageDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ChatMessageDto::new)
    }
}

impl ::protobuf::Clear for ChatMessageDto {
    fn clear(&mut self) {
        self.id = 0;
        self.messageID.clear();
        self.messageReplyID.clear();
        self.socketID.clear();
        self.message.clear();
        self.ts.clear();
        self.roomID.clear();
        self.usertag.clear();
        self.username.clear();
        self.nickname.clear();
        self.userColor.clear();
        self.isBot = false;
        self.isPlugin = false;
        self.isMod = false;
        self.isSystem = false;
        self.isAdmin = false;
        self.isDev = false;
        self.isRoomOwner = false;
        self.isGuest = false;
        self.isProMember = false;
        self.whisperer.clear();
        self.autoDelete = false;
        self.modifiedDate.clear();
        self.user_uuid.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ChatMessageDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ChatMessageDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomChatHistory {
    // message fields
    pub lastMessages: ::protobuf::RepeatedField<ChatMessageDto>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomChatHistory {
    fn default() -> &'a RoomChatHistory {
        <RoomChatHistory as ::protobuf::Message>::default_instance()
    }
}

impl RoomChatHistory {
    pub fn new() -> RoomChatHistory {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDto lastMessages = 1;


    pub fn get_lastMessages(&self) -> &[ChatMessageDto] {
        &self.lastMessages
    }
    pub fn clear_lastMessages(&mut self) {
        self.lastMessages.clear();
    }

    // Param is passed by value, moved
    pub fn set_lastMessages(&mut self, v: ::protobuf::RepeatedField<ChatMessageDto>) {
        self.lastMessages = v;
    }

    // Mutable pointer to the field.
    pub fn mut_lastMessages(&mut self) -> &mut ::protobuf::RepeatedField<ChatMessageDto> {
        &mut self.lastMessages
    }

    // Take field
    pub fn take_lastMessages(&mut self) -> ::protobuf::RepeatedField<ChatMessageDto> {
        ::std::mem::replace(&mut self.lastMessages, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for RoomChatHistory {
    fn is_initialized(&self) -> bool {
        for v in &self.lastMessages {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.lastMessages)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.lastMessages {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.lastMessages {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomChatHistory {
        RoomChatHistory::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ChatMessageDto>>(
                "lastMessages",
                |m: &RoomChatHistory| { &m.lastMessages },
                |m: &mut RoomChatHistory| { &mut m.lastMessages },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomChatHistory>(
                "RoomChatHistory",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomChatHistory {
        static instance: ::protobuf::rt::LazyV2<RoomChatHistory> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomChatHistory::new)
    }
}

impl ::protobuf::Clear for RoomChatHistory {
    fn clear(&mut self) {
        self.lastMessages.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomChatHistory {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomChatHistory {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ChatMessageDtoList {
    // message fields
    pub messages: ::protobuf::RepeatedField<ChatMessageDto>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ChatMessageDtoList {
    fn default() -> &'a ChatMessageDtoList {
        <ChatMessageDtoList as ::protobuf::Message>::default_instance()
    }
}

impl ChatMessageDtoList {
    pub fn new() -> ChatMessageDtoList {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDto messages = 1;


    pub fn get_messages(&self) -> &[ChatMessageDto] {
        &self.messages
    }
    pub fn clear_messages(&mut self) {
        self.messages.clear();
    }

    // Param is passed by value, moved
    pub fn set_messages(&mut self, v: ::protobuf::RepeatedField<ChatMessageDto>) {
        self.messages = v;
    }

    // Mutable pointer to the field.
    pub fn mut_messages(&mut self) -> &mut ::protobuf::RepeatedField<ChatMessageDto> {
        &mut self.messages
    }

    // Take field
    pub fn take_messages(&mut self) -> ::protobuf::RepeatedField<ChatMessageDto> {
        ::std::mem::replace(&mut self.messages, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for ChatMessageDtoList {
    fn is_initialized(&self) -> bool {
        for v in &self.messages {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.messages)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.messages {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.messages {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ChatMessageDtoList {
        ChatMessageDtoList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ChatMessageDto>>(
                "messages",
                |m: &ChatMessageDtoList| { &m.messages },
                |m: &mut ChatMessageDtoList| { &mut m.messages },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ChatMessageDtoList>(
                "ChatMessageDtoList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ChatMessageDtoList {
        static instance: ::protobuf::rt::LazyV2<ChatMessageDtoList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ChatMessageDtoList::new)
    }
}

impl ::protobuf::Clear for ChatMessageDtoList {
    fn clear(&mut self) {
        self.messages.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ChatMessageDtoList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ChatMessageDtoList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct JoinedRoomData {
    // message fields
    pub roomID: ::std::string::String,
    pub roomName: ::std::string::String,
    pub roomType: super::room_renditions::RoomType,
    pub roomStatus: super::room_renditions::RoomStatus,
    pub roomSettings: ::protobuf::SingularPtrField<super::room_renditions::RoomSettings>,
    pub roomOwner: ::std::string::String,
    // message oneof groups
    pub _selfHostedCountryCode: ::std::option::Option<JoinedRoomData_oneof__selfHostedCountryCode>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a JoinedRoomData {
    fn default() -> &'a JoinedRoomData {
        <JoinedRoomData as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum JoinedRoomData_oneof__selfHostedCountryCode {
    selfHostedCountryCode(::std::string::String),
}

impl JoinedRoomData {
    pub fn new() -> JoinedRoomData {
        ::std::default::Default::default()
    }

    // string roomID = 1;


    pub fn get_roomID(&self) -> &str {
        &self.roomID
    }
    pub fn clear_roomID(&mut self) {
        self.roomID.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.roomID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        &mut self.roomID
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomID, ::std::string::String::new())
    }

    // string roomName = 2;


    pub fn get_roomName(&self) -> &str {
        &self.roomName
    }
    pub fn clear_roomName(&mut self) {
        self.roomName.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomName(&mut self, v: ::std::string::String) {
        self.roomName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomName(&mut self) -> &mut ::std::string::String {
        &mut self.roomName
    }

    // Take field
    pub fn take_roomName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomName, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomType roomType = 3;


    pub fn get_roomType(&self) -> super::room_renditions::RoomType {
        self.roomType
    }
    pub fn clear_roomType(&mut self) {
        self.roomType = super::room_renditions::RoomType::Lobby;
    }

    // Param is passed by value, moved
    pub fn set_roomType(&mut self, v: super::room_renditions::RoomType) {
        self.roomType = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStatus roomStatus = 4;


    pub fn get_roomStatus(&self) -> super::room_renditions::RoomStatus {
        self.roomStatus
    }
    pub fn clear_roomStatus(&mut self) {
        self.roomStatus = super::room_renditions::RoomStatus::Public;
    }

    // Param is passed by value, moved
    pub fn set_roomStatus(&mut self, v: super::room_renditions::RoomStatus) {
        self.roomStatus = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomSettings roomSettings = 5;


    pub fn get_roomSettings(&self) -> &super::room_renditions::RoomSettings {
        self.roomSettings.as_ref().unwrap_or_else(|| <super::room_renditions::RoomSettings as ::protobuf::Message>::default_instance())
    }
    pub fn clear_roomSettings(&mut self) {
        self.roomSettings.clear();
    }

    pub fn has_roomSettings(&self) -> bool {
        self.roomSettings.is_some()
    }

    // Param is passed by value, moved
    pub fn set_roomSettings(&mut self, v: super::room_renditions::RoomSettings) {
        self.roomSettings = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomSettings(&mut self) -> &mut super::room_renditions::RoomSettings {
        if self.roomSettings.is_none() {
            self.roomSettings.set_default();
        }
        self.roomSettings.as_mut().unwrap()
    }

    // Take field
    pub fn take_roomSettings(&mut self) -> super::room_renditions::RoomSettings {
        self.roomSettings.take().unwrap_or_else(|| super::room_renditions::RoomSettings::new())
    }

    // string roomOwner = 6;


    pub fn get_roomOwner(&self) -> &str {
        &self.roomOwner
    }
    pub fn clear_roomOwner(&mut self) {
        self.roomOwner.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomOwner(&mut self, v: ::std::string::String) {
        self.roomOwner = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomOwner(&mut self) -> &mut ::std::string::String {
        &mut self.roomOwner
    }

    // Take field
    pub fn take_roomOwner(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomOwner, ::std::string::String::new())
    }

    // string selfHostedCountryCode = 7;


    pub fn get_selfHostedCountryCode(&self) -> &str {
        match self._selfHostedCountryCode {
            ::std::option::Option::Some(JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_selfHostedCountryCode(&mut self) {
        self._selfHostedCountryCode = ::std::option::Option::None;
    }

    pub fn has_selfHostedCountryCode(&self) -> bool {
        match self._selfHostedCountryCode {
            ::std::option::Option::Some(JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_selfHostedCountryCode(&mut self, v: ::std::string::String) {
        self._selfHostedCountryCode = ::std::option::Option::Some(JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(v))
    }

    // Mutable pointer to the field.
    pub fn mut_selfHostedCountryCode(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(_)) = self._selfHostedCountryCode {
        } else {
            self._selfHostedCountryCode = ::std::option::Option::Some(JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(::std::string::String::new()));
        }
        match self._selfHostedCountryCode {
            ::std::option::Option::Some(JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_selfHostedCountryCode(&mut self) -> ::std::string::String {
        if self.has_selfHostedCountryCode() {
            match self._selfHostedCountryCode.take() {
                ::std::option::Option::Some(JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }
}

impl ::protobuf::Message for JoinedRoomData {
    fn is_initialized(&self) -> bool {
        for v in &self.roomSettings {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomID)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomName)?;
                },
                3 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.roomType, 3, &mut self.unknown_fields)?
                },
                4 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.roomStatus, 4, &mut self.unknown_fields)?
                },
                5 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.roomSettings)?;
                },
                6 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomOwner)?;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._selfHostedCountryCode = ::std::option::Option::Some(JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(is.read_string()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.roomID.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.roomID);
        }
        if !self.roomName.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.roomName);
        }
        if self.roomType != super::room_renditions::RoomType::Lobby {
            my_size += ::protobuf::rt::enum_size(3, self.roomType);
        }
        if self.roomStatus != super::room_renditions::RoomStatus::Public {
            my_size += ::protobuf::rt::enum_size(4, self.roomStatus);
        }
        if let Some(ref v) = self.roomSettings.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if !self.roomOwner.is_empty() {
            my_size += ::protobuf::rt::string_size(6, &self.roomOwner);
        }
        if let ::std::option::Option::Some(ref v) = self._selfHostedCountryCode {
            match v {
                &JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(ref v) => {
                    my_size += ::protobuf::rt::string_size(7, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.roomID.is_empty() {
            os.write_string(1, &self.roomID)?;
        }
        if !self.roomName.is_empty() {
            os.write_string(2, &self.roomName)?;
        }
        if self.roomType != super::room_renditions::RoomType::Lobby {
            os.write_enum(3, ::protobuf::ProtobufEnum::value(&self.roomType))?;
        }
        if self.roomStatus != super::room_renditions::RoomStatus::Public {
            os.write_enum(4, ::protobuf::ProtobufEnum::value(&self.roomStatus))?;
        }
        if let Some(ref v) = self.roomSettings.as_ref() {
            os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if !self.roomOwner.is_empty() {
            os.write_string(6, &self.roomOwner)?;
        }
        if let ::std::option::Option::Some(ref v) = self._selfHostedCountryCode {
            match v {
                &JoinedRoomData_oneof__selfHostedCountryCode::selfHostedCountryCode(ref v) => {
                    os.write_string(7, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> JoinedRoomData {
        JoinedRoomData::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomID",
                |m: &JoinedRoomData| { &m.roomID },
                |m: &mut JoinedRoomData| { &mut m.roomID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomName",
                |m: &JoinedRoomData| { &m.roomName },
                |m: &mut JoinedRoomData| { &mut m.roomName },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<super::room_renditions::RoomType>>(
                "roomType",
                |m: &JoinedRoomData| { &m.roomType },
                |m: &mut JoinedRoomData| { &mut m.roomType },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<super::room_renditions::RoomStatus>>(
                "roomStatus",
                |m: &JoinedRoomData| { &m.roomStatus },
                |m: &mut JoinedRoomData| { &mut m.roomStatus },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::room_renditions::RoomSettings>>(
                "roomSettings",
                |m: &JoinedRoomData| { &m.roomSettings },
                |m: &mut JoinedRoomData| { &mut m.roomSettings },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomOwner",
                |m: &JoinedRoomData| { &m.roomOwner },
                |m: &mut JoinedRoomData| { &mut m.roomOwner },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "selfHostedCountryCode",
                JoinedRoomData::has_selfHostedCountryCode,
                JoinedRoomData::get_selfHostedCountryCode,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<JoinedRoomData>(
                "JoinedRoomData",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static JoinedRoomData {
        static instance: ::protobuf::rt::LazyV2<JoinedRoomData> = ::protobuf::rt::LazyV2::INIT;
        instance.get(JoinedRoomData::new)
    }
}

impl ::protobuf::Clear for JoinedRoomData {
    fn clear(&mut self) {
        self.roomID.clear();
        self.roomName.clear();
        self.roomType = super::room_renditions::RoomType::Lobby;
        self.roomStatus = super::room_renditions::RoomStatus::Public;
        self.roomSettings.clear();
        self.roomOwner.clear();
        self._selfHostedCountryCode = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for JoinedRoomData {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for JoinedRoomData {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct CommandResponse {
    // message fields
    pub messageType: CommandResponseType,
    // message oneof groups
    pub messageData: ::std::option::Option<CommandResponse_oneof_messageData>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a CommandResponse {
    fn default() -> &'a CommandResponse {
        <CommandResponse as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum CommandResponse_oneof_messageData {
    stringValue(::std::string::String),
    roomsList(CommandResponse_RoomsList),
    roomDto(super::room_renditions::RoomDto),
    validationErrorList(CommandResponse_ClientValidationErrorList),
    joinRoomFailResponse(CommandResponse_JoinRoomFailResponse),
    enterRoomPasswordResponse(CommandResponse_EnterRoomPasswordResponse),
}

impl CommandResponse {
    pub fn new() -> CommandResponse {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponseType messageType = 1;


    pub fn get_messageType(&self) -> CommandResponseType {
        self.messageType
    }
    pub fn clear_messageType(&mut self) {
        self.messageType = CommandResponseType::Error;
    }

    // Param is passed by value, moved
    pub fn set_messageType(&mut self, v: CommandResponseType) {
        self.messageType = v;
    }

    // string stringValue = 2;


    pub fn get_stringValue(&self) -> &str {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::stringValue(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_stringValue(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_stringValue(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::stringValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_stringValue(&mut self, v: ::std::string::String) {
        self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::stringValue(v))
    }

    // Mutable pointer to the field.
    pub fn mut_stringValue(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(CommandResponse_oneof_messageData::stringValue(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::stringValue(::std::string::String::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::stringValue(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_stringValue(&mut self) -> ::std::string::String {
        if self.has_stringValue() {
            match self.messageData.take() {
                ::std::option::Option::Some(CommandResponse_oneof_messageData::stringValue(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.RoomsList roomsList = 3;


    pub fn get_roomsList(&self) -> &CommandResponse_RoomsList {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::roomsList(ref v)) => v,
            _ => <CommandResponse_RoomsList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomsList(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_roomsList(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::roomsList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomsList(&mut self, v: CommandResponse_RoomsList) {
        self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::roomsList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomsList(&mut self) -> &mut CommandResponse_RoomsList {
        if let ::std::option::Option::Some(CommandResponse_oneof_messageData::roomsList(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::roomsList(CommandResponse_RoomsList::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::roomsList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomsList(&mut self) -> CommandResponse_RoomsList {
        if self.has_roomsList() {
            match self.messageData.take() {
                ::std::option::Option::Some(CommandResponse_oneof_messageData::roomsList(v)) => v,
                _ => panic!(),
            }
        } else {
            CommandResponse_RoomsList::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomDto roomDto = 4;


    pub fn get_roomDto(&self) -> &super::room_renditions::RoomDto {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::roomDto(ref v)) => v,
            _ => <super::room_renditions::RoomDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_roomDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::roomDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomDto(&mut self, v: super::room_renditions::RoomDto) {
        self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::roomDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomDto(&mut self) -> &mut super::room_renditions::RoomDto {
        if let ::std::option::Option::Some(CommandResponse_oneof_messageData::roomDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::roomDto(super::room_renditions::RoomDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::roomDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomDto(&mut self) -> super::room_renditions::RoomDto {
        if self.has_roomDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(CommandResponse_oneof_messageData::roomDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::RoomDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.ClientValidationErrorList validationErrorList = 5;


    pub fn get_validationErrorList(&self) -> &CommandResponse_ClientValidationErrorList {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::validationErrorList(ref v)) => v,
            _ => <CommandResponse_ClientValidationErrorList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_validationErrorList(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_validationErrorList(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::validationErrorList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_validationErrorList(&mut self, v: CommandResponse_ClientValidationErrorList) {
        self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::validationErrorList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_validationErrorList(&mut self) -> &mut CommandResponse_ClientValidationErrorList {
        if let ::std::option::Option::Some(CommandResponse_oneof_messageData::validationErrorList(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::validationErrorList(CommandResponse_ClientValidationErrorList::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::validationErrorList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_validationErrorList(&mut self) -> CommandResponse_ClientValidationErrorList {
        if self.has_validationErrorList() {
            match self.messageData.take() {
                ::std::option::Option::Some(CommandResponse_oneof_messageData::validationErrorList(v)) => v,
                _ => panic!(),
            }
        } else {
            CommandResponse_ClientValidationErrorList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.JoinRoomFailResponse joinRoomFailResponse = 6;


    pub fn get_joinRoomFailResponse(&self) -> &CommandResponse_JoinRoomFailResponse {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::joinRoomFailResponse(ref v)) => v,
            _ => <CommandResponse_JoinRoomFailResponse as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_joinRoomFailResponse(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_joinRoomFailResponse(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::joinRoomFailResponse(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_joinRoomFailResponse(&mut self, v: CommandResponse_JoinRoomFailResponse) {
        self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::joinRoomFailResponse(v))
    }

    // Mutable pointer to the field.
    pub fn mut_joinRoomFailResponse(&mut self) -> &mut CommandResponse_JoinRoomFailResponse {
        if let ::std::option::Option::Some(CommandResponse_oneof_messageData::joinRoomFailResponse(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::joinRoomFailResponse(CommandResponse_JoinRoomFailResponse::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::joinRoomFailResponse(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_joinRoomFailResponse(&mut self) -> CommandResponse_JoinRoomFailResponse {
        if self.has_joinRoomFailResponse() {
            match self.messageData.take() {
                ::std::option::Option::Some(CommandResponse_oneof_messageData::joinRoomFailResponse(v)) => v,
                _ => panic!(),
            }
        } else {
            CommandResponse_JoinRoomFailResponse::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.EnterRoomPasswordResponse enterRoomPasswordResponse = 7;


    pub fn get_enterRoomPasswordResponse(&self) -> &CommandResponse_EnterRoomPasswordResponse {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::enterRoomPasswordResponse(ref v)) => v,
            _ => <CommandResponse_EnterRoomPasswordResponse as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_enterRoomPasswordResponse(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_enterRoomPasswordResponse(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::enterRoomPasswordResponse(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_enterRoomPasswordResponse(&mut self, v: CommandResponse_EnterRoomPasswordResponse) {
        self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::enterRoomPasswordResponse(v))
    }

    // Mutable pointer to the field.
    pub fn mut_enterRoomPasswordResponse(&mut self) -> &mut CommandResponse_EnterRoomPasswordResponse {
        if let ::std::option::Option::Some(CommandResponse_oneof_messageData::enterRoomPasswordResponse(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::enterRoomPasswordResponse(CommandResponse_EnterRoomPasswordResponse::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(CommandResponse_oneof_messageData::enterRoomPasswordResponse(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_enterRoomPasswordResponse(&mut self) -> CommandResponse_EnterRoomPasswordResponse {
        if self.has_enterRoomPasswordResponse() {
            match self.messageData.take() {
                ::std::option::Option::Some(CommandResponse_oneof_messageData::enterRoomPasswordResponse(v)) => v,
                _ => panic!(),
            }
        } else {
            CommandResponse_EnterRoomPasswordResponse::new()
        }
    }
}

impl ::protobuf::Message for CommandResponse {
    fn is_initialized(&self) -> bool {
        if let Some(CommandResponse_oneof_messageData::roomsList(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(CommandResponse_oneof_messageData::roomDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(CommandResponse_oneof_messageData::validationErrorList(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(CommandResponse_oneof_messageData::joinRoomFailResponse(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(CommandResponse_oneof_messageData::enterRoomPasswordResponse(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.messageType, 1, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::stringValue(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::roomsList(is.read_message()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::roomDto(is.read_message()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::validationErrorList(is.read_message()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::joinRoomFailResponse(is.read_message()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(CommandResponse_oneof_messageData::enterRoomPasswordResponse(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.messageType != CommandResponseType::Error {
            my_size += ::protobuf::rt::enum_size(1, self.messageType);
        }
        if let ::std::option::Option::Some(ref v) = self.messageData {
            match v {
                &CommandResponse_oneof_messageData::stringValue(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
                &CommandResponse_oneof_messageData::roomsList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &CommandResponse_oneof_messageData::roomDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &CommandResponse_oneof_messageData::validationErrorList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &CommandResponse_oneof_messageData::joinRoomFailResponse(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &CommandResponse_oneof_messageData::enterRoomPasswordResponse(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.messageType != CommandResponseType::Error {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.messageType))?;
        }
        if let ::std::option::Option::Some(ref v) = self.messageData {
            match v {
                &CommandResponse_oneof_messageData::stringValue(ref v) => {
                    os.write_string(2, v)?;
                },
                &CommandResponse_oneof_messageData::roomsList(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &CommandResponse_oneof_messageData::roomDto(ref v) => {
                    os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &CommandResponse_oneof_messageData::validationErrorList(ref v) => {
                    os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &CommandResponse_oneof_messageData::joinRoomFailResponse(ref v) => {
                    os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &CommandResponse_oneof_messageData::enterRoomPasswordResponse(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> CommandResponse {
        CommandResponse::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<CommandResponseType>>(
                "messageType",
                |m: &CommandResponse| { &m.messageType },
                |m: &mut CommandResponse| { &mut m.messageType },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "stringValue",
                CommandResponse::has_stringValue,
                CommandResponse::get_stringValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, CommandResponse_RoomsList>(
                "roomsList",
                CommandResponse::has_roomsList,
                CommandResponse::get_roomsList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::RoomDto>(
                "roomDto",
                CommandResponse::has_roomDto,
                CommandResponse::get_roomDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, CommandResponse_ClientValidationErrorList>(
                "validationErrorList",
                CommandResponse::has_validationErrorList,
                CommandResponse::get_validationErrorList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, CommandResponse_JoinRoomFailResponse>(
                "joinRoomFailResponse",
                CommandResponse::has_joinRoomFailResponse,
                CommandResponse::get_joinRoomFailResponse,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, CommandResponse_EnterRoomPasswordResponse>(
                "enterRoomPasswordResponse",
                CommandResponse::has_enterRoomPasswordResponse,
                CommandResponse::get_enterRoomPasswordResponse,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<CommandResponse>(
                "CommandResponse",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static CommandResponse {
        static instance: ::protobuf::rt::LazyV2<CommandResponse> = ::protobuf::rt::LazyV2::INIT;
        instance.get(CommandResponse::new)
    }
}

impl ::protobuf::Clear for CommandResponse {
    fn clear(&mut self) {
        self.messageType = CommandResponseType::Error;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for CommandResponse {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for CommandResponse {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct CommandResponse_RoomsList {
    // message fields
    pub list: ::protobuf::RepeatedField<super::room_renditions::BasicRoomDto>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a CommandResponse_RoomsList {
    fn default() -> &'a CommandResponse_RoomsList {
        <CommandResponse_RoomsList as ::protobuf::Message>::default_instance()
    }
}

impl CommandResponse_RoomsList {
    pub fn new() -> CommandResponse_RoomsList {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.RoomRenditions.BasicRoomDto list = 1;


    pub fn get_list(&self) -> &[super::room_renditions::BasicRoomDto] {
        &self.list
    }
    pub fn clear_list(&mut self) {
        self.list.clear();
    }

    // Param is passed by value, moved
    pub fn set_list(&mut self, v: ::protobuf::RepeatedField<super::room_renditions::BasicRoomDto>) {
        self.list = v;
    }

    // Mutable pointer to the field.
    pub fn mut_list(&mut self) -> &mut ::protobuf::RepeatedField<super::room_renditions::BasicRoomDto> {
        &mut self.list
    }

    // Take field
    pub fn take_list(&mut self) -> ::protobuf::RepeatedField<super::room_renditions::BasicRoomDto> {
        ::std::mem::replace(&mut self.list, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for CommandResponse_RoomsList {
    fn is_initialized(&self) -> bool {
        for v in &self.list {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.list)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.list {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.list {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> CommandResponse_RoomsList {
        CommandResponse_RoomsList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::room_renditions::BasicRoomDto>>(
                "list",
                |m: &CommandResponse_RoomsList| { &m.list },
                |m: &mut CommandResponse_RoomsList| { &mut m.list },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<CommandResponse_RoomsList>(
                "CommandResponse.RoomsList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static CommandResponse_RoomsList {
        static instance: ::protobuf::rt::LazyV2<CommandResponse_RoomsList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(CommandResponse_RoomsList::new)
    }
}

impl ::protobuf::Clear for CommandResponse_RoomsList {
    fn clear(&mut self) {
        self.list.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for CommandResponse_RoomsList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for CommandResponse_RoomsList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct CommandResponse_ClientValidationErrorList {
    // message fields
    pub data: ::protobuf::RepeatedField<ClientValidationError>,
    pub errorType: ClientValidationErrorMsgType,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a CommandResponse_ClientValidationErrorList {
    fn default() -> &'a CommandResponse_ClientValidationErrorList {
        <CommandResponse_ClientValidationErrorList as ::protobuf::Message>::default_instance()
    }
}

impl CommandResponse_ClientValidationErrorList {
    pub fn new() -> CommandResponse_ClientValidationErrorList {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.Msgs.ClientValidationError data = 1;


    pub fn get_data(&self) -> &[ClientValidationError] {
        &self.data
    }
    pub fn clear_data(&mut self) {
        self.data.clear();
    }

    // Param is passed by value, moved
    pub fn set_data(&mut self, v: ::protobuf::RepeatedField<ClientValidationError>) {
        self.data = v;
    }

    // Mutable pointer to the field.
    pub fn mut_data(&mut self) -> &mut ::protobuf::RepeatedField<ClientValidationError> {
        &mut self.data
    }

    // Take field
    pub fn take_data(&mut self) -> ::protobuf::RepeatedField<ClientValidationError> {
        ::std::mem::replace(&mut self.data, ::protobuf::RepeatedField::new())
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ClientValidationErrorMsgType errorType = 2;


    pub fn get_errorType(&self) -> ClientValidationErrorMsgType {
        self.errorType
    }
    pub fn clear_errorType(&mut self) {
        self.errorType = ClientValidationErrorMsgType::CreateRoom;
    }

    // Param is passed by value, moved
    pub fn set_errorType(&mut self, v: ClientValidationErrorMsgType) {
        self.errorType = v;
    }
}

impl ::protobuf::Message for CommandResponse_ClientValidationErrorList {
    fn is_initialized(&self) -> bool {
        for v in &self.data {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.data)?;
                },
                2 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.errorType, 2, &mut self.unknown_fields)?
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.data {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if self.errorType != ClientValidationErrorMsgType::CreateRoom {
            my_size += ::protobuf::rt::enum_size(2, self.errorType);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.data {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if self.errorType != ClientValidationErrorMsgType::CreateRoom {
            os.write_enum(2, ::protobuf::ProtobufEnum::value(&self.errorType))?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> CommandResponse_ClientValidationErrorList {
        CommandResponse_ClientValidationErrorList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ClientValidationError>>(
                "data",
                |m: &CommandResponse_ClientValidationErrorList| { &m.data },
                |m: &mut CommandResponse_ClientValidationErrorList| { &mut m.data },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<ClientValidationErrorMsgType>>(
                "errorType",
                |m: &CommandResponse_ClientValidationErrorList| { &m.errorType },
                |m: &mut CommandResponse_ClientValidationErrorList| { &mut m.errorType },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<CommandResponse_ClientValidationErrorList>(
                "CommandResponse.ClientValidationErrorList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static CommandResponse_ClientValidationErrorList {
        static instance: ::protobuf::rt::LazyV2<CommandResponse_ClientValidationErrorList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(CommandResponse_ClientValidationErrorList::new)
    }
}

impl ::protobuf::Clear for CommandResponse_ClientValidationErrorList {
    fn clear(&mut self) {
        self.data.clear();
        self.errorType = ClientValidationErrorMsgType::CreateRoom;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for CommandResponse_ClientValidationErrorList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for CommandResponse_ClientValidationErrorList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct CommandResponse_JoinRoomFailResponse {
    // message fields
    pub roomID: ::std::string::String,
    pub reason: JoinRoomFailType,
    pub roomName: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a CommandResponse_JoinRoomFailResponse {
    fn default() -> &'a CommandResponse_JoinRoomFailResponse {
        <CommandResponse_JoinRoomFailResponse as ::protobuf::Message>::default_instance()
    }
}

impl CommandResponse_JoinRoomFailResponse {
    pub fn new() -> CommandResponse_JoinRoomFailResponse {
        ::std::default::Default::default()
    }

    // string roomID = 1;


    pub fn get_roomID(&self) -> &str {
        &self.roomID
    }
    pub fn clear_roomID(&mut self) {
        self.roomID.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.roomID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        &mut self.roomID
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomID, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.JoinRoomFailType reason = 2;


    pub fn get_reason(&self) -> JoinRoomFailType {
        self.reason
    }
    pub fn clear_reason(&mut self) {
        self.reason = JoinRoomFailType::QueuedToJoinRoom;
    }

    // Param is passed by value, moved
    pub fn set_reason(&mut self, v: JoinRoomFailType) {
        self.reason = v;
    }

    // string roomName = 3;


    pub fn get_roomName(&self) -> &str {
        &self.roomName
    }
    pub fn clear_roomName(&mut self) {
        self.roomName.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomName(&mut self, v: ::std::string::String) {
        self.roomName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomName(&mut self) -> &mut ::std::string::String {
        &mut self.roomName
    }

    // Take field
    pub fn take_roomName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomName, ::std::string::String::new())
    }
}

impl ::protobuf::Message for CommandResponse_JoinRoomFailResponse {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomID)?;
                },
                2 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.reason, 2, &mut self.unknown_fields)?
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomName)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.roomID.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.roomID);
        }
        if self.reason != JoinRoomFailType::QueuedToJoinRoom {
            my_size += ::protobuf::rt::enum_size(2, self.reason);
        }
        if !self.roomName.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.roomName);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.roomID.is_empty() {
            os.write_string(1, &self.roomID)?;
        }
        if self.reason != JoinRoomFailType::QueuedToJoinRoom {
            os.write_enum(2, ::protobuf::ProtobufEnum::value(&self.reason))?;
        }
        if !self.roomName.is_empty() {
            os.write_string(3, &self.roomName)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> CommandResponse_JoinRoomFailResponse {
        CommandResponse_JoinRoomFailResponse::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomID",
                |m: &CommandResponse_JoinRoomFailResponse| { &m.roomID },
                |m: &mut CommandResponse_JoinRoomFailResponse| { &mut m.roomID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<JoinRoomFailType>>(
                "reason",
                |m: &CommandResponse_JoinRoomFailResponse| { &m.reason },
                |m: &mut CommandResponse_JoinRoomFailResponse| { &mut m.reason },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomName",
                |m: &CommandResponse_JoinRoomFailResponse| { &m.roomName },
                |m: &mut CommandResponse_JoinRoomFailResponse| { &mut m.roomName },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<CommandResponse_JoinRoomFailResponse>(
                "CommandResponse.JoinRoomFailResponse",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static CommandResponse_JoinRoomFailResponse {
        static instance: ::protobuf::rt::LazyV2<CommandResponse_JoinRoomFailResponse> = ::protobuf::rt::LazyV2::INIT;
        instance.get(CommandResponse_JoinRoomFailResponse::new)
    }
}

impl ::protobuf::Clear for CommandResponse_JoinRoomFailResponse {
    fn clear(&mut self) {
        self.roomID.clear();
        self.reason = JoinRoomFailType::QueuedToJoinRoom;
        self.roomName.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for CommandResponse_JoinRoomFailResponse {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for CommandResponse_JoinRoomFailResponse {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct CommandResponse_EnterRoomPasswordResponse {
    // message fields
    pub roomID: ::std::string::String,
    pub roomName: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a CommandResponse_EnterRoomPasswordResponse {
    fn default() -> &'a CommandResponse_EnterRoomPasswordResponse {
        <CommandResponse_EnterRoomPasswordResponse as ::protobuf::Message>::default_instance()
    }
}

impl CommandResponse_EnterRoomPasswordResponse {
    pub fn new() -> CommandResponse_EnterRoomPasswordResponse {
        ::std::default::Default::default()
    }

    // string roomID = 1;


    pub fn get_roomID(&self) -> &str {
        &self.roomID
    }
    pub fn clear_roomID(&mut self) {
        self.roomID.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.roomID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        &mut self.roomID
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomID, ::std::string::String::new())
    }

    // string roomName = 2;


    pub fn get_roomName(&self) -> &str {
        &self.roomName
    }
    pub fn clear_roomName(&mut self) {
        self.roomName.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomName(&mut self, v: ::std::string::String) {
        self.roomName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomName(&mut self) -> &mut ::std::string::String {
        &mut self.roomName
    }

    // Take field
    pub fn take_roomName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomName, ::std::string::String::new())
    }
}

impl ::protobuf::Message for CommandResponse_EnterRoomPasswordResponse {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomID)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomName)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.roomID.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.roomID);
        }
        if !self.roomName.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.roomName);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.roomID.is_empty() {
            os.write_string(1, &self.roomID)?;
        }
        if !self.roomName.is_empty() {
            os.write_string(2, &self.roomName)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> CommandResponse_EnterRoomPasswordResponse {
        CommandResponse_EnterRoomPasswordResponse::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomID",
                |m: &CommandResponse_EnterRoomPasswordResponse| { &m.roomID },
                |m: &mut CommandResponse_EnterRoomPasswordResponse| { &mut m.roomID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomName",
                |m: &CommandResponse_EnterRoomPasswordResponse| { &m.roomName },
                |m: &mut CommandResponse_EnterRoomPasswordResponse| { &mut m.roomName },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<CommandResponse_EnterRoomPasswordResponse>(
                "CommandResponse.EnterRoomPasswordResponse",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static CommandResponse_EnterRoomPasswordResponse {
        static instance: ::protobuf::rt::LazyV2<CommandResponse_EnterRoomPasswordResponse> = ::protobuf::rt::LazyV2::INIT;
        instance.get(CommandResponse_EnterRoomPasswordResponse::new)
    }
}

impl ::protobuf::Clear for CommandResponse_EnterRoomPasswordResponse {
    fn clear(&mut self) {
        self.roomID.clear();
        self.roomName.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for CommandResponse_EnterRoomPasswordResponse {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for CommandResponse_EnterRoomPasswordResponse {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct UserDtoList {
    // message fields
    pub userDto: ::protobuf::RepeatedField<super::user_renditions::UserDto>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UserDtoList {
    fn default() -> &'a UserDtoList {
        <UserDtoList as ::protobuf::Message>::default_instance()
    }
}

impl UserDtoList {
    pub fn new() -> UserDtoList {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserDto userDto = 1;


    pub fn get_userDto(&self) -> &[super::user_renditions::UserDto] {
        &self.userDto
    }
    pub fn clear_userDto(&mut self) {
        self.userDto.clear();
    }

    // Param is passed by value, moved
    pub fn set_userDto(&mut self, v: ::protobuf::RepeatedField<super::user_renditions::UserDto>) {
        self.userDto = v;
    }

    // Mutable pointer to the field.
    pub fn mut_userDto(&mut self) -> &mut ::protobuf::RepeatedField<super::user_renditions::UserDto> {
        &mut self.userDto
    }

    // Take field
    pub fn take_userDto(&mut self) -> ::protobuf::RepeatedField<super::user_renditions::UserDto> {
        ::std::mem::replace(&mut self.userDto, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for UserDtoList {
    fn is_initialized(&self) -> bool {
        for v in &self.userDto {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.userDto)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.userDto {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.userDto {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UserDtoList {
        UserDtoList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::user_renditions::UserDto>>(
                "userDto",
                |m: &UserDtoList| { &m.userDto },
                |m: &mut UserDtoList| { &mut m.userDto },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<UserDtoList>(
                "UserDtoList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static UserDtoList {
        static instance: ::protobuf::rt::LazyV2<UserDtoList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(UserDtoList::new)
    }
}

impl ::protobuf::Clear for UserDtoList {
    fn clear(&mut self) {
        self.userDto.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for UserDtoList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for UserDtoList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ClientSideUserDtoList {
    // message fields
    pub list: ::protobuf::RepeatedField<super::user_renditions::ClientSideUserDto>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ClientSideUserDtoList {
    fn default() -> &'a ClientSideUserDtoList {
        <ClientSideUserDtoList as ::protobuf::Message>::default_instance()
    }
}

impl ClientSideUserDtoList {
    pub fn new() -> ClientSideUserDtoList {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.UserRenditions.ClientSideUserDto list = 1;


    pub fn get_list(&self) -> &[super::user_renditions::ClientSideUserDto] {
        &self.list
    }
    pub fn clear_list(&mut self) {
        self.list.clear();
    }

    // Param is passed by value, moved
    pub fn set_list(&mut self, v: ::protobuf::RepeatedField<super::user_renditions::ClientSideUserDto>) {
        self.list = v;
    }

    // Mutable pointer to the field.
    pub fn mut_list(&mut self) -> &mut ::protobuf::RepeatedField<super::user_renditions::ClientSideUserDto> {
        &mut self.list
    }

    // Take field
    pub fn take_list(&mut self) -> ::protobuf::RepeatedField<super::user_renditions::ClientSideUserDto> {
        ::std::mem::replace(&mut self.list, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for ClientSideUserDtoList {
    fn is_initialized(&self) -> bool {
        for v in &self.list {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.list)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.list {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.list {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ClientSideUserDtoList {
        ClientSideUserDtoList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::user_renditions::ClientSideUserDto>>(
                "list",
                |m: &ClientSideUserDtoList| { &m.list },
                |m: &mut ClientSideUserDtoList| { &mut m.list },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ClientSideUserDtoList>(
                "ClientSideUserDtoList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ClientSideUserDtoList {
        static instance: ::protobuf::rt::LazyV2<ClientSideUserDtoList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ClientSideUserDtoList::new)
    }
}

impl ::protobuf::Clear for ClientSideUserDtoList {
    fn clear(&mut self) {
        self.list.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ClientSideUserDtoList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ClientSideUserDtoList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct SocketIdList {
    // message fields
    pub socketIDs: ::protobuf::RepeatedField<::std::string::String>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SocketIdList {
    fn default() -> &'a SocketIdList {
        <SocketIdList as ::protobuf::Message>::default_instance()
    }
}

impl SocketIdList {
    pub fn new() -> SocketIdList {
        ::std::default::Default::default()
    }

    // repeated string socketIDs = 1;


    pub fn get_socketIDs(&self) -> &[::std::string::String] {
        &self.socketIDs
    }
    pub fn clear_socketIDs(&mut self) {
        self.socketIDs.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketIDs(&mut self, v: ::protobuf::RepeatedField<::std::string::String>) {
        self.socketIDs = v;
    }

    // Mutable pointer to the field.
    pub fn mut_socketIDs(&mut self) -> &mut ::protobuf::RepeatedField<::std::string::String> {
        &mut self.socketIDs
    }

    // Take field
    pub fn take_socketIDs(&mut self) -> ::protobuf::RepeatedField<::std::string::String> {
        ::std::mem::replace(&mut self.socketIDs, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for SocketIdList {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_string_into(wire_type, is, &mut self.socketIDs)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.socketIDs {
            my_size += ::protobuf::rt::string_size(1, &value);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.socketIDs {
            os.write_string(1, &v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SocketIdList {
        SocketIdList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketIDs",
                |m: &SocketIdList| { &m.socketIDs },
                |m: &mut SocketIdList| { &mut m.socketIDs },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<SocketIdList>(
                "SocketIdList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static SocketIdList {
        static instance: ::protobuf::rt::LazyV2<SocketIdList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(SocketIdList::new)
    }
}

impl ::protobuf::Clear for SocketIdList {
    fn clear(&mut self) {
        self.socketIDs.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for SocketIdList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for SocketIdList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct FriendDtoList {
    // message fields
    pub friendDto: ::protobuf::RepeatedField<super::user_renditions::FriendDto>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a FriendDtoList {
    fn default() -> &'a FriendDtoList {
        <FriendDtoList as ::protobuf::Message>::default_instance()
    }
}

impl FriendDtoList {
    pub fn new() -> FriendDtoList {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.UserRenditions.FriendDto friendDto = 1;


    pub fn get_friendDto(&self) -> &[super::user_renditions::FriendDto] {
        &self.friendDto
    }
    pub fn clear_friendDto(&mut self) {
        self.friendDto.clear();
    }

    // Param is passed by value, moved
    pub fn set_friendDto(&mut self, v: ::protobuf::RepeatedField<super::user_renditions::FriendDto>) {
        self.friendDto = v;
    }

    // Mutable pointer to the field.
    pub fn mut_friendDto(&mut self) -> &mut ::protobuf::RepeatedField<super::user_renditions::FriendDto> {
        &mut self.friendDto
    }

    // Take field
    pub fn take_friendDto(&mut self) -> ::protobuf::RepeatedField<super::user_renditions::FriendDto> {
        ::std::mem::replace(&mut self.friendDto, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for FriendDtoList {
    fn is_initialized(&self) -> bool {
        for v in &self.friendDto {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.friendDto)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.friendDto {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.friendDto {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> FriendDtoList {
        FriendDtoList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::user_renditions::FriendDto>>(
                "friendDto",
                |m: &FriendDtoList| { &m.friendDto },
                |m: &mut FriendDtoList| { &mut m.friendDto },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<FriendDtoList>(
                "FriendDtoList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static FriendDtoList {
        static instance: ::protobuf::rt::LazyV2<FriendDtoList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(FriendDtoList::new)
    }
}

impl ::protobuf::Clear for FriendDtoList {
    fn clear(&mut self) {
        self.friendDto.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for FriendDtoList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for FriendDtoList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct PendingFriendRequestList {
    // message fields
    pub pendingFriendRequest: ::protobuf::RepeatedField<super::user_renditions::PendingFriendRequest>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a PendingFriendRequestList {
    fn default() -> &'a PendingFriendRequestList {
        <PendingFriendRequestList as ::protobuf::Message>::default_instance()
    }
}

impl PendingFriendRequestList {
    pub fn new() -> PendingFriendRequestList {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.UserRenditions.PendingFriendRequest pendingFriendRequest = 1;


    pub fn get_pendingFriendRequest(&self) -> &[super::user_renditions::PendingFriendRequest] {
        &self.pendingFriendRequest
    }
    pub fn clear_pendingFriendRequest(&mut self) {
        self.pendingFriendRequest.clear();
    }

    // Param is passed by value, moved
    pub fn set_pendingFriendRequest(&mut self, v: ::protobuf::RepeatedField<super::user_renditions::PendingFriendRequest>) {
        self.pendingFriendRequest = v;
    }

    // Mutable pointer to the field.
    pub fn mut_pendingFriendRequest(&mut self) -> &mut ::protobuf::RepeatedField<super::user_renditions::PendingFriendRequest> {
        &mut self.pendingFriendRequest
    }

    // Take field
    pub fn take_pendingFriendRequest(&mut self) -> ::protobuf::RepeatedField<super::user_renditions::PendingFriendRequest> {
        ::std::mem::replace(&mut self.pendingFriendRequest, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for PendingFriendRequestList {
    fn is_initialized(&self) -> bool {
        for v in &self.pendingFriendRequest {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.pendingFriendRequest)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.pendingFriendRequest {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.pendingFriendRequest {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> PendingFriendRequestList {
        PendingFriendRequestList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::user_renditions::PendingFriendRequest>>(
                "pendingFriendRequest",
                |m: &PendingFriendRequestList| { &m.pendingFriendRequest },
                |m: &mut PendingFriendRequestList| { &mut m.pendingFriendRequest },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<PendingFriendRequestList>(
                "PendingFriendRequestList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static PendingFriendRequestList {
        static instance: ::protobuf::rt::LazyV2<PendingFriendRequestList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(PendingFriendRequestList::new)
    }
}

impl ::protobuf::Clear for PendingFriendRequestList {
    fn clear(&mut self) {
        self.pendingFriendRequest.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for PendingFriendRequestList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for PendingFriendRequestList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct KickedUsersList {
    // message fields
    pub kickedUsers: ::protobuf::RepeatedField<super::user_renditions::KickedUserData>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a KickedUsersList {
    fn default() -> &'a KickedUsersList {
        <KickedUsersList as ::protobuf::Message>::default_instance()
    }
}

impl KickedUsersList {
    pub fn new() -> KickedUsersList {
        ::std::default::Default::default()
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.UserRenditions.KickedUserData kickedUsers = 1;


    pub fn get_kickedUsers(&self) -> &[super::user_renditions::KickedUserData] {
        &self.kickedUsers
    }
    pub fn clear_kickedUsers(&mut self) {
        self.kickedUsers.clear();
    }

    // Param is passed by value, moved
    pub fn set_kickedUsers(&mut self, v: ::protobuf::RepeatedField<super::user_renditions::KickedUserData>) {
        self.kickedUsers = v;
    }

    // Mutable pointer to the field.
    pub fn mut_kickedUsers(&mut self) -> &mut ::protobuf::RepeatedField<super::user_renditions::KickedUserData> {
        &mut self.kickedUsers
    }

    // Take field
    pub fn take_kickedUsers(&mut self) -> ::protobuf::RepeatedField<super::user_renditions::KickedUserData> {
        ::std::mem::replace(&mut self.kickedUsers, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for KickedUsersList {
    fn is_initialized(&self) -> bool {
        for v in &self.kickedUsers {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.kickedUsers)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.kickedUsers {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.kickedUsers {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> KickedUsersList {
        KickedUsersList::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::user_renditions::KickedUserData>>(
                "kickedUsers",
                |m: &KickedUsersList| { &m.kickedUsers },
                |m: &mut KickedUsersList| { &mut m.kickedUsers },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<KickedUsersList>(
                "KickedUsersList",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static KickedUsersList {
        static instance: ::protobuf::rt::LazyV2<KickedUsersList> = ::protobuf::rt::LazyV2::INIT;
        instance.get(KickedUsersList::new)
    }
}

impl ::protobuf::Clear for KickedUsersList {
    fn clear(&mut self) {
        self.kickedUsers.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for KickedUsersList {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for KickedUsersList {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct WelcomeDto {
    // message fields
    pub userClientDto: ::protobuf::SingularPtrField<super::user_renditions::UserClientDto>,
    // message oneof groups
    pub _settings: ::std::option::Option<WelcomeDto_oneof__settings>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WelcomeDto {
    fn default() -> &'a WelcomeDto {
        <WelcomeDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum WelcomeDto_oneof__settings {
    settings(::std::string::String),
}

impl WelcomeDto {
    pub fn new() -> WelcomeDto {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserClientDto userClientDto = 1;


    pub fn get_userClientDto(&self) -> &super::user_renditions::UserClientDto {
        self.userClientDto.as_ref().unwrap_or_else(|| <super::user_renditions::UserClientDto as ::protobuf::Message>::default_instance())
    }
    pub fn clear_userClientDto(&mut self) {
        self.userClientDto.clear();
    }

    pub fn has_userClientDto(&self) -> bool {
        self.userClientDto.is_some()
    }

    // Param is passed by value, moved
    pub fn set_userClientDto(&mut self, v: super::user_renditions::UserClientDto) {
        self.userClientDto = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_userClientDto(&mut self) -> &mut super::user_renditions::UserClientDto {
        if self.userClientDto.is_none() {
            self.userClientDto.set_default();
        }
        self.userClientDto.as_mut().unwrap()
    }

    // Take field
    pub fn take_userClientDto(&mut self) -> super::user_renditions::UserClientDto {
        self.userClientDto.take().unwrap_or_else(|| super::user_renditions::UserClientDto::new())
    }

    // string settings = 2;


    pub fn get_settings(&self) -> &str {
        match self._settings {
            ::std::option::Option::Some(WelcomeDto_oneof__settings::settings(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_settings(&mut self) {
        self._settings = ::std::option::Option::None;
    }

    pub fn has_settings(&self) -> bool {
        match self._settings {
            ::std::option::Option::Some(WelcomeDto_oneof__settings::settings(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_settings(&mut self, v: ::std::string::String) {
        self._settings = ::std::option::Option::Some(WelcomeDto_oneof__settings::settings(v))
    }

    // Mutable pointer to the field.
    pub fn mut_settings(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WelcomeDto_oneof__settings::settings(_)) = self._settings {
        } else {
            self._settings = ::std::option::Option::Some(WelcomeDto_oneof__settings::settings(::std::string::String::new()));
        }
        match self._settings {
            ::std::option::Option::Some(WelcomeDto_oneof__settings::settings(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_settings(&mut self) -> ::std::string::String {
        if self.has_settings() {
            match self._settings.take() {
                ::std::option::Option::Some(WelcomeDto_oneof__settings::settings(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }
}

impl ::protobuf::Message for WelcomeDto {
    fn is_initialized(&self) -> bool {
        for v in &self.userClientDto {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.userClientDto)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._settings = ::std::option::Option::Some(WelcomeDto_oneof__settings::settings(is.read_string()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.userClientDto.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let ::std::option::Option::Some(ref v) = self._settings {
            match v {
                &WelcomeDto_oneof__settings::settings(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.userClientDto.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let ::std::option::Option::Some(ref v) = self._settings {
            match v {
                &WelcomeDto_oneof__settings::settings(ref v) => {
                    os.write_string(2, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WelcomeDto {
        WelcomeDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::user_renditions::UserClientDto>>(
                "userClientDto",
                |m: &WelcomeDto| { &m.userClientDto },
                |m: &mut WelcomeDto| { &mut m.userClientDto },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "settings",
                WelcomeDto::has_settings,
                WelcomeDto::get_settings,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<WelcomeDto>(
                "WelcomeDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static WelcomeDto {
        static instance: ::protobuf::rt::LazyV2<WelcomeDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(WelcomeDto::new)
    }
}

impl ::protobuf::Clear for WelcomeDto {
    fn clear(&mut self) {
        self.userClientDto.clear();
        self._settings = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for WelcomeDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for WelcomeDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AvatarMessageDto {
    // message fields
    pub commandType: AvatarMessageDto_AvatarMessageCommandType,
    pub socketID: ::std::string::String,
    // message oneof groups
    pub commandData: ::std::option::Option<AvatarMessageDto_oneof_commandData>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AvatarMessageDto {
    fn default() -> &'a AvatarMessageDto {
        <AvatarMessageDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarMessageDto_oneof_commandData {
    worldPosition(super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition),
    intValue(i32),
}

impl AvatarMessageDto {
    pub fn new() -> AvatarMessageDto {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.AvatarMessageDto.AvatarMessageCommandType commandType = 1;


    pub fn get_commandType(&self) -> AvatarMessageDto_AvatarMessageCommandType {
        self.commandType
    }
    pub fn clear_commandType(&mut self) {
        self.commandType = AvatarMessageDto_AvatarMessageCommandType::Invalid;
    }

    // Param is passed by value, moved
    pub fn set_commandType(&mut self, v: AvatarMessageDto_AvatarMessageCommandType) {
        self.commandType = v;
    }

    // string socketID = 2;


    pub fn get_socketID(&self) -> &str {
        &self.socketID
    }
    pub fn clear_socketID(&mut self) {
        self.socketID.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.socketID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        &mut self.socketID
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketID, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.AvatarWorldDataDto.AvatarMessageWorldPosition worldPosition = 3;


    pub fn get_worldPosition(&self) -> &super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition {
        match self.commandData {
            ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::worldPosition(ref v)) => v,
            _ => <super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_worldPosition(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_worldPosition(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::worldPosition(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_worldPosition(&mut self, v: super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition) {
        self.commandData = ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::worldPosition(v))
    }

    // Mutable pointer to the field.
    pub fn mut_worldPosition(&mut self) -> &mut super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition {
        if let ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::worldPosition(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::worldPosition(super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::worldPosition(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_worldPosition(&mut self) -> super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition {
        if self.has_worldPosition() {
            match self.commandData.take() {
                ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::worldPosition(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition::new()
        }
    }

    // int32 intValue = 4;


    pub fn get_intValue(&self) -> i32 {
        match self.commandData {
            ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::intValue(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_intValue(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_intValue(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::intValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_intValue(&mut self, v: i32) {
        self.commandData = ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::intValue(v))
    }
}

impl ::protobuf::Message for AvatarMessageDto {
    fn is_initialized(&self) -> bool {
        if let Some(AvatarMessageDto_oneof_commandData::worldPosition(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.commandType, 1, &mut self.unknown_fields)?
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketID)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::worldPosition(is.read_message()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(AvatarMessageDto_oneof_commandData::intValue(is.read_int32()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.commandType != AvatarMessageDto_AvatarMessageCommandType::Invalid {
            my_size += ::protobuf::rt::enum_size(1, self.commandType);
        }
        if !self.socketID.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.socketID);
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &AvatarMessageDto_oneof_commandData::worldPosition(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AvatarMessageDto_oneof_commandData::intValue(v) => {
                    my_size += ::protobuf::rt::value_size(4, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.commandType != AvatarMessageDto_AvatarMessageCommandType::Invalid {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.commandType))?;
        }
        if !self.socketID.is_empty() {
            os.write_string(2, &self.socketID)?;
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &AvatarMessageDto_oneof_commandData::worldPosition(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AvatarMessageDto_oneof_commandData::intValue(v) => {
                    os.write_int32(4, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AvatarMessageDto {
        AvatarMessageDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<AvatarMessageDto_AvatarMessageCommandType>>(
                "commandType",
                |m: &AvatarMessageDto| { &m.commandType },
                |m: &mut AvatarMessageDto| { &mut m.commandType },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketID",
                |m: &AvatarMessageDto| { &m.socketID },
                |m: &mut AvatarMessageDto| { &mut m.socketID },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition>(
                "worldPosition",
                AvatarMessageDto::has_worldPosition,
                AvatarMessageDto::get_worldPosition,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "intValue",
                AvatarMessageDto::has_intValue,
                AvatarMessageDto::get_intValue,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AvatarMessageDto>(
                "AvatarMessageDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AvatarMessageDto {
        static instance: ::protobuf::rt::LazyV2<AvatarMessageDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AvatarMessageDto::new)
    }
}

impl ::protobuf::Clear for AvatarMessageDto {
    fn clear(&mut self) {
        self.commandType = AvatarMessageDto_AvatarMessageCommandType::Invalid;
        self.socketID.clear();
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AvatarMessageDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AvatarMessageDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum AvatarMessageDto_AvatarMessageCommandType {
    Invalid = 0,
    SetPosition = 1,
    SetPianoBenchTarget = 2,
}

impl ::protobuf::ProtobufEnum for AvatarMessageDto_AvatarMessageCommandType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<AvatarMessageDto_AvatarMessageCommandType> {
        match value {
            0 => ::std::option::Option::Some(AvatarMessageDto_AvatarMessageCommandType::Invalid),
            1 => ::std::option::Option::Some(AvatarMessageDto_AvatarMessageCommandType::SetPosition),
            2 => ::std::option::Option::Some(AvatarMessageDto_AvatarMessageCommandType::SetPianoBenchTarget),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [AvatarMessageDto_AvatarMessageCommandType] = &[
            AvatarMessageDto_AvatarMessageCommandType::Invalid,
            AvatarMessageDto_AvatarMessageCommandType::SetPosition,
            AvatarMessageDto_AvatarMessageCommandType::SetPianoBenchTarget,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<AvatarMessageDto_AvatarMessageCommandType>("AvatarMessageDto.AvatarMessageCommandType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for AvatarMessageDto_AvatarMessageCommandType {
}

impl ::std::default::Default for AvatarMessageDto_AvatarMessageCommandType {
    fn default() -> Self {
        AvatarMessageDto_AvatarMessageCommandType::Invalid
    }
}

impl ::protobuf::reflect::ProtobufValue for AvatarMessageDto_AvatarMessageCommandType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ClientMessage {
    // message fields
    pub messageType: ClientMessageType,
    // message oneof groups
    pub messageData: ::std::option::Option<ClientMessage_oneof_messageData>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ClientMessage {
    fn default() -> &'a ClientMessage {
        <ClientMessage as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum ClientMessage_oneof_messageData {
    stringValue(::std::string::String),
    intValue(i32),
    midiMessageOutputDto(MidiMessageOutputDto),
    chatMessageDto(ChatMessageDto),
    roomChatHistory(RoomChatHistory),
    joinedRoomData(JoinedRoomData),
    userDto(super::user_renditions::UserDto),
    userClientDto(super::user_renditions::UserClientDto),
    commandResponse(CommandResponse),
    userDtoList(UserDtoList),
    welcomeDto(WelcomeDto),
    basicRoomDto(super::room_renditions::BasicRoomDto),
    userUpdateCommand(super::user_renditions::UserUpdateCommand),
    socketId(::std::string::String),
    usertag(::std::string::String),
    roomSettings(super::room_renditions::RoomSettings),
    friendDtoList(FriendDtoList),
    friendDto(super::user_renditions::FriendDto),
    pendingFriendRequestList(PendingFriendRequestList),
    pendingFriendRequest(super::user_renditions::PendingFriendRequest),
    userId(::std::string::String),
    socketIDList(SocketIdList),
    kickedUsersList(KickedUsersList),
    roomFullDetails(super::room_renditions::RoomFullDetails),
    boolValue(bool),
    clientSideUserDtoList(ClientSideUserDtoList),
    chatMessageDtoList(ChatMessageDtoList),
    avatarMessageDto(AvatarMessageDto),
}

impl ClientMessage {
    pub fn new() -> ClientMessage {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ClientMessageType messageType = 1;


    pub fn get_messageType(&self) -> ClientMessageType {
        self.messageType
    }
    pub fn clear_messageType(&mut self) {
        self.messageType = ClientMessageType::None;
    }

    // Param is passed by value, moved
    pub fn set_messageType(&mut self, v: ClientMessageType) {
        self.messageType = v;
    }

    // string stringValue = 2;


    pub fn get_stringValue(&self) -> &str {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::stringValue(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_stringValue(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_stringValue(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::stringValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_stringValue(&mut self, v: ::std::string::String) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::stringValue(v))
    }

    // Mutable pointer to the field.
    pub fn mut_stringValue(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::stringValue(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::stringValue(::std::string::String::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::stringValue(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_stringValue(&mut self) -> ::std::string::String {
        if self.has_stringValue() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::stringValue(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // int32 intValue = 3;


    pub fn get_intValue(&self) -> i32 {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::intValue(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_intValue(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_intValue(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::intValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_intValue(&mut self, v: i32) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::intValue(v))
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.MidiMessageOutputDto midiMessageOutputDto = 4;


    pub fn get_midiMessageOutputDto(&self) -> &MidiMessageOutputDto {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::midiMessageOutputDto(ref v)) => v,
            _ => <MidiMessageOutputDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_midiMessageOutputDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_midiMessageOutputDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::midiMessageOutputDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_midiMessageOutputDto(&mut self, v: MidiMessageOutputDto) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::midiMessageOutputDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_midiMessageOutputDto(&mut self) -> &mut MidiMessageOutputDto {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::midiMessageOutputDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::midiMessageOutputDto(MidiMessageOutputDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::midiMessageOutputDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_midiMessageOutputDto(&mut self) -> MidiMessageOutputDto {
        if self.has_midiMessageOutputDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::midiMessageOutputDto(v)) => v,
                _ => panic!(),
            }
        } else {
            MidiMessageOutputDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDto chatMessageDto = 5;


    pub fn get_chatMessageDto(&self) -> &ChatMessageDto {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDto(ref v)) => v,
            _ => <ChatMessageDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_chatMessageDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_chatMessageDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_chatMessageDto(&mut self, v: ChatMessageDto) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_chatMessageDto(&mut self) -> &mut ChatMessageDto {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDto(ChatMessageDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_chatMessageDto(&mut self) -> ChatMessageDto {
        if self.has_chatMessageDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDto(v)) => v,
                _ => panic!(),
            }
        } else {
            ChatMessageDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.RoomChatHistory roomChatHistory = 6;


    pub fn get_roomChatHistory(&self) -> &RoomChatHistory {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::roomChatHistory(ref v)) => v,
            _ => <RoomChatHistory as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomChatHistory(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_roomChatHistory(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::roomChatHistory(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomChatHistory(&mut self, v: RoomChatHistory) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::roomChatHistory(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomChatHistory(&mut self) -> &mut RoomChatHistory {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::roomChatHistory(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::roomChatHistory(RoomChatHistory::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::roomChatHistory(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomChatHistory(&mut self) -> RoomChatHistory {
        if self.has_roomChatHistory() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::roomChatHistory(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomChatHistory::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.JoinedRoomData joinedRoomData = 7;


    pub fn get_joinedRoomData(&self) -> &JoinedRoomData {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::joinedRoomData(ref v)) => v,
            _ => <JoinedRoomData as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_joinedRoomData(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_joinedRoomData(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::joinedRoomData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_joinedRoomData(&mut self, v: JoinedRoomData) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::joinedRoomData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_joinedRoomData(&mut self) -> &mut JoinedRoomData {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::joinedRoomData(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::joinedRoomData(JoinedRoomData::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::joinedRoomData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_joinedRoomData(&mut self) -> JoinedRoomData {
        if self.has_joinedRoomData() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::joinedRoomData(v)) => v,
                _ => panic!(),
            }
        } else {
            JoinedRoomData::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserDto userDto = 8;


    pub fn get_userDto(&self) -> &super::user_renditions::UserDto {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userDto(ref v)) => v,
            _ => <super::user_renditions::UserDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_userDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userDto(&mut self, v: super::user_renditions::UserDto) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userDto(&mut self) -> &mut super::user_renditions::UserDto {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::userDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userDto(super::user_renditions::UserDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userDto(&mut self) -> super::user_renditions::UserDto {
        if self.has_userDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::userDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::UserDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserClientDto userClientDto = 9;


    pub fn get_userClientDto(&self) -> &super::user_renditions::UserClientDto {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userClientDto(ref v)) => v,
            _ => <super::user_renditions::UserClientDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userClientDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_userClientDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userClientDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userClientDto(&mut self, v: super::user_renditions::UserClientDto) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userClientDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userClientDto(&mut self) -> &mut super::user_renditions::UserClientDto {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::userClientDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userClientDto(super::user_renditions::UserClientDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userClientDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userClientDto(&mut self) -> super::user_renditions::UserClientDto {
        if self.has_userClientDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::userClientDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::UserClientDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse commandResponse = 10;


    pub fn get_commandResponse(&self) -> &CommandResponse {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::commandResponse(ref v)) => v,
            _ => <CommandResponse as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_commandResponse(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_commandResponse(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::commandResponse(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_commandResponse(&mut self, v: CommandResponse) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::commandResponse(v))
    }

    // Mutable pointer to the field.
    pub fn mut_commandResponse(&mut self) -> &mut CommandResponse {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::commandResponse(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::commandResponse(CommandResponse::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::commandResponse(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_commandResponse(&mut self) -> CommandResponse {
        if self.has_commandResponse() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::commandResponse(v)) => v,
                _ => panic!(),
            }
        } else {
            CommandResponse::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.UserDtoList userDtoList = 11;


    pub fn get_userDtoList(&self) -> &UserDtoList {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userDtoList(ref v)) => v,
            _ => <UserDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userDtoList(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_userDtoList(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userDtoList(&mut self, v: UserDtoList) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userDtoList(&mut self) -> &mut UserDtoList {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::userDtoList(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userDtoList(UserDtoList::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userDtoList(&mut self) -> UserDtoList {
        if self.has_userDtoList() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::userDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            UserDtoList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.WelcomeDto welcomeDto = 12;


    pub fn get_welcomeDto(&self) -> &WelcomeDto {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::welcomeDto(ref v)) => v,
            _ => <WelcomeDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_welcomeDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_welcomeDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::welcomeDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_welcomeDto(&mut self, v: WelcomeDto) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::welcomeDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_welcomeDto(&mut self) -> &mut WelcomeDto {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::welcomeDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::welcomeDto(WelcomeDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::welcomeDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_welcomeDto(&mut self) -> WelcomeDto {
        if self.has_welcomeDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::welcomeDto(v)) => v,
                _ => panic!(),
            }
        } else {
            WelcomeDto::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.BasicRoomDto basicRoomDto = 13;


    pub fn get_basicRoomDto(&self) -> &super::room_renditions::BasicRoomDto {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::basicRoomDto(ref v)) => v,
            _ => <super::room_renditions::BasicRoomDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_basicRoomDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_basicRoomDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::basicRoomDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_basicRoomDto(&mut self, v: super::room_renditions::BasicRoomDto) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::basicRoomDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_basicRoomDto(&mut self) -> &mut super::room_renditions::BasicRoomDto {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::basicRoomDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::basicRoomDto(super::room_renditions::BasicRoomDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::basicRoomDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_basicRoomDto(&mut self) -> super::room_renditions::BasicRoomDto {
        if self.has_basicRoomDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::basicRoomDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::BasicRoomDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserUpdateCommand userUpdateCommand = 14;


    pub fn get_userUpdateCommand(&self) -> &super::user_renditions::UserUpdateCommand {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userUpdateCommand(ref v)) => v,
            _ => <super::user_renditions::UserUpdateCommand as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userUpdateCommand(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_userUpdateCommand(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userUpdateCommand(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userUpdateCommand(&mut self, v: super::user_renditions::UserUpdateCommand) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userUpdateCommand(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userUpdateCommand(&mut self) -> &mut super::user_renditions::UserUpdateCommand {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::userUpdateCommand(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userUpdateCommand(super::user_renditions::UserUpdateCommand::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userUpdateCommand(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userUpdateCommand(&mut self) -> super::user_renditions::UserUpdateCommand {
        if self.has_userUpdateCommand() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::userUpdateCommand(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::UserUpdateCommand::new()
        }
    }

    // string socketId = 15;


    pub fn get_socketId(&self) -> &str {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::socketId(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_socketId(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_socketId(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::socketId(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketId(&mut self, v: ::std::string::String) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::socketId(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketId(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::socketId(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::socketId(::std::string::String::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::socketId(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketId(&mut self) -> ::std::string::String {
        if self.has_socketId() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::socketId(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string usertag = 16;


    pub fn get_usertag(&self) -> &str {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::usertag(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_usertag(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_usertag(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::usertag(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::usertag(v))
    }

    // Mutable pointer to the field.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::usertag(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::usertag(::std::string::String::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::usertag(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        if self.has_usertag() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::usertag(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomSettings roomSettings = 17;


    pub fn get_roomSettings(&self) -> &super::room_renditions::RoomSettings {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::roomSettings(ref v)) => v,
            _ => <super::room_renditions::RoomSettings as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomSettings(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_roomSettings(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::roomSettings(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomSettings(&mut self, v: super::room_renditions::RoomSettings) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::roomSettings(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomSettings(&mut self) -> &mut super::room_renditions::RoomSettings {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::roomSettings(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::roomSettings(super::room_renditions::RoomSettings::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::roomSettings(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomSettings(&mut self) -> super::room_renditions::RoomSettings {
        if self.has_roomSettings() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::roomSettings(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::RoomSettings::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.FriendDtoList friendDtoList = 18;


    pub fn get_friendDtoList(&self) -> &FriendDtoList {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDtoList(ref v)) => v,
            _ => <FriendDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_friendDtoList(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_friendDtoList(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_friendDtoList(&mut self, v: FriendDtoList) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_friendDtoList(&mut self) -> &mut FriendDtoList {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDtoList(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDtoList(FriendDtoList::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_friendDtoList(&mut self) -> FriendDtoList {
        if self.has_friendDtoList() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            FriendDtoList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.FriendDto friendDto = 19;


    pub fn get_friendDto(&self) -> &super::user_renditions::FriendDto {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDto(ref v)) => v,
            _ => <super::user_renditions::FriendDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_friendDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_friendDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_friendDto(&mut self, v: super::user_renditions::FriendDto) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_friendDto(&mut self) -> &mut super::user_renditions::FriendDto {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDto(super::user_renditions::FriendDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_friendDto(&mut self) -> super::user_renditions::FriendDto {
        if self.has_friendDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::FriendDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.PendingFriendRequestList pendingFriendRequestList = 20;


    pub fn get_pendingFriendRequestList(&self) -> &PendingFriendRequestList {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequestList(ref v)) => v,
            _ => <PendingFriendRequestList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_pendingFriendRequestList(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_pendingFriendRequestList(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequestList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pendingFriendRequestList(&mut self, v: PendingFriendRequestList) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequestList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_pendingFriendRequestList(&mut self) -> &mut PendingFriendRequestList {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequestList(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequestList(PendingFriendRequestList::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequestList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_pendingFriendRequestList(&mut self) -> PendingFriendRequestList {
        if self.has_pendingFriendRequestList() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequestList(v)) => v,
                _ => panic!(),
            }
        } else {
            PendingFriendRequestList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.PendingFriendRequest pendingFriendRequest = 21;


    pub fn get_pendingFriendRequest(&self) -> &super::user_renditions::PendingFriendRequest {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequest(ref v)) => v,
            _ => <super::user_renditions::PendingFriendRequest as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_pendingFriendRequest(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_pendingFriendRequest(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequest(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pendingFriendRequest(&mut self, v: super::user_renditions::PendingFriendRequest) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequest(v))
    }

    // Mutable pointer to the field.
    pub fn mut_pendingFriendRequest(&mut self) -> &mut super::user_renditions::PendingFriendRequest {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequest(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequest(super::user_renditions::PendingFriendRequest::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequest(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_pendingFriendRequest(&mut self) -> super::user_renditions::PendingFriendRequest {
        if self.has_pendingFriendRequest() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequest(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::PendingFriendRequest::new()
        }
    }

    // string userId = 22;


    pub fn get_userId(&self) -> &str {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userId(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_userId(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_userId(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userId(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userId(&mut self, v: ::std::string::String) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userId(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userId(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::userId(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userId(::std::string::String::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::userId(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userId(&mut self) -> ::std::string::String {
        if self.has_userId() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::userId(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.SocketIdList socketIDList = 23;


    pub fn get_socketIDList(&self) -> &SocketIdList {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::socketIDList(ref v)) => v,
            _ => <SocketIdList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_socketIDList(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_socketIDList(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::socketIDList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketIDList(&mut self, v: SocketIdList) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::socketIDList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketIDList(&mut self) -> &mut SocketIdList {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::socketIDList(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::socketIDList(SocketIdList::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::socketIDList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketIDList(&mut self) -> SocketIdList {
        if self.has_socketIDList() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::socketIDList(v)) => v,
                _ => panic!(),
            }
        } else {
            SocketIdList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.KickedUsersList kickedUsersList = 24;


    pub fn get_kickedUsersList(&self) -> &KickedUsersList {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::kickedUsersList(ref v)) => v,
            _ => <KickedUsersList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_kickedUsersList(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_kickedUsersList(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::kickedUsersList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_kickedUsersList(&mut self, v: KickedUsersList) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::kickedUsersList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_kickedUsersList(&mut self) -> &mut KickedUsersList {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::kickedUsersList(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::kickedUsersList(KickedUsersList::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::kickedUsersList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_kickedUsersList(&mut self) -> KickedUsersList {
        if self.has_kickedUsersList() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::kickedUsersList(v)) => v,
                _ => panic!(),
            }
        } else {
            KickedUsersList::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomFullDetails roomFullDetails = 25;


    pub fn get_roomFullDetails(&self) -> &super::room_renditions::RoomFullDetails {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::roomFullDetails(ref v)) => v,
            _ => <super::room_renditions::RoomFullDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomFullDetails(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_roomFullDetails(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::roomFullDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomFullDetails(&mut self, v: super::room_renditions::RoomFullDetails) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::roomFullDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomFullDetails(&mut self) -> &mut super::room_renditions::RoomFullDetails {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::roomFullDetails(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::roomFullDetails(super::room_renditions::RoomFullDetails::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::roomFullDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomFullDetails(&mut self) -> super::room_renditions::RoomFullDetails {
        if self.has_roomFullDetails() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::roomFullDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::RoomFullDetails::new()
        }
    }

    // bool boolValue = 26;


    pub fn get_boolValue(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::boolValue(v)) => v,
            _ => false,
        }
    }
    pub fn clear_boolValue(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_boolValue(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::boolValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_boolValue(&mut self, v: bool) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::boolValue(v))
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ClientSideUserDtoList clientSideUserDtoList = 27;


    pub fn get_clientSideUserDtoList(&self) -> &ClientSideUserDtoList {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::clientSideUserDtoList(ref v)) => v,
            _ => <ClientSideUserDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_clientSideUserDtoList(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_clientSideUserDtoList(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::clientSideUserDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clientSideUserDtoList(&mut self, v: ClientSideUserDtoList) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::clientSideUserDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clientSideUserDtoList(&mut self) -> &mut ClientSideUserDtoList {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::clientSideUserDtoList(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::clientSideUserDtoList(ClientSideUserDtoList::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::clientSideUserDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clientSideUserDtoList(&mut self) -> ClientSideUserDtoList {
        if self.has_clientSideUserDtoList() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::clientSideUserDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            ClientSideUserDtoList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDtoList chatMessageDtoList = 28;


    pub fn get_chatMessageDtoList(&self) -> &ChatMessageDtoList {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDtoList(ref v)) => v,
            _ => <ChatMessageDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_chatMessageDtoList(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_chatMessageDtoList(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_chatMessageDtoList(&mut self, v: ChatMessageDtoList) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_chatMessageDtoList(&mut self) -> &mut ChatMessageDtoList {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDtoList(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDtoList(ChatMessageDtoList::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_chatMessageDtoList(&mut self) -> ChatMessageDtoList {
        if self.has_chatMessageDtoList() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            ChatMessageDtoList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.AvatarMessageDto avatarMessageDto = 29;


    pub fn get_avatarMessageDto(&self) -> &AvatarMessageDto {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::avatarMessageDto(ref v)) => v,
            _ => <AvatarMessageDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_avatarMessageDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_avatarMessageDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::avatarMessageDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_avatarMessageDto(&mut self, v: AvatarMessageDto) {
        self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::avatarMessageDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_avatarMessageDto(&mut self) -> &mut AvatarMessageDto {
        if let ::std::option::Option::Some(ClientMessage_oneof_messageData::avatarMessageDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::avatarMessageDto(AvatarMessageDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ClientMessage_oneof_messageData::avatarMessageDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_avatarMessageDto(&mut self) -> AvatarMessageDto {
        if self.has_avatarMessageDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(ClientMessage_oneof_messageData::avatarMessageDto(v)) => v,
                _ => panic!(),
            }
        } else {
            AvatarMessageDto::new()
        }
    }
}

impl ::protobuf::Message for ClientMessage {
    fn is_initialized(&self) -> bool {
        if let Some(ClientMessage_oneof_messageData::midiMessageOutputDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::chatMessageDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::roomChatHistory(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::joinedRoomData(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::userDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::userClientDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::commandResponse(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::userDtoList(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::welcomeDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::basicRoomDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::userUpdateCommand(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::roomSettings(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::friendDtoList(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::friendDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::pendingFriendRequestList(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::pendingFriendRequest(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::socketIDList(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::kickedUsersList(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::roomFullDetails(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::clientSideUserDtoList(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::chatMessageDtoList(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ClientMessage_oneof_messageData::avatarMessageDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.messageType, 1, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::stringValue(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::intValue(is.read_int32()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::midiMessageOutputDto(is.read_message()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDto(is.read_message()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::roomChatHistory(is.read_message()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::joinedRoomData(is.read_message()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userDto(is.read_message()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userClientDto(is.read_message()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::commandResponse(is.read_message()?));
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userDtoList(is.read_message()?));
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::welcomeDto(is.read_message()?));
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::basicRoomDto(is.read_message()?));
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userUpdateCommand(is.read_message()?));
                },
                15 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::socketId(is.read_string()?));
                },
                16 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::usertag(is.read_string()?));
                },
                17 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::roomSettings(is.read_message()?));
                },
                18 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDtoList(is.read_message()?));
                },
                19 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::friendDto(is.read_message()?));
                },
                20 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequestList(is.read_message()?));
                },
                21 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::pendingFriendRequest(is.read_message()?));
                },
                22 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::userId(is.read_string()?));
                },
                23 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::socketIDList(is.read_message()?));
                },
                24 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::kickedUsersList(is.read_message()?));
                },
                25 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::roomFullDetails(is.read_message()?));
                },
                26 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::boolValue(is.read_bool()?));
                },
                27 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::clientSideUserDtoList(is.read_message()?));
                },
                28 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::chatMessageDtoList(is.read_message()?));
                },
                29 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ClientMessage_oneof_messageData::avatarMessageDto(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.messageType != ClientMessageType::None {
            my_size += ::protobuf::rt::enum_size(1, self.messageType);
        }
        if let ::std::option::Option::Some(ref v) = self.messageData {
            match v {
                &ClientMessage_oneof_messageData::stringValue(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
                &ClientMessage_oneof_messageData::intValue(v) => {
                    my_size += ::protobuf::rt::value_size(3, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &ClientMessage_oneof_messageData::midiMessageOutputDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::chatMessageDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::roomChatHistory(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::joinedRoomData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::userDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::userClientDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::commandResponse(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::userDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::welcomeDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::basicRoomDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::userUpdateCommand(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::socketId(ref v) => {
                    my_size += ::protobuf::rt::string_size(15, &v);
                },
                &ClientMessage_oneof_messageData::usertag(ref v) => {
                    my_size += ::protobuf::rt::string_size(16, &v);
                },
                &ClientMessage_oneof_messageData::roomSettings(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::friendDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::friendDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::pendingFriendRequestList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::pendingFriendRequest(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::userId(ref v) => {
                    my_size += ::protobuf::rt::string_size(22, &v);
                },
                &ClientMessage_oneof_messageData::socketIDList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::kickedUsersList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::roomFullDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::boolValue(v) => {
                    my_size += 3;
                },
                &ClientMessage_oneof_messageData::clientSideUserDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::chatMessageDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ClientMessage_oneof_messageData::avatarMessageDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.messageType != ClientMessageType::None {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.messageType))?;
        }
        if let ::std::option::Option::Some(ref v) = self.messageData {
            match v {
                &ClientMessage_oneof_messageData::stringValue(ref v) => {
                    os.write_string(2, v)?;
                },
                &ClientMessage_oneof_messageData::intValue(v) => {
                    os.write_int32(3, v)?;
                },
                &ClientMessage_oneof_messageData::midiMessageOutputDto(ref v) => {
                    os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::chatMessageDto(ref v) => {
                    os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::roomChatHistory(ref v) => {
                    os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::joinedRoomData(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::userDto(ref v) => {
                    os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::userClientDto(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::commandResponse(ref v) => {
                    os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::userDtoList(ref v) => {
                    os.write_tag(11, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::welcomeDto(ref v) => {
                    os.write_tag(12, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::basicRoomDto(ref v) => {
                    os.write_tag(13, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::userUpdateCommand(ref v) => {
                    os.write_tag(14, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::socketId(ref v) => {
                    os.write_string(15, v)?;
                },
                &ClientMessage_oneof_messageData::usertag(ref v) => {
                    os.write_string(16, v)?;
                },
                &ClientMessage_oneof_messageData::roomSettings(ref v) => {
                    os.write_tag(17, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::friendDtoList(ref v) => {
                    os.write_tag(18, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::friendDto(ref v) => {
                    os.write_tag(19, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::pendingFriendRequestList(ref v) => {
                    os.write_tag(20, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::pendingFriendRequest(ref v) => {
                    os.write_tag(21, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::userId(ref v) => {
                    os.write_string(22, v)?;
                },
                &ClientMessage_oneof_messageData::socketIDList(ref v) => {
                    os.write_tag(23, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::kickedUsersList(ref v) => {
                    os.write_tag(24, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::roomFullDetails(ref v) => {
                    os.write_tag(25, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::boolValue(v) => {
                    os.write_bool(26, v)?;
                },
                &ClientMessage_oneof_messageData::clientSideUserDtoList(ref v) => {
                    os.write_tag(27, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::chatMessageDtoList(ref v) => {
                    os.write_tag(28, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ClientMessage_oneof_messageData::avatarMessageDto(ref v) => {
                    os.write_tag(29, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ClientMessage {
        ClientMessage::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<ClientMessageType>>(
                "messageType",
                |m: &ClientMessage| { &m.messageType },
                |m: &mut ClientMessage| { &mut m.messageType },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "stringValue",
                ClientMessage::has_stringValue,
                ClientMessage::get_stringValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "intValue",
                ClientMessage::has_intValue,
                ClientMessage::get_intValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, MidiMessageOutputDto>(
                "midiMessageOutputDto",
                ClientMessage::has_midiMessageOutputDto,
                ClientMessage::get_midiMessageOutputDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ChatMessageDto>(
                "chatMessageDto",
                ClientMessage::has_chatMessageDto,
                ClientMessage::get_chatMessageDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomChatHistory>(
                "roomChatHistory",
                ClientMessage::has_roomChatHistory,
                ClientMessage::get_roomChatHistory,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, JoinedRoomData>(
                "joinedRoomData",
                ClientMessage::has_joinedRoomData,
                ClientMessage::get_joinedRoomData,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::UserDto>(
                "userDto",
                ClientMessage::has_userDto,
                ClientMessage::get_userDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::UserClientDto>(
                "userClientDto",
                ClientMessage::has_userClientDto,
                ClientMessage::get_userClientDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, CommandResponse>(
                "commandResponse",
                ClientMessage::has_commandResponse,
                ClientMessage::get_commandResponse,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, UserDtoList>(
                "userDtoList",
                ClientMessage::has_userDtoList,
                ClientMessage::get_userDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, WelcomeDto>(
                "welcomeDto",
                ClientMessage::has_welcomeDto,
                ClientMessage::get_welcomeDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::BasicRoomDto>(
                "basicRoomDto",
                ClientMessage::has_basicRoomDto,
                ClientMessage::get_basicRoomDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::UserUpdateCommand>(
                "userUpdateCommand",
                ClientMessage::has_userUpdateCommand,
                ClientMessage::get_userUpdateCommand,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "socketId",
                ClientMessage::has_socketId,
                ClientMessage::get_socketId,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "usertag",
                ClientMessage::has_usertag,
                ClientMessage::get_usertag,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::RoomSettings>(
                "roomSettings",
                ClientMessage::has_roomSettings,
                ClientMessage::get_roomSettings,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, FriendDtoList>(
                "friendDtoList",
                ClientMessage::has_friendDtoList,
                ClientMessage::get_friendDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::FriendDto>(
                "friendDto",
                ClientMessage::has_friendDto,
                ClientMessage::get_friendDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, PendingFriendRequestList>(
                "pendingFriendRequestList",
                ClientMessage::has_pendingFriendRequestList,
                ClientMessage::get_pendingFriendRequestList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::PendingFriendRequest>(
                "pendingFriendRequest",
                ClientMessage::has_pendingFriendRequest,
                ClientMessage::get_pendingFriendRequest,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "userId",
                ClientMessage::has_userId,
                ClientMessage::get_userId,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, SocketIdList>(
                "socketIDList",
                ClientMessage::has_socketIDList,
                ClientMessage::get_socketIDList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, KickedUsersList>(
                "kickedUsersList",
                ClientMessage::has_kickedUsersList,
                ClientMessage::get_kickedUsersList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::RoomFullDetails>(
                "roomFullDetails",
                ClientMessage::has_roomFullDetails,
                ClientMessage::get_roomFullDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "boolValue",
                ClientMessage::has_boolValue,
                ClientMessage::get_boolValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ClientSideUserDtoList>(
                "clientSideUserDtoList",
                ClientMessage::has_clientSideUserDtoList,
                ClientMessage::get_clientSideUserDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ChatMessageDtoList>(
                "chatMessageDtoList",
                ClientMessage::has_chatMessageDtoList,
                ClientMessage::get_chatMessageDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AvatarMessageDto>(
                "avatarMessageDto",
                ClientMessage::has_avatarMessageDto,
                ClientMessage::get_avatarMessageDto,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ClientMessage>(
                "ClientMessage",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ClientMessage {
        static instance: ::protobuf::rt::LazyV2<ClientMessage> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ClientMessage::new)
    }
}

impl ::protobuf::Clear for ClientMessage {
    fn clear(&mut self) {
        self.messageType = ClientMessageType::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ClientMessage {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ClientMessage {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum ClientValidationErrorMsg {
    CreateRoomValidationError = 0,
    UpdateRoomValidationError = 1,
    RegistrationValidationError = 2,
    LoginValidationError = 3,
    ForgotPasswordValidationError = 4,
    ResetPasswordValidationError = 5,
    ResendEmailVerificationValidationError = 6,
}

impl ::protobuf::ProtobufEnum for ClientValidationErrorMsg {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<ClientValidationErrorMsg> {
        match value {
            0 => ::std::option::Option::Some(ClientValidationErrorMsg::CreateRoomValidationError),
            1 => ::std::option::Option::Some(ClientValidationErrorMsg::UpdateRoomValidationError),
            2 => ::std::option::Option::Some(ClientValidationErrorMsg::RegistrationValidationError),
            3 => ::std::option::Option::Some(ClientValidationErrorMsg::LoginValidationError),
            4 => ::std::option::Option::Some(ClientValidationErrorMsg::ForgotPasswordValidationError),
            5 => ::std::option::Option::Some(ClientValidationErrorMsg::ResetPasswordValidationError),
            6 => ::std::option::Option::Some(ClientValidationErrorMsg::ResendEmailVerificationValidationError),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [ClientValidationErrorMsg] = &[
            ClientValidationErrorMsg::CreateRoomValidationError,
            ClientValidationErrorMsg::UpdateRoomValidationError,
            ClientValidationErrorMsg::RegistrationValidationError,
            ClientValidationErrorMsg::LoginValidationError,
            ClientValidationErrorMsg::ForgotPasswordValidationError,
            ClientValidationErrorMsg::ResetPasswordValidationError,
            ClientValidationErrorMsg::ResendEmailVerificationValidationError,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<ClientValidationErrorMsg>("ClientValidationErrorMsg", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for ClientValidationErrorMsg {
}

impl ::std::default::Default for ClientValidationErrorMsg {
    fn default() -> Self {
        ClientValidationErrorMsg::CreateRoomValidationError
    }
}

impl ::protobuf::reflect::ProtobufValue for ClientValidationErrorMsg {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum ClientMessageType {
    None = 0,
    Welcome = 1,
    CmdResponse = 2,
    ApiResponse = 3,
    ClearChat = 4,
    ClearChatBySocketID = 5,
    ClearChatByUsername = 6,
    ClearChatByMessageID = 7,
    ClearChatByAmount = 8,
    RoomChatMessage = 9,
    MidiMessage = 10,
    ServerEventInfo = 11,
    LoadServerCommands = 12,
    LoadRoomOwnerCommands = 13,
    UnloadRoomOwnerCommands = 14,
    RoomSettingsUpdated = 15,
    SessionExpired = 16,
    RejoinRecoveredRoom = 17,
    GetFriendsList = 18,
    GetPendingFriendRequestsList = 19,
    GetUserNotificationSettings = 20,
    RoomStoreServiceUp = 21,
    EditMessageByID = 22,
    Announcements = 23,
    LoadRoomChatHistory = 24,
    LoadRoomWelcomeMessage = 25,
    JoinedRoomSuccess = 26,
    JoinedRoomFail = 27,
    UsersInRoomList = 28,
    UserJoined = 29,
    UserUpdated = 30,
    UserLeft = 31,
    UsersTyping = 32,
    BasicRoomApiCreated = 33,
    BasicRoomApiUpdated = 34,
    BasicRoomApiDeleted = 35,
    ClientUpdated = 36,
    Unauthenticated = 37,
    RoomOwnerUpdated = 38,
    FriendStatusUpdated = 39,
    AddPendingFriendRequest = 40,
    RemovePendingFriendRequest = 41,
    GetKickedUsersList = 42,
    GetRoomFullDetails = 43,
    MaintenanceModeActive = 44,
}

impl ::protobuf::ProtobufEnum for ClientMessageType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<ClientMessageType> {
        match value {
            0 => ::std::option::Option::Some(ClientMessageType::None),
            1 => ::std::option::Option::Some(ClientMessageType::Welcome),
            2 => ::std::option::Option::Some(ClientMessageType::CmdResponse),
            3 => ::std::option::Option::Some(ClientMessageType::ApiResponse),
            4 => ::std::option::Option::Some(ClientMessageType::ClearChat),
            5 => ::std::option::Option::Some(ClientMessageType::ClearChatBySocketID),
            6 => ::std::option::Option::Some(ClientMessageType::ClearChatByUsername),
            7 => ::std::option::Option::Some(ClientMessageType::ClearChatByMessageID),
            8 => ::std::option::Option::Some(ClientMessageType::ClearChatByAmount),
            9 => ::std::option::Option::Some(ClientMessageType::RoomChatMessage),
            10 => ::std::option::Option::Some(ClientMessageType::MidiMessage),
            11 => ::std::option::Option::Some(ClientMessageType::ServerEventInfo),
            12 => ::std::option::Option::Some(ClientMessageType::LoadServerCommands),
            13 => ::std::option::Option::Some(ClientMessageType::LoadRoomOwnerCommands),
            14 => ::std::option::Option::Some(ClientMessageType::UnloadRoomOwnerCommands),
            15 => ::std::option::Option::Some(ClientMessageType::RoomSettingsUpdated),
            16 => ::std::option::Option::Some(ClientMessageType::SessionExpired),
            17 => ::std::option::Option::Some(ClientMessageType::RejoinRecoveredRoom),
            18 => ::std::option::Option::Some(ClientMessageType::GetFriendsList),
            19 => ::std::option::Option::Some(ClientMessageType::GetPendingFriendRequestsList),
            20 => ::std::option::Option::Some(ClientMessageType::GetUserNotificationSettings),
            21 => ::std::option::Option::Some(ClientMessageType::RoomStoreServiceUp),
            22 => ::std::option::Option::Some(ClientMessageType::EditMessageByID),
            23 => ::std::option::Option::Some(ClientMessageType::Announcements),
            24 => ::std::option::Option::Some(ClientMessageType::LoadRoomChatHistory),
            25 => ::std::option::Option::Some(ClientMessageType::LoadRoomWelcomeMessage),
            26 => ::std::option::Option::Some(ClientMessageType::JoinedRoomSuccess),
            27 => ::std::option::Option::Some(ClientMessageType::JoinedRoomFail),
            28 => ::std::option::Option::Some(ClientMessageType::UsersInRoomList),
            29 => ::std::option::Option::Some(ClientMessageType::UserJoined),
            30 => ::std::option::Option::Some(ClientMessageType::UserUpdated),
            31 => ::std::option::Option::Some(ClientMessageType::UserLeft),
            32 => ::std::option::Option::Some(ClientMessageType::UsersTyping),
            33 => ::std::option::Option::Some(ClientMessageType::BasicRoomApiCreated),
            34 => ::std::option::Option::Some(ClientMessageType::BasicRoomApiUpdated),
            35 => ::std::option::Option::Some(ClientMessageType::BasicRoomApiDeleted),
            36 => ::std::option::Option::Some(ClientMessageType::ClientUpdated),
            37 => ::std::option::Option::Some(ClientMessageType::Unauthenticated),
            38 => ::std::option::Option::Some(ClientMessageType::RoomOwnerUpdated),
            39 => ::std::option::Option::Some(ClientMessageType::FriendStatusUpdated),
            40 => ::std::option::Option::Some(ClientMessageType::AddPendingFriendRequest),
            41 => ::std::option::Option::Some(ClientMessageType::RemovePendingFriendRequest),
            42 => ::std::option::Option::Some(ClientMessageType::GetKickedUsersList),
            43 => ::std::option::Option::Some(ClientMessageType::GetRoomFullDetails),
            44 => ::std::option::Option::Some(ClientMessageType::MaintenanceModeActive),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [ClientMessageType] = &[
            ClientMessageType::None,
            ClientMessageType::Welcome,
            ClientMessageType::CmdResponse,
            ClientMessageType::ApiResponse,
            ClientMessageType::ClearChat,
            ClientMessageType::ClearChatBySocketID,
            ClientMessageType::ClearChatByUsername,
            ClientMessageType::ClearChatByMessageID,
            ClientMessageType::ClearChatByAmount,
            ClientMessageType::RoomChatMessage,
            ClientMessageType::MidiMessage,
            ClientMessageType::ServerEventInfo,
            ClientMessageType::LoadServerCommands,
            ClientMessageType::LoadRoomOwnerCommands,
            ClientMessageType::UnloadRoomOwnerCommands,
            ClientMessageType::RoomSettingsUpdated,
            ClientMessageType::SessionExpired,
            ClientMessageType::RejoinRecoveredRoom,
            ClientMessageType::GetFriendsList,
            ClientMessageType::GetPendingFriendRequestsList,
            ClientMessageType::GetUserNotificationSettings,
            ClientMessageType::RoomStoreServiceUp,
            ClientMessageType::EditMessageByID,
            ClientMessageType::Announcements,
            ClientMessageType::LoadRoomChatHistory,
            ClientMessageType::LoadRoomWelcomeMessage,
            ClientMessageType::JoinedRoomSuccess,
            ClientMessageType::JoinedRoomFail,
            ClientMessageType::UsersInRoomList,
            ClientMessageType::UserJoined,
            ClientMessageType::UserUpdated,
            ClientMessageType::UserLeft,
            ClientMessageType::UsersTyping,
            ClientMessageType::BasicRoomApiCreated,
            ClientMessageType::BasicRoomApiUpdated,
            ClientMessageType::BasicRoomApiDeleted,
            ClientMessageType::ClientUpdated,
            ClientMessageType::Unauthenticated,
            ClientMessageType::RoomOwnerUpdated,
            ClientMessageType::FriendStatusUpdated,
            ClientMessageType::AddPendingFriendRequest,
            ClientMessageType::RemovePendingFriendRequest,
            ClientMessageType::GetKickedUsersList,
            ClientMessageType::GetRoomFullDetails,
            ClientMessageType::MaintenanceModeActive,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<ClientMessageType>("ClientMessageType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for ClientMessageType {
}

impl ::std::default::Default for ClientMessageType {
    fn default() -> Self {
        ClientMessageType::None
    }
}

impl ::protobuf::reflect::ProtobufValue for ClientMessageType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum CommandResponseType {
    Error = 0,
    ValidationError = 1,
    JoinRoomFail = 3,
    LeftRoom = 4,
    RoomsList = 5,
    KickedUsersInRoomList = 6,
    RoomSettings = 7,
    RegistrationSuccess = 8,
    Toast = 9,
    EnterRoomPassword = 10,
}

impl ::protobuf::ProtobufEnum for CommandResponseType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<CommandResponseType> {
        match value {
            0 => ::std::option::Option::Some(CommandResponseType::Error),
            1 => ::std::option::Option::Some(CommandResponseType::ValidationError),
            3 => ::std::option::Option::Some(CommandResponseType::JoinRoomFail),
            4 => ::std::option::Option::Some(CommandResponseType::LeftRoom),
            5 => ::std::option::Option::Some(CommandResponseType::RoomsList),
            6 => ::std::option::Option::Some(CommandResponseType::KickedUsersInRoomList),
            7 => ::std::option::Option::Some(CommandResponseType::RoomSettings),
            8 => ::std::option::Option::Some(CommandResponseType::RegistrationSuccess),
            9 => ::std::option::Option::Some(CommandResponseType::Toast),
            10 => ::std::option::Option::Some(CommandResponseType::EnterRoomPassword),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [CommandResponseType] = &[
            CommandResponseType::Error,
            CommandResponseType::ValidationError,
            CommandResponseType::JoinRoomFail,
            CommandResponseType::LeftRoom,
            CommandResponseType::RoomsList,
            CommandResponseType::KickedUsersInRoomList,
            CommandResponseType::RoomSettings,
            CommandResponseType::RegistrationSuccess,
            CommandResponseType::Toast,
            CommandResponseType::EnterRoomPassword,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<CommandResponseType>("CommandResponseType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for CommandResponseType {
}

impl ::std::default::Default for CommandResponseType {
    fn default() -> Self {
        CommandResponseType::Error
    }
}

impl ::protobuf::reflect::ProtobufValue for CommandResponseType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum ClientValidationErrorMsgType {
    CreateRoom = 0,
    UpdateRoom = 1,
    Registration = 2,
    Login = 3,
    ForgotPassword = 4,
    ResetPassword = 5,
    ResendEmail = 6,
}

impl ::protobuf::ProtobufEnum for ClientValidationErrorMsgType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<ClientValidationErrorMsgType> {
        match value {
            0 => ::std::option::Option::Some(ClientValidationErrorMsgType::CreateRoom),
            1 => ::std::option::Option::Some(ClientValidationErrorMsgType::UpdateRoom),
            2 => ::std::option::Option::Some(ClientValidationErrorMsgType::Registration),
            3 => ::std::option::Option::Some(ClientValidationErrorMsgType::Login),
            4 => ::std::option::Option::Some(ClientValidationErrorMsgType::ForgotPassword),
            5 => ::std::option::Option::Some(ClientValidationErrorMsgType::ResetPassword),
            6 => ::std::option::Option::Some(ClientValidationErrorMsgType::ResendEmail),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [ClientValidationErrorMsgType] = &[
            ClientValidationErrorMsgType::CreateRoom,
            ClientValidationErrorMsgType::UpdateRoom,
            ClientValidationErrorMsgType::Registration,
            ClientValidationErrorMsgType::Login,
            ClientValidationErrorMsgType::ForgotPassword,
            ClientValidationErrorMsgType::ResetPassword,
            ClientValidationErrorMsgType::ResendEmail,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<ClientValidationErrorMsgType>("ClientValidationErrorMsgType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for ClientValidationErrorMsgType {
}

impl ::std::default::Default for ClientValidationErrorMsgType {
    fn default() -> Self {
        ClientValidationErrorMsgType::CreateRoom
    }
}

impl ::protobuf::reflect::ProtobufValue for ClientValidationErrorMsgType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum JoinRoomFailType {
    QueuedToJoinRoom = 0,
    AlreadyInRoom = 1,
    RoomFull = 2,
    NoGuests = 3,
    ProMembersOnly = 4,
    NotAllowed = 5,
    MaintenanceMode = 6,
    IncorrectPassword = 7,
    ServerError = 8,
    RoomNotFound = 9,
    EnterPassword = 10,
}

impl ::protobuf::ProtobufEnum for JoinRoomFailType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<JoinRoomFailType> {
        match value {
            0 => ::std::option::Option::Some(JoinRoomFailType::QueuedToJoinRoom),
            1 => ::std::option::Option::Some(JoinRoomFailType::AlreadyInRoom),
            2 => ::std::option::Option::Some(JoinRoomFailType::RoomFull),
            3 => ::std::option::Option::Some(JoinRoomFailType::NoGuests),
            4 => ::std::option::Option::Some(JoinRoomFailType::ProMembersOnly),
            5 => ::std::option::Option::Some(JoinRoomFailType::NotAllowed),
            6 => ::std::option::Option::Some(JoinRoomFailType::MaintenanceMode),
            7 => ::std::option::Option::Some(JoinRoomFailType::IncorrectPassword),
            8 => ::std::option::Option::Some(JoinRoomFailType::ServerError),
            9 => ::std::option::Option::Some(JoinRoomFailType::RoomNotFound),
            10 => ::std::option::Option::Some(JoinRoomFailType::EnterPassword),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [JoinRoomFailType] = &[
            JoinRoomFailType::QueuedToJoinRoom,
            JoinRoomFailType::AlreadyInRoom,
            JoinRoomFailType::RoomFull,
            JoinRoomFailType::NoGuests,
            JoinRoomFailType::ProMembersOnly,
            JoinRoomFailType::NotAllowed,
            JoinRoomFailType::MaintenanceMode,
            JoinRoomFailType::IncorrectPassword,
            JoinRoomFailType::ServerError,
            JoinRoomFailType::RoomNotFound,
            JoinRoomFailType::EnterPassword,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<JoinRoomFailType>("JoinRoomFailType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for JoinRoomFailType {
}

impl ::std::default::Default for JoinRoomFailType {
    fn default() -> Self {
        JoinRoomFailType::QueuedToJoinRoom
    }
}

impl ::protobuf::reflect::ProtobufValue for JoinRoomFailType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x14client-message.proto\x12-PianoRhythm.Serialization.ServerToClient.\
    Msgs\x1a\x15room-renditions.proto\x1a\x15user-renditions.proto\x1a\x15mi\
    di-renditions.proto\"M\n\x15ClientValidationError\x12\x1c\n\tFieldName\
    \x18\x01\x20\x01(\tR\tFieldName\x12\x16\n\x06Reason\x18\x02\x20\x01(\tR\
    \x06Reason\"\xfc\x04\n\x07MidiDto\x12R\n\x0bmessageType\x18\x01\x20\x01(\
    \x0e20.PianoRhythm.Serialization.Midi.Msgs.MidiDtoTypeR\x0bmessageType\
    \x12T\n\nnoteSource\x18\xe7\x07\x20\x01(\x0e23.PianoRhythm.Serialization\
    .Midi.Msgs.MidiNoteSourceR\nnoteSource\x12Q\n\x06noteOn\x18\x02\x20\x01(\
    \x0b27.PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiNoteOnH\0R\x06not\
    eOn\x12T\n\x07noteOff\x18\x03\x20\x01(\x0b28.PianoRhythm.Serialization.M\
    idi.Msgs.MidiDto.MidiNoteOffH\0R\x07noteOff\x12X\n\x07sustain\x18\x04\
    \x20\x01(\x0b2<.PianoRhythm.Serialization.Midi.Msgs.MidiDto.MidiNoteSust\
    ainH\0R\x07sustain\x12`\n\x0ballSoundOff\x18\x05\x20\x01(\x0b2<.PianoRhy\
    thm.Serialization.Midi.Msgs.MidiDto.MidiAllSoundOffH\0R\x0ballSoundOff\
    \x12Z\n\tpitchBend\x18\x06\x20\x01(\x0b2:.PianoRhythm.Serialization.Midi\
    .Msgs.MidiDto.MidiPitchBendH\0R\tpitchBendB\x06\n\x04data\"\xa8\x02\n\
    \x14MidiMessageOutputDto\x12\x1a\n\x08socketID\x18\x01\x20\x01(\tR\x08so\
    cketID\x12\x12\n\x04time\x18\x02\x20\x01(\tR\x04time\x12i\n\x04data\x18\
    \x03\x20\x03(\x0b2U.PianoRhythm.Serialization.ServerToClient.Msgs.MidiMe\
    ssageOutputDto.MidiMessageBufferR\x04data\x1au\n\x11MidiMessageBuffer\
    \x12J\n\x04data\x18\x01\x20\x01(\x0b26.PianoRhythm.Serialization.ServerT\
    oClient.Msgs.MidiDtoR\x04data\x12\x14\n\x05delay\x18\x02\x20\x01(\x01R\
    \x05delay\"\xa5\x05\n\x0eChatMessageDto\x12\x0e\n\x02id\x18\x01\x20\x01(\
    \x05R\x02id\x12\x1c\n\tmessageID\x18\x02\x20\x01(\tR\tmessageID\x12&\n\
    \x0emessageReplyID\x18\x03\x20\x01(\tR\x0emessageReplyID\x12\x1a\n\x08so\
    cketID\x18\x04\x20\x01(\tR\x08socketID\x12\x18\n\x07message\x18\x05\x20\
    \x01(\tR\x07message\x12\x0e\n\x02ts\x18\x06\x20\x01(\tR\x02ts\x12\x16\n\
    \x06roomID\x18\x07\x20\x01(\tR\x06roomID\x12\x18\n\x07usertag\x18\x08\
    \x20\x01(\tR\x07usertag\x12\x1a\n\x08username\x18\t\x20\x01(\tR\x08usern\
    ame\x12\x1a\n\x08nickname\x18\n\x20\x01(\tR\x08nickname\x12\x1c\n\tuserC\
    olor\x18\x0b\x20\x01(\tR\tuserColor\x12\x14\n\x05isBot\x18\x0c\x20\x01(\
    \x08R\x05isBot\x12\x1a\n\x08isPlugin\x18\r\x20\x01(\x08R\x08isPlugin\x12\
    \x14\n\x05isMod\x18\x0e\x20\x01(\x08R\x05isMod\x12\x1a\n\x08isSystem\x18\
    \x0f\x20\x01(\x08R\x08isSystem\x12\x18\n\x07isAdmin\x18\x10\x20\x01(\x08\
    R\x07isAdmin\x12\x14\n\x05isDev\x18\x11\x20\x01(\x08R\x05isDev\x12\x20\n\
    \x0bisRoomOwner\x18\x12\x20\x01(\x08R\x0bisRoomOwner\x12\x18\n\x07isGues\
    t\x18\x13\x20\x01(\x08R\x07isGuest\x12\x20\n\x0bisProMember\x18\x14\x20\
    \x01(\x08R\x0bisProMember\x12\x1c\n\twhisperer\x18\x15\x20\x01(\tR\twhis\
    perer\x12\x1e\n\nautoDelete\x18\x16\x20\x01(\x08R\nautoDelete\x12\"\n\
    \x0cmodifiedDate\x18\x17\x20\x01(\tR\x0cmodifiedDate\x12\x1b\n\tuser_uui\
    d\x18\x18\x20\x01(\tR\x08userUuid\"t\n\x0fRoomChatHistory\x12a\n\x0clast\
    Messages\x18\x01\x20\x03(\x0b2=.PianoRhythm.Serialization.ServerToClient\
    .Msgs.ChatMessageDtoR\x0clastMessages\"o\n\x12ChatMessageDtoList\x12Y\n\
    \x08messages\x18\x01\x20\x03(\x0b2=.PianoRhythm.Serialization.ServerToCl\
    ient.Msgs.ChatMessageDtoR\x08messages\"\xb9\x03\n\x0eJoinedRoomData\x12\
    \x16\n\x06roomID\x18\x01\x20\x01(\tR\x06roomID\x12\x1a\n\x08roomName\x18\
    \x02\x20\x01(\tR\x08roomName\x12N\n\x08roomType\x18\x03\x20\x01(\x0e22.P\
    ianoRhythm.Serialization.RoomRenditions.RoomTypeR\x08roomType\x12T\n\nro\
    omStatus\x18\x04\x20\x01(\x0e24.PianoRhythm.Serialization.RoomRenditions\
    .RoomStatusR\nroomStatus\x12Z\n\x0croomSettings\x18\x05\x20\x01(\x0b26.P\
    ianoRhythm.Serialization.RoomRenditions.RoomSettingsR\x0croomSettings\
    \x12\x1c\n\troomOwner\x18\x06\x20\x01(\tR\troomOwner\x129\n\x15selfHoste\
    dCountryCode\x18\x07\x20\x01(\tH\0R\x15selfHostedCountryCode\x88\x01\x01\
    B\x18\n\x16_selfHostedCountryCode\"\xcc\n\n\x0fCommandResponse\x12d\n\
    \x0bmessageType\x18\x01\x20\x01(\x0e2B.PianoRhythm.Serialization.ServerT\
    oClient.Msgs.CommandResponseTypeR\x0bmessageType\x12\"\n\x0bstringValue\
    \x18\x02\x20\x01(\tH\0R\x0bstringValue\x12h\n\troomsList\x18\x03\x20\x01\
    (\x0b2H.PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.Ro\
    omsListH\0R\troomsList\x12M\n\x07roomDto\x18\x04\x20\x01(\x0b21.PianoRhy\
    thm.Serialization.RoomRenditions.RoomDtoH\0R\x07roomDto\x12\x8c\x01\n\
    \x13validationErrorList\x18\x05\x20\x01(\x0b2X.PianoRhythm.Serialization\
    .ServerToClient.Msgs.CommandResponse.ClientValidationErrorListH\0R\x13va\
    lidationErrorList\x12\x89\x01\n\x14joinRoomFailResponse\x18\x06\x20\x01(\
    \x0b2S.PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.Joi\
    nRoomFailResponseH\0R\x14joinRoomFailResponse\x12\x98\x01\n\x19enterRoom\
    PasswordResponse\x18\x07\x20\x01(\x0b2X.PianoRhythm.Serialization.Server\
    ToClient.Msgs.CommandResponse.EnterRoomPasswordResponseH\0R\x19enterRoom\
    PasswordResponse\x1aW\n\tRoomsList\x12J\n\x04list\x18\x01\x20\x03(\x0b26\
    .PianoRhythm.Serialization.RoomRenditions.BasicRoomDtoR\x04list\x1a\xe0\
    \x01\n\x19ClientValidationErrorList\x12X\n\x04data\x18\x01\x20\x03(\x0b2\
    D.PianoRhythm.Serialization.ServerToClient.Msgs.ClientValidationErrorR\
    \x04data\x12i\n\terrorType\x18\x02\x20\x01(\x0e2K.PianoRhythm.Serializat\
    ion.ServerToClient.Msgs.ClientValidationErrorMsgTypeR\terrorType\x1a\xa3\
    \x01\n\x14JoinRoomFailResponse\x12\x16\n\x06roomID\x18\x01\x20\x01(\tR\
    \x06roomID\x12W\n\x06reason\x18\x02\x20\x01(\x0e2?.PianoRhythm.Serializa\
    tion.ServerToClient.Msgs.JoinRoomFailTypeR\x06reason\x12\x1a\n\x08roomNa\
    me\x18\x03\x20\x01(\tR\x08roomName\x1aO\n\x19EnterRoomPasswordResponse\
    \x12\x16\n\x06roomID\x18\x01\x20\x01(\tR\x06roomID\x12\x1a\n\x08roomName\
    \x18\x02\x20\x01(\tR\x08roomNameB\r\n\x0bmessageData\"i\n\x0bUserDtoList\
    \x12Z\n\x07userDto\x18\x01\x20\x03(\<EMAIL>\
    erToClient.UserRenditions.UserDtoR\x07userDto\"w\n\x15ClientSideUserDtoL\
    ist\x12^\n\x04list\x18\x01\x20\x03(\x0b2J.PianoRhythm.Serialization.Serv\
    erToClient.UserRenditions.ClientSideUserDtoR\x04list\",\n\x0cSocketIdLis\
    t\x12\x1c\n\tsocketIDs\x18\x01\x20\x03(\tR\tsocketIDs\"q\n\rFriendDtoLis\
    t\x12`\n\tfriendDto\x18\x01\x20\x03(\x0b2B.PianoRhythm.Serialization.Ser\
    verToClient.UserRenditions.FriendDtoR\tfriendDto\"\x9e\x01\n\x18PendingF\
    riendRequestList\x12\x81\x01\n\x14pendingFriendRequest\x18\x01\x20\x03(\
    \x0b2M.PianoRhythm.Serialization.ServerToClient.UserRenditions.PendingFr\
    iendRequestR\x14pendingFriendRequest\"|\n\x0fKickedUsersList\x12i\n\x0bk\
    ickedUsers\x18\x01\x20\x03(\x0b2G.PianoRhythm.Serialization.ServerToClie\
    nt.UserRenditions.KickedUserDataR\x0bkickedUsers\"\xa8\x01\n\nWelcomeDto\
    \x12l\n\ruserClientDto\x18\x01\x20\x01(\x0b2F.PianoRhythm.Serialization.\
    ServerToClient.UserRenditions.UserClientDtoR\ruserClientDto\x12\x1f\n\
    \x08settings\x18\x02\x20\x01(\tH\0R\x08settings\x88\x01\x01B\x0b\n\t_set\
    tings\"\xbb\x03\n\x10AvatarMessageDto\x12z\n\x0bcommandType\x18\x01\x20\
    \x01(\x0e2X.PianoRhythm.Serialization.ServerToClient.Msgs.AvatarMessageD\
    to.AvatarMessageCommandTypeR\x0bcommandType\x12\x1a\n\x08socketID\x18\
    \x02\x20\x01(\tR\x08socketID\x12\x8e\x01\n\rworldPosition\x18\x03\x20\
    \x01(\x0b2f.PianoRhythm.Serialization.ServerToClient.UserRenditions.Avat\
    arWorldDataDto.AvatarMessageWorldPositionH\0R\rworldPosition\x12\x1c\n\
    \x08intValue\x18\x04\x20\x01(\x05H\0R\x08intValue\"Q\n\x18AvatarMessageC\
    ommandType\x12\x0b\n\x07Invalid\x10\0\x12\x0f\n\x0bSetPosition\x10\x01\
    \x12\x17\n\x13SetPianoBenchTarget\x10\x02B\r\n\x0bcommandData\"\x90\x15\
    \n\rClientMessage\x12b\n\x0bmessageType\x18\x01\x20\x01(\x0e2@.PianoRhyt\
    hm.Serialization.ServerToClient.Msgs.ClientMessageTypeR\x0bmessageType\
    \x12\"\n\x0bstringValue\x18\x02\x20\x01(\tH\0R\x0bstringValue\x12\x1c\n\
    \x08intValue\x18\x03\x20\x01(\x05H\0R\x08intValue\x12y\n\x14midiMessageO\
    utputDto\x18\x04\x20\x01(\x0b2C.PianoRhythm.Serialization.ServerToClient\
    .Msgs.MidiMessageOutputDtoH\0R\x14midiMessageOutputDto\x12g\n\x0echatMes\
    sageDto\x18\x05\x20\x01(\x0b2=.PianoRhythm.Serialization.ServerToClient.\
    Msgs.ChatMessageDtoH\0R\x0echatMessageDto\x12j\n\x0froomChatHistory\x18\
    \x06\x20\x01(\x0b2>.PianoRhythm.Serialization.ServerToClient.Msgs.RoomCh\
    atHistoryH\0R\x0froomChatHistory\x12g\n\x0ejoinedRoomData\x18\x07\x20\
    \x01(\x0b2=.PianoRhythm.Serialization.ServerToClient.Msgs.JoinedRoomData\
    H\0R\x0ejoinedRoomData\x12\\\n\x07userDto\x18\x08\x20\x01(\x0b2@.PianoRh\
    ythm.Serialization.ServerToClient.UserRenditions.UserDtoH\0R\x07userDto\
    \x12n\n\ruserClientDto\x18\t\x20\x01(\x0b2F.PianoRhythm.Serialization.Se\
    rverToClient.UserRenditions.UserClientDtoH\0R\ruserClientDto\x12j\n\x0fc\
    ommandResponse\x18\n\x20\x01(\x0b2>.PianoRhythm.Serialization.ServerToCl\
    ient.Msgs.CommandResponseH\0R\x0fcommandResponse\x12^\n\x0buserDtoList\
    \x18\x0b\x20\x01(\x0b2:.PianoRhythm.Serialization.ServerToClient.Msgs.Us\
    erDtoListH\0R\x0buserDtoList\x12[\n\nwelcomeDto\x18\x0c\x20\x01(\x0b29.P\
    ianoRhythm.Serialization.ServerToClient.Msgs.WelcomeDtoH\0R\nwelcomeDto\
    \x12\\\n\x0cbasicRoomDto\x18\r\x20\x01(\x0b26.PianoRhythm.Serialization.\
    RoomRenditions.BasicRoomDtoH\0R\x0cbasicRoomDto\x12z\n\x11userUpdateComm\
    and\x18\x0e\x20\x01(\x0b2J.PianoRhythm.Serialization.ServerToClient.User\
    Renditions.UserUpdateCommandH\0R\x11userUpdateCommand\x12\x1c\n\x08socke\
    tId\x18\x0f\x20\x01(\tH\0R\x08socketId\x12\x1a\n\x07usertag\x18\x10\x20\
    \x01(\tH\0R\x07usertag\x12\\\n\x0croomSettings\x18\x11\x20\x01(\x0b26.Pi\
    anoRhythm.Serialization.RoomRenditions.RoomSettingsH\0R\x0croomSettings\
    \x12d\n\rfriendDtoList\x18\x12\x20\x01(\x0b2<.PianoRhythm.Serialization.\
    ServerToClient.Msgs.FriendDtoListH\0R\rfriendDtoList\x12b\n\tfriendDto\
    \x18\x13\x20\x01(\x0b2B.PianoRhythm.Serialization.ServerToClient.UserRen\
    ditions.FriendDtoH\0R\tfriendDto\x12\x85\x01\n\x18pendingFriendRequestLi\
    st\x18\x14\x20\x01(\x0b2G.PianoRhythm.Serialization.ServerToClient.Msgs.\
    PendingFriendRequestListH\0R\x18pendingFriendRequestList\x12\x83\x01\n\
    \x14pendingFriendRequest\x18\x15\x20\x01(\x0b2M.PianoRhythm.Serializatio\
    n.ServerToClient.UserRenditions.PendingFriendRequestH\0R\x14pendingFrien\
    dRequest\x12\x18\n\x06userId\x18\x16\x20\x01(\tH\0R\x06userId\x12a\n\x0c\
    socketIDList\x18\x17\x20\x01(\x0b2;.PianoRhythm.Serialization.ServerToCl\
    ient.Msgs.SocketIdListH\0R\x0csocketIDList\x12j\n\x0fkickedUsersList\x18\
    \x18\x20\x01(\x0b2>.PianoRhythm.Serialization.ServerToClient.Msgs.Kicked\
    UsersListH\0R\x0fkickedUsersList\x12e\n\x0froomFullDetails\x18\x19\x20\
    \x01(\x0b29.PianoRhythm.Serialization.RoomRenditions.RoomFullDetailsH\0R\
    \x0froomFullDetails\x12\x1e\n\tboolValue\x18\x1a\x20\x01(\x08H\0R\tboolV\
    alue\x12|\n\x15clientSideUserDtoList\x18\x1b\x20\x01(\x0b2D.PianoRhythm.\
    Serialization.ServerToClient.Msgs.ClientSideUserDtoListH\0R\x15clientSid\
    eUserDtoList\x12s\n\x12chatMessageDtoList\x18\x1c\x20\x01(\x0b2A.PianoRh\
    ythm.Serialization.ServerToClient.Msgs.ChatMessageDtoListH\0R\x12chatMes\
    sageDtoList\x12m\n\x10avatarMessageDto\x18\x1d\x20\x01(\x0b2?.PianoRhyth\
    m.Serialization.ServerToClient.Msgs.AvatarMessageDtoH\0R\x10avatarMessag\
    eDtoB\r\n\x0bmessageData*\x84\x02\n\x18ClientValidationErrorMsg\x12\x1d\
    \n\x19CreateRoomValidationError\x10\0\x12\x1d\n\x19UpdateRoomValidationE\
    rror\x10\x01\x12\x1f\n\x1bRegistrationValidationError\x10\x02\x12\x18\n\
    \x14LoginValidationError\x10\x03\x12!\n\x1dForgotPasswordValidationError\
    \x10\x04\x12\x20\n\x1cResetPasswordValidationError\x10\x05\x12*\n&Resend\
    EmailVerificationValidationError\x10\x06*\x85\x08\n\x11ClientMessageType\
    \x12\x08\n\x04None\x10\0\x12\x0b\n\x07Welcome\x10\x01\x12\x0f\n\x0bCmdRe\
    sponse\x10\x02\x12\x0f\n\x0bApiResponse\x10\x03\x12\r\n\tClearChat\x10\
    \x04\x12\x17\n\x13ClearChatBySocketID\x10\x05\x12\x17\n\x13ClearChatByUs\
    ername\x10\x06\x12\x18\n\x14ClearChatByMessageID\x10\x07\x12\x15\n\x11Cl\
    earChatByAmount\x10\x08\x12\x13\n\x0fRoomChatMessage\x10\t\x12\x0f\n\x0b\
    MidiMessage\x10\n\x12\x13\n\x0fServerEventInfo\x10\x0b\x12\x16\n\x12Load\
    ServerCommands\x10\x0c\x12\x19\n\x15LoadRoomOwnerCommands\x10\r\x12\x1b\
    \n\x17UnloadRoomOwnerCommands\x10\x0e\x12\x17\n\x13RoomSettingsUpdated\
    \x10\x0f\x12\x12\n\x0eSessionExpired\x10\x10\x12\x17\n\x13RejoinRecovere\
    dRoom\x10\x11\x12\x12\n\x0eGetFriendsList\x10\x12\x12\x20\n\x1cGetPendin\
    gFriendRequestsList\x10\x13\x12\x1f\n\x1bGetUserNotificationSettings\x10\
    \x14\x12\x16\n\x12RoomStoreServiceUp\x10\x15\x12\x13\n\x0fEditMessageByI\
    D\x10\x16\x12\x11\n\rAnnouncements\x10\x17\x12\x17\n\x13LoadRoomChatHist\
    ory\x10\x18\x12\x1a\n\x16LoadRoomWelcomeMessage\x10\x19\x12\x15\n\x11Joi\
    nedRoomSuccess\x10\x1a\x12\x12\n\x0eJoinedRoomFail\x10\x1b\x12\x13\n\x0f\
    UsersInRoomList\x10\x1c\x12\x0e\n\nUserJoined\x10\x1d\x12\x0f\n\x0bUserU\
    pdated\x10\x1e\x12\x0c\n\x08UserLeft\x10\x1f\x12\x0f\n\x0bUsersTyping\
    \x10\x20\x12\x17\n\x13BasicRoomApiCreated\x10!\x12\x17\n\x13BasicRoomApi\
    Updated\x10\"\x12\x17\n\x13BasicRoomApiDeleted\x10#\x12\x11\n\rClientUpd\
    ated\x10$\x12\x13\n\x0fUnauthenticated\x10%\x12\x14\n\x10RoomOwnerUpdate\
    d\x10&\x12\x17\n\x13FriendStatusUpdated\x10'\x12\x1b\n\x17AddPendingFrie\
    ndRequest\x10(\x12\x1e\n\x1aRemovePendingFriendRequest\x10)\x12\x16\n\
    \x12GetKickedUsersList\x10*\x12\x16\n\x12GetRoomFullDetails\x10+\x12\x19\
    \n\x15MaintenanceModeActive\x10,*\xcc\x01\n\x13CommandResponseType\x12\t\
    \n\x05Error\x10\0\x12\x13\n\x0fValidationError\x10\x01\x12\x10\n\x0cJoin\
    RoomFail\x10\x03\x12\x0c\n\x08LeftRoom\x10\x04\x12\r\n\tRoomsList\x10\
    \x05\x12\x19\n\x15KickedUsersInRoomList\x10\x06\x12\x10\n\x0cRoomSetting\
    s\x10\x07\x12\x17\n\x13RegistrationSuccess\x10\x08\x12\t\n\x05Toast\x10\
    \t\x12\x15\n\x11EnterRoomPassword\x10\n*\x93\x01\n\x1cClientValidationEr\
    rorMsgType\x12\x0e\n\nCreateRoom\x10\0\x12\x0e\n\nUpdateRoom\x10\x01\x12\
    \x10\n\x0cRegistration\x10\x02\x12\t\n\x05Login\x10\x03\x12\x12\n\x0eFor\
    gotPassword\x10\x04\x12\x11\n\rResetPassword\x10\x05\x12\x0f\n\x0bResend\
    Email\x10\x06*\xdd\x01\n\x10JoinRoomFailType\x12\x14\n\x10QueuedToJoinRo\
    om\x10\0\x12\x11\n\rAlreadyInRoom\x10\x01\x12\x0c\n\x08RoomFull\x10\x02\
    \x12\x0c\n\x08NoGuests\x10\x03\x12\x12\n\x0eProMembersOnly\x10\x04\x12\
    \x0e\n\nNotAllowed\x10\x05\x12\x13\n\x0fMaintenanceMode\x10\x06\x12\x15\
    \n\x11IncorrectPassword\x10\x07\x12\x0f\n\x0bServerError\x10\x08\x12\x10\
    \n\x0cRoomNotFound\x10\t\x12\x11\n\rEnterPassword\x10\nb\x06proto3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
