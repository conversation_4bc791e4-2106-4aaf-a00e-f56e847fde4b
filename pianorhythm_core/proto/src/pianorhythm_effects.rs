// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `pianorhythm-effects.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>)]
pub struct AppStateEffects {
    // message fields
    pub action: AppStateEffects_Action,
    // message oneof groups
    pub data: ::std::option::Option<AppStateEffects_oneof_data>,
    pub _sourceSocketID: ::std::option::Option<AppStateEffects_oneof__sourceSocketID>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AppStateEffects {
    fn default() -> &'a AppStateEffects {
        <AppStateEffects as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AppStateEffects_oneof_data {
    processedMidiMessageOutput(AppStateEffects_ProcessedMidiMessageOutput),
    clientSideUserDto(super::user_renditions::ClientSideUserDto),
    socketId(::std::string::String),
    userDtoList(super::client_message::UserDtoList),
    clientSideUserDtoList(super::client_message::ClientSideUserDtoList),
    joinedRoomData(super::client_message::JoinedRoomData),
    roomChatHistory(super::client_message::RoomChatHistory),
    chatMessageDto(super::client_message::ChatMessageDto),
    midiMessageOutputDto(super::client_message::MidiMessageOutputDto),
    socketIdList(super::client_message::SocketIdList),
    message(::std::string::String),
    joinRoomFailResponse(super::client_message::CommandResponse_JoinRoomFailResponse),
    roomsList(super::client_message::CommandResponse_RoomsList),
    basicRoomDto(super::room_renditions::BasicRoomDto),
    roomId(::std::string::String),
    chatMessageDtoList(super::client_message::ChatMessageDtoList),
    messageId(::std::string::String),
    welcomeDto(super::client_message::WelcomeDto),
    clientValidationErrorList(super::client_message::CommandResponse_ClientValidationErrorList),
    roomSettings(super::room_renditions::RoomSettings),
    roomFullDetails(super::room_renditions::RoomFullDetails),
    userDto(super::user_renditions::UserDto),
    audioChannel(super::midi_renditions::AudioChannel),
    slotMode(super::midi_renditions::ActiveChannelsMode),
    int32Value(i32),
    uint32Value(u32),
    boolValue(bool),
    userClientDto(super::user_renditions::UserClientDto),
    instrumentsList(super::midi_renditions::InstrumentsList),
    loadRoomStageDetails(AppStateEffects_LoadRoomStageDetails),
    bytesPayload(AppStateEffects_BytesPayload),
    stringValue(::std::string::String),
    appPianoKey(super::pianorhythm_app_renditions::AppPianoKey),
    appPianoPedal(super::pianorhythm_app_renditions::AppPianoPedal),
    appRenderableEntity(super::pianorhythm_app_renditions::AppRenderableEntity),
    appPageLoaderDetail(super::pianorhythm_app_renditions::AppPageloaderDetail),
    appNotificationConfig(super::pianorhythm_app_renditions::AppNotificationConfig),
    appSettings(super::pianorhythm_app_renditions::AppSettings),
    midiSequencerEvent(super::pianorhythm_app_renditions::AppMidiSequencerEvent),
    appMidiTrack(super::pianorhythm_app_renditions::AppMidiTrack),
    appRoomStageLoaded(super::pianorhythm_app_renditions::AppRoomStageLoaded),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AppStateEffects_oneof__sourceSocketID {
    sourceSocketID(::std::string::String),
}

impl AppStateEffects {
    pub fn new() -> AppStateEffects {
        ::std::default::Default::default()
    }

    // .PianoRhythm.AppStateEffects.AppStateEffects.Action action = 1;


    pub fn get_action(&self) -> AppStateEffects_Action {
        self.action
    }
    pub fn clear_action(&mut self) {
        self.action = AppStateEffects_Action::Unknown;
    }

    // Param is passed by value, moved
    pub fn set_action(&mut self, v: AppStateEffects_Action) {
        self.action = v;
    }

    // string sourceSocketID = 999;


    pub fn get_sourceSocketID(&self) -> &str {
        match self._sourceSocketID {
            ::std::option::Option::Some(AppStateEffects_oneof__sourceSocketID::sourceSocketID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_sourceSocketID(&mut self) {
        self._sourceSocketID = ::std::option::Option::None;
    }

    pub fn has_sourceSocketID(&self) -> bool {
        match self._sourceSocketID {
            ::std::option::Option::Some(AppStateEffects_oneof__sourceSocketID::sourceSocketID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_sourceSocketID(&mut self, v: ::std::string::String) {
        self._sourceSocketID = ::std::option::Option::Some(AppStateEffects_oneof__sourceSocketID::sourceSocketID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_sourceSocketID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateEffects_oneof__sourceSocketID::sourceSocketID(_)) = self._sourceSocketID {
        } else {
            self._sourceSocketID = ::std::option::Option::Some(AppStateEffects_oneof__sourceSocketID::sourceSocketID(::std::string::String::new()));
        }
        match self._sourceSocketID {
            ::std::option::Option::Some(AppStateEffects_oneof__sourceSocketID::sourceSocketID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_sourceSocketID(&mut self) -> ::std::string::String {
        if self.has_sourceSocketID() {
            match self._sourceSocketID.take() {
                ::std::option::Option::Some(AppStateEffects_oneof__sourceSocketID::sourceSocketID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.AppStateEffects.AppStateEffects.ProcessedMidiMessageOutput processedMidiMessageOutput = 2;


    pub fn get_processedMidiMessageOutput(&self) -> &AppStateEffects_ProcessedMidiMessageOutput {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::processedMidiMessageOutput(ref v)) => v,
            _ => <AppStateEffects_ProcessedMidiMessageOutput as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_processedMidiMessageOutput(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_processedMidiMessageOutput(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::processedMidiMessageOutput(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_processedMidiMessageOutput(&mut self, v: AppStateEffects_ProcessedMidiMessageOutput) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::processedMidiMessageOutput(v))
    }

    // Mutable pointer to the field.
    pub fn mut_processedMidiMessageOutput(&mut self) -> &mut AppStateEffects_ProcessedMidiMessageOutput {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::processedMidiMessageOutput(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::processedMidiMessageOutput(AppStateEffects_ProcessedMidiMessageOutput::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::processedMidiMessageOutput(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_processedMidiMessageOutput(&mut self) -> AppStateEffects_ProcessedMidiMessageOutput {
        if self.has_processedMidiMessageOutput() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::processedMidiMessageOutput(v)) => v,
                _ => panic!(),
            }
        } else {
            AppStateEffects_ProcessedMidiMessageOutput::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.ClientSideUserDto clientSideUserDto = 3;


    pub fn get_clientSideUserDto(&self) -> &super::user_renditions::ClientSideUserDto {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDto(ref v)) => v,
            _ => <super::user_renditions::ClientSideUserDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_clientSideUserDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_clientSideUserDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clientSideUserDto(&mut self, v: super::user_renditions::ClientSideUserDto) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clientSideUserDto(&mut self) -> &mut super::user_renditions::ClientSideUserDto {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDto(super::user_renditions::ClientSideUserDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clientSideUserDto(&mut self) -> super::user_renditions::ClientSideUserDto {
        if self.has_clientSideUserDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::ClientSideUserDto::new()
        }
    }

    // string socketId = 4;


    pub fn get_socketId(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::socketId(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_socketId(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_socketId(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::socketId(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketId(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::socketId(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketId(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::socketId(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::socketId(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::socketId(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketId(&mut self) -> ::std::string::String {
        if self.has_socketId() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::socketId(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.UserDtoList userDtoList = 5;


    pub fn get_userDtoList(&self) -> &super::client_message::UserDtoList {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::userDtoList(ref v)) => v,
            _ => <super::client_message::UserDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userDtoList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userDtoList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::userDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userDtoList(&mut self, v: super::client_message::UserDtoList) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::userDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userDtoList(&mut self) -> &mut super::client_message::UserDtoList {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::userDtoList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::userDtoList(super::client_message::UserDtoList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::userDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userDtoList(&mut self) -> super::client_message::UserDtoList {
        if self.has_userDtoList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::userDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::UserDtoList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ClientSideUserDtoList clientSideUserDtoList = 6;


    pub fn get_clientSideUserDtoList(&self) -> &super::client_message::ClientSideUserDtoList {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDtoList(ref v)) => v,
            _ => <super::client_message::ClientSideUserDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_clientSideUserDtoList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_clientSideUserDtoList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clientSideUserDtoList(&mut self, v: super::client_message::ClientSideUserDtoList) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clientSideUserDtoList(&mut self) -> &mut super::client_message::ClientSideUserDtoList {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDtoList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDtoList(super::client_message::ClientSideUserDtoList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clientSideUserDtoList(&mut self) -> super::client_message::ClientSideUserDtoList {
        if self.has_clientSideUserDtoList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::ClientSideUserDtoList::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.JoinedRoomData joinedRoomData = 7;


    pub fn get_joinedRoomData(&self) -> &super::client_message::JoinedRoomData {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::joinedRoomData(ref v)) => v,
            _ => <super::client_message::JoinedRoomData as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_joinedRoomData(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_joinedRoomData(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::joinedRoomData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_joinedRoomData(&mut self, v: super::client_message::JoinedRoomData) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::joinedRoomData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_joinedRoomData(&mut self) -> &mut super::client_message::JoinedRoomData {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::joinedRoomData(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::joinedRoomData(super::client_message::JoinedRoomData::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::joinedRoomData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_joinedRoomData(&mut self) -> super::client_message::JoinedRoomData {
        if self.has_joinedRoomData() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::joinedRoomData(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::JoinedRoomData::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.RoomChatHistory roomChatHistory = 8;


    pub fn get_roomChatHistory(&self) -> &super::client_message::RoomChatHistory {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomChatHistory(ref v)) => v,
            _ => <super::client_message::RoomChatHistory as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomChatHistory(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomChatHistory(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomChatHistory(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomChatHistory(&mut self, v: super::client_message::RoomChatHistory) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomChatHistory(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomChatHistory(&mut self) -> &mut super::client_message::RoomChatHistory {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::roomChatHistory(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomChatHistory(super::client_message::RoomChatHistory::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomChatHistory(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomChatHistory(&mut self) -> super::client_message::RoomChatHistory {
        if self.has_roomChatHistory() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::roomChatHistory(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::RoomChatHistory::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDto chatMessageDto = 9;


    pub fn get_chatMessageDto(&self) -> &super::client_message::ChatMessageDto {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDto(ref v)) => v,
            _ => <super::client_message::ChatMessageDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_chatMessageDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_chatMessageDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_chatMessageDto(&mut self, v: super::client_message::ChatMessageDto) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_chatMessageDto(&mut self) -> &mut super::client_message::ChatMessageDto {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDto(super::client_message::ChatMessageDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_chatMessageDto(&mut self) -> super::client_message::ChatMessageDto {
        if self.has_chatMessageDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::ChatMessageDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.MidiMessageOutputDto midiMessageOutputDto = 10;


    pub fn get_midiMessageOutputDto(&self) -> &super::client_message::MidiMessageOutputDto {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::midiMessageOutputDto(ref v)) => v,
            _ => <super::client_message::MidiMessageOutputDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_midiMessageOutputDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_midiMessageOutputDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::midiMessageOutputDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_midiMessageOutputDto(&mut self, v: super::client_message::MidiMessageOutputDto) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::midiMessageOutputDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_midiMessageOutputDto(&mut self) -> &mut super::client_message::MidiMessageOutputDto {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::midiMessageOutputDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::midiMessageOutputDto(super::client_message::MidiMessageOutputDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::midiMessageOutputDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_midiMessageOutputDto(&mut self) -> super::client_message::MidiMessageOutputDto {
        if self.has_midiMessageOutputDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::midiMessageOutputDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::MidiMessageOutputDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.SocketIdList socketIdList = 11;


    pub fn get_socketIdList(&self) -> &super::client_message::SocketIdList {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::socketIdList(ref v)) => v,
            _ => <super::client_message::SocketIdList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_socketIdList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_socketIdList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::socketIdList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketIdList(&mut self, v: super::client_message::SocketIdList) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::socketIdList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketIdList(&mut self) -> &mut super::client_message::SocketIdList {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::socketIdList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::socketIdList(super::client_message::SocketIdList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::socketIdList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketIdList(&mut self) -> super::client_message::SocketIdList {
        if self.has_socketIdList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::socketIdList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::SocketIdList::new()
        }
    }

    // string message = 12;


    pub fn get_message(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::message(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_message(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_message(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::message(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_message(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::message(v))
    }

    // Mutable pointer to the field.
    pub fn mut_message(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::message(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::message(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::message(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_message(&mut self) -> ::std::string::String {
        if self.has_message() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::message(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.JoinRoomFailResponse joinRoomFailResponse = 13;


    pub fn get_joinRoomFailResponse(&self) -> &super::client_message::CommandResponse_JoinRoomFailResponse {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::joinRoomFailResponse(ref v)) => v,
            _ => <super::client_message::CommandResponse_JoinRoomFailResponse as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_joinRoomFailResponse(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_joinRoomFailResponse(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::joinRoomFailResponse(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_joinRoomFailResponse(&mut self, v: super::client_message::CommandResponse_JoinRoomFailResponse) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::joinRoomFailResponse(v))
    }

    // Mutable pointer to the field.
    pub fn mut_joinRoomFailResponse(&mut self) -> &mut super::client_message::CommandResponse_JoinRoomFailResponse {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::joinRoomFailResponse(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::joinRoomFailResponse(super::client_message::CommandResponse_JoinRoomFailResponse::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::joinRoomFailResponse(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_joinRoomFailResponse(&mut self) -> super::client_message::CommandResponse_JoinRoomFailResponse {
        if self.has_joinRoomFailResponse() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::joinRoomFailResponse(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::CommandResponse_JoinRoomFailResponse::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.RoomsList roomsList = 14;


    pub fn get_roomsList(&self) -> &super::client_message::CommandResponse_RoomsList {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomsList(ref v)) => v,
            _ => <super::client_message::CommandResponse_RoomsList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomsList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomsList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomsList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomsList(&mut self, v: super::client_message::CommandResponse_RoomsList) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomsList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomsList(&mut self) -> &mut super::client_message::CommandResponse_RoomsList {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::roomsList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomsList(super::client_message::CommandResponse_RoomsList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomsList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomsList(&mut self) -> super::client_message::CommandResponse_RoomsList {
        if self.has_roomsList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::roomsList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::CommandResponse_RoomsList::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.BasicRoomDto basicRoomDto = 15;


    pub fn get_basicRoomDto(&self) -> &super::room_renditions::BasicRoomDto {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::basicRoomDto(ref v)) => v,
            _ => <super::room_renditions::BasicRoomDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_basicRoomDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_basicRoomDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::basicRoomDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_basicRoomDto(&mut self, v: super::room_renditions::BasicRoomDto) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::basicRoomDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_basicRoomDto(&mut self) -> &mut super::room_renditions::BasicRoomDto {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::basicRoomDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::basicRoomDto(super::room_renditions::BasicRoomDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::basicRoomDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_basicRoomDto(&mut self) -> super::room_renditions::BasicRoomDto {
        if self.has_basicRoomDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::basicRoomDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::BasicRoomDto::new()
        }
    }

    // string roomId = 16;


    pub fn get_roomId(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomId(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_roomId(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomId(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomId(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomId(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomId(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomId(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::roomId(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomId(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomId(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomId(&mut self) -> ::std::string::String {
        if self.has_roomId() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::roomId(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDtoList chatMessageDtoList = 17;


    pub fn get_chatMessageDtoList(&self) -> &super::client_message::ChatMessageDtoList {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDtoList(ref v)) => v,
            _ => <super::client_message::ChatMessageDtoList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_chatMessageDtoList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_chatMessageDtoList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDtoList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_chatMessageDtoList(&mut self, v: super::client_message::ChatMessageDtoList) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDtoList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_chatMessageDtoList(&mut self) -> &mut super::client_message::ChatMessageDtoList {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDtoList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDtoList(super::client_message::ChatMessageDtoList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDtoList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_chatMessageDtoList(&mut self) -> super::client_message::ChatMessageDtoList {
        if self.has_chatMessageDtoList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDtoList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::ChatMessageDtoList::new()
        }
    }

    // string messageId = 18;


    pub fn get_messageId(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::messageId(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_messageId(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_messageId(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::messageId(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_messageId(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::messageId(v))
    }

    // Mutable pointer to the field.
    pub fn mut_messageId(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::messageId(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::messageId(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::messageId(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_messageId(&mut self) -> ::std::string::String {
        if self.has_messageId() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::messageId(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.WelcomeDto welcomeDto = 19;


    pub fn get_welcomeDto(&self) -> &super::client_message::WelcomeDto {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::welcomeDto(ref v)) => v,
            _ => <super::client_message::WelcomeDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_welcomeDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_welcomeDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::welcomeDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_welcomeDto(&mut self, v: super::client_message::WelcomeDto) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::welcomeDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_welcomeDto(&mut self) -> &mut super::client_message::WelcomeDto {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::welcomeDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::welcomeDto(super::client_message::WelcomeDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::welcomeDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_welcomeDto(&mut self) -> super::client_message::WelcomeDto {
        if self.has_welcomeDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::welcomeDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::WelcomeDto::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.ClientValidationErrorList clientValidationErrorList = 20;


    pub fn get_clientValidationErrorList(&self) -> &super::client_message::CommandResponse_ClientValidationErrorList {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::clientValidationErrorList(ref v)) => v,
            _ => <super::client_message::CommandResponse_ClientValidationErrorList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_clientValidationErrorList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_clientValidationErrorList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::clientValidationErrorList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clientValidationErrorList(&mut self, v: super::client_message::CommandResponse_ClientValidationErrorList) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::clientValidationErrorList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clientValidationErrorList(&mut self) -> &mut super::client_message::CommandResponse_ClientValidationErrorList {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::clientValidationErrorList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::clientValidationErrorList(super::client_message::CommandResponse_ClientValidationErrorList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::clientValidationErrorList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clientValidationErrorList(&mut self) -> super::client_message::CommandResponse_ClientValidationErrorList {
        if self.has_clientValidationErrorList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::clientValidationErrorList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::client_message::CommandResponse_ClientValidationErrorList::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomSettings roomSettings = 21;


    pub fn get_roomSettings(&self) -> &super::room_renditions::RoomSettings {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomSettings(ref v)) => v,
            _ => <super::room_renditions::RoomSettings as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomSettings(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomSettings(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomSettings(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomSettings(&mut self, v: super::room_renditions::RoomSettings) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomSettings(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomSettings(&mut self) -> &mut super::room_renditions::RoomSettings {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::roomSettings(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomSettings(super::room_renditions::RoomSettings::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomSettings(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomSettings(&mut self) -> super::room_renditions::RoomSettings {
        if self.has_roomSettings() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::roomSettings(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::RoomSettings::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomFullDetails roomFullDetails = 22;


    pub fn get_roomFullDetails(&self) -> &super::room_renditions::RoomFullDetails {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomFullDetails(ref v)) => v,
            _ => <super::room_renditions::RoomFullDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomFullDetails(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_roomFullDetails(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomFullDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomFullDetails(&mut self, v: super::room_renditions::RoomFullDetails) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomFullDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomFullDetails(&mut self) -> &mut super::room_renditions::RoomFullDetails {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::roomFullDetails(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomFullDetails(super::room_renditions::RoomFullDetails::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::roomFullDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomFullDetails(&mut self) -> super::room_renditions::RoomFullDetails {
        if self.has_roomFullDetails() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::roomFullDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::RoomFullDetails::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserDto userDto = 23;


    pub fn get_userDto(&self) -> &super::user_renditions::UserDto {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::userDto(ref v)) => v,
            _ => <super::user_renditions::UserDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::userDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userDto(&mut self, v: super::user_renditions::UserDto) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::userDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userDto(&mut self) -> &mut super::user_renditions::UserDto {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::userDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::userDto(super::user_renditions::UserDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::userDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userDto(&mut self) -> super::user_renditions::UserDto {
        if self.has_userDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::userDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::UserDto::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.AudioChannel audioChannel = 24;


    pub fn get_audioChannel(&self) -> &super::midi_renditions::AudioChannel {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::audioChannel(ref v)) => v,
            _ => <super::midi_renditions::AudioChannel as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_audioChannel(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_audioChannel(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::audioChannel(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_audioChannel(&mut self, v: super::midi_renditions::AudioChannel) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::audioChannel(v))
    }

    // Mutable pointer to the field.
    pub fn mut_audioChannel(&mut self) -> &mut super::midi_renditions::AudioChannel {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::audioChannel(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::audioChannel(super::midi_renditions::AudioChannel::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::audioChannel(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_audioChannel(&mut self) -> super::midi_renditions::AudioChannel {
        if self.has_audioChannel() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::audioChannel(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::AudioChannel::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.ActiveChannelsMode slotMode = 25;


    pub fn get_slotMode(&self) -> super::midi_renditions::ActiveChannelsMode {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::slotMode(v)) => v,
            _ => super::midi_renditions::ActiveChannelsMode::SINGLE,
        }
    }
    pub fn clear_slotMode(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_slotMode(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::slotMode(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_slotMode(&mut self, v: super::midi_renditions::ActiveChannelsMode) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::slotMode(v))
    }

    // int32 int32Value = 26;


    pub fn get_int32Value(&self) -> i32 {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::int32Value(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_int32Value(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_int32Value(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::int32Value(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_int32Value(&mut self, v: i32) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::int32Value(v))
    }

    // uint32 uint32Value = 27;


    pub fn get_uint32Value(&self) -> u32 {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::uint32Value(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_uint32Value(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_uint32Value(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::uint32Value(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_uint32Value(&mut self, v: u32) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::uint32Value(v))
    }

    // bool boolValue = 28;


    pub fn get_boolValue(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::boolValue(v)) => v,
            _ => false,
        }
    }
    pub fn clear_boolValue(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_boolValue(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::boolValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_boolValue(&mut self, v: bool) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::boolValue(v))
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserClientDto userClientDto = 29;


    pub fn get_userClientDto(&self) -> &super::user_renditions::UserClientDto {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::userClientDto(ref v)) => v,
            _ => <super::user_renditions::UserClientDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userClientDto(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userClientDto(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::userClientDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userClientDto(&mut self, v: super::user_renditions::UserClientDto) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::userClientDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userClientDto(&mut self) -> &mut super::user_renditions::UserClientDto {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::userClientDto(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::userClientDto(super::user_renditions::UserClientDto::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::userClientDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userClientDto(&mut self) -> super::user_renditions::UserClientDto {
        if self.has_userClientDto() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::userClientDto(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::UserClientDto::new()
        }
    }

    // .PianoRhythm.Serialization.Midi.Msgs.InstrumentsList instrumentsList = 30;


    pub fn get_instrumentsList(&self) -> &super::midi_renditions::InstrumentsList {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::instrumentsList(ref v)) => v,
            _ => <super::midi_renditions::InstrumentsList as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_instrumentsList(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_instrumentsList(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::instrumentsList(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_instrumentsList(&mut self, v: super::midi_renditions::InstrumentsList) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::instrumentsList(v))
    }

    // Mutable pointer to the field.
    pub fn mut_instrumentsList(&mut self) -> &mut super::midi_renditions::InstrumentsList {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::instrumentsList(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::instrumentsList(super::midi_renditions::InstrumentsList::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::instrumentsList(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_instrumentsList(&mut self) -> super::midi_renditions::InstrumentsList {
        if self.has_instrumentsList() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::instrumentsList(v)) => v,
                _ => panic!(),
            }
        } else {
            super::midi_renditions::InstrumentsList::new()
        }
    }

    // .PianoRhythm.AppStateEffects.AppStateEffects.LoadRoomStageDetails loadRoomStageDetails = 31;


    pub fn get_loadRoomStageDetails(&self) -> &AppStateEffects_LoadRoomStageDetails {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::loadRoomStageDetails(ref v)) => v,
            _ => <AppStateEffects_LoadRoomStageDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_loadRoomStageDetails(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_loadRoomStageDetails(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::loadRoomStageDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_loadRoomStageDetails(&mut self, v: AppStateEffects_LoadRoomStageDetails) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::loadRoomStageDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_loadRoomStageDetails(&mut self) -> &mut AppStateEffects_LoadRoomStageDetails {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::loadRoomStageDetails(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::loadRoomStageDetails(AppStateEffects_LoadRoomStageDetails::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::loadRoomStageDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_loadRoomStageDetails(&mut self) -> AppStateEffects_LoadRoomStageDetails {
        if self.has_loadRoomStageDetails() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::loadRoomStageDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            AppStateEffects_LoadRoomStageDetails::new()
        }
    }

    // .PianoRhythm.AppStateEffects.AppStateEffects.BytesPayload bytesPayload = 32;


    pub fn get_bytesPayload(&self) -> &AppStateEffects_BytesPayload {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::bytesPayload(ref v)) => v,
            _ => <AppStateEffects_BytesPayload as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_bytesPayload(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_bytesPayload(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::bytesPayload(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_bytesPayload(&mut self, v: AppStateEffects_BytesPayload) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::bytesPayload(v))
    }

    // Mutable pointer to the field.
    pub fn mut_bytesPayload(&mut self) -> &mut AppStateEffects_BytesPayload {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::bytesPayload(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::bytesPayload(AppStateEffects_BytesPayload::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::bytesPayload(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_bytesPayload(&mut self) -> AppStateEffects_BytesPayload {
        if self.has_bytesPayload() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::bytesPayload(v)) => v,
                _ => panic!(),
            }
        } else {
            AppStateEffects_BytesPayload::new()
        }
    }

    // string stringValue = 33;


    pub fn get_stringValue(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::stringValue(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_stringValue(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_stringValue(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::stringValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_stringValue(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::stringValue(v))
    }

    // Mutable pointer to the field.
    pub fn mut_stringValue(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::stringValue(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::stringValue(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::stringValue(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_stringValue(&mut self) -> ::std::string::String {
        if self.has_stringValue() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::stringValue(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppPianoKey appPianoKey = 5000;


    pub fn get_appPianoKey(&self) -> &super::pianorhythm_app_renditions::AppPianoKey {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoKey(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppPianoKey as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appPianoKey(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appPianoKey(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoKey(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appPianoKey(&mut self, v: super::pianorhythm_app_renditions::AppPianoKey) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoKey(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appPianoKey(&mut self) -> &mut super::pianorhythm_app_renditions::AppPianoKey {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoKey(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoKey(super::pianorhythm_app_renditions::AppPianoKey::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoKey(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appPianoKey(&mut self) -> super::pianorhythm_app_renditions::AppPianoKey {
        if self.has_appPianoKey() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoKey(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppPianoKey::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppPianoPedal appPianoPedal = 5001;


    pub fn get_appPianoPedal(&self) -> &super::pianorhythm_app_renditions::AppPianoPedal {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoPedal(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppPianoPedal as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appPianoPedal(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appPianoPedal(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoPedal(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appPianoPedal(&mut self, v: super::pianorhythm_app_renditions::AppPianoPedal) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoPedal(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appPianoPedal(&mut self) -> &mut super::pianorhythm_app_renditions::AppPianoPedal {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoPedal(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoPedal(super::pianorhythm_app_renditions::AppPianoPedal::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoPedal(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appPianoPedal(&mut self) -> super::pianorhythm_app_renditions::AppPianoPedal {
        if self.has_appPianoPedal() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoPedal(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppPianoPedal::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppRenderableEntity appRenderableEntity = 5002;


    pub fn get_appRenderableEntity(&self) -> &super::pianorhythm_app_renditions::AppRenderableEntity {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appRenderableEntity(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppRenderableEntity as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appRenderableEntity(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appRenderableEntity(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appRenderableEntity(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appRenderableEntity(&mut self, v: super::pianorhythm_app_renditions::AppRenderableEntity) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appRenderableEntity(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appRenderableEntity(&mut self) -> &mut super::pianorhythm_app_renditions::AppRenderableEntity {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::appRenderableEntity(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appRenderableEntity(super::pianorhythm_app_renditions::AppRenderableEntity::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appRenderableEntity(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appRenderableEntity(&mut self) -> super::pianorhythm_app_renditions::AppRenderableEntity {
        if self.has_appRenderableEntity() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::appRenderableEntity(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppRenderableEntity::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppPageloaderDetail appPageLoaderDetail = 5003;


    pub fn get_appPageLoaderDetail(&self) -> &super::pianorhythm_app_renditions::AppPageloaderDetail {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appPageLoaderDetail(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppPageloaderDetail as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appPageLoaderDetail(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appPageLoaderDetail(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appPageLoaderDetail(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appPageLoaderDetail(&mut self, v: super::pianorhythm_app_renditions::AppPageloaderDetail) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appPageLoaderDetail(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appPageLoaderDetail(&mut self) -> &mut super::pianorhythm_app_renditions::AppPageloaderDetail {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::appPageLoaderDetail(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appPageLoaderDetail(super::pianorhythm_app_renditions::AppPageloaderDetail::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appPageLoaderDetail(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appPageLoaderDetail(&mut self) -> super::pianorhythm_app_renditions::AppPageloaderDetail {
        if self.has_appPageLoaderDetail() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::appPageLoaderDetail(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppPageloaderDetail::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppNotificationConfig appNotificationConfig = 5004;


    pub fn get_appNotificationConfig(&self) -> &super::pianorhythm_app_renditions::AppNotificationConfig {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appNotificationConfig(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppNotificationConfig as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appNotificationConfig(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appNotificationConfig(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appNotificationConfig(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appNotificationConfig(&mut self, v: super::pianorhythm_app_renditions::AppNotificationConfig) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appNotificationConfig(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appNotificationConfig(&mut self) -> &mut super::pianorhythm_app_renditions::AppNotificationConfig {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::appNotificationConfig(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appNotificationConfig(super::pianorhythm_app_renditions::AppNotificationConfig::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appNotificationConfig(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appNotificationConfig(&mut self) -> super::pianorhythm_app_renditions::AppNotificationConfig {
        if self.has_appNotificationConfig() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::appNotificationConfig(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppNotificationConfig::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppSettings appSettings = 5005;


    pub fn get_appSettings(&self) -> &super::pianorhythm_app_renditions::AppSettings {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appSettings(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppSettings as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appSettings(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appSettings(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appSettings(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appSettings(&mut self, v: super::pianorhythm_app_renditions::AppSettings) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appSettings(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appSettings(&mut self) -> &mut super::pianorhythm_app_renditions::AppSettings {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::appSettings(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appSettings(super::pianorhythm_app_renditions::AppSettings::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appSettings(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appSettings(&mut self) -> super::pianorhythm_app_renditions::AppSettings {
        if self.has_appSettings() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::appSettings(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppSettings::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppMidiSequencerEvent midiSequencerEvent = 5006;


    pub fn get_midiSequencerEvent(&self) -> &super::pianorhythm_app_renditions::AppMidiSequencerEvent {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::midiSequencerEvent(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppMidiSequencerEvent as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_midiSequencerEvent(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_midiSequencerEvent(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::midiSequencerEvent(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_midiSequencerEvent(&mut self, v: super::pianorhythm_app_renditions::AppMidiSequencerEvent) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::midiSequencerEvent(v))
    }

    // Mutable pointer to the field.
    pub fn mut_midiSequencerEvent(&mut self) -> &mut super::pianorhythm_app_renditions::AppMidiSequencerEvent {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::midiSequencerEvent(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::midiSequencerEvent(super::pianorhythm_app_renditions::AppMidiSequencerEvent::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::midiSequencerEvent(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_midiSequencerEvent(&mut self) -> super::pianorhythm_app_renditions::AppMidiSequencerEvent {
        if self.has_midiSequencerEvent() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::midiSequencerEvent(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppMidiSequencerEvent::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppMidiTrack appMidiTrack = 5007;


    pub fn get_appMidiTrack(&self) -> &super::pianorhythm_app_renditions::AppMidiTrack {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appMidiTrack(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppMidiTrack as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appMidiTrack(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appMidiTrack(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appMidiTrack(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appMidiTrack(&mut self, v: super::pianorhythm_app_renditions::AppMidiTrack) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appMidiTrack(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appMidiTrack(&mut self) -> &mut super::pianorhythm_app_renditions::AppMidiTrack {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::appMidiTrack(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appMidiTrack(super::pianorhythm_app_renditions::AppMidiTrack::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appMidiTrack(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appMidiTrack(&mut self) -> super::pianorhythm_app_renditions::AppMidiTrack {
        if self.has_appMidiTrack() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::appMidiTrack(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppMidiTrack::new()
        }
    }

    // .PianoRhythm.AppRenditions.AppRoomStageLoaded appRoomStageLoaded = 5008;


    pub fn get_appRoomStageLoaded(&self) -> &super::pianorhythm_app_renditions::AppRoomStageLoaded {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appRoomStageLoaded(ref v)) => v,
            _ => <super::pianorhythm_app_renditions::AppRoomStageLoaded as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_appRoomStageLoaded(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_appRoomStageLoaded(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appRoomStageLoaded(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_appRoomStageLoaded(&mut self, v: super::pianorhythm_app_renditions::AppRoomStageLoaded) {
        self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appRoomStageLoaded(v))
    }

    // Mutable pointer to the field.
    pub fn mut_appRoomStageLoaded(&mut self) -> &mut super::pianorhythm_app_renditions::AppRoomStageLoaded {
        if let ::std::option::Option::Some(AppStateEffects_oneof_data::appRoomStageLoaded(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appRoomStageLoaded(super::pianorhythm_app_renditions::AppRoomStageLoaded::new()));
        }
        match self.data {
            ::std::option::Option::Some(AppStateEffects_oneof_data::appRoomStageLoaded(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_appRoomStageLoaded(&mut self) -> super::pianorhythm_app_renditions::AppRoomStageLoaded {
        if self.has_appRoomStageLoaded() {
            match self.data.take() {
                ::std::option::Option::Some(AppStateEffects_oneof_data::appRoomStageLoaded(v)) => v,
                _ => panic!(),
            }
        } else {
            super::pianorhythm_app_renditions::AppRoomStageLoaded::new()
        }
    }
}

impl ::protobuf::Message for AppStateEffects {
    fn is_initialized(&self) -> bool {
        if let Some(AppStateEffects_oneof_data::processedMidiMessageOutput(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::clientSideUserDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::userDtoList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::clientSideUserDtoList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::joinedRoomData(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::roomChatHistory(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::chatMessageDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::midiMessageOutputDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::socketIdList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::joinRoomFailResponse(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::roomsList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::basicRoomDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::chatMessageDtoList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::welcomeDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::clientValidationErrorList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::roomSettings(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::roomFullDetails(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::userDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::audioChannel(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::userClientDto(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::instrumentsList(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::loadRoomStageDetails(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::bytesPayload(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::appPianoKey(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::appPianoPedal(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::appRenderableEntity(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::appPageLoaderDetail(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::appNotificationConfig(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::appSettings(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::midiSequencerEvent(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::appMidiTrack(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AppStateEffects_oneof_data::appRoomStageLoaded(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.action, 1, &mut self.unknown_fields)?
                },
                999 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._sourceSocketID = ::std::option::Option::Some(AppStateEffects_oneof__sourceSocketID::sourceSocketID(is.read_string()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::processedMidiMessageOutput(is.read_message()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDto(is.read_message()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::socketId(is.read_string()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::userDtoList(is.read_message()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::clientSideUserDtoList(is.read_message()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::joinedRoomData(is.read_message()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomChatHistory(is.read_message()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDto(is.read_message()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::midiMessageOutputDto(is.read_message()?));
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::socketIdList(is.read_message()?));
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::message(is.read_string()?));
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::joinRoomFailResponse(is.read_message()?));
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomsList(is.read_message()?));
                },
                15 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::basicRoomDto(is.read_message()?));
                },
                16 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomId(is.read_string()?));
                },
                17 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::chatMessageDtoList(is.read_message()?));
                },
                18 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::messageId(is.read_string()?));
                },
                19 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::welcomeDto(is.read_message()?));
                },
                20 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::clientValidationErrorList(is.read_message()?));
                },
                21 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomSettings(is.read_message()?));
                },
                22 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::roomFullDetails(is.read_message()?));
                },
                23 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::userDto(is.read_message()?));
                },
                24 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::audioChannel(is.read_message()?));
                },
                25 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::slotMode(is.read_enum()?));
                },
                26 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::int32Value(is.read_int32()?));
                },
                27 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::uint32Value(is.read_uint32()?));
                },
                28 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::boolValue(is.read_bool()?));
                },
                29 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::userClientDto(is.read_message()?));
                },
                30 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::instrumentsList(is.read_message()?));
                },
                31 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::loadRoomStageDetails(is.read_message()?));
                },
                32 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::bytesPayload(is.read_message()?));
                },
                33 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::stringValue(is.read_string()?));
                },
                5000 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoKey(is.read_message()?));
                },
                5001 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appPianoPedal(is.read_message()?));
                },
                5002 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appRenderableEntity(is.read_message()?));
                },
                5003 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appPageLoaderDetail(is.read_message()?));
                },
                5004 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appNotificationConfig(is.read_message()?));
                },
                5005 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appSettings(is.read_message()?));
                },
                5006 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::midiSequencerEvent(is.read_message()?));
                },
                5007 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appMidiTrack(is.read_message()?));
                },
                5008 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(AppStateEffects_oneof_data::appRoomStageLoaded(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.action != AppStateEffects_Action::Unknown {
            my_size += ::protobuf::rt::enum_size(1, self.action);
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &AppStateEffects_oneof_data::processedMidiMessageOutput(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::clientSideUserDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::socketId(ref v) => {
                    my_size += ::protobuf::rt::string_size(4, &v);
                },
                &AppStateEffects_oneof_data::userDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::clientSideUserDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::joinedRoomData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::roomChatHistory(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::chatMessageDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::midiMessageOutputDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::socketIdList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::message(ref v) => {
                    my_size += ::protobuf::rt::string_size(12, &v);
                },
                &AppStateEffects_oneof_data::joinRoomFailResponse(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::roomsList(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::basicRoomDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::roomId(ref v) => {
                    my_size += ::protobuf::rt::string_size(16, &v);
                },
                &AppStateEffects_oneof_data::chatMessageDtoList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::messageId(ref v) => {
                    my_size += ::protobuf::rt::string_size(18, &v);
                },
                &AppStateEffects_oneof_data::welcomeDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::clientValidationErrorList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::roomSettings(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::roomFullDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::userDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::audioChannel(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::slotMode(v) => {
                    my_size += ::protobuf::rt::enum_size(25, v);
                },
                &AppStateEffects_oneof_data::int32Value(v) => {
                    my_size += ::protobuf::rt::value_size(26, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &AppStateEffects_oneof_data::uint32Value(v) => {
                    my_size += ::protobuf::rt::value_size(27, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &AppStateEffects_oneof_data::boolValue(v) => {
                    my_size += 3;
                },
                &AppStateEffects_oneof_data::userClientDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::instrumentsList(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::loadRoomStageDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::bytesPayload(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::stringValue(ref v) => {
                    my_size += ::protobuf::rt::string_size(33, &v);
                },
                &AppStateEffects_oneof_data::appPianoKey(ref v) => {
                    let len = v.compute_size();
                    my_size += 3 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::appPianoPedal(ref v) => {
                    let len = v.compute_size();
                    my_size += 3 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::appRenderableEntity(ref v) => {
                    let len = v.compute_size();
                    my_size += 3 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::appPageLoaderDetail(ref v) => {
                    let len = v.compute_size();
                    my_size += 3 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::appNotificationConfig(ref v) => {
                    let len = v.compute_size();
                    my_size += 3 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::appSettings(ref v) => {
                    let len = v.compute_size();
                    my_size += 3 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::midiSequencerEvent(ref v) => {
                    let len = v.compute_size();
                    my_size += 3 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::appMidiTrack(ref v) => {
                    let len = v.compute_size();
                    my_size += 3 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AppStateEffects_oneof_data::appRoomStageLoaded(ref v) => {
                    let len = v.compute_size();
                    my_size += 3 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._sourceSocketID {
            match v {
                &AppStateEffects_oneof__sourceSocketID::sourceSocketID(ref v) => {
                    my_size += ::protobuf::rt::string_size(999, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.action != AppStateEffects_Action::Unknown {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.action))?;
        }
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &AppStateEffects_oneof_data::processedMidiMessageOutput(ref v) => {
                    os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::clientSideUserDto(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::socketId(ref v) => {
                    os.write_string(4, v)?;
                },
                &AppStateEffects_oneof_data::userDtoList(ref v) => {
                    os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::clientSideUserDtoList(ref v) => {
                    os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::joinedRoomData(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::roomChatHistory(ref v) => {
                    os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::chatMessageDto(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::midiMessageOutputDto(ref v) => {
                    os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::socketIdList(ref v) => {
                    os.write_tag(11, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::message(ref v) => {
                    os.write_string(12, v)?;
                },
                &AppStateEffects_oneof_data::joinRoomFailResponse(ref v) => {
                    os.write_tag(13, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::roomsList(ref v) => {
                    os.write_tag(14, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::basicRoomDto(ref v) => {
                    os.write_tag(15, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::roomId(ref v) => {
                    os.write_string(16, v)?;
                },
                &AppStateEffects_oneof_data::chatMessageDtoList(ref v) => {
                    os.write_tag(17, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::messageId(ref v) => {
                    os.write_string(18, v)?;
                },
                &AppStateEffects_oneof_data::welcomeDto(ref v) => {
                    os.write_tag(19, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::clientValidationErrorList(ref v) => {
                    os.write_tag(20, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::roomSettings(ref v) => {
                    os.write_tag(21, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::roomFullDetails(ref v) => {
                    os.write_tag(22, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::userDto(ref v) => {
                    os.write_tag(23, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::audioChannel(ref v) => {
                    os.write_tag(24, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::slotMode(v) => {
                    os.write_enum(25, ::protobuf::ProtobufEnum::value(&v))?;
                },
                &AppStateEffects_oneof_data::int32Value(v) => {
                    os.write_int32(26, v)?;
                },
                &AppStateEffects_oneof_data::uint32Value(v) => {
                    os.write_uint32(27, v)?;
                },
                &AppStateEffects_oneof_data::boolValue(v) => {
                    os.write_bool(28, v)?;
                },
                &AppStateEffects_oneof_data::userClientDto(ref v) => {
                    os.write_tag(29, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::instrumentsList(ref v) => {
                    os.write_tag(30, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::loadRoomStageDetails(ref v) => {
                    os.write_tag(31, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::bytesPayload(ref v) => {
                    os.write_tag(32, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::stringValue(ref v) => {
                    os.write_string(33, v)?;
                },
                &AppStateEffects_oneof_data::appPianoKey(ref v) => {
                    os.write_tag(5000, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::appPianoPedal(ref v) => {
                    os.write_tag(5001, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::appRenderableEntity(ref v) => {
                    os.write_tag(5002, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::appPageLoaderDetail(ref v) => {
                    os.write_tag(5003, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::appNotificationConfig(ref v) => {
                    os.write_tag(5004, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::appSettings(ref v) => {
                    os.write_tag(5005, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::midiSequencerEvent(ref v) => {
                    os.write_tag(5006, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::appMidiTrack(ref v) => {
                    os.write_tag(5007, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AppStateEffects_oneof_data::appRoomStageLoaded(ref v) => {
                    os.write_tag(5008, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._sourceSocketID {
            match v {
                &AppStateEffects_oneof__sourceSocketID::sourceSocketID(ref v) => {
                    os.write_string(999, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AppStateEffects {
        AppStateEffects::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<AppStateEffects_Action>>(
                "action",
                |m: &AppStateEffects| { &m.action },
                |m: &mut AppStateEffects| { &mut m.action },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "sourceSocketID",
                AppStateEffects::has_sourceSocketID,
                AppStateEffects::get_sourceSocketID,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AppStateEffects_ProcessedMidiMessageOutput>(
                "processedMidiMessageOutput",
                AppStateEffects::has_processedMidiMessageOutput,
                AppStateEffects::get_processedMidiMessageOutput,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::ClientSideUserDto>(
                "clientSideUserDto",
                AppStateEffects::has_clientSideUserDto,
                AppStateEffects::get_clientSideUserDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "socketId",
                AppStateEffects::has_socketId,
                AppStateEffects::get_socketId,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::UserDtoList>(
                "userDtoList",
                AppStateEffects::has_userDtoList,
                AppStateEffects::get_userDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::ClientSideUserDtoList>(
                "clientSideUserDtoList",
                AppStateEffects::has_clientSideUserDtoList,
                AppStateEffects::get_clientSideUserDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::JoinedRoomData>(
                "joinedRoomData",
                AppStateEffects::has_joinedRoomData,
                AppStateEffects::get_joinedRoomData,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::RoomChatHistory>(
                "roomChatHistory",
                AppStateEffects::has_roomChatHistory,
                AppStateEffects::get_roomChatHistory,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::ChatMessageDto>(
                "chatMessageDto",
                AppStateEffects::has_chatMessageDto,
                AppStateEffects::get_chatMessageDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::MidiMessageOutputDto>(
                "midiMessageOutputDto",
                AppStateEffects::has_midiMessageOutputDto,
                AppStateEffects::get_midiMessageOutputDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::SocketIdList>(
                "socketIdList",
                AppStateEffects::has_socketIdList,
                AppStateEffects::get_socketIdList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "message",
                AppStateEffects::has_message,
                AppStateEffects::get_message,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::CommandResponse_JoinRoomFailResponse>(
                "joinRoomFailResponse",
                AppStateEffects::has_joinRoomFailResponse,
                AppStateEffects::get_joinRoomFailResponse,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::CommandResponse_RoomsList>(
                "roomsList",
                AppStateEffects::has_roomsList,
                AppStateEffects::get_roomsList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::BasicRoomDto>(
                "basicRoomDto",
                AppStateEffects::has_basicRoomDto,
                AppStateEffects::get_basicRoomDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "roomId",
                AppStateEffects::has_roomId,
                AppStateEffects::get_roomId,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::ChatMessageDtoList>(
                "chatMessageDtoList",
                AppStateEffects::has_chatMessageDtoList,
                AppStateEffects::get_chatMessageDtoList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "messageId",
                AppStateEffects::has_messageId,
                AppStateEffects::get_messageId,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::WelcomeDto>(
                "welcomeDto",
                AppStateEffects::has_welcomeDto,
                AppStateEffects::get_welcomeDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::client_message::CommandResponse_ClientValidationErrorList>(
                "clientValidationErrorList",
                AppStateEffects::has_clientValidationErrorList,
                AppStateEffects::get_clientValidationErrorList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::RoomSettings>(
                "roomSettings",
                AppStateEffects::has_roomSettings,
                AppStateEffects::get_roomSettings,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::RoomFullDetails>(
                "roomFullDetails",
                AppStateEffects::has_roomFullDetails,
                AppStateEffects::get_roomFullDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::UserDto>(
                "userDto",
                AppStateEffects::has_userDto,
                AppStateEffects::get_userDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::AudioChannel>(
                "audioChannel",
                AppStateEffects::has_audioChannel,
                AppStateEffects::get_audioChannel,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_enum_accessor::<_, super::midi_renditions::ActiveChannelsMode>(
                "slotMode",
                AppStateEffects::has_slotMode,
                AppStateEffects::get_slotMode,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "int32Value",
                AppStateEffects::has_int32Value,
                AppStateEffects::get_int32Value,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "uint32Value",
                AppStateEffects::has_uint32Value,
                AppStateEffects::get_uint32Value,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "boolValue",
                AppStateEffects::has_boolValue,
                AppStateEffects::get_boolValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::UserClientDto>(
                "userClientDto",
                AppStateEffects::has_userClientDto,
                AppStateEffects::get_userClientDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::midi_renditions::InstrumentsList>(
                "instrumentsList",
                AppStateEffects::has_instrumentsList,
                AppStateEffects::get_instrumentsList,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AppStateEffects_LoadRoomStageDetails>(
                "loadRoomStageDetails",
                AppStateEffects::has_loadRoomStageDetails,
                AppStateEffects::get_loadRoomStageDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AppStateEffects_BytesPayload>(
                "bytesPayload",
                AppStateEffects::has_bytesPayload,
                AppStateEffects::get_bytesPayload,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "stringValue",
                AppStateEffects::has_stringValue,
                AppStateEffects::get_stringValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppPianoKey>(
                "appPianoKey",
                AppStateEffects::has_appPianoKey,
                AppStateEffects::get_appPianoKey,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppPianoPedal>(
                "appPianoPedal",
                AppStateEffects::has_appPianoPedal,
                AppStateEffects::get_appPianoPedal,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppRenderableEntity>(
                "appRenderableEntity",
                AppStateEffects::has_appRenderableEntity,
                AppStateEffects::get_appRenderableEntity,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppPageloaderDetail>(
                "appPageLoaderDetail",
                AppStateEffects::has_appPageLoaderDetail,
                AppStateEffects::get_appPageLoaderDetail,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppNotificationConfig>(
                "appNotificationConfig",
                AppStateEffects::has_appNotificationConfig,
                AppStateEffects::get_appNotificationConfig,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppSettings>(
                "appSettings",
                AppStateEffects::has_appSettings,
                AppStateEffects::get_appSettings,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppMidiSequencerEvent>(
                "midiSequencerEvent",
                AppStateEffects::has_midiSequencerEvent,
                AppStateEffects::get_midiSequencerEvent,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppMidiTrack>(
                "appMidiTrack",
                AppStateEffects::has_appMidiTrack,
                AppStateEffects::get_appMidiTrack,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::pianorhythm_app_renditions::AppRoomStageLoaded>(
                "appRoomStageLoaded",
                AppStateEffects::has_appRoomStageLoaded,
                AppStateEffects::get_appRoomStageLoaded,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AppStateEffects>(
                "AppStateEffects",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AppStateEffects {
        static instance: ::protobuf::rt::LazyV2<AppStateEffects> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AppStateEffects::new)
    }
}

impl ::protobuf::Clear for AppStateEffects {
    fn clear(&mut self) {
        self.action = AppStateEffects_Action::Unknown;
        self._sourceSocketID = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AppStateEffects {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AppStateEffects {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AppStateEffects_ProcessedMidiMessageOutput {
    // message fields
    pub device_name: ::std::string::String,
    pub ms: f32,
    pub delay: f64,
    pub socketId: ::std::string::String,
    pub data: ::protobuf::SingularPtrField<super::client_message::MidiMessageOutputDto_MidiMessageBuffer>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AppStateEffects_ProcessedMidiMessageOutput {
    fn default() -> &'a AppStateEffects_ProcessedMidiMessageOutput {
        <AppStateEffects_ProcessedMidiMessageOutput as ::protobuf::Message>::default_instance()
    }
}

impl AppStateEffects_ProcessedMidiMessageOutput {
    pub fn new() -> AppStateEffects_ProcessedMidiMessageOutput {
        ::std::default::Default::default()
    }

    // string device_name = 1;


    pub fn get_device_name(&self) -> &str {
        &self.device_name
    }
    pub fn clear_device_name(&mut self) {
        self.device_name.clear();
    }

    // Param is passed by value, moved
    pub fn set_device_name(&mut self, v: ::std::string::String) {
        self.device_name = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_device_name(&mut self) -> &mut ::std::string::String {
        &mut self.device_name
    }

    // Take field
    pub fn take_device_name(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.device_name, ::std::string::String::new())
    }

    // float ms = 2;


    pub fn get_ms(&self) -> f32 {
        self.ms
    }
    pub fn clear_ms(&mut self) {
        self.ms = 0.;
    }

    // Param is passed by value, moved
    pub fn set_ms(&mut self, v: f32) {
        self.ms = v;
    }

    // double delay = 3;


    pub fn get_delay(&self) -> f64 {
        self.delay
    }
    pub fn clear_delay(&mut self) {
        self.delay = 0.;
    }

    // Param is passed by value, moved
    pub fn set_delay(&mut self, v: f64) {
        self.delay = v;
    }

    // string socketId = 4;


    pub fn get_socketId(&self) -> &str {
        &self.socketId
    }
    pub fn clear_socketId(&mut self) {
        self.socketId.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketId(&mut self, v: ::std::string::String) {
        self.socketId = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketId(&mut self) -> &mut ::std::string::String {
        &mut self.socketId
    }

    // Take field
    pub fn take_socketId(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketId, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.ServerToClient.Msgs.MidiMessageOutputDto.MidiMessageBuffer data = 5;


    pub fn get_data(&self) -> &super::client_message::MidiMessageOutputDto_MidiMessageBuffer {
        self.data.as_ref().unwrap_or_else(|| <super::client_message::MidiMessageOutputDto_MidiMessageBuffer as ::protobuf::Message>::default_instance())
    }
    pub fn clear_data(&mut self) {
        self.data.clear();
    }

    pub fn has_data(&self) -> bool {
        self.data.is_some()
    }

    // Param is passed by value, moved
    pub fn set_data(&mut self, v: super::client_message::MidiMessageOutputDto_MidiMessageBuffer) {
        self.data = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_data(&mut self) -> &mut super::client_message::MidiMessageOutputDto_MidiMessageBuffer {
        if self.data.is_none() {
            self.data.set_default();
        }
        self.data.as_mut().unwrap()
    }

    // Take field
    pub fn take_data(&mut self) -> super::client_message::MidiMessageOutputDto_MidiMessageBuffer {
        self.data.take().unwrap_or_else(|| super::client_message::MidiMessageOutputDto_MidiMessageBuffer::new())
    }
}

impl ::protobuf::Message for AppStateEffects_ProcessedMidiMessageOutput {
    fn is_initialized(&self) -> bool {
        for v in &self.data {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.device_name)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed32 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_float()?;
                    self.ms = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed64 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_double()?;
                    self.delay = tmp;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketId)?;
                },
                5 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.data)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.device_name.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.device_name);
        }
        if self.ms != 0. {
            my_size += 5;
        }
        if self.delay != 0. {
            my_size += 9;
        }
        if !self.socketId.is_empty() {
            my_size += ::protobuf::rt::string_size(4, &self.socketId);
        }
        if let Some(ref v) = self.data.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.device_name.is_empty() {
            os.write_string(1, &self.device_name)?;
        }
        if self.ms != 0. {
            os.write_float(2, self.ms)?;
        }
        if self.delay != 0. {
            os.write_double(3, self.delay)?;
        }
        if !self.socketId.is_empty() {
            os.write_string(4, &self.socketId)?;
        }
        if let Some(ref v) = self.data.as_ref() {
            os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AppStateEffects_ProcessedMidiMessageOutput {
        AppStateEffects_ProcessedMidiMessageOutput::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "device_name",
                |m: &AppStateEffects_ProcessedMidiMessageOutput| { &m.device_name },
                |m: &mut AppStateEffects_ProcessedMidiMessageOutput| { &mut m.device_name },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeFloat>(
                "ms",
                |m: &AppStateEffects_ProcessedMidiMessageOutput| { &m.ms },
                |m: &mut AppStateEffects_ProcessedMidiMessageOutput| { &mut m.ms },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeDouble>(
                "delay",
                |m: &AppStateEffects_ProcessedMidiMessageOutput| { &m.delay },
                |m: &mut AppStateEffects_ProcessedMidiMessageOutput| { &mut m.delay },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketId",
                |m: &AppStateEffects_ProcessedMidiMessageOutput| { &m.socketId },
                |m: &mut AppStateEffects_ProcessedMidiMessageOutput| { &mut m.socketId },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::client_message::MidiMessageOutputDto_MidiMessageBuffer>>(
                "data",
                |m: &AppStateEffects_ProcessedMidiMessageOutput| { &m.data },
                |m: &mut AppStateEffects_ProcessedMidiMessageOutput| { &mut m.data },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AppStateEffects_ProcessedMidiMessageOutput>(
                "AppStateEffects.ProcessedMidiMessageOutput",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AppStateEffects_ProcessedMidiMessageOutput {
        static instance: ::protobuf::rt::LazyV2<AppStateEffects_ProcessedMidiMessageOutput> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AppStateEffects_ProcessedMidiMessageOutput::new)
    }
}

impl ::protobuf::Clear for AppStateEffects_ProcessedMidiMessageOutput {
    fn clear(&mut self) {
        self.device_name.clear();
        self.ms = 0.;
        self.delay = 0.;
        self.socketId.clear();
        self.data.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AppStateEffects_ProcessedMidiMessageOutput {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AppStateEffects_ProcessedMidiMessageOutput {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AppStateEffects_LoadRoomStageDetails {
    // message fields
    pub roomStage: super::room_renditions::RoomStages,
    pub roomType: super::room_renditions::RoomType,
    // message oneof groups
    pub _roomStageDetails: ::std::option::Option<AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AppStateEffects_LoadRoomStageDetails {
    fn default() -> &'a AppStateEffects_LoadRoomStageDetails {
        <AppStateEffects_LoadRoomStageDetails as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails {
    roomStageDetails(super::room_renditions::RoomStageDetails),
}

impl AppStateEffects_LoadRoomStageDetails {
    pub fn new() -> AppStateEffects_LoadRoomStageDetails {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStages roomStage = 1;


    pub fn get_roomStage(&self) -> super::room_renditions::RoomStages {
        self.roomStage
    }
    pub fn clear_roomStage(&mut self) {
        self.roomStage = super::room_renditions::RoomStages::UNKNOWN;
    }

    // Param is passed by value, moved
    pub fn set_roomStage(&mut self, v: super::room_renditions::RoomStages) {
        self.roomStage = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomType roomType = 2;


    pub fn get_roomType(&self) -> super::room_renditions::RoomType {
        self.roomType
    }
    pub fn clear_roomType(&mut self) {
        self.roomType = super::room_renditions::RoomType::Lobby;
    }

    // Param is passed by value, moved
    pub fn set_roomType(&mut self, v: super::room_renditions::RoomType) {
        self.roomType = v;
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStageDetails roomStageDetails = 3;


    pub fn get_roomStageDetails(&self) -> &super::room_renditions::RoomStageDetails {
        match self._roomStageDetails {
            ::std::option::Option::Some(AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(ref v)) => v,
            _ => <super::room_renditions::RoomStageDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomStageDetails(&mut self) {
        self._roomStageDetails = ::std::option::Option::None;
    }

    pub fn has_roomStageDetails(&self) -> bool {
        match self._roomStageDetails {
            ::std::option::Option::Some(AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomStageDetails(&mut self, v: super::room_renditions::RoomStageDetails) {
        self._roomStageDetails = ::std::option::Option::Some(AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomStageDetails(&mut self) -> &mut super::room_renditions::RoomStageDetails {
        if let ::std::option::Option::Some(AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(_)) = self._roomStageDetails {
        } else {
            self._roomStageDetails = ::std::option::Option::Some(AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(super::room_renditions::RoomStageDetails::new()));
        }
        match self._roomStageDetails {
            ::std::option::Option::Some(AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomStageDetails(&mut self) -> super::room_renditions::RoomStageDetails {
        if self.has_roomStageDetails() {
            match self._roomStageDetails.take() {
                ::std::option::Option::Some(AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::RoomStageDetails::new()
        }
    }
}

impl ::protobuf::Message for AppStateEffects_LoadRoomStageDetails {
    fn is_initialized(&self) -> bool {
        if let Some(AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(ref v)) = self._roomStageDetails {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.roomStage, 1, &mut self.unknown_fields)?
                },
                2 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.roomType, 2, &mut self.unknown_fields)?
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._roomStageDetails = ::std::option::Option::Some(AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.roomStage != super::room_renditions::RoomStages::UNKNOWN {
            my_size += ::protobuf::rt::enum_size(1, self.roomStage);
        }
        if self.roomType != super::room_renditions::RoomType::Lobby {
            my_size += ::protobuf::rt::enum_size(2, self.roomType);
        }
        if let ::std::option::Option::Some(ref v) = self._roomStageDetails {
            match v {
                &AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.roomStage != super::room_renditions::RoomStages::UNKNOWN {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.roomStage))?;
        }
        if self.roomType != super::room_renditions::RoomType::Lobby {
            os.write_enum(2, ::protobuf::ProtobufEnum::value(&self.roomType))?;
        }
        if let ::std::option::Option::Some(ref v) = self._roomStageDetails {
            match v {
                &AppStateEffects_LoadRoomStageDetails_oneof__roomStageDetails::roomStageDetails(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AppStateEffects_LoadRoomStageDetails {
        AppStateEffects_LoadRoomStageDetails::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<super::room_renditions::RoomStages>>(
                "roomStage",
                |m: &AppStateEffects_LoadRoomStageDetails| { &m.roomStage },
                |m: &mut AppStateEffects_LoadRoomStageDetails| { &mut m.roomStage },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<super::room_renditions::RoomType>>(
                "roomType",
                |m: &AppStateEffects_LoadRoomStageDetails| { &m.roomType },
                |m: &mut AppStateEffects_LoadRoomStageDetails| { &mut m.roomType },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::RoomStageDetails>(
                "roomStageDetails",
                AppStateEffects_LoadRoomStageDetails::has_roomStageDetails,
                AppStateEffects_LoadRoomStageDetails::get_roomStageDetails,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AppStateEffects_LoadRoomStageDetails>(
                "AppStateEffects.LoadRoomStageDetails",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AppStateEffects_LoadRoomStageDetails {
        static instance: ::protobuf::rt::LazyV2<AppStateEffects_LoadRoomStageDetails> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AppStateEffects_LoadRoomStageDetails::new)
    }
}

impl ::protobuf::Clear for AppStateEffects_LoadRoomStageDetails {
    fn clear(&mut self) {
        self.roomStage = super::room_renditions::RoomStages::UNKNOWN;
        self.roomType = super::room_renditions::RoomType::Lobby;
        self._roomStageDetails = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AppStateEffects_LoadRoomStageDetails {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AppStateEffects_LoadRoomStageDetails {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AppStateEffects_BytesPayload {
    // message fields
    pub payload: ::std::vec::Vec<u32>,
    // message oneof groups
    pub _id: ::std::option::Option<AppStateEffects_BytesPayload_oneof__id>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AppStateEffects_BytesPayload {
    fn default() -> &'a AppStateEffects_BytesPayload {
        <AppStateEffects_BytesPayload as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AppStateEffects_BytesPayload_oneof__id {
    id(::std::string::String),
}

impl AppStateEffects_BytesPayload {
    pub fn new() -> AppStateEffects_BytesPayload {
        ::std::default::Default::default()
    }

    // repeated uint32 payload = 1;


    pub fn get_payload(&self) -> &[u32] {
        &self.payload
    }
    pub fn clear_payload(&mut self) {
        self.payload.clear();
    }

    // Param is passed by value, moved
    pub fn set_payload(&mut self, v: ::std::vec::Vec<u32>) {
        self.payload = v;
    }

    // Mutable pointer to the field.
    pub fn mut_payload(&mut self) -> &mut ::std::vec::Vec<u32> {
        &mut self.payload
    }

    // Take field
    pub fn take_payload(&mut self) -> ::std::vec::Vec<u32> {
        ::std::mem::replace(&mut self.payload, ::std::vec::Vec::new())
    }

    // string id = 2;


    pub fn get_id(&self) -> &str {
        match self._id {
            ::std::option::Option::Some(AppStateEffects_BytesPayload_oneof__id::id(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_id(&mut self) {
        self._id = ::std::option::Option::None;
    }

    pub fn has_id(&self) -> bool {
        match self._id {
            ::std::option::Option::Some(AppStateEffects_BytesPayload_oneof__id::id(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: ::std::string::String) {
        self._id = ::std::option::Option::Some(AppStateEffects_BytesPayload_oneof__id::id(v))
    }

    // Mutable pointer to the field.
    pub fn mut_id(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AppStateEffects_BytesPayload_oneof__id::id(_)) = self._id {
        } else {
            self._id = ::std::option::Option::Some(AppStateEffects_BytesPayload_oneof__id::id(::std::string::String::new()));
        }
        match self._id {
            ::std::option::Option::Some(AppStateEffects_BytesPayload_oneof__id::id(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_id(&mut self) -> ::std::string::String {
        if self.has_id() {
            match self._id.take() {
                ::std::option::Option::Some(AppStateEffects_BytesPayload_oneof__id::id(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }
}

impl ::protobuf::Message for AppStateEffects_BytesPayload {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_uint32_into(wire_type, is, &mut self.payload)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._id = ::std::option::Option::Some(AppStateEffects_BytesPayload_oneof__id::id(is.read_string()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.payload {
            my_size += ::protobuf::rt::value_size(1, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        if let ::std::option::Option::Some(ref v) = self._id {
            match v {
                &AppStateEffects_BytesPayload_oneof__id::id(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        for v in &self.payload {
            os.write_uint32(1, *v)?;
        };
        if let ::std::option::Option::Some(ref v) = self._id {
            match v {
                &AppStateEffects_BytesPayload_oneof__id::id(ref v) => {
                    os.write_string(2, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AppStateEffects_BytesPayload {
        AppStateEffects_BytesPayload::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                "payload",
                |m: &AppStateEffects_BytesPayload| { &m.payload },
                |m: &mut AppStateEffects_BytesPayload| { &mut m.payload },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "id",
                AppStateEffects_BytesPayload::has_id,
                AppStateEffects_BytesPayload::get_id,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AppStateEffects_BytesPayload>(
                "AppStateEffects.BytesPayload",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AppStateEffects_BytesPayload {
        static instance: ::protobuf::rt::LazyV2<AppStateEffects_BytesPayload> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AppStateEffects_BytesPayload::new)
    }
}

impl ::protobuf::Clear for AppStateEffects_BytesPayload {
    fn clear(&mut self) {
        self.payload.clear();
        self._id = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AppStateEffects_BytesPayload {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AppStateEffects_BytesPayload {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum AppStateEffects_Action {
    Unknown = 0,
    UpdateUser = 1,
    AddUser = 2,
    RemoveUser = 3,
    UsersSet = 4,
    JoinedRoomSuccess = 5,
    UsersTypingSet = 6,
    JoinRoomFailResponse = 7,
    Toast = 8,
    SetRooms = 9,
    AddRoom = 11,
    UpdateRoom = 12,
    DeleteRoom = 13,
    SetRoomChatHistory = 14,
    AddChatMessage = 15,
    EditChatMessage = 16,
    DeleteChatMessage = 17,
    OnWelcome = 18,
    SetChatMessages = 19,
    CreateRoomValidationErrors = 20,
    UpdateRoomValidationErrors = 21,
    SetRoomSettings = 22,
    SetRoomFullDetails = 23,
    SetRoomOwner = 24,
    MidiMessageOutput = 25,
    MuteEveryoneElse = 26,
    SynthSoundfontFailedToLoad = 27,
    SynthChannelUpdated = 28,
    SetSlotMode = 29,
    SetPrimaryChannel = 30,
    ListenToProgramChanges = 31,
    ClientIsMuted = 32,
    DrumChannelIsMuted = 33,
    MousePositionSetsVelocity = 34,
    MaxMultiModeChannels = 35,
    EqualizerEnabled = 36,
    ReverbEnabled = 37,
    ClientUpdated = 38,
    SoundfontPresetsLoaded = 39,
    SetPageLoaderDetails = 40,
    LoadRoomStage = 41,
    AppSettingsUpdated = 42,
    WebsocketDisconnected = 43,
    RoomStageLoaded = 44,
    EmittingNotesToServer = 45,
    ConnectionState = 46,
    UserChatMuted = 47,
    UserNotesMuted = 48,
    RendererPianoKeyUpdate = 1002,
    RendererPianoPedalUpdate = 1003,
    RendererEntityUpdate = 1004,
    RendererMeshLoaded = 1005,
    RendererCameraLock = 1006,
    RendererCameraReset = 1007,
    RendererLoaded = 1008,
    MidiSequencerEvent = 2000,
    MidiSequencerDownloadMidi = 2001,
    MidiSequencerFailedToLoadMidi = 2002,
    AudioApplyVelocityCurve = 3000,
    AppMidiLooperTrackUpdated = 4000,
    AvatarCustomizationUpdated = 5000,
}

impl ::protobuf::ProtobufEnum for AppStateEffects_Action {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<AppStateEffects_Action> {
        match value {
            0 => ::std::option::Option::Some(AppStateEffects_Action::Unknown),
            1 => ::std::option::Option::Some(AppStateEffects_Action::UpdateUser),
            2 => ::std::option::Option::Some(AppStateEffects_Action::AddUser),
            3 => ::std::option::Option::Some(AppStateEffects_Action::RemoveUser),
            4 => ::std::option::Option::Some(AppStateEffects_Action::UsersSet),
            5 => ::std::option::Option::Some(AppStateEffects_Action::JoinedRoomSuccess),
            6 => ::std::option::Option::Some(AppStateEffects_Action::UsersTypingSet),
            7 => ::std::option::Option::Some(AppStateEffects_Action::JoinRoomFailResponse),
            8 => ::std::option::Option::Some(AppStateEffects_Action::Toast),
            9 => ::std::option::Option::Some(AppStateEffects_Action::SetRooms),
            11 => ::std::option::Option::Some(AppStateEffects_Action::AddRoom),
            12 => ::std::option::Option::Some(AppStateEffects_Action::UpdateRoom),
            13 => ::std::option::Option::Some(AppStateEffects_Action::DeleteRoom),
            14 => ::std::option::Option::Some(AppStateEffects_Action::SetRoomChatHistory),
            15 => ::std::option::Option::Some(AppStateEffects_Action::AddChatMessage),
            16 => ::std::option::Option::Some(AppStateEffects_Action::EditChatMessage),
            17 => ::std::option::Option::Some(AppStateEffects_Action::DeleteChatMessage),
            18 => ::std::option::Option::Some(AppStateEffects_Action::OnWelcome),
            19 => ::std::option::Option::Some(AppStateEffects_Action::SetChatMessages),
            20 => ::std::option::Option::Some(AppStateEffects_Action::CreateRoomValidationErrors),
            21 => ::std::option::Option::Some(AppStateEffects_Action::UpdateRoomValidationErrors),
            22 => ::std::option::Option::Some(AppStateEffects_Action::SetRoomSettings),
            23 => ::std::option::Option::Some(AppStateEffects_Action::SetRoomFullDetails),
            24 => ::std::option::Option::Some(AppStateEffects_Action::SetRoomOwner),
            25 => ::std::option::Option::Some(AppStateEffects_Action::MidiMessageOutput),
            26 => ::std::option::Option::Some(AppStateEffects_Action::MuteEveryoneElse),
            27 => ::std::option::Option::Some(AppStateEffects_Action::SynthSoundfontFailedToLoad),
            28 => ::std::option::Option::Some(AppStateEffects_Action::SynthChannelUpdated),
            29 => ::std::option::Option::Some(AppStateEffects_Action::SetSlotMode),
            30 => ::std::option::Option::Some(AppStateEffects_Action::SetPrimaryChannel),
            31 => ::std::option::Option::Some(AppStateEffects_Action::ListenToProgramChanges),
            32 => ::std::option::Option::Some(AppStateEffects_Action::ClientIsMuted),
            33 => ::std::option::Option::Some(AppStateEffects_Action::DrumChannelIsMuted),
            34 => ::std::option::Option::Some(AppStateEffects_Action::MousePositionSetsVelocity),
            35 => ::std::option::Option::Some(AppStateEffects_Action::MaxMultiModeChannels),
            36 => ::std::option::Option::Some(AppStateEffects_Action::EqualizerEnabled),
            37 => ::std::option::Option::Some(AppStateEffects_Action::ReverbEnabled),
            38 => ::std::option::Option::Some(AppStateEffects_Action::ClientUpdated),
            39 => ::std::option::Option::Some(AppStateEffects_Action::SoundfontPresetsLoaded),
            40 => ::std::option::Option::Some(AppStateEffects_Action::SetPageLoaderDetails),
            41 => ::std::option::Option::Some(AppStateEffects_Action::LoadRoomStage),
            42 => ::std::option::Option::Some(AppStateEffects_Action::AppSettingsUpdated),
            43 => ::std::option::Option::Some(AppStateEffects_Action::WebsocketDisconnected),
            44 => ::std::option::Option::Some(AppStateEffects_Action::RoomStageLoaded),
            45 => ::std::option::Option::Some(AppStateEffects_Action::EmittingNotesToServer),
            46 => ::std::option::Option::Some(AppStateEffects_Action::ConnectionState),
            47 => ::std::option::Option::Some(AppStateEffects_Action::UserChatMuted),
            48 => ::std::option::Option::Some(AppStateEffects_Action::UserNotesMuted),
            1002 => ::std::option::Option::Some(AppStateEffects_Action::RendererPianoKeyUpdate),
            1003 => ::std::option::Option::Some(AppStateEffects_Action::RendererPianoPedalUpdate),
            1004 => ::std::option::Option::Some(AppStateEffects_Action::RendererEntityUpdate),
            1005 => ::std::option::Option::Some(AppStateEffects_Action::RendererMeshLoaded),
            1006 => ::std::option::Option::Some(AppStateEffects_Action::RendererCameraLock),
            1007 => ::std::option::Option::Some(AppStateEffects_Action::RendererCameraReset),
            1008 => ::std::option::Option::Some(AppStateEffects_Action::RendererLoaded),
            2000 => ::std::option::Option::Some(AppStateEffects_Action::MidiSequencerEvent),
            2001 => ::std::option::Option::Some(AppStateEffects_Action::MidiSequencerDownloadMidi),
            2002 => ::std::option::Option::Some(AppStateEffects_Action::MidiSequencerFailedToLoadMidi),
            3000 => ::std::option::Option::Some(AppStateEffects_Action::AudioApplyVelocityCurve),
            4000 => ::std::option::Option::Some(AppStateEffects_Action::AppMidiLooperTrackUpdated),
            5000 => ::std::option::Option::Some(AppStateEffects_Action::AvatarCustomizationUpdated),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [AppStateEffects_Action] = &[
            AppStateEffects_Action::Unknown,
            AppStateEffects_Action::UpdateUser,
            AppStateEffects_Action::AddUser,
            AppStateEffects_Action::RemoveUser,
            AppStateEffects_Action::UsersSet,
            AppStateEffects_Action::JoinedRoomSuccess,
            AppStateEffects_Action::UsersTypingSet,
            AppStateEffects_Action::JoinRoomFailResponse,
            AppStateEffects_Action::Toast,
            AppStateEffects_Action::SetRooms,
            AppStateEffects_Action::AddRoom,
            AppStateEffects_Action::UpdateRoom,
            AppStateEffects_Action::DeleteRoom,
            AppStateEffects_Action::SetRoomChatHistory,
            AppStateEffects_Action::AddChatMessage,
            AppStateEffects_Action::EditChatMessage,
            AppStateEffects_Action::DeleteChatMessage,
            AppStateEffects_Action::OnWelcome,
            AppStateEffects_Action::SetChatMessages,
            AppStateEffects_Action::CreateRoomValidationErrors,
            AppStateEffects_Action::UpdateRoomValidationErrors,
            AppStateEffects_Action::SetRoomSettings,
            AppStateEffects_Action::SetRoomFullDetails,
            AppStateEffects_Action::SetRoomOwner,
            AppStateEffects_Action::MidiMessageOutput,
            AppStateEffects_Action::MuteEveryoneElse,
            AppStateEffects_Action::SynthSoundfontFailedToLoad,
            AppStateEffects_Action::SynthChannelUpdated,
            AppStateEffects_Action::SetSlotMode,
            AppStateEffects_Action::SetPrimaryChannel,
            AppStateEffects_Action::ListenToProgramChanges,
            AppStateEffects_Action::ClientIsMuted,
            AppStateEffects_Action::DrumChannelIsMuted,
            AppStateEffects_Action::MousePositionSetsVelocity,
            AppStateEffects_Action::MaxMultiModeChannels,
            AppStateEffects_Action::EqualizerEnabled,
            AppStateEffects_Action::ReverbEnabled,
            AppStateEffects_Action::ClientUpdated,
            AppStateEffects_Action::SoundfontPresetsLoaded,
            AppStateEffects_Action::SetPageLoaderDetails,
            AppStateEffects_Action::LoadRoomStage,
            AppStateEffects_Action::AppSettingsUpdated,
            AppStateEffects_Action::WebsocketDisconnected,
            AppStateEffects_Action::RoomStageLoaded,
            AppStateEffects_Action::EmittingNotesToServer,
            AppStateEffects_Action::ConnectionState,
            AppStateEffects_Action::UserChatMuted,
            AppStateEffects_Action::UserNotesMuted,
            AppStateEffects_Action::RendererPianoKeyUpdate,
            AppStateEffects_Action::RendererPianoPedalUpdate,
            AppStateEffects_Action::RendererEntityUpdate,
            AppStateEffects_Action::RendererMeshLoaded,
            AppStateEffects_Action::RendererCameraLock,
            AppStateEffects_Action::RendererCameraReset,
            AppStateEffects_Action::RendererLoaded,
            AppStateEffects_Action::MidiSequencerEvent,
            AppStateEffects_Action::MidiSequencerDownloadMidi,
            AppStateEffects_Action::MidiSequencerFailedToLoadMidi,
            AppStateEffects_Action::AudioApplyVelocityCurve,
            AppStateEffects_Action::AppMidiLooperTrackUpdated,
            AppStateEffects_Action::AvatarCustomizationUpdated,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<AppStateEffects_Action>("AppStateEffects.Action", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for AppStateEffects_Action {
}

impl ::std::default::Default for AppStateEffects_Action {
    fn default() -> Self {
        AppStateEffects_Action::Unknown
    }
}

impl ::protobuf::reflect::ProtobufValue for AppStateEffects_Action {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x19pianorhythm-effects.proto\x12\x1bPianoRhythm.AppStateEffects\x1a\
    \x15room-renditions.proto\x1a\x15user-renditions.proto\x1a\x15midi-rendi\
    tions.proto\x1a\x14client-message.proto\x1a\x20pianorhythm-app-rendition\
    s.proto\"\x91.\n\x0fAppStateEffects\x12K\n\x06action\x18\x01\x20\x01(\
    \x0e23.PianoRhythm.AppStateEffects.AppStateEffects.ActionR\x06action\x12\
    ,\n\x0esourceSocketID\x18\xe7\x07\x20\x01(\tH\x01R\x0esourceSocketID\x88\
    \x01\x01\x12\x89\x01\n\x1aprocessedMidiMessageOutput\x18\x02\x20\x01(\
    \x0b2G.PianoRhythm.AppStateEffects.AppStateEffects.ProcessedMidiMessageO\
    utputH\0R\x1aprocessedMidiMessageOutput\x12z\n\x11clientSideUserDto\x18\
    \x03\x20\x01(\x0b2J.PianoRhythm.Serialization.ServerToClient.UserRenditi\
    ons.ClientSideUserDtoH\0R\x11clientSideUserDto\x12\x1c\n\x08socketId\x18\
    \x04\x20\x01(\tH\0R\x08socketId\x12^\n\x0buserDtoList\x18\x05\x20\x01(\
    \x0b2:.PianoRhythm.Serialization.ServerToClient.Msgs.UserDtoListH\0R\x0b\
    userDtoList\x12|\n\x15clientSideUserDtoList\x18\x06\x20\x01(\x0b2D.Piano\
    Rhythm.Serialization.ServerToClient.Msgs.ClientSideUserDtoListH\0R\x15cl\
    ientSideUserDtoList\x12g\n\x0ejoinedRoomData\x18\x07\x20\x01(\x0b2=.Pian\
    oRhythm.Serialization.ServerToClient.Msgs.JoinedRoomDataH\0R\x0ejoinedRo\
    omData\x12j\n\x0froomChatHistory\x18\x08\x20\x01(\x0b2>.PianoRhythm.Seri\
    alization.ServerToClient.Msgs.RoomChatHistoryH\0R\x0froomChatHistory\x12\
    g\n\x0echatMessageDto\x18\t\x20\x01(\x0b2=.PianoRhythm.Serialization.Ser\
    verToClient.Msgs.ChatMessageDtoH\0R\x0echatMessageDto\x12y\n\x14midiMess\
    ageOutputDto\x18\n\x20\x01(\x0b2C.PianoRhythm.Serialization.ServerToClie\
    nt.Msgs.MidiMessageOutputDtoH\0R\x14midiMessageOutputDto\x12a\n\x0csocke\
    tIdList\x18\x0b\x20\x01(\x0b2;.PianoRhythm.Serialization.ServerToClient.\
    Msgs.SocketIdListH\0R\x0csocketIdList\x12\x1a\n\x07message\x18\x0c\x20\
    \x01(\tH\0R\x07message\x12\x89\x01\n\x14joinRoomFailResponse\x18\r\x20\
    \x01(\x0b2S.PianoRhythm.Serialization.ServerToClient.Msgs.CommandRespons\
    e.JoinRoomFailResponseH\0R\x14joinRoomFailResponse\x12h\n\troomsList\x18\
    \x0e\x20\x01(\x0b2H.PianoRhythm.Serialization.ServerToClient.Msgs.Comman\
    dResponse.RoomsListH\0R\troomsList\x12\\\n\x0cbasicRoomDto\x18\x0f\x20\
    \x01(\x0b26.PianoRhythm.Serialization.RoomRenditions.BasicRoomDtoH\0R\
    \x0cbasicRoomDto\x12\x18\n\x06roomId\x18\x10\x20\x01(\tH\0R\x06roomId\
    \x12s\n\x12chatMessageDtoList\x18\x11\x20\x01(\x0b2A.PianoRhythm.Seriali\
    zation.ServerToClient.Msgs.ChatMessageDtoListH\0R\x12chatMessageDtoList\
    \x12\x1e\n\tmessageId\x18\x12\x20\x01(\tH\0R\tmessageId\x12[\n\nwelcomeD\
    to\x18\x13\x20\x01(\x0b29.PianoRhythm.Serialization.ServerToClient.Msgs.\
    WelcomeDtoH\0R\nwelcomeDto\x12\x98\x01\n\x19clientValidationErrorList\
    \x18\x14\x20\x01(\x0b2X.PianoRhythm.Serialization.ServerToClient.Msgs.Co\
    mmandResponse.ClientValidationErrorListH\0R\x19clientValidationErrorList\
    \x12\\\n\x0croomSettings\x18\x15\x20\x01(\x0b26.PianoRhythm.Serializatio\
    n.RoomRenditions.RoomSettingsH\0R\x0croomSettings\x12e\n\x0froomFullDeta\
    ils\x18\x16\x20\x01(\x0b29.PianoRhythm.Serialization.RoomRenditions.Room\
    FullDetailsH\0R\x0froomFullDetails\x12\\\n\x07userDto\x18\x17\x20\x01(\
    \<EMAIL>\
    \0R\x07userDto\x12W\n\x0caudioChannel\x18\x18\x20\x01(\x0b21.PianoRhythm\
    .Serialization.Midi.Msgs.AudioChannelH\0R\x0caudioChannel\x12U\n\x08slot\
    Mode\x18\x19\x20\x01(\x0e27.PianoRhythm.Serialization.Midi.Msgs.ActiveCh\
    annelsModeH\0R\x08slotMode\x12\x20\n\nint32Value\x18\x1a\x20\x01(\x05H\0\
    R\nint32Value\x12\"\n\x0buint32Value\x18\x1b\x20\x01(\rH\0R\x0buint32Val\
    ue\x12\x1e\n\tboolValue\x18\x1c\x20\x01(\x08H\0R\tboolValue\x12n\n\ruser\
    ClientDto\x18\x1d\x20\x01(\x0b2F.PianoRhythm.Serialization.ServerToClien\
    t.UserRenditions.UserClientDtoH\0R\ruserClientDto\x12`\n\x0finstrumentsL\
    ist\x18\x1e\x20\x01(\x0b24.PianoRhythm.Serialization.Midi.Msgs.Instrumen\
    tsListH\0R\x0finstrumentsList\x12w\n\x14loadRoomStageDetails\x18\x1f\x20\
    \x01(\x0b2A.PianoRhythm.AppStateEffects.AppStateEffects.LoadRoomStageDet\
    ailsH\0R\x14loadRoomStageDetails\x12_\n\x0cbytesPayload\x18\x20\x20\x01(\
    \x0b29.PianoRhythm.AppStateEffects.AppStateEffects.BytesPayloadH\0R\x0cb\
    ytesPayload\x12\"\n\x0bstringValue\x18!\x20\x01(\tH\0R\x0bstringValue\
    \x12K\n\x0bappPianoKey\x18\x88'\x20\x01(\x0b2&.PianoRhythm.AppRenditions\
    .AppPianoKeyH\0R\x0bappPianoKey\x12Q\n\rappPianoPedal\x18\x89'\x20\x01(\
    \x0b2(.PianoRhythm.AppRenditions.AppPianoPedalH\0R\rappPianoPedal\x12c\n\
    \x13appRenderableEntity\x18\x8a'\x20\x01(\x0b2..PianoRhythm.AppRendition\
    s.AppRenderableEntityH\0R\x13appRenderableEntity\x12c\n\x13appPageLoader\
    Detail\x18\x8b'\x20\x01(\x0b2..PianoRhythm.AppRenditions.AppPageloaderDe\
    tailH\0R\x13appPageLoaderDetail\x12i\n\x15appNotificationConfig\x18\x8c'\
    \x20\x01(\x0b20.PianoRhythm.AppRenditions.AppNotificationConfigH\0R\x15a\
    ppNotificationConfig\x12K\n\x0bappSettings\x18\x8d'\x20\x01(\x0b2&.Piano\
    Rhythm.AppRenditions.AppSettingsH\0R\x0bappSettings\x12c\n\x12midiSequen\
    cerEvent\x18\x8e'\x20\x01(\x0b20.PianoRhythm.AppRenditions.AppMidiSequen\
    cerEventH\0R\x12midiSequencerEvent\x12N\n\x0cappMidiTrack\x18\x8f'\x20\
    \x01(\x0b2'.PianoRhythm.AppRenditions.AppMidiTrackH\0R\x0cappMidiTrack\
    \x12`\n\x12appRoomStageLoaded\x18\x90'\x20\x01(\x0b2-.PianoRhythm.AppRen\
    ditions.AppRoomStageLoadedH\0R\x12appRoomStageLoaded\x1a\xea\x01\n\x1aPr\
    ocessedMidiMessageOutput\x12\x1f\n\x0bdevice_name\x18\x01\x20\x01(\tR\nd\
    eviceName\x12\x0e\n\x02ms\x18\x02\x20\x01(\x02R\x02ms\x12\x14\n\x05delay\
    \x18\x03\x20\x01(\x01R\x05delay\x12\x1a\n\x08socketId\x18\x04\x20\x01(\t\
    R\x08socketId\x12i\n\x04data\x18\x05\x20\x01(\x0b2U.PianoRhythm.Serializ\
    ation.ServerToClient.Msgs.MidiMessageOutputDto.MidiMessageBufferR\x04dat\
    a\x1a\xbc\x02\n\x14LoadRoomStageDetails\x12R\n\troomStage\x18\x01\x20\
    \x01(\x0e24.PianoRhythm.Serialization.RoomRenditions.RoomStagesR\troomSt\
    age\x12N\n\x08roomType\x18\x02\x20\x01(\x0e22.PianoRhythm.Serialization.\
    RoomRenditions.RoomTypeR\x08roomType\x12k\n\x10roomStageDetails\x18\x03\
    \x20\x01(\x0b2:.PianoRhythm.Serialization.RoomRenditions.RoomStageDetail\
    sH\0R\x10roomStageDetails\x88\x01\x01B\x13\n\x11_roomStageDetails\x1aD\n\
    \x0cBytesPayload\x12\x18\n\x07payload\x18\x01\x20\x03(\rR\x07payload\x12\
    \x13\n\x02id\x18\x02\x20\x01(\tH\0R\x02id\x88\x01\x01B\x05\n\x03_id\"\
    \xfc\n\n\x06Action\x12\x0b\n\x07Unknown\x10\0\x12\x0e\n\nUpdateUser\x10\
    \x01\x12\x0b\n\x07AddUser\x10\x02\x12\x0e\n\nRemoveUser\x10\x03\x12\x0c\
    \n\x08UsersSet\x10\x04\x12\x15\n\x11JoinedRoomSuccess\x10\x05\x12\x12\n\
    \x0eUsersTypingSet\x10\x06\x12\x18\n\x14JoinRoomFailResponse\x10\x07\x12\
    \t\n\x05Toast\x10\x08\x12\x0c\n\x08SetRooms\x10\t\x12\x0b\n\x07AddRoom\
    \x10\x0b\x12\x0e\n\nUpdateRoom\x10\x0c\x12\x0e\n\nDeleteRoom\x10\r\x12\
    \x16\n\x12SetRoomChatHistory\x10\x0e\x12\x12\n\x0eAddChatMessage\x10\x0f\
    \x12\x13\n\x0fEditChatMessage\x10\x10\x12\x15\n\x11DeleteChatMessage\x10\
    \x11\x12\r\n\tOnWelcome\x10\x12\x12\x13\n\x0fSetChatMessages\x10\x13\x12\
    \x1e\n\x1aCreateRoomValidationErrors\x10\x14\x12\x1e\n\x1aUpdateRoomVali\
    dationErrors\x10\x15\x12\x13\n\x0fSetRoomSettings\x10\x16\x12\x16\n\x12S\
    etRoomFullDetails\x10\x17\x12\x10\n\x0cSetRoomOwner\x10\x18\x12\x15\n\
    \x11MidiMessageOutput\x10\x19\x12\x14\n\x10MuteEveryoneElse\x10\x1a\x12\
    \x1e\n\x1aSynthSoundfontFailedToLoad\x10\x1b\x12\x17\n\x13SynthChannelUp\
    dated\x10\x1c\x12\x0f\n\x0bSetSlotMode\x10\x1d\x12\x15\n\x11SetPrimaryCh\
    annel\x10\x1e\x12\x1a\n\x16ListenToProgramChanges\x10\x1f\x12\x11\n\rCli\
    entIsMuted\x10\x20\x12\x16\n\x12DrumChannelIsMuted\x10!\x12\x1d\n\x19Mou\
    sePositionSetsVelocity\x10\"\x12\x18\n\x14MaxMultiModeChannels\x10#\x12\
    \x14\n\x10EqualizerEnabled\x10$\x12\x11\n\rReverbEnabled\x10%\x12\x11\n\
    \rClientUpdated\x10&\x12\x1a\n\x16SoundfontPresetsLoaded\x10'\x12\x18\n\
    \x14SetPageLoaderDetails\x10(\x12\x11\n\rLoadRoomStage\x10)\x12\x16\n\
    \x12AppSettingsUpdated\x10*\x12\x19\n\x15WebsocketDisconnected\x10+\x12\
    \x13\n\x0fRoomStageLoaded\x10,\x12\x19\n\x15EmittingNotesToServer\x10-\
    \x12\x13\n\x0fConnectionState\x10.\x12\x11\n\rUserChatMuted\x10/\x12\x12\
    \n\x0eUserNotesMuted\x100\x12\x1b\n\x16RendererPianoKeyUpdate\x10\xea\
    \x07\x12\x1d\n\x18RendererPianoPedalUpdate\x10\xeb\x07\x12\x19\n\x14Rend\
    ererEntityUpdate\x10\xec\x07\x12\x17\n\x12RendererMeshLoaded\x10\xed\x07\
    \x12\x17\n\x12RendererCameraLock\x10\xee\x07\x12\x18\n\x13RendererCamera\
    Reset\x10\xef\x07\x12\x13\n\x0eRendererLoaded\x10\xf0\x07\x12\x17\n\x12M\
    idiSequencerEvent\x10\xd0\x0f\x12\x1e\n\x19MidiSequencerDownloadMidi\x10\
    \xd1\x0f\x12\"\n\x1dMidiSequencerFailedToLoadMidi\x10\xd2\x0f\x12\x1c\n\
    \x17AudioApplyVelocityCurve\x10\xb8\x17\x12\x1e\n\x19AppMidiLooperTrackU\
    pdated\x10\xa0\x1f\x12\x1f\n\x1aAvatarCustomizationUpdated\x10\x88'B\x06\
    \n\x04dataB\x11\n\x0f_sourceSocketIDb\x06proto3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
