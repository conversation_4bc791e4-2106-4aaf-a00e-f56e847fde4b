// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `server-message.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>ult)]
pub struct ChatMessageInputData {
    // message fields
    pub text: ::std::string::String,
    pub options: ::protobuf::SingularPtrField<ChatMessageInputData_ChatMessageDataOptions>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ChatMessageInputData {
    fn default() -> &'a ChatMessageInputData {
        <ChatMessageInputData as ::protobuf::Message>::default_instance()
    }
}

impl ChatMessageInputData {
    pub fn new() -> ChatMessageInputData {
        ::std::default::Default::default()
    }

    // string text = 1;


    pub fn get_text(&self) -> &str {
        &self.text
    }
    pub fn clear_text(&mut self) {
        self.text.clear();
    }

    // Param is passed by value, moved
    pub fn set_text(&mut self, v: ::std::string::String) {
        self.text = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_text(&mut self) -> &mut ::std::string::String {
        &mut self.text
    }

    // Take field
    pub fn take_text(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.text, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ChatMessageInputData.ChatMessageDataOptions options = 2;


    pub fn get_options(&self) -> &ChatMessageInputData_ChatMessageDataOptions {
        self.options.as_ref().unwrap_or_else(|| <ChatMessageInputData_ChatMessageDataOptions as ::protobuf::Message>::default_instance())
    }
    pub fn clear_options(&mut self) {
        self.options.clear();
    }

    pub fn has_options(&self) -> bool {
        self.options.is_some()
    }

    // Param is passed by value, moved
    pub fn set_options(&mut self, v: ChatMessageInputData_ChatMessageDataOptions) {
        self.options = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_options(&mut self) -> &mut ChatMessageInputData_ChatMessageDataOptions {
        if self.options.is_none() {
            self.options.set_default();
        }
        self.options.as_mut().unwrap()
    }

    // Take field
    pub fn take_options(&mut self) -> ChatMessageInputData_ChatMessageDataOptions {
        self.options.take().unwrap_or_else(|| ChatMessageInputData_ChatMessageDataOptions::new())
    }
}

impl ::protobuf::Message for ChatMessageInputData {
    fn is_initialized(&self) -> bool {
        for v in &self.options {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.text)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.options)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.text.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.text);
        }
        if let Some(ref v) = self.options.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.text.is_empty() {
            os.write_string(1, &self.text)?;
        }
        if let Some(ref v) = self.options.as_ref() {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ChatMessageInputData {
        ChatMessageInputData::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "text",
                |m: &ChatMessageInputData| { &m.text },
                |m: &mut ChatMessageInputData| { &mut m.text },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ChatMessageInputData_ChatMessageDataOptions>>(
                "options",
                |m: &ChatMessageInputData| { &m.options },
                |m: &mut ChatMessageInputData| { &mut m.options },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ChatMessageInputData>(
                "ChatMessageInputData",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ChatMessageInputData {
        static instance: ::protobuf::rt::LazyV2<ChatMessageInputData> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ChatMessageInputData::new)
    }
}

impl ::protobuf::Clear for ChatMessageInputData {
    fn clear(&mut self) {
        self.text.clear();
        self.options.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ChatMessageInputData {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ChatMessageInputData {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ChatMessageInputData_ChatMessageDataOptions {
    // message fields
    pub syncToDiscord: bool,
    pub isFromPlugin: bool,
    pub messageReplyID: ::std::string::String,
    pub messageEditID: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ChatMessageInputData_ChatMessageDataOptions {
    fn default() -> &'a ChatMessageInputData_ChatMessageDataOptions {
        <ChatMessageInputData_ChatMessageDataOptions as ::protobuf::Message>::default_instance()
    }
}

impl ChatMessageInputData_ChatMessageDataOptions {
    pub fn new() -> ChatMessageInputData_ChatMessageDataOptions {
        ::std::default::Default::default()
    }

    // bool syncToDiscord = 1;


    pub fn get_syncToDiscord(&self) -> bool {
        self.syncToDiscord
    }
    pub fn clear_syncToDiscord(&mut self) {
        self.syncToDiscord = false;
    }

    // Param is passed by value, moved
    pub fn set_syncToDiscord(&mut self, v: bool) {
        self.syncToDiscord = v;
    }

    // bool isFromPlugin = 2;


    pub fn get_isFromPlugin(&self) -> bool {
        self.isFromPlugin
    }
    pub fn clear_isFromPlugin(&mut self) {
        self.isFromPlugin = false;
    }

    // Param is passed by value, moved
    pub fn set_isFromPlugin(&mut self, v: bool) {
        self.isFromPlugin = v;
    }

    // string messageReplyID = 3;


    pub fn get_messageReplyID(&self) -> &str {
        &self.messageReplyID
    }
    pub fn clear_messageReplyID(&mut self) {
        self.messageReplyID.clear();
    }

    // Param is passed by value, moved
    pub fn set_messageReplyID(&mut self, v: ::std::string::String) {
        self.messageReplyID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_messageReplyID(&mut self) -> &mut ::std::string::String {
        &mut self.messageReplyID
    }

    // Take field
    pub fn take_messageReplyID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.messageReplyID, ::std::string::String::new())
    }

    // string messageEditID = 4;


    pub fn get_messageEditID(&self) -> &str {
        &self.messageEditID
    }
    pub fn clear_messageEditID(&mut self) {
        self.messageEditID.clear();
    }

    // Param is passed by value, moved
    pub fn set_messageEditID(&mut self, v: ::std::string::String) {
        self.messageEditID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_messageEditID(&mut self) -> &mut ::std::string::String {
        &mut self.messageEditID
    }

    // Take field
    pub fn take_messageEditID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.messageEditID, ::std::string::String::new())
    }
}

impl ::protobuf::Message for ChatMessageInputData_ChatMessageDataOptions {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.syncToDiscord = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isFromPlugin = tmp;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.messageReplyID)?;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.messageEditID)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.syncToDiscord != false {
            my_size += 2;
        }
        if self.isFromPlugin != false {
            my_size += 2;
        }
        if !self.messageReplyID.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.messageReplyID);
        }
        if !self.messageEditID.is_empty() {
            my_size += ::protobuf::rt::string_size(4, &self.messageEditID);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.syncToDiscord != false {
            os.write_bool(1, self.syncToDiscord)?;
        }
        if self.isFromPlugin != false {
            os.write_bool(2, self.isFromPlugin)?;
        }
        if !self.messageReplyID.is_empty() {
            os.write_string(3, &self.messageReplyID)?;
        }
        if !self.messageEditID.is_empty() {
            os.write_string(4, &self.messageEditID)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ChatMessageInputData_ChatMessageDataOptions {
        ChatMessageInputData_ChatMessageDataOptions::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "syncToDiscord",
                |m: &ChatMessageInputData_ChatMessageDataOptions| { &m.syncToDiscord },
                |m: &mut ChatMessageInputData_ChatMessageDataOptions| { &mut m.syncToDiscord },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isFromPlugin",
                |m: &ChatMessageInputData_ChatMessageDataOptions| { &m.isFromPlugin },
                |m: &mut ChatMessageInputData_ChatMessageDataOptions| { &mut m.isFromPlugin },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "messageReplyID",
                |m: &ChatMessageInputData_ChatMessageDataOptions| { &m.messageReplyID },
                |m: &mut ChatMessageInputData_ChatMessageDataOptions| { &mut m.messageReplyID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "messageEditID",
                |m: &ChatMessageInputData_ChatMessageDataOptions| { &m.messageEditID },
                |m: &mut ChatMessageInputData_ChatMessageDataOptions| { &mut m.messageEditID },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ChatMessageInputData_ChatMessageDataOptions>(
                "ChatMessageInputData.ChatMessageDataOptions",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ChatMessageInputData_ChatMessageDataOptions {
        static instance: ::protobuf::rt::LazyV2<ChatMessageInputData_ChatMessageDataOptions> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ChatMessageInputData_ChatMessageDataOptions::new)
    }
}

impl ::protobuf::Clear for ChatMessageInputData_ChatMessageDataOptions {
    fn clear(&mut self) {
        self.syncToDiscord = false;
        self.isFromPlugin = false;
        self.messageReplyID.clear();
        self.messageEditID.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ChatMessageInputData_ChatMessageDataOptions {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ChatMessageInputData_ChatMessageDataOptions {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiMessageInputDto {
    // message fields
    pub time: ::std::string::String,
    pub data: ::protobuf::RepeatedField<MidiMessageInputDto_MidiMessageInputBuffer>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiMessageInputDto {
    fn default() -> &'a MidiMessageInputDto {
        <MidiMessageInputDto as ::protobuf::Message>::default_instance()
    }
}

impl MidiMessageInputDto {
    pub fn new() -> MidiMessageInputDto {
        ::std::default::Default::default()
    }

    // string time = 1;


    pub fn get_time(&self) -> &str {
        &self.time
    }
    pub fn clear_time(&mut self) {
        self.time.clear();
    }

    // Param is passed by value, moved
    pub fn set_time(&mut self, v: ::std::string::String) {
        self.time = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_time(&mut self) -> &mut ::std::string::String {
        &mut self.time
    }

    // Take field
    pub fn take_time(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.time, ::std::string::String::new())
    }

    // repeated .PianoRhythm.Serialization.ClientToServer.Msgs.MidiMessageInputDto.MidiMessageInputBuffer data = 2;


    pub fn get_data(&self) -> &[MidiMessageInputDto_MidiMessageInputBuffer] {
        &self.data
    }
    pub fn clear_data(&mut self) {
        self.data.clear();
    }

    // Param is passed by value, moved
    pub fn set_data(&mut self, v: ::protobuf::RepeatedField<MidiMessageInputDto_MidiMessageInputBuffer>) {
        self.data = v;
    }

    // Mutable pointer to the field.
    pub fn mut_data(&mut self) -> &mut ::protobuf::RepeatedField<MidiMessageInputDto_MidiMessageInputBuffer> {
        &mut self.data
    }

    // Take field
    pub fn take_data(&mut self) -> ::protobuf::RepeatedField<MidiMessageInputDto_MidiMessageInputBuffer> {
        ::std::mem::replace(&mut self.data, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for MidiMessageInputDto {
    fn is_initialized(&self) -> bool {
        for v in &self.data {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.time)?;
                },
                2 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.data)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.time.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.time);
        }
        for value in &self.data {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.time.is_empty() {
            os.write_string(1, &self.time)?;
        }
        for v in &self.data {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiMessageInputDto {
        MidiMessageInputDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "time",
                |m: &MidiMessageInputDto| { &m.time },
                |m: &mut MidiMessageInputDto| { &mut m.time },
            ));
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<MidiMessageInputDto_MidiMessageInputBuffer>>(
                "data",
                |m: &MidiMessageInputDto| { &m.data },
                |m: &mut MidiMessageInputDto| { &mut m.data },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiMessageInputDto>(
                "MidiMessageInputDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiMessageInputDto {
        static instance: ::protobuf::rt::LazyV2<MidiMessageInputDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiMessageInputDto::new)
    }
}

impl ::protobuf::Clear for MidiMessageInputDto {
    fn clear(&mut self) {
        self.time.clear();
        self.data.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiMessageInputDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiMessageInputDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MidiMessageInputDto_MidiMessageInputBuffer {
    // message fields
    pub data: ::protobuf::SingularPtrField<super::midi_renditions::MidiDto>,
    pub delay: f64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MidiMessageInputDto_MidiMessageInputBuffer {
    fn default() -> &'a MidiMessageInputDto_MidiMessageInputBuffer {
        <MidiMessageInputDto_MidiMessageInputBuffer as ::protobuf::Message>::default_instance()
    }
}

impl MidiMessageInputDto_MidiMessageInputBuffer {
    pub fn new() -> MidiMessageInputDto_MidiMessageInputBuffer {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.Midi.Msgs.MidiDto data = 1;


    pub fn get_data(&self) -> &super::midi_renditions::MidiDto {
        self.data.as_ref().unwrap_or_else(|| <super::midi_renditions::MidiDto as ::protobuf::Message>::default_instance())
    }
    pub fn clear_data(&mut self) {
        self.data.clear();
    }

    pub fn has_data(&self) -> bool {
        self.data.is_some()
    }

    // Param is passed by value, moved
    pub fn set_data(&mut self, v: super::midi_renditions::MidiDto) {
        self.data = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_data(&mut self) -> &mut super::midi_renditions::MidiDto {
        if self.data.is_none() {
            self.data.set_default();
        }
        self.data.as_mut().unwrap()
    }

    // Take field
    pub fn take_data(&mut self) -> super::midi_renditions::MidiDto {
        self.data.take().unwrap_or_else(|| super::midi_renditions::MidiDto::new())
    }

    // double delay = 2;


    pub fn get_delay(&self) -> f64 {
        self.delay
    }
    pub fn clear_delay(&mut self) {
        self.delay = 0.;
    }

    // Param is passed by value, moved
    pub fn set_delay(&mut self, v: f64) {
        self.delay = v;
    }
}

impl ::protobuf::Message for MidiMessageInputDto_MidiMessageInputBuffer {
    fn is_initialized(&self) -> bool {
        for v in &self.data {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.data)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed64 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_double()?;
                    self.delay = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.data.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.delay != 0. {
            my_size += 9;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.data.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.delay != 0. {
            os.write_double(2, self.delay)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MidiMessageInputDto_MidiMessageInputBuffer {
        MidiMessageInputDto_MidiMessageInputBuffer::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::midi_renditions::MidiDto>>(
                "data",
                |m: &MidiMessageInputDto_MidiMessageInputBuffer| { &m.data },
                |m: &mut MidiMessageInputDto_MidiMessageInputBuffer| { &mut m.data },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeDouble>(
                "delay",
                |m: &MidiMessageInputDto_MidiMessageInputBuffer| { &m.delay },
                |m: &mut MidiMessageInputDto_MidiMessageInputBuffer| { &mut m.delay },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<MidiMessageInputDto_MidiMessageInputBuffer>(
                "MidiMessageInputDto.MidiMessageInputBuffer",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static MidiMessageInputDto_MidiMessageInputBuffer {
        static instance: ::protobuf::rt::LazyV2<MidiMessageInputDto_MidiMessageInputBuffer> = ::protobuf::rt::LazyV2::INIT;
        instance.get(MidiMessageInputDto_MidiMessageInputBuffer::new)
    }
}

impl ::protobuf::Clear for MidiMessageInputDto_MidiMessageInputBuffer {
    fn clear(&mut self) {
        self.data.clear();
        self.delay = 0.;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for MidiMessageInputDto_MidiMessageInputBuffer {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for MidiMessageInputDto_MidiMessageInputBuffer {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomIDWithPassword {
    // message fields
    pub roomID: ::std::string::String,
    pub password: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomIDWithPassword {
    fn default() -> &'a RoomIDWithPassword {
        <RoomIDWithPassword as ::protobuf::Message>::default_instance()
    }
}

impl RoomIDWithPassword {
    pub fn new() -> RoomIDWithPassword {
        ::std::default::Default::default()
    }

    // string roomID = 1;


    pub fn get_roomID(&self) -> &str {
        &self.roomID
    }
    pub fn clear_roomID(&mut self) {
        self.roomID.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.roomID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        &mut self.roomID
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomID, ::std::string::String::new())
    }

    // string password = 2;


    pub fn get_password(&self) -> &str {
        &self.password
    }
    pub fn clear_password(&mut self) {
        self.password.clear();
    }

    // Param is passed by value, moved
    pub fn set_password(&mut self, v: ::std::string::String) {
        self.password = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_password(&mut self) -> &mut ::std::string::String {
        &mut self.password
    }

    // Take field
    pub fn take_password(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.password, ::std::string::String::new())
    }
}

impl ::protobuf::Message for RoomIDWithPassword {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomID)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.password)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.roomID.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.roomID);
        }
        if !self.password.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.password);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.roomID.is_empty() {
            os.write_string(1, &self.roomID)?;
        }
        if !self.password.is_empty() {
            os.write_string(2, &self.password)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomIDWithPassword {
        RoomIDWithPassword::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomID",
                |m: &RoomIDWithPassword| { &m.roomID },
                |m: &mut RoomIDWithPassword| { &mut m.roomID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "password",
                |m: &RoomIDWithPassword| { &m.password },
                |m: &mut RoomIDWithPassword| { &mut m.password },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomIDWithPassword>(
                "RoomIDWithPassword",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomIDWithPassword {
        static instance: ::protobuf::rt::LazyV2<RoomIDWithPassword> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomIDWithPassword::new)
    }
}

impl ::protobuf::Clear for RoomIDWithPassword {
    fn clear(&mut self) {
        self.roomID.clear();
        self.password.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomIDWithPassword {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomIDWithPassword {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct LoginDataDto {
    // message fields
    pub username: ::std::string::String,
    pub password: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a LoginDataDto {
    fn default() -> &'a LoginDataDto {
        <LoginDataDto as ::protobuf::Message>::default_instance()
    }
}

impl LoginDataDto {
    pub fn new() -> LoginDataDto {
        ::std::default::Default::default()
    }

    // string username = 1;


    pub fn get_username(&self) -> &str {
        &self.username
    }
    pub fn clear_username(&mut self) {
        self.username.clear();
    }

    // Param is passed by value, moved
    pub fn set_username(&mut self, v: ::std::string::String) {
        self.username = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_username(&mut self) -> &mut ::std::string::String {
        &mut self.username
    }

    // Take field
    pub fn take_username(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.username, ::std::string::String::new())
    }

    // string password = 2;


    pub fn get_password(&self) -> &str {
        &self.password
    }
    pub fn clear_password(&mut self) {
        self.password.clear();
    }

    // Param is passed by value, moved
    pub fn set_password(&mut self, v: ::std::string::String) {
        self.password = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_password(&mut self) -> &mut ::std::string::String {
        &mut self.password
    }

    // Take field
    pub fn take_password(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.password, ::std::string::String::new())
    }
}

impl ::protobuf::Message for LoginDataDto {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.username)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.password)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.username.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.username);
        }
        if !self.password.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.password);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.username.is_empty() {
            os.write_string(1, &self.username)?;
        }
        if !self.password.is_empty() {
            os.write_string(2, &self.password)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> LoginDataDto {
        LoginDataDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "username",
                |m: &LoginDataDto| { &m.username },
                |m: &mut LoginDataDto| { &mut m.username },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "password",
                |m: &LoginDataDto| { &m.password },
                |m: &mut LoginDataDto| { &mut m.password },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<LoginDataDto>(
                "LoginDataDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static LoginDataDto {
        static instance: ::protobuf::rt::LazyV2<LoginDataDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(LoginDataDto::new)
    }
}

impl ::protobuf::Clear for LoginDataDto {
    fn clear(&mut self) {
        self.username.clear();
        self.password.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for LoginDataDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for LoginDataDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct SimpleEmailDto {
    // message fields
    pub email: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SimpleEmailDto {
    fn default() -> &'a SimpleEmailDto {
        <SimpleEmailDto as ::protobuf::Message>::default_instance()
    }
}

impl SimpleEmailDto {
    pub fn new() -> SimpleEmailDto {
        ::std::default::Default::default()
    }

    // string email = 1;


    pub fn get_email(&self) -> &str {
        &self.email
    }
    pub fn clear_email(&mut self) {
        self.email.clear();
    }

    // Param is passed by value, moved
    pub fn set_email(&mut self, v: ::std::string::String) {
        self.email = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_email(&mut self) -> &mut ::std::string::String {
        &mut self.email
    }

    // Take field
    pub fn take_email(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.email, ::std::string::String::new())
    }
}

impl ::protobuf::Message for SimpleEmailDto {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.email)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.email.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.email);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.email.is_empty() {
            os.write_string(1, &self.email)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SimpleEmailDto {
        SimpleEmailDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "email",
                |m: &SimpleEmailDto| { &m.email },
                |m: &mut SimpleEmailDto| { &mut m.email },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<SimpleEmailDto>(
                "SimpleEmailDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static SimpleEmailDto {
        static instance: ::protobuf::rt::LazyV2<SimpleEmailDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(SimpleEmailDto::new)
    }
}

impl ::protobuf::Clear for SimpleEmailDto {
    fn clear(&mut self) {
        self.email.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for SimpleEmailDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for SimpleEmailDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ResetPasswordDataDto {
    // message fields
    pub password: ::std::string::String,
    pub confirmpassword: ::std::string::String,
    pub token: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ResetPasswordDataDto {
    fn default() -> &'a ResetPasswordDataDto {
        <ResetPasswordDataDto as ::protobuf::Message>::default_instance()
    }
}

impl ResetPasswordDataDto {
    pub fn new() -> ResetPasswordDataDto {
        ::std::default::Default::default()
    }

    // string password = 1;


    pub fn get_password(&self) -> &str {
        &self.password
    }
    pub fn clear_password(&mut self) {
        self.password.clear();
    }

    // Param is passed by value, moved
    pub fn set_password(&mut self, v: ::std::string::String) {
        self.password = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_password(&mut self) -> &mut ::std::string::String {
        &mut self.password
    }

    // Take field
    pub fn take_password(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.password, ::std::string::String::new())
    }

    // string confirmpassword = 2;


    pub fn get_confirmpassword(&self) -> &str {
        &self.confirmpassword
    }
    pub fn clear_confirmpassword(&mut self) {
        self.confirmpassword.clear();
    }

    // Param is passed by value, moved
    pub fn set_confirmpassword(&mut self, v: ::std::string::String) {
        self.confirmpassword = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_confirmpassword(&mut self) -> &mut ::std::string::String {
        &mut self.confirmpassword
    }

    // Take field
    pub fn take_confirmpassword(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.confirmpassword, ::std::string::String::new())
    }

    // string token = 3;


    pub fn get_token(&self) -> &str {
        &self.token
    }
    pub fn clear_token(&mut self) {
        self.token.clear();
    }

    // Param is passed by value, moved
    pub fn set_token(&mut self, v: ::std::string::String) {
        self.token = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_token(&mut self) -> &mut ::std::string::String {
        &mut self.token
    }

    // Take field
    pub fn take_token(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.token, ::std::string::String::new())
    }
}

impl ::protobuf::Message for ResetPasswordDataDto {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.password)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.confirmpassword)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.token)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.password.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.password);
        }
        if !self.confirmpassword.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.confirmpassword);
        }
        if !self.token.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.token);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.password.is_empty() {
            os.write_string(1, &self.password)?;
        }
        if !self.confirmpassword.is_empty() {
            os.write_string(2, &self.confirmpassword)?;
        }
        if !self.token.is_empty() {
            os.write_string(3, &self.token)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ResetPasswordDataDto {
        ResetPasswordDataDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "password",
                |m: &ResetPasswordDataDto| { &m.password },
                |m: &mut ResetPasswordDataDto| { &mut m.password },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "confirmpassword",
                |m: &ResetPasswordDataDto| { &m.confirmpassword },
                |m: &mut ResetPasswordDataDto| { &mut m.confirmpassword },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "token",
                |m: &ResetPasswordDataDto| { &m.token },
                |m: &mut ResetPasswordDataDto| { &mut m.token },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ResetPasswordDataDto>(
                "ResetPasswordDataDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ResetPasswordDataDto {
        static instance: ::protobuf::rt::LazyV2<ResetPasswordDataDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ResetPasswordDataDto::new)
    }
}

impl ::protobuf::Clear for ResetPasswordDataDto {
    fn clear(&mut self) {
        self.password.clear();
        self.confirmpassword.clear();
        self.token.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ResetPasswordDataDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ResetPasswordDataDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RegistrationData {
    // message fields
    pub username: ::std::string::String,
    pub email: ::std::string::String,
    pub password: ::std::string::String,
    pub confirmpassword: ::std::string::String,
    pub acceptedtos: bool,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RegistrationData {
    fn default() -> &'a RegistrationData {
        <RegistrationData as ::protobuf::Message>::default_instance()
    }
}

impl RegistrationData {
    pub fn new() -> RegistrationData {
        ::std::default::Default::default()
    }

    // string username = 1;


    pub fn get_username(&self) -> &str {
        &self.username
    }
    pub fn clear_username(&mut self) {
        self.username.clear();
    }

    // Param is passed by value, moved
    pub fn set_username(&mut self, v: ::std::string::String) {
        self.username = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_username(&mut self) -> &mut ::std::string::String {
        &mut self.username
    }

    // Take field
    pub fn take_username(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.username, ::std::string::String::new())
    }

    // string email = 2;


    pub fn get_email(&self) -> &str {
        &self.email
    }
    pub fn clear_email(&mut self) {
        self.email.clear();
    }

    // Param is passed by value, moved
    pub fn set_email(&mut self, v: ::std::string::String) {
        self.email = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_email(&mut self) -> &mut ::std::string::String {
        &mut self.email
    }

    // Take field
    pub fn take_email(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.email, ::std::string::String::new())
    }

    // string password = 3;


    pub fn get_password(&self) -> &str {
        &self.password
    }
    pub fn clear_password(&mut self) {
        self.password.clear();
    }

    // Param is passed by value, moved
    pub fn set_password(&mut self, v: ::std::string::String) {
        self.password = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_password(&mut self) -> &mut ::std::string::String {
        &mut self.password
    }

    // Take field
    pub fn take_password(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.password, ::std::string::String::new())
    }

    // string confirmpassword = 4;


    pub fn get_confirmpassword(&self) -> &str {
        &self.confirmpassword
    }
    pub fn clear_confirmpassword(&mut self) {
        self.confirmpassword.clear();
    }

    // Param is passed by value, moved
    pub fn set_confirmpassword(&mut self, v: ::std::string::String) {
        self.confirmpassword = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_confirmpassword(&mut self) -> &mut ::std::string::String {
        &mut self.confirmpassword
    }

    // Take field
    pub fn take_confirmpassword(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.confirmpassword, ::std::string::String::new())
    }

    // bool acceptedtos = 5;


    pub fn get_acceptedtos(&self) -> bool {
        self.acceptedtos
    }
    pub fn clear_acceptedtos(&mut self) {
        self.acceptedtos = false;
    }

    // Param is passed by value, moved
    pub fn set_acceptedtos(&mut self, v: bool) {
        self.acceptedtos = v;
    }
}

impl ::protobuf::Message for RegistrationData {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.username)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.email)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.password)?;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.confirmpassword)?;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.acceptedtos = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.username.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.username);
        }
        if !self.email.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.email);
        }
        if !self.password.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.password);
        }
        if !self.confirmpassword.is_empty() {
            my_size += ::protobuf::rt::string_size(4, &self.confirmpassword);
        }
        if self.acceptedtos != false {
            my_size += 2;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.username.is_empty() {
            os.write_string(1, &self.username)?;
        }
        if !self.email.is_empty() {
            os.write_string(2, &self.email)?;
        }
        if !self.password.is_empty() {
            os.write_string(3, &self.password)?;
        }
        if !self.confirmpassword.is_empty() {
            os.write_string(4, &self.confirmpassword)?;
        }
        if self.acceptedtos != false {
            os.write_bool(5, self.acceptedtos)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RegistrationData {
        RegistrationData::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "username",
                |m: &RegistrationData| { &m.username },
                |m: &mut RegistrationData| { &mut m.username },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "email",
                |m: &RegistrationData| { &m.email },
                |m: &mut RegistrationData| { &mut m.email },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "password",
                |m: &RegistrationData| { &m.password },
                |m: &mut RegistrationData| { &mut m.password },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "confirmpassword",
                |m: &RegistrationData| { &m.confirmpassword },
                |m: &mut RegistrationData| { &mut m.confirmpassword },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "acceptedtos",
                |m: &RegistrationData| { &m.acceptedtos },
                |m: &mut RegistrationData| { &mut m.acceptedtos },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RegistrationData>(
                "RegistrationData",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RegistrationData {
        static instance: ::protobuf::rt::LazyV2<RegistrationData> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RegistrationData::new)
    }
}

impl ::protobuf::Clear for RegistrationData {
    fn clear(&mut self) {
        self.username.clear();
        self.email.clear();
        self.password.clear();
        self.confirmpassword.clear();
        self.acceptedtos = false;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RegistrationData {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RegistrationData {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ServerCommandDU {
    // message fields
    pub commandType: ServerCommandDU_CommandType,
    // message oneof groups
    pub commandData: ::std::option::Option<ServerCommandDU_oneof_commandData>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ServerCommandDU {
    fn default() -> &'a ServerCommandDU {
        <ServerCommandDU as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum ServerCommandDU_oneof_commandData {
    roomID(::std::string::String),
    roomName(::std::string::String),
    roomIDorName(::std::string::String),
    usertag(::std::string::String),
    jsonValue(::std::string::String),
    boolean(bool),
    roomIDAndPassword(RoomIDWithPassword),
    loginData(LoginDataDto),
    emailData(SimpleEmailDto),
    resetPasswordData(ResetPasswordDataDto),
    registrationData(RegistrationData),
    stringValue(::std::string::String),
}

impl ServerCommandDU {
    pub fn new() -> ServerCommandDU {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ServerCommandDU.CommandType commandType = 1;


    pub fn get_commandType(&self) -> ServerCommandDU_CommandType {
        self.commandType
    }
    pub fn clear_commandType(&mut self) {
        self.commandType = ServerCommandDU_CommandType::Invalid;
    }

    // Param is passed by value, moved
    pub fn set_commandType(&mut self, v: ServerCommandDU_CommandType) {
        self.commandType = v;
    }

    // string roomID = 2;


    pub fn get_roomID(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_roomID(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_roomID(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomID(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomID(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        if self.has_roomID() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string roomName = 3;


    pub fn get_roomName(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomName(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_roomName(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_roomName(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomName(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomName(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomName(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomName(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomName(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomName(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomName(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomName(&mut self) -> ::std::string::String {
        if self.has_roomName() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomName(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string roomIDorName = 4;


    pub fn get_roomIDorName(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDorName(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_roomIDorName(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_roomIDorName(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDorName(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomIDorName(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDorName(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomIDorName(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDorName(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDorName(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDorName(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomIDorName(&mut self) -> ::std::string::String {
        if self.has_roomIDorName() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDorName(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string usertag = 5;


    pub fn get_usertag(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::usertag(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_usertag(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_usertag(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::usertag(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::usertag(v))
    }

    // Mutable pointer to the field.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::usertag(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::usertag(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::usertag(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        if self.has_usertag() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::usertag(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string jsonValue = 7;


    pub fn get_jsonValue(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::jsonValue(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_jsonValue(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_jsonValue(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::jsonValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_jsonValue(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::jsonValue(v))
    }

    // Mutable pointer to the field.
    pub fn mut_jsonValue(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::jsonValue(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::jsonValue(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::jsonValue(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_jsonValue(&mut self) -> ::std::string::String {
        if self.has_jsonValue() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::jsonValue(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // bool boolean = 8;


    pub fn get_boolean(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::boolean(v)) => v,
            _ => false,
        }
    }
    pub fn clear_boolean(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_boolean(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::boolean(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_boolean(&mut self, v: bool) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::boolean(v))
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.RoomIDWithPassword roomIDAndPassword = 9;


    pub fn get_roomIDAndPassword(&self) -> &RoomIDWithPassword {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDAndPassword(ref v)) => v,
            _ => <RoomIDWithPassword as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomIDAndPassword(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_roomIDAndPassword(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDAndPassword(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomIDAndPassword(&mut self, v: RoomIDWithPassword) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDAndPassword(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomIDAndPassword(&mut self) -> &mut RoomIDWithPassword {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDAndPassword(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDAndPassword(RoomIDWithPassword::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDAndPassword(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomIDAndPassword(&mut self) -> RoomIDWithPassword {
        if self.has_roomIDAndPassword() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDAndPassword(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomIDWithPassword::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.LoginDataDto loginData = 10;


    pub fn get_loginData(&self) -> &LoginDataDto {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::loginData(ref v)) => v,
            _ => <LoginDataDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_loginData(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_loginData(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::loginData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_loginData(&mut self, v: LoginDataDto) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::loginData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_loginData(&mut self) -> &mut LoginDataDto {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::loginData(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::loginData(LoginDataDto::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::loginData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_loginData(&mut self) -> LoginDataDto {
        if self.has_loginData() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::loginData(v)) => v,
                _ => panic!(),
            }
        } else {
            LoginDataDto::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.SimpleEmailDto emailData = 11;


    pub fn get_emailData(&self) -> &SimpleEmailDto {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::emailData(ref v)) => v,
            _ => <SimpleEmailDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_emailData(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_emailData(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::emailData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_emailData(&mut self, v: SimpleEmailDto) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::emailData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_emailData(&mut self) -> &mut SimpleEmailDto {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::emailData(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::emailData(SimpleEmailDto::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::emailData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_emailData(&mut self) -> SimpleEmailDto {
        if self.has_emailData() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::emailData(v)) => v,
                _ => panic!(),
            }
        } else {
            SimpleEmailDto::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ResetPasswordDataDto resetPasswordData = 12;


    pub fn get_resetPasswordData(&self) -> &ResetPasswordDataDto {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::resetPasswordData(ref v)) => v,
            _ => <ResetPasswordDataDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_resetPasswordData(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_resetPasswordData(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::resetPasswordData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_resetPasswordData(&mut self, v: ResetPasswordDataDto) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::resetPasswordData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_resetPasswordData(&mut self) -> &mut ResetPasswordDataDto {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::resetPasswordData(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::resetPasswordData(ResetPasswordDataDto::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::resetPasswordData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_resetPasswordData(&mut self) -> ResetPasswordDataDto {
        if self.has_resetPasswordData() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::resetPasswordData(v)) => v,
                _ => panic!(),
            }
        } else {
            ResetPasswordDataDto::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.RegistrationData registrationData = 13;


    pub fn get_registrationData(&self) -> &RegistrationData {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::registrationData(ref v)) => v,
            _ => <RegistrationData as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_registrationData(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_registrationData(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::registrationData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_registrationData(&mut self, v: RegistrationData) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::registrationData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_registrationData(&mut self) -> &mut RegistrationData {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::registrationData(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::registrationData(RegistrationData::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::registrationData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_registrationData(&mut self) -> RegistrationData {
        if self.has_registrationData() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::registrationData(v)) => v,
                _ => panic!(),
            }
        } else {
            RegistrationData::new()
        }
    }

    // string stringValue = 14;


    pub fn get_stringValue(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::stringValue(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_stringValue(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_stringValue(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::stringValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_stringValue(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::stringValue(v))
    }

    // Mutable pointer to the field.
    pub fn mut_stringValue(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerCommandDU_oneof_commandData::stringValue(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::stringValue(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerCommandDU_oneof_commandData::stringValue(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_stringValue(&mut self) -> ::std::string::String {
        if self.has_stringValue() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerCommandDU_oneof_commandData::stringValue(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }
}

impl ::protobuf::Message for ServerCommandDU {
    fn is_initialized(&self) -> bool {
        if let Some(ServerCommandDU_oneof_commandData::roomIDAndPassword(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerCommandDU_oneof_commandData::loginData(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerCommandDU_oneof_commandData::emailData(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerCommandDU_oneof_commandData::resetPasswordData(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerCommandDU_oneof_commandData::registrationData(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.commandType, 1, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomID(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomName(is.read_string()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDorName(is.read_string()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::usertag(is.read_string()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::jsonValue(is.read_string()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::boolean(is.read_bool()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::roomIDAndPassword(is.read_message()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::loginData(is.read_message()?));
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::emailData(is.read_message()?));
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::resetPasswordData(is.read_message()?));
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::registrationData(is.read_message()?));
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerCommandDU_oneof_commandData::stringValue(is.read_string()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.commandType != ServerCommandDU_CommandType::Invalid {
            my_size += ::protobuf::rt::enum_size(1, self.commandType);
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &ServerCommandDU_oneof_commandData::roomID(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
                &ServerCommandDU_oneof_commandData::roomName(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
                &ServerCommandDU_oneof_commandData::roomIDorName(ref v) => {
                    my_size += ::protobuf::rt::string_size(4, &v);
                },
                &ServerCommandDU_oneof_commandData::usertag(ref v) => {
                    my_size += ::protobuf::rt::string_size(5, &v);
                },
                &ServerCommandDU_oneof_commandData::jsonValue(ref v) => {
                    my_size += ::protobuf::rt::string_size(7, &v);
                },
                &ServerCommandDU_oneof_commandData::boolean(v) => {
                    my_size += 2;
                },
                &ServerCommandDU_oneof_commandData::roomIDAndPassword(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerCommandDU_oneof_commandData::loginData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerCommandDU_oneof_commandData::emailData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerCommandDU_oneof_commandData::resetPasswordData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerCommandDU_oneof_commandData::registrationData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerCommandDU_oneof_commandData::stringValue(ref v) => {
                    my_size += ::protobuf::rt::string_size(14, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.commandType != ServerCommandDU_CommandType::Invalid {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.commandType))?;
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &ServerCommandDU_oneof_commandData::roomID(ref v) => {
                    os.write_string(2, v)?;
                },
                &ServerCommandDU_oneof_commandData::roomName(ref v) => {
                    os.write_string(3, v)?;
                },
                &ServerCommandDU_oneof_commandData::roomIDorName(ref v) => {
                    os.write_string(4, v)?;
                },
                &ServerCommandDU_oneof_commandData::usertag(ref v) => {
                    os.write_string(5, v)?;
                },
                &ServerCommandDU_oneof_commandData::jsonValue(ref v) => {
                    os.write_string(7, v)?;
                },
                &ServerCommandDU_oneof_commandData::boolean(v) => {
                    os.write_bool(8, v)?;
                },
                &ServerCommandDU_oneof_commandData::roomIDAndPassword(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerCommandDU_oneof_commandData::loginData(ref v) => {
                    os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerCommandDU_oneof_commandData::emailData(ref v) => {
                    os.write_tag(11, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerCommandDU_oneof_commandData::resetPasswordData(ref v) => {
                    os.write_tag(12, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerCommandDU_oneof_commandData::registrationData(ref v) => {
                    os.write_tag(13, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerCommandDU_oneof_commandData::stringValue(ref v) => {
                    os.write_string(14, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ServerCommandDU {
        ServerCommandDU::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<ServerCommandDU_CommandType>>(
                "commandType",
                |m: &ServerCommandDU| { &m.commandType },
                |m: &mut ServerCommandDU| { &mut m.commandType },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "roomID",
                ServerCommandDU::has_roomID,
                ServerCommandDU::get_roomID,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "roomName",
                ServerCommandDU::has_roomName,
                ServerCommandDU::get_roomName,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "roomIDorName",
                ServerCommandDU::has_roomIDorName,
                ServerCommandDU::get_roomIDorName,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "usertag",
                ServerCommandDU::has_usertag,
                ServerCommandDU::get_usertag,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "jsonValue",
                ServerCommandDU::has_jsonValue,
                ServerCommandDU::get_jsonValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "boolean",
                ServerCommandDU::has_boolean,
                ServerCommandDU::get_boolean,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomIDWithPassword>(
                "roomIDAndPassword",
                ServerCommandDU::has_roomIDAndPassword,
                ServerCommandDU::get_roomIDAndPassword,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, LoginDataDto>(
                "loginData",
                ServerCommandDU::has_loginData,
                ServerCommandDU::get_loginData,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, SimpleEmailDto>(
                "emailData",
                ServerCommandDU::has_emailData,
                ServerCommandDU::get_emailData,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ResetPasswordDataDto>(
                "resetPasswordData",
                ServerCommandDU::has_resetPasswordData,
                ServerCommandDU::get_resetPasswordData,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RegistrationData>(
                "registrationData",
                ServerCommandDU::has_registrationData,
                ServerCommandDU::get_registrationData,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "stringValue",
                ServerCommandDU::has_stringValue,
                ServerCommandDU::get_stringValue,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ServerCommandDU>(
                "ServerCommandDU",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ServerCommandDU {
        static instance: ::protobuf::rt::LazyV2<ServerCommandDU> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ServerCommandDU::new)
    }
}

impl ::protobuf::Clear for ServerCommandDU {
    fn clear(&mut self) {
        self.commandType = ServerCommandDU_CommandType::Invalid;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ServerCommandDU {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ServerCommandDU {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum ServerCommandDU_CommandType {
    Invalid = 0,
    UserUpdateCommand = 2,
    PermissionUpdatedCommand = 3,
    Join = 7,
    JoinRoomWithPassword = 8,
    CreateOrJoinRoom = 10,
    GetRoomSettings = 11,
    EnterLobby = 12,
    CreateRoom = 13,
    UpdateRoom = 14,
    LeaveRoom = 15,
    Ping = 16,
    GetAllRooms = 17,
    GetAllUsersInRoom = 18,
    Login = 19,
    Register = 20,
    ResendEmailVerification = 21,
    ResetPassword = 22,
    ForgotPassword = 23,
    IsTyping = 24,
    RoomLoaded = 25,
    UploadClientSettings = 26,
    SendFriendRequest = 27,
    SendUnfriendRequest = 28,
    AcceptFriendRequest = 29,
    DenyFriendRequest = 30,
    DeleteSentFriendRequest = 31,
    UploadOrchestraModelCustomizationData = 33,
    UploadCharacterData = 34,
    DeleteChatMessageById = 35,
    DeleteChatMessageByUsertag = 36,
    GetRoomFullDetails = 37,
}

impl ::protobuf::ProtobufEnum for ServerCommandDU_CommandType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<ServerCommandDU_CommandType> {
        match value {
            0 => ::std::option::Option::Some(ServerCommandDU_CommandType::Invalid),
            2 => ::std::option::Option::Some(ServerCommandDU_CommandType::UserUpdateCommand),
            3 => ::std::option::Option::Some(ServerCommandDU_CommandType::PermissionUpdatedCommand),
            7 => ::std::option::Option::Some(ServerCommandDU_CommandType::Join),
            8 => ::std::option::Option::Some(ServerCommandDU_CommandType::JoinRoomWithPassword),
            10 => ::std::option::Option::Some(ServerCommandDU_CommandType::CreateOrJoinRoom),
            11 => ::std::option::Option::Some(ServerCommandDU_CommandType::GetRoomSettings),
            12 => ::std::option::Option::Some(ServerCommandDU_CommandType::EnterLobby),
            13 => ::std::option::Option::Some(ServerCommandDU_CommandType::CreateRoom),
            14 => ::std::option::Option::Some(ServerCommandDU_CommandType::UpdateRoom),
            15 => ::std::option::Option::Some(ServerCommandDU_CommandType::LeaveRoom),
            16 => ::std::option::Option::Some(ServerCommandDU_CommandType::Ping),
            17 => ::std::option::Option::Some(ServerCommandDU_CommandType::GetAllRooms),
            18 => ::std::option::Option::Some(ServerCommandDU_CommandType::GetAllUsersInRoom),
            19 => ::std::option::Option::Some(ServerCommandDU_CommandType::Login),
            20 => ::std::option::Option::Some(ServerCommandDU_CommandType::Register),
            21 => ::std::option::Option::Some(ServerCommandDU_CommandType::ResendEmailVerification),
            22 => ::std::option::Option::Some(ServerCommandDU_CommandType::ResetPassword),
            23 => ::std::option::Option::Some(ServerCommandDU_CommandType::ForgotPassword),
            24 => ::std::option::Option::Some(ServerCommandDU_CommandType::IsTyping),
            25 => ::std::option::Option::Some(ServerCommandDU_CommandType::RoomLoaded),
            26 => ::std::option::Option::Some(ServerCommandDU_CommandType::UploadClientSettings),
            27 => ::std::option::Option::Some(ServerCommandDU_CommandType::SendFriendRequest),
            28 => ::std::option::Option::Some(ServerCommandDU_CommandType::SendUnfriendRequest),
            29 => ::std::option::Option::Some(ServerCommandDU_CommandType::AcceptFriendRequest),
            30 => ::std::option::Option::Some(ServerCommandDU_CommandType::DenyFriendRequest),
            31 => ::std::option::Option::Some(ServerCommandDU_CommandType::DeleteSentFriendRequest),
            33 => ::std::option::Option::Some(ServerCommandDU_CommandType::UploadOrchestraModelCustomizationData),
            34 => ::std::option::Option::Some(ServerCommandDU_CommandType::UploadCharacterData),
            35 => ::std::option::Option::Some(ServerCommandDU_CommandType::DeleteChatMessageById),
            36 => ::std::option::Option::Some(ServerCommandDU_CommandType::DeleteChatMessageByUsertag),
            37 => ::std::option::Option::Some(ServerCommandDU_CommandType::GetRoomFullDetails),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [ServerCommandDU_CommandType] = &[
            ServerCommandDU_CommandType::Invalid,
            ServerCommandDU_CommandType::UserUpdateCommand,
            ServerCommandDU_CommandType::PermissionUpdatedCommand,
            ServerCommandDU_CommandType::Join,
            ServerCommandDU_CommandType::JoinRoomWithPassword,
            ServerCommandDU_CommandType::CreateOrJoinRoom,
            ServerCommandDU_CommandType::GetRoomSettings,
            ServerCommandDU_CommandType::EnterLobby,
            ServerCommandDU_CommandType::CreateRoom,
            ServerCommandDU_CommandType::UpdateRoom,
            ServerCommandDU_CommandType::LeaveRoom,
            ServerCommandDU_CommandType::Ping,
            ServerCommandDU_CommandType::GetAllRooms,
            ServerCommandDU_CommandType::GetAllUsersInRoom,
            ServerCommandDU_CommandType::Login,
            ServerCommandDU_CommandType::Register,
            ServerCommandDU_CommandType::ResendEmailVerification,
            ServerCommandDU_CommandType::ResetPassword,
            ServerCommandDU_CommandType::ForgotPassword,
            ServerCommandDU_CommandType::IsTyping,
            ServerCommandDU_CommandType::RoomLoaded,
            ServerCommandDU_CommandType::UploadClientSettings,
            ServerCommandDU_CommandType::SendFriendRequest,
            ServerCommandDU_CommandType::SendUnfriendRequest,
            ServerCommandDU_CommandType::AcceptFriendRequest,
            ServerCommandDU_CommandType::DenyFriendRequest,
            ServerCommandDU_CommandType::DeleteSentFriendRequest,
            ServerCommandDU_CommandType::UploadOrchestraModelCustomizationData,
            ServerCommandDU_CommandType::UploadCharacterData,
            ServerCommandDU_CommandType::DeleteChatMessageById,
            ServerCommandDU_CommandType::DeleteChatMessageByUsertag,
            ServerCommandDU_CommandType::GetRoomFullDetails,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<ServerCommandDU_CommandType>("ServerCommandDU.CommandType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for ServerCommandDU_CommandType {
}

impl ::std::default::Default for ServerCommandDU_CommandType {
    fn default() -> Self {
        ServerCommandDU_CommandType::Invalid
    }
}

impl ::protobuf::reflect::ProtobufValue for ServerCommandDU_CommandType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomOwnerCommandDU {
    // message fields
    pub commandType: RoomOwnerCommandDU_CommandType,
    // message oneof groups
    pub commandData: ::std::option::Option<RoomOwnerCommandDU_oneof_commandData>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomOwnerCommandDU {
    fn default() -> &'a RoomOwnerCommandDU {
        <RoomOwnerCommandDU as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomOwnerCommandDU_oneof_commandData {
    usertag(::std::string::String),
    socketID(::std::string::String),
    kickedUserData(RoomOwnerCommandDU_KickedUserData),
}

impl RoomOwnerCommandDU {
    pub fn new() -> RoomOwnerCommandDU {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.RoomOwnerCommandDU.CommandType commandType = 1;


    pub fn get_commandType(&self) -> RoomOwnerCommandDU_CommandType {
        self.commandType
    }
    pub fn clear_commandType(&mut self) {
        self.commandType = RoomOwnerCommandDU_CommandType::Invalid;
    }

    // Param is passed by value, moved
    pub fn set_commandType(&mut self, v: RoomOwnerCommandDU_CommandType) {
        self.commandType = v;
    }

    // string usertag = 2;


    pub fn get_usertag(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::usertag(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_usertag(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_usertag(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::usertag(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::usertag(v))
    }

    // Mutable pointer to the field.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::usertag(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::usertag(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::usertag(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        if self.has_usertag() {
            match self.commandData.take() {
                ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::usertag(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string socketID = 3;


    pub fn get_socketID(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::socketID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_socketID(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_socketID(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::socketID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::socketID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::socketID(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::socketID(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::socketID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        if self.has_socketID() {
            match self.commandData.take() {
                ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::socketID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.RoomOwnerCommandDU.KickedUserData kickedUserData = 4;


    pub fn get_kickedUserData(&self) -> &RoomOwnerCommandDU_KickedUserData {
        match self.commandData {
            ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::kickedUserData(ref v)) => v,
            _ => <RoomOwnerCommandDU_KickedUserData as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_kickedUserData(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_kickedUserData(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::kickedUserData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_kickedUserData(&mut self, v: RoomOwnerCommandDU_KickedUserData) {
        self.commandData = ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::kickedUserData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_kickedUserData(&mut self) -> &mut RoomOwnerCommandDU_KickedUserData {
        if let ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::kickedUserData(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::kickedUserData(RoomOwnerCommandDU_KickedUserData::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::kickedUserData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_kickedUserData(&mut self) -> RoomOwnerCommandDU_KickedUserData {
        if self.has_kickedUserData() {
            match self.commandData.take() {
                ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::kickedUserData(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomOwnerCommandDU_KickedUserData::new()
        }
    }
}

impl ::protobuf::Message for RoomOwnerCommandDU {
    fn is_initialized(&self) -> bool {
        if let Some(RoomOwnerCommandDU_oneof_commandData::kickedUserData(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.commandType, 1, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::usertag(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::socketID(is.read_string()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(RoomOwnerCommandDU_oneof_commandData::kickedUserData(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.commandType != RoomOwnerCommandDU_CommandType::Invalid {
            my_size += ::protobuf::rt::enum_size(1, self.commandType);
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &RoomOwnerCommandDU_oneof_commandData::usertag(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
                &RoomOwnerCommandDU_oneof_commandData::socketID(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
                &RoomOwnerCommandDU_oneof_commandData::kickedUserData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.commandType != RoomOwnerCommandDU_CommandType::Invalid {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.commandType))?;
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &RoomOwnerCommandDU_oneof_commandData::usertag(ref v) => {
                    os.write_string(2, v)?;
                },
                &RoomOwnerCommandDU_oneof_commandData::socketID(ref v) => {
                    os.write_string(3, v)?;
                },
                &RoomOwnerCommandDU_oneof_commandData::kickedUserData(ref v) => {
                    os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomOwnerCommandDU {
        RoomOwnerCommandDU::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<RoomOwnerCommandDU_CommandType>>(
                "commandType",
                |m: &RoomOwnerCommandDU| { &m.commandType },
                |m: &mut RoomOwnerCommandDU| { &mut m.commandType },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "usertag",
                RoomOwnerCommandDU::has_usertag,
                RoomOwnerCommandDU::get_usertag,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "socketID",
                RoomOwnerCommandDU::has_socketID,
                RoomOwnerCommandDU::get_socketID,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomOwnerCommandDU_KickedUserData>(
                "kickedUserData",
                RoomOwnerCommandDU::has_kickedUserData,
                RoomOwnerCommandDU::get_kickedUserData,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomOwnerCommandDU>(
                "RoomOwnerCommandDU",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomOwnerCommandDU {
        static instance: ::protobuf::rt::LazyV2<RoomOwnerCommandDU> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomOwnerCommandDU::new)
    }
}

impl ::protobuf::Clear for RoomOwnerCommandDU {
    fn clear(&mut self) {
        self.commandType = RoomOwnerCommandDU_CommandType::Invalid;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomOwnerCommandDU {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomOwnerCommandDU {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomOwnerCommandDU_KickedUserData {
    // message fields
    pub usertag: ::std::string::String,
    pub socketID: ::std::string::String,
    // message oneof groups
    pub _time: ::std::option::Option<RoomOwnerCommandDU_KickedUserData_oneof__time>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomOwnerCommandDU_KickedUserData {
    fn default() -> &'a RoomOwnerCommandDU_KickedUserData {
        <RoomOwnerCommandDU_KickedUserData as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum RoomOwnerCommandDU_KickedUserData_oneof__time {
    time(::std::string::String),
}

impl RoomOwnerCommandDU_KickedUserData {
    pub fn new() -> RoomOwnerCommandDU_KickedUserData {
        ::std::default::Default::default()
    }

    // string usertag = 1;


    pub fn get_usertag(&self) -> &str {
        &self.usertag
    }
    pub fn clear_usertag(&mut self) {
        self.usertag.clear();
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.usertag = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        &mut self.usertag
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.usertag, ::std::string::String::new())
    }

    // string socketID = 2;


    pub fn get_socketID(&self) -> &str {
        &self.socketID
    }
    pub fn clear_socketID(&mut self) {
        self.socketID.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.socketID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        &mut self.socketID
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketID, ::std::string::String::new())
    }

    // string time = 3;


    pub fn get_time(&self) -> &str {
        match self._time {
            ::std::option::Option::Some(RoomOwnerCommandDU_KickedUserData_oneof__time::time(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_time(&mut self) {
        self._time = ::std::option::Option::None;
    }

    pub fn has_time(&self) -> bool {
        match self._time {
            ::std::option::Option::Some(RoomOwnerCommandDU_KickedUserData_oneof__time::time(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_time(&mut self, v: ::std::string::String) {
        self._time = ::std::option::Option::Some(RoomOwnerCommandDU_KickedUserData_oneof__time::time(v))
    }

    // Mutable pointer to the field.
    pub fn mut_time(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(RoomOwnerCommandDU_KickedUserData_oneof__time::time(_)) = self._time {
        } else {
            self._time = ::std::option::Option::Some(RoomOwnerCommandDU_KickedUserData_oneof__time::time(::std::string::String::new()));
        }
        match self._time {
            ::std::option::Option::Some(RoomOwnerCommandDU_KickedUserData_oneof__time::time(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_time(&mut self) -> ::std::string::String {
        if self.has_time() {
            match self._time.take() {
                ::std::option::Option::Some(RoomOwnerCommandDU_KickedUserData_oneof__time::time(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }
}

impl ::protobuf::Message for RoomOwnerCommandDU_KickedUserData {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.usertag)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketID)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._time = ::std::option::Option::Some(RoomOwnerCommandDU_KickedUserData_oneof__time::time(is.read_string()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.usertag.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.usertag);
        }
        if !self.socketID.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.socketID);
        }
        if let ::std::option::Option::Some(ref v) = self._time {
            match v {
                &RoomOwnerCommandDU_KickedUserData_oneof__time::time(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.usertag.is_empty() {
            os.write_string(1, &self.usertag)?;
        }
        if !self.socketID.is_empty() {
            os.write_string(2, &self.socketID)?;
        }
        if let ::std::option::Option::Some(ref v) = self._time {
            match v {
                &RoomOwnerCommandDU_KickedUserData_oneof__time::time(ref v) => {
                    os.write_string(3, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomOwnerCommandDU_KickedUserData {
        RoomOwnerCommandDU_KickedUserData::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "usertag",
                |m: &RoomOwnerCommandDU_KickedUserData| { &m.usertag },
                |m: &mut RoomOwnerCommandDU_KickedUserData| { &mut m.usertag },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketID",
                |m: &RoomOwnerCommandDU_KickedUserData| { &m.socketID },
                |m: &mut RoomOwnerCommandDU_KickedUserData| { &mut m.socketID },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "time",
                RoomOwnerCommandDU_KickedUserData::has_time,
                RoomOwnerCommandDU_KickedUserData::get_time,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomOwnerCommandDU_KickedUserData>(
                "RoomOwnerCommandDU.KickedUserData",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomOwnerCommandDU_KickedUserData {
        static instance: ::protobuf::rt::LazyV2<RoomOwnerCommandDU_KickedUserData> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomOwnerCommandDU_KickedUserData::new)
    }
}

impl ::protobuf::Clear for RoomOwnerCommandDU_KickedUserData {
    fn clear(&mut self) {
        self.usertag.clear();
        self.socketID.clear();
        self._time = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomOwnerCommandDU_KickedUserData {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomOwnerCommandDU_KickedUserData {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum RoomOwnerCommandDU_CommandType {
    Invalid = 0,
    KickUser = 1,
    RemovedKickedUser = 2,
    GetKickedUsersList = 3,
    GiveCrown = 4,
    ClearChat = 5,
    GetRoomSettings = 6,
}

impl ::protobuf::ProtobufEnum for RoomOwnerCommandDU_CommandType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<RoomOwnerCommandDU_CommandType> {
        match value {
            0 => ::std::option::Option::Some(RoomOwnerCommandDU_CommandType::Invalid),
            1 => ::std::option::Option::Some(RoomOwnerCommandDU_CommandType::KickUser),
            2 => ::std::option::Option::Some(RoomOwnerCommandDU_CommandType::RemovedKickedUser),
            3 => ::std::option::Option::Some(RoomOwnerCommandDU_CommandType::GetKickedUsersList),
            4 => ::std::option::Option::Some(RoomOwnerCommandDU_CommandType::GiveCrown),
            5 => ::std::option::Option::Some(RoomOwnerCommandDU_CommandType::ClearChat),
            6 => ::std::option::Option::Some(RoomOwnerCommandDU_CommandType::GetRoomSettings),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [RoomOwnerCommandDU_CommandType] = &[
            RoomOwnerCommandDU_CommandType::Invalid,
            RoomOwnerCommandDU_CommandType::KickUser,
            RoomOwnerCommandDU_CommandType::RemovedKickedUser,
            RoomOwnerCommandDU_CommandType::GetKickedUsersList,
            RoomOwnerCommandDU_CommandType::GiveCrown,
            RoomOwnerCommandDU_CommandType::ClearChat,
            RoomOwnerCommandDU_CommandType::GetRoomSettings,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<RoomOwnerCommandDU_CommandType>("RoomOwnerCommandDU.CommandType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for RoomOwnerCommandDU_CommandType {
}

impl ::std::default::Default for RoomOwnerCommandDU_CommandType {
    fn default() -> Self {
        RoomOwnerCommandDU_CommandType::Invalid
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomOwnerCommandDU_CommandType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ServerModCommandDto {
    // message fields
    pub commandType: ServerModCommandDto_ModCommandType,
    // message oneof groups
    pub commandData: ::std::option::Option<ServerModCommandDto_oneof_commandData>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ServerModCommandDto {
    fn default() -> &'a ServerModCommandDto {
        <ServerModCommandDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum ServerModCommandDto_oneof_commandData {
    stringValue(::std::string::String),
    jsonValue(::std::string::String),
    usertag(::std::string::String),
    socketID(::std::string::String),
    userBadge(super::user_renditions::UserBadges),
}

impl ServerModCommandDto {
    pub fn new() -> ServerModCommandDto {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ServerModCommandDto.ModCommandType commandType = 1;


    pub fn get_commandType(&self) -> ServerModCommandDto_ModCommandType {
        self.commandType
    }
    pub fn clear_commandType(&mut self) {
        self.commandType = ServerModCommandDto_ModCommandType::Invalid;
    }

    // Param is passed by value, moved
    pub fn set_commandType(&mut self, v: ServerModCommandDto_ModCommandType) {
        self.commandType = v;
    }

    // string stringValue = 2;


    pub fn get_stringValue(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::stringValue(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_stringValue(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_stringValue(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::stringValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_stringValue(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::stringValue(v))
    }

    // Mutable pointer to the field.
    pub fn mut_stringValue(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::stringValue(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::stringValue(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::stringValue(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_stringValue(&mut self) -> ::std::string::String {
        if self.has_stringValue() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::stringValue(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string jsonValue = 3;


    pub fn get_jsonValue(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::jsonValue(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_jsonValue(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_jsonValue(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::jsonValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_jsonValue(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::jsonValue(v))
    }

    // Mutable pointer to the field.
    pub fn mut_jsonValue(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::jsonValue(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::jsonValue(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::jsonValue(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_jsonValue(&mut self) -> ::std::string::String {
        if self.has_jsonValue() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::jsonValue(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string usertag = 4;


    pub fn get_usertag(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::usertag(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_usertag(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_usertag(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::usertag(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::usertag(v))
    }

    // Mutable pointer to the field.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::usertag(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::usertag(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::usertag(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        if self.has_usertag() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::usertag(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string socketID = 5;


    pub fn get_socketID(&self) -> &str {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::socketID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_socketID(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_socketID(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::socketID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::socketID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::socketID(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::socketID(::std::string::String::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::socketID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        if self.has_socketID() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::socketID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserBadges userBadge = 6;


    pub fn get_userBadge(&self) -> &super::user_renditions::UserBadges {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::userBadge(ref v)) => v,
            _ => <super::user_renditions::UserBadges as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_userBadge(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_userBadge(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::userBadge(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userBadge(&mut self, v: super::user_renditions::UserBadges) {
        self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::userBadge(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userBadge(&mut self) -> &mut super::user_renditions::UserBadges {
        if let ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::userBadge(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::userBadge(super::user_renditions::UserBadges::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::userBadge(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userBadge(&mut self) -> super::user_renditions::UserBadges {
        if self.has_userBadge() {
            match self.commandData.take() {
                ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::userBadge(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::UserBadges::new()
        }
    }
}

impl ::protobuf::Message for ServerModCommandDto {
    fn is_initialized(&self) -> bool {
        if let Some(ServerModCommandDto_oneof_commandData::userBadge(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.commandType, 1, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::stringValue(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::jsonValue(is.read_string()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::usertag(is.read_string()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::socketID(is.read_string()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(ServerModCommandDto_oneof_commandData::userBadge(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.commandType != ServerModCommandDto_ModCommandType::Invalid {
            my_size += ::protobuf::rt::enum_size(1, self.commandType);
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &ServerModCommandDto_oneof_commandData::stringValue(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
                &ServerModCommandDto_oneof_commandData::jsonValue(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
                &ServerModCommandDto_oneof_commandData::usertag(ref v) => {
                    my_size += ::protobuf::rt::string_size(4, &v);
                },
                &ServerModCommandDto_oneof_commandData::socketID(ref v) => {
                    my_size += ::protobuf::rt::string_size(5, &v);
                },
                &ServerModCommandDto_oneof_commandData::userBadge(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.commandType != ServerModCommandDto_ModCommandType::Invalid {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.commandType))?;
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &ServerModCommandDto_oneof_commandData::stringValue(ref v) => {
                    os.write_string(2, v)?;
                },
                &ServerModCommandDto_oneof_commandData::jsonValue(ref v) => {
                    os.write_string(3, v)?;
                },
                &ServerModCommandDto_oneof_commandData::usertag(ref v) => {
                    os.write_string(4, v)?;
                },
                &ServerModCommandDto_oneof_commandData::socketID(ref v) => {
                    os.write_string(5, v)?;
                },
                &ServerModCommandDto_oneof_commandData::userBadge(ref v) => {
                    os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ServerModCommandDto {
        ServerModCommandDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<ServerModCommandDto_ModCommandType>>(
                "commandType",
                |m: &ServerModCommandDto| { &m.commandType },
                |m: &mut ServerModCommandDto| { &mut m.commandType },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "stringValue",
                ServerModCommandDto::has_stringValue,
                ServerModCommandDto::get_stringValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "jsonValue",
                ServerModCommandDto::has_jsonValue,
                ServerModCommandDto::get_jsonValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "usertag",
                ServerModCommandDto::has_usertag,
                ServerModCommandDto::get_usertag,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "socketID",
                ServerModCommandDto::has_socketID,
                ServerModCommandDto::get_socketID,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::UserBadges>(
                "userBadge",
                ServerModCommandDto::has_userBadge,
                ServerModCommandDto::get_userBadge,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ServerModCommandDto>(
                "ServerModCommandDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ServerModCommandDto {
        static instance: ::protobuf::rt::LazyV2<ServerModCommandDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ServerModCommandDto::new)
    }
}

impl ::protobuf::Clear for ServerModCommandDto {
    fn clear(&mut self) {
        self.commandType = ServerModCommandDto_ModCommandType::Invalid;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ServerModCommandDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ServerModCommandDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum ServerModCommandDto_ModCommandType {
    Invalid = 0,
    BanUser = 1,
    UnbanAccount = 2,
    UnbanIp = 3,
    AddBadge = 4,
    RemoveBadge = 5,
    PermissionUpdatedCommand = 6,
    KickUser = 7,
    ClearChat = 8,
}

impl ::protobuf::ProtobufEnum for ServerModCommandDto_ModCommandType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<ServerModCommandDto_ModCommandType> {
        match value {
            0 => ::std::option::Option::Some(ServerModCommandDto_ModCommandType::Invalid),
            1 => ::std::option::Option::Some(ServerModCommandDto_ModCommandType::BanUser),
            2 => ::std::option::Option::Some(ServerModCommandDto_ModCommandType::UnbanAccount),
            3 => ::std::option::Option::Some(ServerModCommandDto_ModCommandType::UnbanIp),
            4 => ::std::option::Option::Some(ServerModCommandDto_ModCommandType::AddBadge),
            5 => ::std::option::Option::Some(ServerModCommandDto_ModCommandType::RemoveBadge),
            6 => ::std::option::Option::Some(ServerModCommandDto_ModCommandType::PermissionUpdatedCommand),
            7 => ::std::option::Option::Some(ServerModCommandDto_ModCommandType::KickUser),
            8 => ::std::option::Option::Some(ServerModCommandDto_ModCommandType::ClearChat),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [ServerModCommandDto_ModCommandType] = &[
            ServerModCommandDto_ModCommandType::Invalid,
            ServerModCommandDto_ModCommandType::BanUser,
            ServerModCommandDto_ModCommandType::UnbanAccount,
            ServerModCommandDto_ModCommandType::UnbanIp,
            ServerModCommandDto_ModCommandType::AddBadge,
            ServerModCommandDto_ModCommandType::RemoveBadge,
            ServerModCommandDto_ModCommandType::PermissionUpdatedCommand,
            ServerModCommandDto_ModCommandType::KickUser,
            ServerModCommandDto_ModCommandType::ClearChat,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<ServerModCommandDto_ModCommandType>("ServerModCommandDto.ModCommandType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for ServerModCommandDto_ModCommandType {
}

impl ::std::default::Default for ServerModCommandDto_ModCommandType {
    fn default() -> Self {
        ServerModCommandDto_ModCommandType::Invalid
    }
}

impl ::protobuf::reflect::ProtobufValue for ServerModCommandDto_ModCommandType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ServerModCommandDU {
    // message fields
    pub usertag: ::std::string::String,
    pub serverCommand: ::protobuf::SingularPtrField<ServerModCommandDto>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ServerModCommandDU {
    fn default() -> &'a ServerModCommandDU {
        <ServerModCommandDU as ::protobuf::Message>::default_instance()
    }
}

impl ServerModCommandDU {
    pub fn new() -> ServerModCommandDU {
        ::std::default::Default::default()
    }

    // string usertag = 1;


    pub fn get_usertag(&self) -> &str {
        &self.usertag
    }
    pub fn clear_usertag(&mut self) {
        self.usertag.clear();
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.usertag = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        &mut self.usertag
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.usertag, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ServerModCommandDto serverCommand = 2;


    pub fn get_serverCommand(&self) -> &ServerModCommandDto {
        self.serverCommand.as_ref().unwrap_or_else(|| <ServerModCommandDto as ::protobuf::Message>::default_instance())
    }
    pub fn clear_serverCommand(&mut self) {
        self.serverCommand.clear();
    }

    pub fn has_serverCommand(&self) -> bool {
        self.serverCommand.is_some()
    }

    // Param is passed by value, moved
    pub fn set_serverCommand(&mut self, v: ServerModCommandDto) {
        self.serverCommand = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_serverCommand(&mut self) -> &mut ServerModCommandDto {
        if self.serverCommand.is_none() {
            self.serverCommand.set_default();
        }
        self.serverCommand.as_mut().unwrap()
    }

    // Take field
    pub fn take_serverCommand(&mut self) -> ServerModCommandDto {
        self.serverCommand.take().unwrap_or_else(|| ServerModCommandDto::new())
    }
}

impl ::protobuf::Message for ServerModCommandDU {
    fn is_initialized(&self) -> bool {
        for v in &self.serverCommand {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.usertag)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.serverCommand)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.usertag.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.usertag);
        }
        if let Some(ref v) = self.serverCommand.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.usertag.is_empty() {
            os.write_string(1, &self.usertag)?;
        }
        if let Some(ref v) = self.serverCommand.as_ref() {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ServerModCommandDU {
        ServerModCommandDU::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "usertag",
                |m: &ServerModCommandDU| { &m.usertag },
                |m: &mut ServerModCommandDU| { &mut m.usertag },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ServerModCommandDto>>(
                "serverCommand",
                |m: &ServerModCommandDU| { &m.serverCommand },
                |m: &mut ServerModCommandDU| { &mut m.serverCommand },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ServerModCommandDU>(
                "ServerModCommandDU",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ServerModCommandDU {
        static instance: ::protobuf::rt::LazyV2<ServerModCommandDU> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ServerModCommandDU::new)
    }
}

impl ::protobuf::Clear for ServerModCommandDU {
    fn clear(&mut self) {
        self.usertag.clear();
        self.serverCommand.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ServerModCommandDU {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ServerModCommandDU {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RoomChatServerCommandMessage {
    // message fields
    pub command: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RoomChatServerCommandMessage {
    fn default() -> &'a RoomChatServerCommandMessage {
        <RoomChatServerCommandMessage as ::protobuf::Message>::default_instance()
    }
}

impl RoomChatServerCommandMessage {
    pub fn new() -> RoomChatServerCommandMessage {
        ::std::default::Default::default()
    }

    // string command = 1;


    pub fn get_command(&self) -> &str {
        &self.command
    }
    pub fn clear_command(&mut self) {
        self.command.clear();
    }

    // Param is passed by value, moved
    pub fn set_command(&mut self, v: ::std::string::String) {
        self.command = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_command(&mut self) -> &mut ::std::string::String {
        &mut self.command
    }

    // Take field
    pub fn take_command(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.command, ::std::string::String::new())
    }
}

impl ::protobuf::Message for RoomChatServerCommandMessage {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.command)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.command.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.command);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.command.is_empty() {
            os.write_string(1, &self.command)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RoomChatServerCommandMessage {
        RoomChatServerCommandMessage::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "command",
                |m: &RoomChatServerCommandMessage| { &m.command },
                |m: &mut RoomChatServerCommandMessage| { &mut m.command },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<RoomChatServerCommandMessage>(
                "RoomChatServerCommandMessage",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static RoomChatServerCommandMessage {
        static instance: ::protobuf::rt::LazyV2<RoomChatServerCommandMessage> = ::protobuf::rt::LazyV2::INIT;
        instance.get(RoomChatServerCommandMessage::new)
    }
}

impl ::protobuf::Clear for RoomChatServerCommandMessage {
    fn clear(&mut self) {
        self.command.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for RoomChatServerCommandMessage {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for RoomChatServerCommandMessage {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct CreateRoomParam {
    // message fields
    pub RoomName: ::std::string::String,
    pub RoomType: super::room_renditions::RoomType,
    pub RoomOwner: ::std::string::String,
    pub RoomStatus: super::room_renditions::RoomStatus,
    pub MaxPlayers: i32,
    pub AutoRemove: bool,
    pub OnlyOwnerCanChat: bool,
    pub OnlyOwnerCanPlay: bool,
    pub AllowBlackMidi: bool,
    pub AllowGuests: bool,
    pub AllowBots: bool,
    pub OnlyMods: bool,
    pub FilterProfanity: bool,
    pub NoChatAllowed: bool,
    pub NoPlayingAllowed: bool,
    pub StageDetailsPROTO: ::std::vec::Vec<u8>,
    // message oneof groups
    pub _RoomID: ::std::option::Option<CreateRoomParam_oneof__RoomID>,
    pub _Password: ::std::option::Option<CreateRoomParam_oneof__Password>,
    pub _WelcomeMessage: ::std::option::Option<CreateRoomParam_oneof__WelcomeMessage>,
    pub _StageDetailsJSON: ::std::option::Option<CreateRoomParam_oneof__StageDetailsJSON>,
    pub _HostDetails: ::std::option::Option<CreateRoomParam_oneof__HostDetails>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a CreateRoomParam {
    fn default() -> &'a CreateRoomParam {
        <CreateRoomParam as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum CreateRoomParam_oneof__RoomID {
    RoomID(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum CreateRoomParam_oneof__Password {
    Password(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum CreateRoomParam_oneof__WelcomeMessage {
    WelcomeMessage(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum CreateRoomParam_oneof__StageDetailsJSON {
    StageDetailsJSON(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum CreateRoomParam_oneof__HostDetails {
    HostDetails(super::room_renditions::RoomHostDetails),
}

impl CreateRoomParam {
    pub fn new() -> CreateRoomParam {
        ::std::default::Default::default()
    }

    // string RoomID = 1;


    pub fn get_RoomID(&self) -> &str {
        match self._RoomID {
            ::std::option::Option::Some(CreateRoomParam_oneof__RoomID::RoomID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_RoomID(&mut self) {
        self._RoomID = ::std::option::Option::None;
    }

    pub fn has_RoomID(&self) -> bool {
        match self._RoomID {
            ::std::option::Option::Some(CreateRoomParam_oneof__RoomID::RoomID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_RoomID(&mut self, v: ::std::string::String) {
        self._RoomID = ::std::option::Option::Some(CreateRoomParam_oneof__RoomID::RoomID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_RoomID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(CreateRoomParam_oneof__RoomID::RoomID(_)) = self._RoomID {
        } else {
            self._RoomID = ::std::option::Option::Some(CreateRoomParam_oneof__RoomID::RoomID(::std::string::String::new()));
        }
        match self._RoomID {
            ::std::option::Option::Some(CreateRoomParam_oneof__RoomID::RoomID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_RoomID(&mut self) -> ::std::string::String {
        if self.has_RoomID() {
            match self._RoomID.take() {
                ::std::option::Option::Some(CreateRoomParam_oneof__RoomID::RoomID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string RoomName = 2;


    pub fn get_RoomName(&self) -> &str {
        &self.RoomName
    }
    pub fn clear_RoomName(&mut self) {
        self.RoomName.clear();
    }

    // Param is passed by value, moved
    pub fn set_RoomName(&mut self, v: ::std::string::String) {
        self.RoomName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_RoomName(&mut self) -> &mut ::std::string::String {
        &mut self.RoomName
    }

    // Take field
    pub fn take_RoomName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.RoomName, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomType RoomType = 3;


    pub fn get_RoomType(&self) -> super::room_renditions::RoomType {
        self.RoomType
    }
    pub fn clear_RoomType(&mut self) {
        self.RoomType = super::room_renditions::RoomType::Lobby;
    }

    // Param is passed by value, moved
    pub fn set_RoomType(&mut self, v: super::room_renditions::RoomType) {
        self.RoomType = v;
    }

    // string RoomOwner = 4;


    pub fn get_RoomOwner(&self) -> &str {
        &self.RoomOwner
    }
    pub fn clear_RoomOwner(&mut self) {
        self.RoomOwner.clear();
    }

    // Param is passed by value, moved
    pub fn set_RoomOwner(&mut self, v: ::std::string::String) {
        self.RoomOwner = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_RoomOwner(&mut self) -> &mut ::std::string::String {
        &mut self.RoomOwner
    }

    // Take field
    pub fn take_RoomOwner(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.RoomOwner, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomStatus RoomStatus = 5;


    pub fn get_RoomStatus(&self) -> super::room_renditions::RoomStatus {
        self.RoomStatus
    }
    pub fn clear_RoomStatus(&mut self) {
        self.RoomStatus = super::room_renditions::RoomStatus::Public;
    }

    // Param is passed by value, moved
    pub fn set_RoomStatus(&mut self, v: super::room_renditions::RoomStatus) {
        self.RoomStatus = v;
    }

    // string Password = 6;


    pub fn get_Password(&self) -> &str {
        match self._Password {
            ::std::option::Option::Some(CreateRoomParam_oneof__Password::Password(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_Password(&mut self) {
        self._Password = ::std::option::Option::None;
    }

    pub fn has_Password(&self) -> bool {
        match self._Password {
            ::std::option::Option::Some(CreateRoomParam_oneof__Password::Password(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_Password(&mut self, v: ::std::string::String) {
        self._Password = ::std::option::Option::Some(CreateRoomParam_oneof__Password::Password(v))
    }

    // Mutable pointer to the field.
    pub fn mut_Password(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(CreateRoomParam_oneof__Password::Password(_)) = self._Password {
        } else {
            self._Password = ::std::option::Option::Some(CreateRoomParam_oneof__Password::Password(::std::string::String::new()));
        }
        match self._Password {
            ::std::option::Option::Some(CreateRoomParam_oneof__Password::Password(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_Password(&mut self) -> ::std::string::String {
        if self.has_Password() {
            match self._Password.take() {
                ::std::option::Option::Some(CreateRoomParam_oneof__Password::Password(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // int32 MaxPlayers = 7;


    pub fn get_MaxPlayers(&self) -> i32 {
        self.MaxPlayers
    }
    pub fn clear_MaxPlayers(&mut self) {
        self.MaxPlayers = 0;
    }

    // Param is passed by value, moved
    pub fn set_MaxPlayers(&mut self, v: i32) {
        self.MaxPlayers = v;
    }

    // string WelcomeMessage = 8;


    pub fn get_WelcomeMessage(&self) -> &str {
        match self._WelcomeMessage {
            ::std::option::Option::Some(CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_WelcomeMessage(&mut self) {
        self._WelcomeMessage = ::std::option::Option::None;
    }

    pub fn has_WelcomeMessage(&self) -> bool {
        match self._WelcomeMessage {
            ::std::option::Option::Some(CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_WelcomeMessage(&mut self, v: ::std::string::String) {
        self._WelcomeMessage = ::std::option::Option::Some(CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(v))
    }

    // Mutable pointer to the field.
    pub fn mut_WelcomeMessage(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(_)) = self._WelcomeMessage {
        } else {
            self._WelcomeMessage = ::std::option::Option::Some(CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(::std::string::String::new()));
        }
        match self._WelcomeMessage {
            ::std::option::Option::Some(CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_WelcomeMessage(&mut self) -> ::std::string::String {
        if self.has_WelcomeMessage() {
            match self._WelcomeMessage.take() {
                ::std::option::Option::Some(CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // bool AutoRemove = 9;


    pub fn get_AutoRemove(&self) -> bool {
        self.AutoRemove
    }
    pub fn clear_AutoRemove(&mut self) {
        self.AutoRemove = false;
    }

    // Param is passed by value, moved
    pub fn set_AutoRemove(&mut self, v: bool) {
        self.AutoRemove = v;
    }

    // bool OnlyOwnerCanChat = 10;


    pub fn get_OnlyOwnerCanChat(&self) -> bool {
        self.OnlyOwnerCanChat
    }
    pub fn clear_OnlyOwnerCanChat(&mut self) {
        self.OnlyOwnerCanChat = false;
    }

    // Param is passed by value, moved
    pub fn set_OnlyOwnerCanChat(&mut self, v: bool) {
        self.OnlyOwnerCanChat = v;
    }

    // bool OnlyOwnerCanPlay = 11;


    pub fn get_OnlyOwnerCanPlay(&self) -> bool {
        self.OnlyOwnerCanPlay
    }
    pub fn clear_OnlyOwnerCanPlay(&mut self) {
        self.OnlyOwnerCanPlay = false;
    }

    // Param is passed by value, moved
    pub fn set_OnlyOwnerCanPlay(&mut self, v: bool) {
        self.OnlyOwnerCanPlay = v;
    }

    // bool AllowBlackMidi = 12;


    pub fn get_AllowBlackMidi(&self) -> bool {
        self.AllowBlackMidi
    }
    pub fn clear_AllowBlackMidi(&mut self) {
        self.AllowBlackMidi = false;
    }

    // Param is passed by value, moved
    pub fn set_AllowBlackMidi(&mut self, v: bool) {
        self.AllowBlackMidi = v;
    }

    // bool AllowGuests = 13;


    pub fn get_AllowGuests(&self) -> bool {
        self.AllowGuests
    }
    pub fn clear_AllowGuests(&mut self) {
        self.AllowGuests = false;
    }

    // Param is passed by value, moved
    pub fn set_AllowGuests(&mut self, v: bool) {
        self.AllowGuests = v;
    }

    // bool AllowBots = 14;


    pub fn get_AllowBots(&self) -> bool {
        self.AllowBots
    }
    pub fn clear_AllowBots(&mut self) {
        self.AllowBots = false;
    }

    // Param is passed by value, moved
    pub fn set_AllowBots(&mut self, v: bool) {
        self.AllowBots = v;
    }

    // bool OnlyMods = 15;


    pub fn get_OnlyMods(&self) -> bool {
        self.OnlyMods
    }
    pub fn clear_OnlyMods(&mut self) {
        self.OnlyMods = false;
    }

    // Param is passed by value, moved
    pub fn set_OnlyMods(&mut self, v: bool) {
        self.OnlyMods = v;
    }

    // bool FilterProfanity = 16;


    pub fn get_FilterProfanity(&self) -> bool {
        self.FilterProfanity
    }
    pub fn clear_FilterProfanity(&mut self) {
        self.FilterProfanity = false;
    }

    // Param is passed by value, moved
    pub fn set_FilterProfanity(&mut self, v: bool) {
        self.FilterProfanity = v;
    }

    // string StageDetailsJSON = 17;


    pub fn get_StageDetailsJSON(&self) -> &str {
        match self._StageDetailsJSON {
            ::std::option::Option::Some(CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_StageDetailsJSON(&mut self) {
        self._StageDetailsJSON = ::std::option::Option::None;
    }

    pub fn has_StageDetailsJSON(&self) -> bool {
        match self._StageDetailsJSON {
            ::std::option::Option::Some(CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_StageDetailsJSON(&mut self, v: ::std::string::String) {
        self._StageDetailsJSON = ::std::option::Option::Some(CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(v))
    }

    // Mutable pointer to the field.
    pub fn mut_StageDetailsJSON(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(_)) = self._StageDetailsJSON {
        } else {
            self._StageDetailsJSON = ::std::option::Option::Some(CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(::std::string::String::new()));
        }
        match self._StageDetailsJSON {
            ::std::option::Option::Some(CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_StageDetailsJSON(&mut self) -> ::std::string::String {
        if self.has_StageDetailsJSON() {
            match self._StageDetailsJSON.take() {
                ::std::option::Option::Some(CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.RoomRenditions.RoomHostDetails HostDetails = 18;


    pub fn get_HostDetails(&self) -> &super::room_renditions::RoomHostDetails {
        match self._HostDetails {
            ::std::option::Option::Some(CreateRoomParam_oneof__HostDetails::HostDetails(ref v)) => v,
            _ => <super::room_renditions::RoomHostDetails as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_HostDetails(&mut self) {
        self._HostDetails = ::std::option::Option::None;
    }

    pub fn has_HostDetails(&self) -> bool {
        match self._HostDetails {
            ::std::option::Option::Some(CreateRoomParam_oneof__HostDetails::HostDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_HostDetails(&mut self, v: super::room_renditions::RoomHostDetails) {
        self._HostDetails = ::std::option::Option::Some(CreateRoomParam_oneof__HostDetails::HostDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_HostDetails(&mut self) -> &mut super::room_renditions::RoomHostDetails {
        if let ::std::option::Option::Some(CreateRoomParam_oneof__HostDetails::HostDetails(_)) = self._HostDetails {
        } else {
            self._HostDetails = ::std::option::Option::Some(CreateRoomParam_oneof__HostDetails::HostDetails(super::room_renditions::RoomHostDetails::new()));
        }
        match self._HostDetails {
            ::std::option::Option::Some(CreateRoomParam_oneof__HostDetails::HostDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_HostDetails(&mut self) -> super::room_renditions::RoomHostDetails {
        if self.has_HostDetails() {
            match self._HostDetails.take() {
                ::std::option::Option::Some(CreateRoomParam_oneof__HostDetails::HostDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            super::room_renditions::RoomHostDetails::new()
        }
    }

    // bool NoChatAllowed = 19;


    pub fn get_NoChatAllowed(&self) -> bool {
        self.NoChatAllowed
    }
    pub fn clear_NoChatAllowed(&mut self) {
        self.NoChatAllowed = false;
    }

    // Param is passed by value, moved
    pub fn set_NoChatAllowed(&mut self, v: bool) {
        self.NoChatAllowed = v;
    }

    // bool NoPlayingAllowed = 20;


    pub fn get_NoPlayingAllowed(&self) -> bool {
        self.NoPlayingAllowed
    }
    pub fn clear_NoPlayingAllowed(&mut self) {
        self.NoPlayingAllowed = false;
    }

    // Param is passed by value, moved
    pub fn set_NoPlayingAllowed(&mut self, v: bool) {
        self.NoPlayingAllowed = v;
    }

    // bytes StageDetailsPROTO = 21;


    pub fn get_StageDetailsPROTO(&self) -> &[u8] {
        &self.StageDetailsPROTO
    }
    pub fn clear_StageDetailsPROTO(&mut self) {
        self.StageDetailsPROTO.clear();
    }

    // Param is passed by value, moved
    pub fn set_StageDetailsPROTO(&mut self, v: ::std::vec::Vec<u8>) {
        self.StageDetailsPROTO = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_StageDetailsPROTO(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.StageDetailsPROTO
    }

    // Take field
    pub fn take_StageDetailsPROTO(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.StageDetailsPROTO, ::std::vec::Vec::new())
    }
}

impl ::protobuf::Message for CreateRoomParam {
    fn is_initialized(&self) -> bool {
        if let Some(CreateRoomParam_oneof__HostDetails::HostDetails(ref v)) = self._HostDetails {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._RoomID = ::std::option::Option::Some(CreateRoomParam_oneof__RoomID::RoomID(is.read_string()?));
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.RoomName)?;
                },
                3 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.RoomType, 3, &mut self.unknown_fields)?
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.RoomOwner)?;
                },
                5 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.RoomStatus, 5, &mut self.unknown_fields)?
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._Password = ::std::option::Option::Some(CreateRoomParam_oneof__Password::Password(is.read_string()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.MaxPlayers = tmp;
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._WelcomeMessage = ::std::option::Option::Some(CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(is.read_string()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AutoRemove = tmp;
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.OnlyOwnerCanChat = tmp;
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.OnlyOwnerCanPlay = tmp;
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AllowBlackMidi = tmp;
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AllowGuests = tmp;
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.AllowBots = tmp;
                },
                15 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.OnlyMods = tmp;
                },
                16 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.FilterProfanity = tmp;
                },
                17 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._StageDetailsJSON = ::std::option::Option::Some(CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(is.read_string()?));
                },
                18 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._HostDetails = ::std::option::Option::Some(CreateRoomParam_oneof__HostDetails::HostDetails(is.read_message()?));
                },
                19 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.NoChatAllowed = tmp;
                },
                20 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.NoPlayingAllowed = tmp;
                },
                21 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.StageDetailsPROTO)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.RoomName.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.RoomName);
        }
        if self.RoomType != super::room_renditions::RoomType::Lobby {
            my_size += ::protobuf::rt::enum_size(3, self.RoomType);
        }
        if !self.RoomOwner.is_empty() {
            my_size += ::protobuf::rt::string_size(4, &self.RoomOwner);
        }
        if self.RoomStatus != super::room_renditions::RoomStatus::Public {
            my_size += ::protobuf::rt::enum_size(5, self.RoomStatus);
        }
        if self.MaxPlayers != 0 {
            my_size += ::protobuf::rt::value_size(7, self.MaxPlayers, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.AutoRemove != false {
            my_size += 2;
        }
        if self.OnlyOwnerCanChat != false {
            my_size += 2;
        }
        if self.OnlyOwnerCanPlay != false {
            my_size += 2;
        }
        if self.AllowBlackMidi != false {
            my_size += 2;
        }
        if self.AllowGuests != false {
            my_size += 2;
        }
        if self.AllowBots != false {
            my_size += 2;
        }
        if self.OnlyMods != false {
            my_size += 2;
        }
        if self.FilterProfanity != false {
            my_size += 3;
        }
        if self.NoChatAllowed != false {
            my_size += 3;
        }
        if self.NoPlayingAllowed != false {
            my_size += 3;
        }
        if !self.StageDetailsPROTO.is_empty() {
            my_size += ::protobuf::rt::bytes_size(21, &self.StageDetailsPROTO);
        }
        if let ::std::option::Option::Some(ref v) = self._RoomID {
            match v {
                &CreateRoomParam_oneof__RoomID::RoomID(ref v) => {
                    my_size += ::protobuf::rt::string_size(1, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._Password {
            match v {
                &CreateRoomParam_oneof__Password::Password(ref v) => {
                    my_size += ::protobuf::rt::string_size(6, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._WelcomeMessage {
            match v {
                &CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(ref v) => {
                    my_size += ::protobuf::rt::string_size(8, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._StageDetailsJSON {
            match v {
                &CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(ref v) => {
                    my_size += ::protobuf::rt::string_size(17, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._HostDetails {
            match v {
                &CreateRoomParam_oneof__HostDetails::HostDetails(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.RoomName.is_empty() {
            os.write_string(2, &self.RoomName)?;
        }
        if self.RoomType != super::room_renditions::RoomType::Lobby {
            os.write_enum(3, ::protobuf::ProtobufEnum::value(&self.RoomType))?;
        }
        if !self.RoomOwner.is_empty() {
            os.write_string(4, &self.RoomOwner)?;
        }
        if self.RoomStatus != super::room_renditions::RoomStatus::Public {
            os.write_enum(5, ::protobuf::ProtobufEnum::value(&self.RoomStatus))?;
        }
        if self.MaxPlayers != 0 {
            os.write_int32(7, self.MaxPlayers)?;
        }
        if self.AutoRemove != false {
            os.write_bool(9, self.AutoRemove)?;
        }
        if self.OnlyOwnerCanChat != false {
            os.write_bool(10, self.OnlyOwnerCanChat)?;
        }
        if self.OnlyOwnerCanPlay != false {
            os.write_bool(11, self.OnlyOwnerCanPlay)?;
        }
        if self.AllowBlackMidi != false {
            os.write_bool(12, self.AllowBlackMidi)?;
        }
        if self.AllowGuests != false {
            os.write_bool(13, self.AllowGuests)?;
        }
        if self.AllowBots != false {
            os.write_bool(14, self.AllowBots)?;
        }
        if self.OnlyMods != false {
            os.write_bool(15, self.OnlyMods)?;
        }
        if self.FilterProfanity != false {
            os.write_bool(16, self.FilterProfanity)?;
        }
        if self.NoChatAllowed != false {
            os.write_bool(19, self.NoChatAllowed)?;
        }
        if self.NoPlayingAllowed != false {
            os.write_bool(20, self.NoPlayingAllowed)?;
        }
        if !self.StageDetailsPROTO.is_empty() {
            os.write_bytes(21, &self.StageDetailsPROTO)?;
        }
        if let ::std::option::Option::Some(ref v) = self._RoomID {
            match v {
                &CreateRoomParam_oneof__RoomID::RoomID(ref v) => {
                    os.write_string(1, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._Password {
            match v {
                &CreateRoomParam_oneof__Password::Password(ref v) => {
                    os.write_string(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._WelcomeMessage {
            match v {
                &CreateRoomParam_oneof__WelcomeMessage::WelcomeMessage(ref v) => {
                    os.write_string(8, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._StageDetailsJSON {
            match v {
                &CreateRoomParam_oneof__StageDetailsJSON::StageDetailsJSON(ref v) => {
                    os.write_string(17, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._HostDetails {
            match v {
                &CreateRoomParam_oneof__HostDetails::HostDetails(ref v) => {
                    os.write_tag(18, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> CreateRoomParam {
        CreateRoomParam::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "RoomID",
                CreateRoomParam::has_RoomID,
                CreateRoomParam::get_RoomID,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "RoomName",
                |m: &CreateRoomParam| { &m.RoomName },
                |m: &mut CreateRoomParam| { &mut m.RoomName },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<super::room_renditions::RoomType>>(
                "RoomType",
                |m: &CreateRoomParam| { &m.RoomType },
                |m: &mut CreateRoomParam| { &mut m.RoomType },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "RoomOwner",
                |m: &CreateRoomParam| { &m.RoomOwner },
                |m: &mut CreateRoomParam| { &mut m.RoomOwner },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<super::room_renditions::RoomStatus>>(
                "RoomStatus",
                |m: &CreateRoomParam| { &m.RoomStatus },
                |m: &mut CreateRoomParam| { &mut m.RoomStatus },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "Password",
                CreateRoomParam::has_Password,
                CreateRoomParam::get_Password,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                "MaxPlayers",
                |m: &CreateRoomParam| { &m.MaxPlayers },
                |m: &mut CreateRoomParam| { &mut m.MaxPlayers },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "WelcomeMessage",
                CreateRoomParam::has_WelcomeMessage,
                CreateRoomParam::get_WelcomeMessage,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AutoRemove",
                |m: &CreateRoomParam| { &m.AutoRemove },
                |m: &mut CreateRoomParam| { &mut m.AutoRemove },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "OnlyOwnerCanChat",
                |m: &CreateRoomParam| { &m.OnlyOwnerCanChat },
                |m: &mut CreateRoomParam| { &mut m.OnlyOwnerCanChat },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "OnlyOwnerCanPlay",
                |m: &CreateRoomParam| { &m.OnlyOwnerCanPlay },
                |m: &mut CreateRoomParam| { &mut m.OnlyOwnerCanPlay },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AllowBlackMidi",
                |m: &CreateRoomParam| { &m.AllowBlackMidi },
                |m: &mut CreateRoomParam| { &mut m.AllowBlackMidi },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AllowGuests",
                |m: &CreateRoomParam| { &m.AllowGuests },
                |m: &mut CreateRoomParam| { &mut m.AllowGuests },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "AllowBots",
                |m: &CreateRoomParam| { &m.AllowBots },
                |m: &mut CreateRoomParam| { &mut m.AllowBots },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "OnlyMods",
                |m: &CreateRoomParam| { &m.OnlyMods },
                |m: &mut CreateRoomParam| { &mut m.OnlyMods },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "FilterProfanity",
                |m: &CreateRoomParam| { &m.FilterProfanity },
                |m: &mut CreateRoomParam| { &mut m.FilterProfanity },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "StageDetailsJSON",
                CreateRoomParam::has_StageDetailsJSON,
                CreateRoomParam::get_StageDetailsJSON,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::room_renditions::RoomHostDetails>(
                "HostDetails",
                CreateRoomParam::has_HostDetails,
                CreateRoomParam::get_HostDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "NoChatAllowed",
                |m: &CreateRoomParam| { &m.NoChatAllowed },
                |m: &mut CreateRoomParam| { &mut m.NoChatAllowed },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "NoPlayingAllowed",
                |m: &CreateRoomParam| { &m.NoPlayingAllowed },
                |m: &mut CreateRoomParam| { &mut m.NoPlayingAllowed },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                "StageDetailsPROTO",
                |m: &CreateRoomParam| { &m.StageDetailsPROTO },
                |m: &mut CreateRoomParam| { &mut m.StageDetailsPROTO },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<CreateRoomParam>(
                "CreateRoomParam",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static CreateRoomParam {
        static instance: ::protobuf::rt::LazyV2<CreateRoomParam> = ::protobuf::rt::LazyV2::INIT;
        instance.get(CreateRoomParam::new)
    }
}

impl ::protobuf::Clear for CreateRoomParam {
    fn clear(&mut self) {
        self._RoomID = ::std::option::Option::None;
        self.RoomName.clear();
        self.RoomType = super::room_renditions::RoomType::Lobby;
        self.RoomOwner.clear();
        self.RoomStatus = super::room_renditions::RoomStatus::Public;
        self._Password = ::std::option::Option::None;
        self.MaxPlayers = 0;
        self._WelcomeMessage = ::std::option::Option::None;
        self.AutoRemove = false;
        self.OnlyOwnerCanChat = false;
        self.OnlyOwnerCanPlay = false;
        self.AllowBlackMidi = false;
        self.AllowGuests = false;
        self.AllowBots = false;
        self.OnlyMods = false;
        self.FilterProfanity = false;
        self._StageDetailsJSON = ::std::option::Option::None;
        self._HostDetails = ::std::option::Option::None;
        self.NoChatAllowed = false;
        self.NoPlayingAllowed = false;
        self.StageDetailsPROTO.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for CreateRoomParam {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for CreateRoomParam {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AvatarCommandDto {
    // message fields
    pub commandType: AvatarCommandDto_AvatarCommandType,
    // message oneof groups
    pub commandData: ::std::option::Option<AvatarCommandDto_oneof_commandData>,
    pub _socketID: ::std::option::Option<AvatarCommandDto_oneof__socketID>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AvatarCommandDto {
    fn default() -> &'a AvatarCommandDto {
        <AvatarCommandDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCommandDto_oneof_commandData {
    worldPosition(super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition),
    intValue(i32),
    avatarCustomizationData(super::world_renditions::AvatarCustomizationData),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarCommandDto_oneof__socketID {
    socketID(::std::string::String),
}

impl AvatarCommandDto {
    pub fn new() -> AvatarCommandDto {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.AvatarCommandDto.AvatarCommandType commandType = 1;


    pub fn get_commandType(&self) -> AvatarCommandDto_AvatarCommandType {
        self.commandType
    }
    pub fn clear_commandType(&mut self) {
        self.commandType = AvatarCommandDto_AvatarCommandType::Invalid;
    }

    // Param is passed by value, moved
    pub fn set_commandType(&mut self, v: AvatarCommandDto_AvatarCommandType) {
        self.commandType = v;
    }

    // string socketID = 2;


    pub fn get_socketID(&self) -> &str {
        match self._socketID {
            ::std::option::Option::Some(AvatarCommandDto_oneof__socketID::socketID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_socketID(&mut self) {
        self._socketID = ::std::option::Option::None;
    }

    pub fn has_socketID(&self) -> bool {
        match self._socketID {
            ::std::option::Option::Some(AvatarCommandDto_oneof__socketID::socketID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self._socketID = ::std::option::Option::Some(AvatarCommandDto_oneof__socketID::socketID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(AvatarCommandDto_oneof__socketID::socketID(_)) = self._socketID {
        } else {
            self._socketID = ::std::option::Option::Some(AvatarCommandDto_oneof__socketID::socketID(::std::string::String::new()));
        }
        match self._socketID {
            ::std::option::Option::Some(AvatarCommandDto_oneof__socketID::socketID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        if self.has_socketID() {
            match self._socketID.take() {
                ::std::option::Option::Some(AvatarCommandDto_oneof__socketID::socketID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.AvatarWorldDataDto.AvatarMessageWorldPosition worldPosition = 3;


    pub fn get_worldPosition(&self) -> &super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition {
        match self.commandData {
            ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::worldPosition(ref v)) => v,
            _ => <super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_worldPosition(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_worldPosition(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::worldPosition(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_worldPosition(&mut self, v: super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition) {
        self.commandData = ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::worldPosition(v))
    }

    // Mutable pointer to the field.
    pub fn mut_worldPosition(&mut self) -> &mut super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition {
        if let ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::worldPosition(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::worldPosition(super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::worldPosition(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_worldPosition(&mut self) -> super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition {
        if self.has_worldPosition() {
            match self.commandData.take() {
                ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::worldPosition(v)) => v,
                _ => panic!(),
            }
        } else {
            super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition::new()
        }
    }

    // int32 intValue = 4;


    pub fn get_intValue(&self) -> i32 {
        match self.commandData {
            ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::intValue(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_intValue(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_intValue(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::intValue(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_intValue(&mut self, v: i32) {
        self.commandData = ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::intValue(v))
    }

    // .PianoRhythm.Serialization.WorldRenditions.AvatarCustomizationData avatarCustomizationData = 5;


    pub fn get_avatarCustomizationData(&self) -> &super::world_renditions::AvatarCustomizationData {
        match self.commandData {
            ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::avatarCustomizationData(ref v)) => v,
            _ => <super::world_renditions::AvatarCustomizationData as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_avatarCustomizationData(&mut self) {
        self.commandData = ::std::option::Option::None;
    }

    pub fn has_avatarCustomizationData(&self) -> bool {
        match self.commandData {
            ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::avatarCustomizationData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_avatarCustomizationData(&mut self, v: super::world_renditions::AvatarCustomizationData) {
        self.commandData = ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::avatarCustomizationData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_avatarCustomizationData(&mut self) -> &mut super::world_renditions::AvatarCustomizationData {
        if let ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::avatarCustomizationData(_)) = self.commandData {
        } else {
            self.commandData = ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::avatarCustomizationData(super::world_renditions::AvatarCustomizationData::new()));
        }
        match self.commandData {
            ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::avatarCustomizationData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_avatarCustomizationData(&mut self) -> super::world_renditions::AvatarCustomizationData {
        if self.has_avatarCustomizationData() {
            match self.commandData.take() {
                ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::avatarCustomizationData(v)) => v,
                _ => panic!(),
            }
        } else {
            super::world_renditions::AvatarCustomizationData::new()
        }
    }
}

impl ::protobuf::Message for AvatarCommandDto {
    fn is_initialized(&self) -> bool {
        if let Some(AvatarCommandDto_oneof_commandData::worldPosition(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(AvatarCommandDto_oneof_commandData::avatarCustomizationData(ref v)) = self.commandData {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.commandType, 1, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._socketID = ::std::option::Option::Some(AvatarCommandDto_oneof__socketID::socketID(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::worldPosition(is.read_message()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::intValue(is.read_int32()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.commandData = ::std::option::Option::Some(AvatarCommandDto_oneof_commandData::avatarCustomizationData(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.commandType != AvatarCommandDto_AvatarCommandType::Invalid {
            my_size += ::protobuf::rt::enum_size(1, self.commandType);
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &AvatarCommandDto_oneof_commandData::worldPosition(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &AvatarCommandDto_oneof_commandData::intValue(v) => {
                    my_size += ::protobuf::rt::value_size(4, v, ::protobuf::wire_format::WireTypeVarint);
                },
                &AvatarCommandDto_oneof_commandData::avatarCustomizationData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._socketID {
            match v {
                &AvatarCommandDto_oneof__socketID::socketID(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.commandType != AvatarCommandDto_AvatarCommandType::Invalid {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.commandType))?;
        }
        if let ::std::option::Option::Some(ref v) = self.commandData {
            match v {
                &AvatarCommandDto_oneof_commandData::worldPosition(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &AvatarCommandDto_oneof_commandData::intValue(v) => {
                    os.write_int32(4, v)?;
                },
                &AvatarCommandDto_oneof_commandData::avatarCustomizationData(ref v) => {
                    os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._socketID {
            match v {
                &AvatarCommandDto_oneof__socketID::socketID(ref v) => {
                    os.write_string(2, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AvatarCommandDto {
        AvatarCommandDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<AvatarCommandDto_AvatarCommandType>>(
                "commandType",
                |m: &AvatarCommandDto| { &m.commandType },
                |m: &mut AvatarCommandDto| { &mut m.commandType },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "socketID",
                AvatarCommandDto::has_socketID,
                AvatarCommandDto::get_socketID,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::user_renditions::AvatarWorldDataDto_AvatarMessageWorldPosition>(
                "worldPosition",
                AvatarCommandDto::has_worldPosition,
                AvatarCommandDto::get_worldPosition,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "intValue",
                AvatarCommandDto::has_intValue,
                AvatarCommandDto::get_intValue,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, super::world_renditions::AvatarCustomizationData>(
                "avatarCustomizationData",
                AvatarCommandDto::has_avatarCustomizationData,
                AvatarCommandDto::get_avatarCustomizationData,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AvatarCommandDto>(
                "AvatarCommandDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AvatarCommandDto {
        static instance: ::protobuf::rt::LazyV2<AvatarCommandDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AvatarCommandDto::new)
    }
}

impl ::protobuf::Clear for AvatarCommandDto {
    fn clear(&mut self) {
        self.commandType = AvatarCommandDto_AvatarCommandType::Invalid;
        self._socketID = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.commandData = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AvatarCommandDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AvatarCommandDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum AvatarCommandDto_AvatarCommandType {
    Invalid = 0,
    SetPosition = 1,
    SetPianoBenchTarget = 2,
    SetAvatarCustomizationData = 3,
}

impl ::protobuf::ProtobufEnum for AvatarCommandDto_AvatarCommandType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<AvatarCommandDto_AvatarCommandType> {
        match value {
            0 => ::std::option::Option::Some(AvatarCommandDto_AvatarCommandType::Invalid),
            1 => ::std::option::Option::Some(AvatarCommandDto_AvatarCommandType::SetPosition),
            2 => ::std::option::Option::Some(AvatarCommandDto_AvatarCommandType::SetPianoBenchTarget),
            3 => ::std::option::Option::Some(AvatarCommandDto_AvatarCommandType::SetAvatarCustomizationData),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [AvatarCommandDto_AvatarCommandType] = &[
            AvatarCommandDto_AvatarCommandType::Invalid,
            AvatarCommandDto_AvatarCommandType::SetPosition,
            AvatarCommandDto_AvatarCommandType::SetPianoBenchTarget,
            AvatarCommandDto_AvatarCommandType::SetAvatarCustomizationData,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<AvatarCommandDto_AvatarCommandType>("AvatarCommandDto.AvatarCommandType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for AvatarCommandDto_AvatarCommandType {
}

impl ::std::default::Default for AvatarCommandDto_AvatarCommandType {
    fn default() -> Self {
        AvatarCommandDto_AvatarCommandType::Invalid
    }
}

impl ::protobuf::reflect::ProtobufValue for AvatarCommandDto_AvatarCommandType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ServerMessage {
    // message fields
    pub messageType: ServerMessageType,
    // message oneof groups
    pub messageData: ::std::option::Option<ServerMessage_oneof_messageData>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ServerMessage {
    fn default() -> &'a ServerMessage {
        <ServerMessage as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum ServerMessage_oneof_messageData {
    chatMessageInputData(ChatMessageInputData),
    midiMessageInputDto(MidiMessageInputDto),
    serverCommand(ServerCommandDU),
    serverModCommand(ServerModCommandDU),
    roomChatServerCommand(RoomChatServerCommandMessage),
    createRoomParam(CreateRoomParam),
    joinRoomRequest(ServerMessage_JoinRoomByNameRequest),
    roomOwnerCommand(RoomOwnerCommandDU),
    avatarCommand(AvatarCommandDto),
}

impl ServerMessage {
    pub fn new() -> ServerMessage {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ServerMessageType messageType = 1;


    pub fn get_messageType(&self) -> ServerMessageType {
        self.messageType
    }
    pub fn clear_messageType(&mut self) {
        self.messageType = ServerMessageType::Invalid;
    }

    // Param is passed by value, moved
    pub fn set_messageType(&mut self, v: ServerMessageType) {
        self.messageType = v;
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ChatMessageInputData chatMessageInputData = 2;


    pub fn get_chatMessageInputData(&self) -> &ChatMessageInputData {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::chatMessageInputData(ref v)) => v,
            _ => <ChatMessageInputData as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_chatMessageInputData(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_chatMessageInputData(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::chatMessageInputData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_chatMessageInputData(&mut self, v: ChatMessageInputData) {
        self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::chatMessageInputData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_chatMessageInputData(&mut self) -> &mut ChatMessageInputData {
        if let ::std::option::Option::Some(ServerMessage_oneof_messageData::chatMessageInputData(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::chatMessageInputData(ChatMessageInputData::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::chatMessageInputData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_chatMessageInputData(&mut self) -> ChatMessageInputData {
        if self.has_chatMessageInputData() {
            match self.messageData.take() {
                ::std::option::Option::Some(ServerMessage_oneof_messageData::chatMessageInputData(v)) => v,
                _ => panic!(),
            }
        } else {
            ChatMessageInputData::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.MidiMessageInputDto midiMessageInputDto = 3;


    pub fn get_midiMessageInputDto(&self) -> &MidiMessageInputDto {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::midiMessageInputDto(ref v)) => v,
            _ => <MidiMessageInputDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_midiMessageInputDto(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_midiMessageInputDto(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::midiMessageInputDto(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_midiMessageInputDto(&mut self, v: MidiMessageInputDto) {
        self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::midiMessageInputDto(v))
    }

    // Mutable pointer to the field.
    pub fn mut_midiMessageInputDto(&mut self) -> &mut MidiMessageInputDto {
        if let ::std::option::Option::Some(ServerMessage_oneof_messageData::midiMessageInputDto(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::midiMessageInputDto(MidiMessageInputDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::midiMessageInputDto(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_midiMessageInputDto(&mut self) -> MidiMessageInputDto {
        if self.has_midiMessageInputDto() {
            match self.messageData.take() {
                ::std::option::Option::Some(ServerMessage_oneof_messageData::midiMessageInputDto(v)) => v,
                _ => panic!(),
            }
        } else {
            MidiMessageInputDto::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ServerCommandDU serverCommand = 4;


    pub fn get_serverCommand(&self) -> &ServerCommandDU {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::serverCommand(ref v)) => v,
            _ => <ServerCommandDU as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_serverCommand(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_serverCommand(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::serverCommand(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_serverCommand(&mut self, v: ServerCommandDU) {
        self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::serverCommand(v))
    }

    // Mutable pointer to the field.
    pub fn mut_serverCommand(&mut self) -> &mut ServerCommandDU {
        if let ::std::option::Option::Some(ServerMessage_oneof_messageData::serverCommand(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::serverCommand(ServerCommandDU::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::serverCommand(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_serverCommand(&mut self) -> ServerCommandDU {
        if self.has_serverCommand() {
            match self.messageData.take() {
                ::std::option::Option::Some(ServerMessage_oneof_messageData::serverCommand(v)) => v,
                _ => panic!(),
            }
        } else {
            ServerCommandDU::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ServerModCommandDU serverModCommand = 5;


    pub fn get_serverModCommand(&self) -> &ServerModCommandDU {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::serverModCommand(ref v)) => v,
            _ => <ServerModCommandDU as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_serverModCommand(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_serverModCommand(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::serverModCommand(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_serverModCommand(&mut self, v: ServerModCommandDU) {
        self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::serverModCommand(v))
    }

    // Mutable pointer to the field.
    pub fn mut_serverModCommand(&mut self) -> &mut ServerModCommandDU {
        if let ::std::option::Option::Some(ServerMessage_oneof_messageData::serverModCommand(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::serverModCommand(ServerModCommandDU::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::serverModCommand(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_serverModCommand(&mut self) -> ServerModCommandDU {
        if self.has_serverModCommand() {
            match self.messageData.take() {
                ::std::option::Option::Some(ServerMessage_oneof_messageData::serverModCommand(v)) => v,
                _ => panic!(),
            }
        } else {
            ServerModCommandDU::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.RoomChatServerCommandMessage roomChatServerCommand = 6;


    pub fn get_roomChatServerCommand(&self) -> &RoomChatServerCommandMessage {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::roomChatServerCommand(ref v)) => v,
            _ => <RoomChatServerCommandMessage as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomChatServerCommand(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_roomChatServerCommand(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::roomChatServerCommand(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomChatServerCommand(&mut self, v: RoomChatServerCommandMessage) {
        self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::roomChatServerCommand(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomChatServerCommand(&mut self) -> &mut RoomChatServerCommandMessage {
        if let ::std::option::Option::Some(ServerMessage_oneof_messageData::roomChatServerCommand(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::roomChatServerCommand(RoomChatServerCommandMessage::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::roomChatServerCommand(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomChatServerCommand(&mut self) -> RoomChatServerCommandMessage {
        if self.has_roomChatServerCommand() {
            match self.messageData.take() {
                ::std::option::Option::Some(ServerMessage_oneof_messageData::roomChatServerCommand(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomChatServerCommandMessage::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.CreateRoomParam createRoomParam = 7;


    pub fn get_createRoomParam(&self) -> &CreateRoomParam {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::createRoomParam(ref v)) => v,
            _ => <CreateRoomParam as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_createRoomParam(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_createRoomParam(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::createRoomParam(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_createRoomParam(&mut self, v: CreateRoomParam) {
        self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::createRoomParam(v))
    }

    // Mutable pointer to the field.
    pub fn mut_createRoomParam(&mut self) -> &mut CreateRoomParam {
        if let ::std::option::Option::Some(ServerMessage_oneof_messageData::createRoomParam(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::createRoomParam(CreateRoomParam::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::createRoomParam(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_createRoomParam(&mut self) -> CreateRoomParam {
        if self.has_createRoomParam() {
            match self.messageData.take() {
                ::std::option::Option::Some(ServerMessage_oneof_messageData::createRoomParam(v)) => v,
                _ => panic!(),
            }
        } else {
            CreateRoomParam::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.ServerMessage.JoinRoomByNameRequest joinRoomRequest = 8;


    pub fn get_joinRoomRequest(&self) -> &ServerMessage_JoinRoomByNameRequest {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::joinRoomRequest(ref v)) => v,
            _ => <ServerMessage_JoinRoomByNameRequest as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_joinRoomRequest(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_joinRoomRequest(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::joinRoomRequest(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_joinRoomRequest(&mut self, v: ServerMessage_JoinRoomByNameRequest) {
        self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::joinRoomRequest(v))
    }

    // Mutable pointer to the field.
    pub fn mut_joinRoomRequest(&mut self) -> &mut ServerMessage_JoinRoomByNameRequest {
        if let ::std::option::Option::Some(ServerMessage_oneof_messageData::joinRoomRequest(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::joinRoomRequest(ServerMessage_JoinRoomByNameRequest::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::joinRoomRequest(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_joinRoomRequest(&mut self) -> ServerMessage_JoinRoomByNameRequest {
        if self.has_joinRoomRequest() {
            match self.messageData.take() {
                ::std::option::Option::Some(ServerMessage_oneof_messageData::joinRoomRequest(v)) => v,
                _ => panic!(),
            }
        } else {
            ServerMessage_JoinRoomByNameRequest::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.RoomOwnerCommandDU roomOwnerCommand = 9;


    pub fn get_roomOwnerCommand(&self) -> &RoomOwnerCommandDU {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::roomOwnerCommand(ref v)) => v,
            _ => <RoomOwnerCommandDU as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_roomOwnerCommand(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_roomOwnerCommand(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::roomOwnerCommand(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomOwnerCommand(&mut self, v: RoomOwnerCommandDU) {
        self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::roomOwnerCommand(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomOwnerCommand(&mut self) -> &mut RoomOwnerCommandDU {
        if let ::std::option::Option::Some(ServerMessage_oneof_messageData::roomOwnerCommand(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::roomOwnerCommand(RoomOwnerCommandDU::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::roomOwnerCommand(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomOwnerCommand(&mut self) -> RoomOwnerCommandDU {
        if self.has_roomOwnerCommand() {
            match self.messageData.take() {
                ::std::option::Option::Some(ServerMessage_oneof_messageData::roomOwnerCommand(v)) => v,
                _ => panic!(),
            }
        } else {
            RoomOwnerCommandDU::new()
        }
    }

    // .PianoRhythm.Serialization.ClientToServer.Msgs.AvatarCommandDto avatarCommand = 10;


    pub fn get_avatarCommand(&self) -> &AvatarCommandDto {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::avatarCommand(ref v)) => v,
            _ => <AvatarCommandDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_avatarCommand(&mut self) {
        self.messageData = ::std::option::Option::None;
    }

    pub fn has_avatarCommand(&self) -> bool {
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::avatarCommand(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_avatarCommand(&mut self, v: AvatarCommandDto) {
        self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::avatarCommand(v))
    }

    // Mutable pointer to the field.
    pub fn mut_avatarCommand(&mut self) -> &mut AvatarCommandDto {
        if let ::std::option::Option::Some(ServerMessage_oneof_messageData::avatarCommand(_)) = self.messageData {
        } else {
            self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::avatarCommand(AvatarCommandDto::new()));
        }
        match self.messageData {
            ::std::option::Option::Some(ServerMessage_oneof_messageData::avatarCommand(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_avatarCommand(&mut self) -> AvatarCommandDto {
        if self.has_avatarCommand() {
            match self.messageData.take() {
                ::std::option::Option::Some(ServerMessage_oneof_messageData::avatarCommand(v)) => v,
                _ => panic!(),
            }
        } else {
            AvatarCommandDto::new()
        }
    }
}

impl ::protobuf::Message for ServerMessage {
    fn is_initialized(&self) -> bool {
        if let Some(ServerMessage_oneof_messageData::chatMessageInputData(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerMessage_oneof_messageData::midiMessageInputDto(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerMessage_oneof_messageData::serverCommand(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerMessage_oneof_messageData::serverModCommand(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerMessage_oneof_messageData::roomChatServerCommand(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerMessage_oneof_messageData::createRoomParam(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerMessage_oneof_messageData::joinRoomRequest(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerMessage_oneof_messageData::roomOwnerCommand(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        if let Some(ServerMessage_oneof_messageData::avatarCommand(ref v)) = self.messageData {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.messageType, 1, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::chatMessageInputData(is.read_message()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::midiMessageInputDto(is.read_message()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::serverCommand(is.read_message()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::serverModCommand(is.read_message()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::roomChatServerCommand(is.read_message()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::createRoomParam(is.read_message()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::joinRoomRequest(is.read_message()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::roomOwnerCommand(is.read_message()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.messageData = ::std::option::Option::Some(ServerMessage_oneof_messageData::avatarCommand(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.messageType != ServerMessageType::Invalid {
            my_size += ::protobuf::rt::enum_size(1, self.messageType);
        }
        if let ::std::option::Option::Some(ref v) = self.messageData {
            match v {
                &ServerMessage_oneof_messageData::chatMessageInputData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerMessage_oneof_messageData::midiMessageInputDto(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerMessage_oneof_messageData::serverCommand(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerMessage_oneof_messageData::serverModCommand(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerMessage_oneof_messageData::roomChatServerCommand(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerMessage_oneof_messageData::createRoomParam(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerMessage_oneof_messageData::joinRoomRequest(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerMessage_oneof_messageData::roomOwnerCommand(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &ServerMessage_oneof_messageData::avatarCommand(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.messageType != ServerMessageType::Invalid {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.messageType))?;
        }
        if let ::std::option::Option::Some(ref v) = self.messageData {
            match v {
                &ServerMessage_oneof_messageData::chatMessageInputData(ref v) => {
                    os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerMessage_oneof_messageData::midiMessageInputDto(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerMessage_oneof_messageData::serverCommand(ref v) => {
                    os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerMessage_oneof_messageData::serverModCommand(ref v) => {
                    os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerMessage_oneof_messageData::roomChatServerCommand(ref v) => {
                    os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerMessage_oneof_messageData::createRoomParam(ref v) => {
                    os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerMessage_oneof_messageData::joinRoomRequest(ref v) => {
                    os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerMessage_oneof_messageData::roomOwnerCommand(ref v) => {
                    os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &ServerMessage_oneof_messageData::avatarCommand(ref v) => {
                    os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ServerMessage {
        ServerMessage::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<ServerMessageType>>(
                "messageType",
                |m: &ServerMessage| { &m.messageType },
                |m: &mut ServerMessage| { &mut m.messageType },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ChatMessageInputData>(
                "chatMessageInputData",
                ServerMessage::has_chatMessageInputData,
                ServerMessage::get_chatMessageInputData,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, MidiMessageInputDto>(
                "midiMessageInputDto",
                ServerMessage::has_midiMessageInputDto,
                ServerMessage::get_midiMessageInputDto,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ServerCommandDU>(
                "serverCommand",
                ServerMessage::has_serverCommand,
                ServerMessage::get_serverCommand,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ServerModCommandDU>(
                "serverModCommand",
                ServerMessage::has_serverModCommand,
                ServerMessage::get_serverModCommand,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomChatServerCommandMessage>(
                "roomChatServerCommand",
                ServerMessage::has_roomChatServerCommand,
                ServerMessage::get_roomChatServerCommand,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, CreateRoomParam>(
                "createRoomParam",
                ServerMessage::has_createRoomParam,
                ServerMessage::get_createRoomParam,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ServerMessage_JoinRoomByNameRequest>(
                "joinRoomRequest",
                ServerMessage::has_joinRoomRequest,
                ServerMessage::get_joinRoomRequest,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, RoomOwnerCommandDU>(
                "roomOwnerCommand",
                ServerMessage::has_roomOwnerCommand,
                ServerMessage::get_roomOwnerCommand,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AvatarCommandDto>(
                "avatarCommand",
                ServerMessage::has_avatarCommand,
                ServerMessage::get_avatarCommand,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ServerMessage>(
                "ServerMessage",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ServerMessage {
        static instance: ::protobuf::rt::LazyV2<ServerMessage> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ServerMessage::new)
    }
}

impl ::protobuf::Clear for ServerMessage {
    fn clear(&mut self) {
        self.messageType = ServerMessageType::Invalid;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.messageData = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ServerMessage {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ServerMessage {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ServerMessage_JoinRoomByNameRequest {
    // message fields
    pub roomName: ::std::string::String,
    pub createRoomIfNotExist: bool,
    pub joinNextAvailableLobby: bool,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ServerMessage_JoinRoomByNameRequest {
    fn default() -> &'a ServerMessage_JoinRoomByNameRequest {
        <ServerMessage_JoinRoomByNameRequest as ::protobuf::Message>::default_instance()
    }
}

impl ServerMessage_JoinRoomByNameRequest {
    pub fn new() -> ServerMessage_JoinRoomByNameRequest {
        ::std::default::Default::default()
    }

    // string roomName = 1;


    pub fn get_roomName(&self) -> &str {
        &self.roomName
    }
    pub fn clear_roomName(&mut self) {
        self.roomName.clear();
    }

    // Param is passed by value, moved
    pub fn set_roomName(&mut self, v: ::std::string::String) {
        self.roomName = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_roomName(&mut self) -> &mut ::std::string::String {
        &mut self.roomName
    }

    // Take field
    pub fn take_roomName(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.roomName, ::std::string::String::new())
    }

    // bool createRoomIfNotExist = 2;


    pub fn get_createRoomIfNotExist(&self) -> bool {
        self.createRoomIfNotExist
    }
    pub fn clear_createRoomIfNotExist(&mut self) {
        self.createRoomIfNotExist = false;
    }

    // Param is passed by value, moved
    pub fn set_createRoomIfNotExist(&mut self, v: bool) {
        self.createRoomIfNotExist = v;
    }

    // bool joinNextAvailableLobby = 3;


    pub fn get_joinNextAvailableLobby(&self) -> bool {
        self.joinNextAvailableLobby
    }
    pub fn clear_joinNextAvailableLobby(&mut self) {
        self.joinNextAvailableLobby = false;
    }

    // Param is passed by value, moved
    pub fn set_joinNextAvailableLobby(&mut self, v: bool) {
        self.joinNextAvailableLobby = v;
    }
}

impl ::protobuf::Message for ServerMessage_JoinRoomByNameRequest {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.roomName)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.createRoomIfNotExist = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.joinNextAvailableLobby = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.roomName.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.roomName);
        }
        if self.createRoomIfNotExist != false {
            my_size += 2;
        }
        if self.joinNextAvailableLobby != false {
            my_size += 2;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.roomName.is_empty() {
            os.write_string(1, &self.roomName)?;
        }
        if self.createRoomIfNotExist != false {
            os.write_bool(2, self.createRoomIfNotExist)?;
        }
        if self.joinNextAvailableLobby != false {
            os.write_bool(3, self.joinNextAvailableLobby)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ServerMessage_JoinRoomByNameRequest {
        ServerMessage_JoinRoomByNameRequest::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "roomName",
                |m: &ServerMessage_JoinRoomByNameRequest| { &m.roomName },
                |m: &mut ServerMessage_JoinRoomByNameRequest| { &mut m.roomName },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "createRoomIfNotExist",
                |m: &ServerMessage_JoinRoomByNameRequest| { &m.createRoomIfNotExist },
                |m: &mut ServerMessage_JoinRoomByNameRequest| { &mut m.createRoomIfNotExist },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "joinNextAvailableLobby",
                |m: &ServerMessage_JoinRoomByNameRequest| { &m.joinNextAvailableLobby },
                |m: &mut ServerMessage_JoinRoomByNameRequest| { &mut m.joinNextAvailableLobby },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ServerMessage_JoinRoomByNameRequest>(
                "ServerMessage.JoinRoomByNameRequest",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ServerMessage_JoinRoomByNameRequest {
        static instance: ::protobuf::rt::LazyV2<ServerMessage_JoinRoomByNameRequest> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ServerMessage_JoinRoomByNameRequest::new)
    }
}

impl ::protobuf::Clear for ServerMessage_JoinRoomByNameRequest {
    fn clear(&mut self) {
        self.roomName.clear();
        self.createRoomIfNotExist = false;
        self.joinNextAvailableLobby = false;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ServerMessage_JoinRoomByNameRequest {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ServerMessage_JoinRoomByNameRequest {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum ServerMessageType {
    Invalid = 0,
    Hello = 1,
    Disconnect = 2,
    RoomChatMessage = 3,
    RoomChatServerCommand = 4,
    MousePosMessage = 5,
    MidiMessage = 6,
    ServerCommand = 7,
    ServerModCommand = 8,
    CreateRoomCommand = 9,
    UpdateRoomCommand = 10,
    JoinRoomByName = 11,
    RoomOwnerCommand = 12,
    JoinNextAvailableLobby = 13,
    AvatarCommand = 14,
}

impl ::protobuf::ProtobufEnum for ServerMessageType {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<ServerMessageType> {
        match value {
            0 => ::std::option::Option::Some(ServerMessageType::Invalid),
            1 => ::std::option::Option::Some(ServerMessageType::Hello),
            2 => ::std::option::Option::Some(ServerMessageType::Disconnect),
            3 => ::std::option::Option::Some(ServerMessageType::RoomChatMessage),
            4 => ::std::option::Option::Some(ServerMessageType::RoomChatServerCommand),
            5 => ::std::option::Option::Some(ServerMessageType::MousePosMessage),
            6 => ::std::option::Option::Some(ServerMessageType::MidiMessage),
            7 => ::std::option::Option::Some(ServerMessageType::ServerCommand),
            8 => ::std::option::Option::Some(ServerMessageType::ServerModCommand),
            9 => ::std::option::Option::Some(ServerMessageType::CreateRoomCommand),
            10 => ::std::option::Option::Some(ServerMessageType::UpdateRoomCommand),
            11 => ::std::option::Option::Some(ServerMessageType::JoinRoomByName),
            12 => ::std::option::Option::Some(ServerMessageType::RoomOwnerCommand),
            13 => ::std::option::Option::Some(ServerMessageType::JoinNextAvailableLobby),
            14 => ::std::option::Option::Some(ServerMessageType::AvatarCommand),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [ServerMessageType] = &[
            ServerMessageType::Invalid,
            ServerMessageType::Hello,
            ServerMessageType::Disconnect,
            ServerMessageType::RoomChatMessage,
            ServerMessageType::RoomChatServerCommand,
            ServerMessageType::MousePosMessage,
            ServerMessageType::MidiMessage,
            ServerMessageType::ServerCommand,
            ServerMessageType::ServerModCommand,
            ServerMessageType::CreateRoomCommand,
            ServerMessageType::UpdateRoomCommand,
            ServerMessageType::JoinRoomByName,
            ServerMessageType::RoomOwnerCommand,
            ServerMessageType::JoinNextAvailableLobby,
            ServerMessageType::AvatarCommand,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<ServerMessageType>("ServerMessageType", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for ServerMessageType {
}

impl ::std::default::Default for ServerMessageType {
    fn default() -> Self {
        ServerMessageType::Invalid
    }
}

impl ::protobuf::reflect::ProtobufValue for ServerMessageType {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x14server-message.proto\x12-PianoRhythm.Serialization.ClientToServer.\
    Msgs\x1a\x15room-renditions.proto\x1a\x15user-renditions.proto\x1a\x15mi\
    di-renditions.proto\x1a\x16world-renditions.proto\"\xd3\x02\n\x14ChatMes\
    sageInputData\x12\x12\n\x04text\x18\x01\x20\x01(\tR\x04text\x12t\n\x07op\
    tions\x18\x02\x20\x01(\x0b2Z.PianoRhythm.Serialization.ClientToServer.Ms\
    gs.ChatMessageInputData.ChatMessageDataOptionsR\x07options\x1a\xb0\x01\n\
    \x16ChatMessageDataOptions\x12$\n\rsyncToDiscord\x18\x01\x20\x01(\x08R\r\
    syncToDiscord\x12\"\n\x0cisFromPlugin\x18\x02\x20\x01(\x08R\x0cisFromPlu\
    gin\x12&\n\x0emessageReplyID\x18\x03\x20\x01(\tR\x0emessageReplyID\x12$\
    \n\rmessageEditID\x18\x04\x20\x01(\tR\rmessageEditID\"\x8a\x02\n\x13Midi\
    MessageInputDto\x12\x12\n\x04time\x18\x01\x20\x01(\tR\x04time\x12m\n\x04\
    data\x18\x02\x20\x03(\x0b2Y.PianoRhythm.Serialization.ClientToServer.Msg\
    s.MidiMessageInputDto.MidiMessageInputBufferR\x04data\x1ap\n\x16MidiMess\
    ageInputBuffer\x12@\n\x04data\x18\x01\x20\x01(\x0b2,.PianoRhythm.Seriali\
    zation.Midi.Msgs.MidiDtoR\x04data\x12\x14\n\x05delay\x18\x02\x20\x01(\
    \x01R\x05delay\"H\n\x12RoomIDWithPassword\x12\x16\n\x06roomID\x18\x01\
    \x20\x01(\tR\x06roomID\x12\x1a\n\x08password\x18\x02\x20\x01(\tR\x08pass\
    word\"F\n\x0cLoginDataDto\x12\x1a\n\x08username\x18\x01\x20\x01(\tR\x08u\
    sername\x12\x1a\n\x08password\x18\x02\x20\x01(\tR\x08password\"&\n\x0eSi\
    mpleEmailDto\x12\x14\n\x05email\x18\x01\x20\x01(\tR\x05email\"r\n\x14Res\
    etPasswordDataDto\x12\x1a\n\x08password\x18\x01\x20\x01(\tR\x08password\
    \x12(\n\x0fconfirmpassword\x18\x02\x20\x01(\tR\x0fconfirmpassword\x12\
    \x14\n\x05token\x18\x03\x20\x01(\tR\x05token\"\xac\x01\n\x10Registration\
    Data\x12\x1a\n\x08username\x18\x01\x20\x01(\tR\x08username\x12\x14\n\x05\
    email\x18\x02\x20\x01(\tR\x05email\x12\x1a\n\x08password\x18\x03\x20\x01\
    (\tR\x08password\x12(\n\x0fconfirmpassword\x18\x04\x20\x01(\tR\x0fconfir\
    mpassword\x12\x20\n\x0bacceptedtos\x18\x05\x20\x01(\x08R\x0bacceptedtos\
    \"\xb6\x0c\n\x0fServerCommandDU\x12l\n\x0bcommandType\x18\x01\x20\x01(\
    \x0e2J.PianoRhythm.Serialization.ClientToServer.Msgs.ServerCommandDU.Com\
    mandTypeR\x0bcommandType\x12\x18\n\x06roomID\x18\x02\x20\x01(\tH\0R\x06r\
    oomID\x12\x1c\n\x08roomName\x18\x03\x20\x01(\tH\0R\x08roomName\x12$\n\
    \x0croomIDorName\x18\x04\x20\x01(\tH\0R\x0croomIDorName\x12\x1a\n\x07use\
    rtag\x18\x05\x20\x01(\tH\0R\x07usertag\x12\x1e\n\tjsonValue\x18\x07\x20\
    \x01(\tH\0R\tjsonValue\x12\x1a\n\x07boolean\x18\x08\x20\x01(\x08H\0R\x07\
    boolean\x12q\n\x11roomIDAndPassword\x18\t\x20\x01(\x0b2A.PianoRhythm.Ser\
    ialization.ClientToServer.Msgs.RoomIDWithPasswordH\0R\x11roomIDAndPasswo\
    rd\x12[\n\tloginData\x18\n\x20\x01(\x0b2;.PianoRhythm.Serialization.Clie\
    ntToServer.Msgs.LoginDataDtoH\0R\tloginData\x12]\n\temailData\x18\x0b\
    \x20\x01(\x0b2=.PianoRhythm.Serialization.ClientToServer.Msgs.SimpleEmai\
    lDtoH\0R\temailData\x12s\n\x11resetPasswordData\x18\x0c\x20\x01(\x0b2C.P\
    ianoRhythm.Serialization.ClientToServer.Msgs.ResetPasswordDataDtoH\0R\
    \x11resetPasswordData\x12m\n\x10registrationData\x18\r\x20\x01(\x0b2?.Pi\
    anoRhythm.Serialization.ClientToServer.Msgs.RegistrationDataH\0R\x10regi\
    strationData\x12\"\n\x0bstringValue\x18\x0e\x20\x01(\tH\0R\x0bstringValu\
    e\"\xb8\x05\n\x0bCommandType\x12\x0b\n\x07Invalid\x10\0\x12\x15\n\x11Use\
    rUpdateCommand\x10\x02\x12\x1c\n\x18PermissionUpdatedCommand\x10\x03\x12\
    \x08\n\x04Join\x10\x07\x12\x18\n\x14JoinRoomWithPassword\x10\x08\x12\x14\
    \n\x10CreateOrJoinRoom\x10\n\x12\x13\n\x0fGetRoomSettings\x10\x0b\x12\
    \x0e\n\nEnterLobby\x10\x0c\x12\x0e\n\nCreateRoom\x10\r\x12\x0e\n\nUpdate\
    Room\x10\x0e\x12\r\n\tLeaveRoom\x10\x0f\x12\x08\n\x04Ping\x10\x10\x12\
    \x0f\n\x0bGetAllRooms\x10\x11\x12\x15\n\x11GetAllUsersInRoom\x10\x12\x12\
    \t\n\x05Login\x10\x13\x12\x0c\n\x08Register\x10\x14\x12\x1b\n\x17ResendE\
    mailVerification\x10\x15\x12\x11\n\rResetPassword\x10\x16\x12\x12\n\x0eF\
    orgotPassword\x10\x17\x12\x0c\n\x08IsTyping\x10\x18\x12\x0e\n\nRoomLoade\
    d\x10\x19\x12\x18\n\x14UploadClientSettings\x10\x1a\x12\x15\n\x11SendFri\
    endRequest\x10\x1b\x12\x17\n\x13SendUnfriendRequest\x10\x1c\x12\x17\n\
    \x13AcceptFriendRequest\x10\x1d\x12\x15\n\x11DenyFriendRequest\x10\x1e\
    \x12\x1b\n\x17DeleteSentFriendRequest\x10\x1f\x12)\n%UploadOrchestraMode\
    lCustomizationData\x10!\x12\x17\n\x13UploadCharacterData\x10\"\x12\x19\n\
    \x15DeleteChatMessageById\x10#\x12\x1e\n\x1aDeleteChatMessageByUsertag\
    \x10$\x12\x16\n\x12GetRoomFullDetails\x10%B\r\n\x0bcommandData\"\xc1\x04\
    \n\x12RoomOwnerCommandDU\x12o\n\x0bcommandType\x18\x01\x20\x01(\x0e2M.Pi\
    anoRhythm.Serialization.ClientToServer.Msgs.RoomOwnerCommandDU.CommandTy\
    peR\x0bcommandType\x12\x1a\n\x07usertag\x18\x02\x20\x01(\tH\0R\x07userta\
    g\x12\x1c\n\x08socketID\x18\x03\x20\x01(\tH\0R\x08socketID\x12z\n\x0ekic\
    kedUserData\x18\x04\x20\x01(\x0b2P.PianoRhythm.Serialization.ClientToSer\
    ver.Msgs.RoomOwnerCommandDU.KickedUserDataH\0R\x0ekickedUserData\x1ah\n\
    \x0eKickedUserData\x12\x18\n\x07usertag\x18\x01\x20\x01(\tR\x07usertag\
    \x12\x1a\n\x08socketID\x18\x02\x20\x01(\tR\x08socketID\x12\x17\n\x04time\
    \x18\x03\x20\x01(\tH\0R\x04time\x88\x01\x01B\x07\n\x05_time\"\x8a\x01\n\
    \x0bCommandType\x12\x0b\n\x07Invalid\x10\0\x12\x0c\n\x08KickUser\x10\x01\
    \x12\x15\n\x11RemovedKickedUser\x10\x02\x12\x16\n\x12GetKickedUsersList\
    \x10\x03\x12\r\n\tGiveCrown\x10\x04\x12\r\n\tClearChat\x10\x05\x12\x13\n\
    \x0fGetRoomSettings\x10\x06B\r\n\x0bcommandData\"\xa2\x04\n\x13ServerMod\
    CommandDto\x12s\n\x0bcommandType\x18\x01\x20\x01(\x0e2Q.PianoRhythm.Seri\
    alization.ClientToServer.Msgs.ServerModCommandDto.ModCommandTypeR\x0bcom\
    mandType\x12\"\n\x0bstringValue\x18\x02\x20\x01(\tH\0R\x0bstringValue\
    \x12\x1e\n\tjsonValue\x18\x03\x20\x01(\tH\0R\tjsonValue\x12\x1a\n\x07use\
    rtag\x18\x04\x20\x01(\tH\0R\x07usertag\x12\x1c\n\x08socketID\x18\x05\x20\
    \x01(\tH\0R\x08socketID\x12c\n\tuserBadge\x18\x06\x20\x01(\x0b2C.PianoRh\
    ythm.Serialization.ServerToClient.UserRenditions.UserBadgesH\0R\tuserBad\
    ge\"\xa3\x01\n\x0eModCommandType\x12\x0b\n\x07Invalid\x10\0\x12\x0b\n\
    \x07BanUser\x10\x01\x12\x10\n\x0cUnbanAccount\x10\x02\x12\x0b\n\x07Unban\
    Ip\x10\x03\x12\x0c\n\x08AddBadge\x10\x04\x12\x0f\n\x0bRemoveBadge\x10\
    \x05\x12\x1c\n\x18PermissionUpdatedCommand\x10\x06\x12\x0c\n\x08KickUser\
    \x10\x07\x12\r\n\tClearChat\x10\x08B\r\n\x0bcommandData\"\x98\x01\n\x12S\
    erverModCommandDU\x12\x18\n\x07usertag\x18\x01\x20\x01(\tR\x07usertag\
    \x12h\n\rserverCommand\x18\x02\x20\x01(\x0b2B.PianoRhythm.Serialization.\
    ClientToServer.Msgs.ServerModCommandDtoR\rserverCommand\"8\n\x1cRoomChat\
    ServerCommandMessage\x12\x18\n\x07command\x18\x01\x20\x01(\tR\x07command\
    \"\x85\x08\n\x0fCreateRoomParam\x12\x1b\n\x06RoomID\x18\x01\x20\x01(\tH\
    \0R\x06RoomID\x88\x01\x01\x12\x1a\n\x08RoomName\x18\x02\x20\x01(\tR\x08R\
    oomName\x12N\n\x08RoomType\x18\x03\x20\x01(\x0e22.PianoRhythm.Serializat\
    ion.RoomRenditions.RoomTypeR\x08RoomType\x12\x1c\n\tRoomOwner\x18\x04\
    \x20\x01(\tR\tRoomOwner\x12T\n\nRoomStatus\x18\x05\x20\x01(\x0e24.PianoR\
    hythm.Serialization.RoomRenditions.RoomStatusR\nRoomStatus\x12\x1f\n\x08\
    Password\x18\x06\x20\x01(\tH\x01R\x08Password\x88\x01\x01\x12\x1e\n\nMax\
    Players\x18\x07\x20\x01(\x05R\nMaxPlayers\x12+\n\x0eWelcomeMessage\x18\
    \x08\x20\x01(\tH\x02R\x0eWelcomeMessage\x88\x01\x01\x12\x1e\n\nAutoRemov\
    e\x18\t\x20\x01(\x08R\nAutoRemove\x12*\n\x10OnlyOwnerCanChat\x18\n\x20\
    \x01(\x08R\x10OnlyOwnerCanChat\x12*\n\x10OnlyOwnerCanPlay\x18\x0b\x20\
    \x01(\x08R\x10OnlyOwnerCanPlay\x12&\n\x0eAllowBlackMidi\x18\x0c\x20\x01(\
    \x08R\x0eAllowBlackMidi\x12\x20\n\x0bAllowGuests\x18\r\x20\x01(\x08R\x0b\
    AllowGuests\x12\x1c\n\tAllowBots\x18\x0e\x20\x01(\x08R\tAllowBots\x12\
    \x1a\n\x08OnlyMods\x18\x0f\x20\x01(\x08R\x08OnlyMods\x12(\n\x0fFilterPro\
    fanity\x18\x10\x20\x01(\x08R\x0fFilterProfanity\x12/\n\x10StageDetailsJS\
    ON\x18\x11\x20\x01(\tH\x03R\x10StageDetailsJSON\x88\x01\x01\x12`\n\x0bHo\
    stDetails\x18\x12\x20\x01(\x0b29.PianoRhythm.Serialization.RoomRendition\
    s.RoomHostDetailsH\x04R\x0bHostDetails\x88\x01\x01\x12$\n\rNoChatAllowed\
    \x18\x13\x20\x01(\x08R\rNoChatAllowed\x12*\n\x10NoPlayingAllowed\x18\x14\
    \x20\x01(\x08R\x10NoPlayingAllowed\x12,\n\x11StageDetailsPROTO\x18\x15\
    \x20\x01(\x0cR\x11StageDetailsPROTOB\t\n\x07_RoomIDB\x0b\n\t_PasswordB\
    \x11\n\x0f_WelcomeMessageB\x13\n\x11_StageDetailsJSONB\x0e\n\x0c_HostDet\
    ails\"\xdf\x04\n\x10AvatarCommandDto\x12s\n\x0bcommandType\x18\x01\x20\
    \x01(\x0e2Q.PianoRhythm.Serialization.ClientToServer.Msgs.AvatarCommandD\
    to.AvatarCommandTypeR\x0bcommandType\x12\x1f\n\x08socketID\x18\x02\x20\
    \x01(\tH\x01R\x08socketID\x88\x01\x01\x12\x8e\x01\n\rworldPosition\x18\
    \x03\x20\x01(\x0b2f.PianoRhythm.Serialization.ServerToClient.UserRenditi\
    ons.AvatarWorldDataDto.AvatarMessageWorldPositionH\0R\rworldPosition\x12\
    \x1c\n\x08intValue\x18\x04\x20\x01(\x05H\0R\x08intValue\x12~\n\x17avatar\
    CustomizationData\x18\x05\x20\x01(\x0b2B.PianoRhythm.Serialization.World\
    Renditions.AvatarCustomizationDataH\0R\x17avatarCustomizationData\"j\n\
    \x11AvatarCommandType\x12\x0b\n\x07Invalid\x10\0\x12\x0f\n\x0bSetPositio\
    n\x10\x01\x12\x17\n\x13SetPianoBenchTarget\x10\x02\x12\x1e\n\x1aSetAvata\
    rCustomizationData\x10\x03B\r\n\x0bcommandDataB\x0b\n\t_socketID\"\xbc\n\
    \n\rServerMessage\x12b\n\x0bmessageType\x18\x01\x20\x01(\x0e2@.PianoRhyt\
    hm.Serialization.ClientToServer.Msgs.ServerMessageTypeR\x0bmessageType\
    \x12y\n\x14chatMessageInputData\x18\x02\x20\x01(\x0b2C.PianoRhythm.Seria\
    lization.ClientToServer.Msgs.ChatMessageInputDataH\0R\x14chatMessageInpu\
    tData\x12v\n\x13midiMessageInputDto\x18\x03\x20\x01(\x0b2B.PianoRhythm.S\
    erialization.ClientToServer.Msgs.MidiMessageInputDtoH\0R\x13midiMessageI\
    nputDto\x12f\n\rserverCommand\x18\x04\x20\x01(\x0b2>.PianoRhythm.Seriali\
    zation.ClientToServer.Msgs.ServerCommandDUH\0R\rserverCommand\x12o\n\x10\
    serverModCommand\x18\x05\x20\x01(\x0b2A.PianoRhythm.Serialization.Client\
    ToServer.Msgs.ServerModCommandDUH\0R\x10serverModCommand\x12\x83\x01\n\
    \x15roomChatServerCommand\x18\x06\x20\x01(\x0b2K.PianoRhythm.Serializati\
    on.ClientToServer.Msgs.RoomChatServerCommandMessageH\0R\x15roomChatServe\
    rCommand\x12j\n\x0fcreateRoomParam\x18\x07\x20\x01(\x0b2>.PianoRhythm.Se\
    rialization.ClientToServer.Msgs.CreateRoomParamH\0R\x0fcreateRoomParam\
    \x12~\n\x0fjoinRoomRequest\x18\x08\x20\x01(\x0b2R.PianoRhythm.Serializat\
    ion.ClientToServer.Msgs.ServerMessage.JoinRoomByNameRequestH\0R\x0fjoinR\
    oomRequest\x12o\n\x10roomOwnerCommand\x18\t\x20\x01(\x0b2A.PianoRhythm.S\
    erialization.ClientToServer.Msgs.RoomOwnerCommandDUH\0R\x10roomOwnerComm\
    and\x12g\n\ravatarCommand\x18\n\x20\x01(\x0b2?.PianoRhythm.Serialization\
    .ClientToServer.Msgs.AvatarCommandDtoH\0R\ravatarCommand\x1a\x9f\x01\n\
    \x15JoinRoomByNameRequest\x12\x1a\n\x08roomName\x18\x01\x20\x01(\tR\x08r\
    oomName\x122\n\x14createRoomIfNotExist\x18\x02\x20\x01(\x08R\x14createRo\
    omIfNotExist\x126\n\x16joinNextAvailableLobby\x18\x03\x20\x01(\x08R\x16j\
    oinNextAvailableLobbyB\r\n\x0bmessageData*\xc1\x02\n\x11ServerMessageTyp\
    e\x12\x0b\n\x07Invalid\x10\0\x12\t\n\x05Hello\x10\x01\x12\x0e\n\nDisconn\
    ect\x10\x02\x12\x13\n\x0fRoomChatMessage\x10\x03\x12\x19\n\x15RoomChatSe\
    rverCommand\x10\x04\x12\x13\n\x0fMousePosMessage\x10\x05\x12\x0f\n\x0bMi\
    diMessage\x10\x06\x12\x11\n\rServerCommand\x10\x07\x12\x14\n\x10ServerMo\
    dCommand\x10\x08\x12\x15\n\x11CreateRoomCommand\x10\t\x12\x15\n\x11Updat\
    eRoomCommand\x10\n\x12\x12\n\x0eJoinRoomByName\x10\x0b\x12\x14\n\x10Room\
    OwnerCommand\x10\x0c\x12\x1a\n\x16JoinNextAvailableLobby\x10\r\x12\x11\n\
    \rAvatarCommand\x10\x0eb\x06proto3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
