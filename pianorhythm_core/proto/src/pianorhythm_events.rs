// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `pianorhythm-events.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(<PERSON><PERSON>,PartialEq,Eq,Debug,Hash)]
pub enum AppStateEvents {
    Unknown = 0,
    UIDisabled = 1,
    UIEnabled = 2,
    ClientLoggedIn = 3,
    ClientLoggedOut = 4,
    CanvasLoaded = 5,
    ClientLoaded = 6,
    ClientUpdated = 7,
    UsersSet = 8,
    JoinRoomFailed = 9,
    RoomChatHistorySet = 10,
    AddedSynthUser = 11,
    AddedClientSynthUser = 12,
    AudioStateInitialized = 13,
    RemovedSynthUser = 14,
    ClearedSynthUsers = 15,
    InstrumentsLoaded = 16,
    SynthSoundfontLoaded = 17,
    AppStateReset = 18,
    AudioChannelsCleared = 19,
    AudioEngineStateUpdated = 20,
    AppStateUpdated = 21,
    AudioChannelsUpdated = 22,
    AudioInstrumentsUpdated = 23,
    ClientStateReadyToRetrieve = 25,
    OfflineModeEnabled = 26,
    OfflineModeDisabled = 27,
    RoomStateUpdated = 2000,
    RoomStageLoading = 2001,
    RoomStageLoaded = 2002,
    JoinedSelfHostedRoom = 2003,
    LeftSelfHostedRoom = 2004,
    RoomMuted = 2005,
    RoomUnmuted = 2006,
}

impl ::protobuf::ProtobufEnum for AppStateEvents {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<AppStateEvents> {
        match value {
            0 => ::std::option::Option::Some(AppStateEvents::Unknown),
            1 => ::std::option::Option::Some(AppStateEvents::UIDisabled),
            2 => ::std::option::Option::Some(AppStateEvents::UIEnabled),
            3 => ::std::option::Option::Some(AppStateEvents::ClientLoggedIn),
            4 => ::std::option::Option::Some(AppStateEvents::ClientLoggedOut),
            5 => ::std::option::Option::Some(AppStateEvents::CanvasLoaded),
            6 => ::std::option::Option::Some(AppStateEvents::ClientLoaded),
            7 => ::std::option::Option::Some(AppStateEvents::ClientUpdated),
            8 => ::std::option::Option::Some(AppStateEvents::UsersSet),
            9 => ::std::option::Option::Some(AppStateEvents::JoinRoomFailed),
            10 => ::std::option::Option::Some(AppStateEvents::RoomChatHistorySet),
            11 => ::std::option::Option::Some(AppStateEvents::AddedSynthUser),
            12 => ::std::option::Option::Some(AppStateEvents::AddedClientSynthUser),
            13 => ::std::option::Option::Some(AppStateEvents::AudioStateInitialized),
            14 => ::std::option::Option::Some(AppStateEvents::RemovedSynthUser),
            15 => ::std::option::Option::Some(AppStateEvents::ClearedSynthUsers),
            16 => ::std::option::Option::Some(AppStateEvents::InstrumentsLoaded),
            17 => ::std::option::Option::Some(AppStateEvents::SynthSoundfontLoaded),
            18 => ::std::option::Option::Some(AppStateEvents::AppStateReset),
            19 => ::std::option::Option::Some(AppStateEvents::AudioChannelsCleared),
            20 => ::std::option::Option::Some(AppStateEvents::AudioEngineStateUpdated),
            21 => ::std::option::Option::Some(AppStateEvents::AppStateUpdated),
            22 => ::std::option::Option::Some(AppStateEvents::AudioChannelsUpdated),
            23 => ::std::option::Option::Some(AppStateEvents::AudioInstrumentsUpdated),
            25 => ::std::option::Option::Some(AppStateEvents::ClientStateReadyToRetrieve),
            26 => ::std::option::Option::Some(AppStateEvents::OfflineModeEnabled),
            27 => ::std::option::Option::Some(AppStateEvents::OfflineModeDisabled),
            2000 => ::std::option::Option::Some(AppStateEvents::RoomStateUpdated),
            2001 => ::std::option::Option::Some(AppStateEvents::RoomStageLoading),
            2002 => ::std::option::Option::Some(AppStateEvents::RoomStageLoaded),
            2003 => ::std::option::Option::Some(AppStateEvents::JoinedSelfHostedRoom),
            2004 => ::std::option::Option::Some(AppStateEvents::LeftSelfHostedRoom),
            2005 => ::std::option::Option::Some(AppStateEvents::RoomMuted),
            2006 => ::std::option::Option::Some(AppStateEvents::RoomUnmuted),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [AppStateEvents] = &[
            AppStateEvents::Unknown,
            AppStateEvents::UIDisabled,
            AppStateEvents::UIEnabled,
            AppStateEvents::ClientLoggedIn,
            AppStateEvents::ClientLoggedOut,
            AppStateEvents::CanvasLoaded,
            AppStateEvents::ClientLoaded,
            AppStateEvents::ClientUpdated,
            AppStateEvents::UsersSet,
            AppStateEvents::JoinRoomFailed,
            AppStateEvents::RoomChatHistorySet,
            AppStateEvents::AddedSynthUser,
            AppStateEvents::AddedClientSynthUser,
            AppStateEvents::AudioStateInitialized,
            AppStateEvents::RemovedSynthUser,
            AppStateEvents::ClearedSynthUsers,
            AppStateEvents::InstrumentsLoaded,
            AppStateEvents::SynthSoundfontLoaded,
            AppStateEvents::AppStateReset,
            AppStateEvents::AudioChannelsCleared,
            AppStateEvents::AudioEngineStateUpdated,
            AppStateEvents::AppStateUpdated,
            AppStateEvents::AudioChannelsUpdated,
            AppStateEvents::AudioInstrumentsUpdated,
            AppStateEvents::ClientStateReadyToRetrieve,
            AppStateEvents::OfflineModeEnabled,
            AppStateEvents::OfflineModeDisabled,
            AppStateEvents::RoomStateUpdated,
            AppStateEvents::RoomStageLoading,
            AppStateEvents::RoomStageLoaded,
            AppStateEvents::JoinedSelfHostedRoom,
            AppStateEvents::LeftSelfHostedRoom,
            AppStateEvents::RoomMuted,
            AppStateEvents::RoomUnmuted,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<AppStateEvents>("AppStateEvents", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for AppStateEvents {
}

impl ::std::default::Default for AppStateEvents {
    fn default() -> Self {
        AppStateEvents::Unknown
    }
}

impl ::protobuf::reflect::ProtobufValue for AppStateEvents {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x18pianorhythm-events.proto\x12\x1aPianoRhythm.AppStateEvents\x1a\x15\
    room-renditions.proto\x1a\x15user-renditions.proto\x1a\x15midi-rendition\
    s.proto\x1a\x14client-message.proto*\xfe\x05\n\x0eAppStateEvents\x12\x0b\
    \n\x07Unknown\x10\0\x12\x0e\n\nUIDisabled\x10\x01\x12\r\n\tUIEnabled\x10\
    \x02\x12\x12\n\x0eClientLoggedIn\x10\x03\x12\x13\n\x0fClientLoggedOut\
    \x10\x04\x12\x10\n\x0cCanvasLoaded\x10\x05\x12\x10\n\x0cClientLoaded\x10\
    \x06\x12\x11\n\rClientUpdated\x10\x07\x12\x0c\n\x08UsersSet\x10\x08\x12\
    \x12\n\x0eJoinRoomFailed\x10\t\x12\x16\n\x12RoomChatHistorySet\x10\n\x12\
    \x12\n\x0eAddedSynthUser\x10\x0b\x12\x18\n\x14AddedClientSynthUser\x10\
    \x0c\x12\x19\n\x15AudioStateInitialized\x10\r\x12\x14\n\x10RemovedSynthU\
    ser\x10\x0e\x12\x15\n\x11ClearedSynthUsers\x10\x0f\x12\x15\n\x11Instrume\
    ntsLoaded\x10\x10\x12\x18\n\x14SynthSoundfontLoaded\x10\x11\x12\x11\n\rA\
    ppStateReset\x10\x12\x12\x18\n\x14AudioChannelsCleared\x10\x13\x12\x1b\n\
    \x17AudioEngineStateUpdated\x10\x14\x12\x13\n\x0fAppStateUpdated\x10\x15\
    \x12\x18\n\x14AudioChannelsUpdated\x10\x16\x12\x1b\n\x17AudioInstruments\
    Updated\x10\x17\x12\x1e\n\x1aClientStateReadyToRetrieve\x10\x19\x12\x16\
    \n\x12OfflineModeEnabled\x10\x1a\x12\x17\n\x13OfflineModeDisabled\x10\
    \x1b\x12\x15\n\x10RoomStateUpdated\x10\xd0\x0f\x12\x15\n\x10RoomStageLoa\
    ding\x10\xd1\x0f\x12\x14\n\x0fRoomStageLoaded\x10\xd2\x0f\x12\x19\n\x14J\
    oinedSelfHostedRoom\x10\xd3\x0f\x12\x17\n\x12LeftSelfHostedRoom\x10\xd4\
    \x0f\x12\x0e\n\tRoomMuted\x10\xd5\x0f\x12\x10\n\x0bRoomUnmuted\x10\xd6\
    \x0fb\x06proto3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
