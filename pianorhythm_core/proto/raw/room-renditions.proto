syntax = "proto3";
package PianoRhythm.Serialization.RoomRenditions;

enum RoomStatus {
  Public = 0;
  Private = 1;
}

message RoomHostDetails {
  string ContinentCode = 1;
  optional string CountryCode = 2;
  optional uint32 NoteBufferInterval = 3;
}

enum RoomType {
  Lobby = 0;
  LobbyTurnBased = 1;
  LobbyOrchestra = 2;
  LobbyPRO = 3;
  WorldLobby = 4;
  World = 5;
  Normal = 6;
  TurnBased = 7;
  Game = 8;
  LobbyGuest = 9;
  SoloGame = 10;
  Orchestra = 11;
  SoloTraining = 12;
  Unknown = 99;
}

enum RoomStages {
  UNKNOWN = 0;
  THE_VOID = 1;
  CLOUD_PALACE = 2;
  KINGS_HALL = 3;
  GRASSLAND = 4;
  TORII_GATE = 5;
  MUSIC_STUDIO = 6;
  ARENA = 7;
  FOREST = 8;
}

message RoomStageVisualEffects {
  optional bool rain = 1;
  optional bool grass = 2;
  optional bool postProcess_blackWhite = 3;
  optional bool postProcess_downSample = 4;
  optional bool postProcess_toneMap = 5;
  optional bool postProcess_emboss = 6;
  optional bool postProcess_vignette = 7;
}

message RoomStageAudioEffect {
  float volume = 1;
  bool loop = 2;
  float playbackRate = 3;
  float pan = 4;
}

message RoomStageAudioEffects {
  optional RoomStageAudioEffect rain = 1;
  optional RoomStageAudioEffect wind = 2;
  optional RoomStageAudioEffect birds = 3;
}

message RoomStageDetails {
  RoomStages stage = 1;
  RoomStageVisualEffects effects = 2;
  RoomStageAudioEffects audioEffects = 3;
}

message RoomSettingsMeta {
  bool AutoRemove = 1;
  bool Unique = 2;
  bool Persistent = 3;
}

message BasicRoomDto {
  string roomName = 1;
  string roomID = 2;
  string roomType = 3;
  int32 userCount = 4;
  int32 maxUsers = 5;
  bool isPasswordProtected = 6;
  optional RoomHostDetails hostDetails = 7;
  string roomOwner = 8;
  RoomStages roomStage = 9;
  optional RoomStageDetails StageDetails = 10;
  string createdDate = 11;
}

message RoomDto {
  string roomName = 1;
  string roomID = 2;
  string roomOwner = 3;
  repeated string users = 4;
}

message RoomSettings {
  optional string WelcomeMessage = 1;
  int32 MaxPlayers = 2;
  int32 MaxChatHistory = 3;
  string RoomOwner = 4;
  RoomStatus RoomStatus = 5;
  RoomType RoomType = 6;
  optional string Password = 7;
  bool OnlyOwnerCanPlay = 8;
  bool OnlyOwnerCanChat = 9;
  bool AllowBlackMidi = 10;
  bool AllowGuests = 11;
  bool AllowBots = 12;
  bool OnlyMods = 13;
  bool NoChatAllowed = 14;
  bool NoPlayingAllowed = 15;
  bool FilterProfanity = 16;
  RoomSettingsMeta Meta = 17;
  optional string StageDetailsJSON = 18;
  optional RoomHostDetails HostDetails = 19;
  RoomStageDetails StageDetails = 21;
}

message RoomFullDetails {
  string roomName = 1;
  string roomID = 2;
  RoomSettings settings = 3;
}

message RoomSettingsBasicDto {
  int32 MaxPlayers = 1;
  int32 MaxChatHistory = 2;
  RoomStatus RoomStatus = 3;
  bool OnlyOwnerCanPlay = 4;
  bool OnlyOwnerCanChat = 5;
  bool AllowBlackMidi = 6;
  bool AllowGuests = 7;
  bool AllowBots = 8;
  bool OnlyMods = 9;
  bool NoChatAllowed = 10;
  bool NoPlayingAllowed = 11;
  bool FilterProfanity = 12;
  optional RoomHostDetails HostDetails = 13;
  RoomStageDetails StageDetails = 14;
}

message RoomProfileBasicInfoDto {
  string roomName = 1;
  string roomID = 2;
  string roomOwner = 3;
  RoomSettingsBasicDto settings = 4;
  optional string stageDetailsJSON = 5;
  RoomStages roomStage = 6;
  RoomStageDetails StageDetails = 8;
}