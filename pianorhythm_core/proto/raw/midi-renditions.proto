syntax = "proto3";
package PianoRhythm.Serialization.Midi.Msgs;

enum MidiDtoType {
  Invalid = 0;
  NoteOn = 1;
  NoteOff = 2;
  Sustain = 3;
  AllSoundOff = 4;
  PitchBend = 5;
}

enum MidiNoteSource {
  IGNORED = 0;
  KEYBOARD = 1;
  MOUSE = 2;
  MIDI = 3;
  MIDI_PLAYER_PREVIEW = 4;
  MIDI_PLAYER = 5;
}

message MidiDto {
  message MidiNoteOn {
    int32 channel = 1;
    int32 note = 2;
    int32 velocity = 3;
    optional int32 program = 4;
    optional int32 volume = 5;
    optional int32 bank = 6;
    optional int32 pan = 7;
    optional int32 expression = 8;
  }
  message MidiNoteOff {
    int32 channel = 1;
    int32 note = 2;
  }
  message MidiNoteSustain {
    int32 channel = 1;
    bool value = 2;
  }
  message MidiAllSoundOff {
    int32 channel = 1;
  }
  message MidiPitchBend {
    uint32 channel = 1;
    uint32 value = 2;
  }

  MidiDtoType messageType = 1;
  MidiNoteSource noteSource = 999;

  oneof data {
    MidiNoteOn noteOn = 2;
    MidiNoteOff noteOff = 3;
    MidiNoteSustain sustain = 4;
    MidiAllSoundOff allSoundOff = 5;
    MidiPitchBend pitchBend = 6;
  }
}

message MidiMessageInputBuffer {
  MidiDtoType type = 1;
  uint32 channel = 2;
  uint32 note = 3;
  uint32 velocity = 4;
  float delay = 5;
  uint32 program = 6;
  uint32 volume = 7;
  uint32 bank = 8;
  uint32 pan = 9;
  uint32 pitch = 10;
  uint32 expression = 11;
}

message MidiMessageInputDto {
  string time = 1;
  repeated MidiMessageInputBuffer data = 2;
  optional MidiNoteSource source = 3;
}

message SF2Program {
  uint32 id = 1;
  string name = 2;
  uint32 bank = 3;
}

message SoundfontPreset {
  string name = 1;
  uint32 bank = 2;
  uint32 preset = 3;
  uint32 key_low = 4;
  uint32 key_high = 5;
}

message Instrument {
  string name = 1;
  string display_name = 2;
  uint32 bank = 3;
  uint32 preset = 4;
  bool is_drum_kit = 5;
  uint32 key_low = 6;  // optional field
  uint32 key_high = 7; // optional field
}

message InstrumentsList {
  repeated Instrument instruments = 1;
}

message AudioChannel {
  uint32 channel = 1;
  bool active = 2;
  optional Instrument instrument = 3; // optional field
  uint32 volume = 4;
  uint32 pan = 5;
  uint32 bank = 6;
  uint32 bank_msb = 7;
  uint32 preset = 8;
  uint32 expression = 9;
  uint32 channel_pressure = 10;
  uint32 pitch_bend = 11;
  uint32 pitch_wheel_sensitivity = 12;
}

message SoundfontSetting {
  string name = 1;
  bool is_default = 2; // optional field
  string path = 3; // optional field
  bool custom = 4; // optional field
}

enum ActiveChannelsMode {
  SINGLE = 0;
  MULTI = 1;
  ALL = 2;
  SPLIT2 = 3;
  SPLIT4 = 4;
  SPLIT8 = 5;
}

enum AudioServiceNoteActivityNoteType {
  ON = 0;
  OFF = 1;
}

message AudioServiceNoteActivity {
  int32 channel = 1;
  AudioServiceNoteActivityNoteType type = 2;
  string socketID = 3; // optional field
  bool isClient = 4;
  int32 velocity = 5; // optional field
}

enum SetChannelInstrumentType {
  Add = 0;
  NextInactive = 1;
  NextEmpty = 2;
}

message SetChannelInstrumentPayload {
  uint32 channel = 1;
  uint32 bank = 2;
  uint32 preset = 3;
  SetChannelInstrumentType type = 4;
  bool setActive = 5;
  optional string socket_id = 6;
  optional uint32 volume = 7;
  optional uint32 pan = 8;
  optional uint32 expression = 9;
}

message SetChannelDetailsPayload {
  uint32 channel = 1;
}

message UpdateChannelPayload {
  uint32 channel = 1;
  oneof data {
    uint32 expression = 2;
    uint32 pan = 3;
    uint32 volume = 4;
    uint32 bank = 5;
    uint32 preset = 6;
    uint32 pitch = 7;
  }
}

message SynthEventProgramChangePayload {
  uint32 channel = 1;
  optional uint32 expression = 2;
  optional uint32 pan = 3;
  optional uint32 volume = 4;
  optional uint32 bank = 5;
  optional uint32 preset = 6;
  optional uint32 pitch = 7;
}

enum PianoRhythmSynthEventName {
  NOTE_ON = 0;
  NOTE_OFF = 1;
  PROGRAM_CHANGE = 2;
  CHANNEL_PRESSURE = 3;
  SYSTEM_RESET = 4;
  POLYPHONIC_KEY_PRESSURE = 5;
  ALL_NOTES_OFF = 6;
  ALL_SOUND_OFF = 7;
  OMNI_MODE_OFF = 8;
  MONO_MODE_ON = 9;
  POLY_MODE_ON = 10;
  OMNI_MODE_ON = 11;
  MAIN_VOLUME_MSB = 12;
  MAIN_VOLUME_LSB = 13;
  FOOT_CONTROLLER_MSB = 14;
  MODULATION_MSB = 15;
  MODULATION_LSB = 16;
  BANK_SELECT_MSB = 17;
  BANK_SELECT_LSB = 18;
  FOOT_CONTROLLER_LSB = 19;
  RESET_ALL_CONTROLLERS = 20;
  PAN_MSB = 21;
  PAN_LSB = 22;
  DAMPER_PEDAL = 23;
  PORTAMENTO = 24;
  SUSTENUTO = 25;
  SOFT_PEDAL = 26;
  LEGATO_FOOTSWITCH = 27;
  HOLD_2 = 28;
  EFFECTS_1_DEPTH = 29;
  TREMELO_EFFECT = 30;
  CHORUS_EFFECT = 31;
  CELESTE_EFFECT = 32;
  PHASER_EFFECT = 33;
  PITCH_BEND = 34;
  CONTROL_CHANGE = 35;
  SOCKET_USER_GAIN_CHANGE = 36;
  UNKNOWN = 37;
}