syntax = "proto3";
package PianoRhythm.Serialization.ServerToClient.UserRenditions;

import "world-renditions.proto";

enum Roles {
  ADMIN = 0;
  SYSTEM = 1;
  DEVELOPER = 2;
  MODERATOR = 3;
  TRIAL_MODERATOR = 4;
  MEMBER = 5;
  ROOMOWNER = 6;
  BOT = 7;
  HELPBOT = 8;
  DISCORDBOT = 9;
  GUEST = 10;
  SHEETMUSICEDITOR = 11;
  MIDIMUSICEDITOR = 12;
  PLUGIN = 13;
  PRO = 14;
  UNKNOWN = 15;
}

enum Badges {
  V3_CLOSED_ALPHA_TESTER = 0;
  V2_OG_MEMBER = 1;
  PRO_MEMBER = 2;
  TranslationMasterContributor = 3;
  TranslationContributor = 4;
  CUSTOM = 5;
}

message UserBadges {
  Badges badge = 1;
  optional string value = 2;
}

enum UserStatus {
  None = 0;
  Unknown = 1;
  Online = 2;
  Idle = 3;
  DoNotDisturb = 4;
  AFK = 5;
}

message UserMetaDto {
  bool bot = 1;
  bool discordBot = 2;
  optional string modifiedDate = 3;
  optional string clientMetaDetails = 4;
  optional string enteredRoomDateTime = 6;
}

message AvatarWorldDataDto {
  message AvatarMessageWorldPosition {
    double x = 1;
    double y = 2;
    double z = 3;
    double rot_x = 4;
    double rot_y = 5;
    double rot_z = 6;
  }

  optional AvatarMessageWorldPosition worldPosition = 1;
  optional int32 pianoBenchSeat = 2;
  optional PianoRhythm.Serialization.WorldRenditions.WorldAvatarUserData avatarUserData = 3;
}

message WorldData {
  optional string characterDataJSON = 1;
  optional string orchestraModelCustomizationDataJSON = 2;
  optional AvatarWorldDataDto avatarWorldData = 3;
}

message UserDto {
  string username = 1;
  string usertag = 2;
  string socketID = 3;
  repeated Roles roles = 4;
  repeated UserBadges badges = 5;
  optional string nickname = 6;
  string color = 7;
  UserMetaDto meta = 8;
  bool selfMuted = 9;
  bool serverNotesMuted = 10;
  bool serverChatMuted = 11;
  UserStatus status = 12;
  optional string ProfileDescription = 13;
  optional string profileImageLastModified = 14;
  optional string profileBackgroundImageLastModified = 15;
  WorldData worldData = 16;
  bool isProMember = 17;
  optional string statusText = 18;
  string uuid = 19;
}

message UserBillingSettings {
  enum SubscriptionPlan {
    Free = 0;
    Level1 = 1;
  }

  message BillingMeta {
    string nextBillingDate = 1;
    string currencyCode = 2;
    string pricePerUnit = 3;
    string priceID = 4;
  }

  string currentPlan = 1;
  bool cancelationInProcess = 2;
  BillingMeta meta = 3;
}

message ClientMetaDto {
  optional string email = 1;
  UserBillingSettings billingSettings = 2;
}

message KickedUserData {
  string socketID = 1;
  string usertag = 2;
  string time = 3;
  string created_date = 4;
}

message UserClientDto {
  UserDto userDto = 1;
  optional ClientMetaDto meta = 2;
  bool canApproveSheetMusic = 3;
  bool has2faEnabled = 4;
  bool isOAuthAccount = 5;
}

message ClientSideUserDto {
  UserDto userDto = 1;
  bool localNotesMuted = 2;
  bool localChatMuted = 3;
  optional string clientMetaDetailsParsed = 4;
  string socketID = 5;
  optional uint32 socketIDHashed = 6;
}

message UserUpdateCommand {
  string socketID = 1;

  oneof data {
    string userColor = 2;
    UserStatus userStatus = 3;
    string clientMetaDetails = 4;
    string profileDescription = 5;
    bool selfMuted = 6;
    string statusText = 7;
    string nickname = 8;
    string profileImageUpdated = 9;
    string profileBackgroundImageUpdated = 10;
    bool profileImageCleared = 11;
    bool profileBackgroundImageCleared = 12;
    string orchestraModel = 13;
    string characterModel = 14;
    bool serverNotesMuted = 15;
    bool serverChatMuted = 16;
    AvatarWorldDataDto.AvatarMessageWorldPosition avatarWorldPosition = 17;
    int32 avatarPianoBenchSeat = 18;
    PianoRhythm.Serialization.WorldRenditions.WorldAvatarUserData avatarUserData = 19;
  }

  repeated UserBadges badges = 50;
}

message FriendDto {
  enum FriendOnlineStatus {
    None = 0;
    Unknown = 1;
    Online = 2;
    Idle = 3;
    Offline = 4;
    PendingAcceptance = 5;
  }

  string username = 1;
  string usertag = 2;
  optional string socketID = 3;
  optional string roomID = 4;
  repeated Roles roles = 5;
  optional string nickname = 6;
  optional string color = 7;
  optional string statusText = 8;
  optional string profileDescription = 9;
  optional string becameFriendsDate = 10;
  optional string profileImageLastModified = 11;
  optional string profileBackgroundImageLastModified = 12;
  optional string lastOnline = 13;
  optional FriendOnlineStatus onlineStatus = 14;
}

message PendingFriendRequest {
  string usertag = 1;
  string uuid = 2;
  string createdDate = 3;
}