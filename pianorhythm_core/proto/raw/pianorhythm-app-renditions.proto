syntax = "proto3";
package PianoRhythm.AppRenditions;

import "room-renditions.proto";
import "midi-renditions.proto";

enum AudioSynthesizerEngine {
  OXISYNTH = 0;
  RUSTYSYNTH = 1;
}

enum EqualizerPreset {
  Flat = 0;
  Acoustic = 1;
  Electronic = 2;
  Latin = 3;
  Piano = 4;
  Pop = 5;
  Rock = 6;
  BassBooster = 7;
  Custom = 8;
}

enum GraphicShadowFilteringMethod {
  ShadowFiltering_Hardware2x2 = 0;
  ShadowFiltering_Gaussian = 1;
  ShadowFiltering_Temporal = 2;
}

enum GraphicsPresets {
  Preset_None = 0;
  Preset_Custom = 1;
  Preset_Low = 2;
  Preset_Medium = 3;
  Preset_High = 4;
  Preset_Ultra = 5;
}

enum GraphicsMsaaSamples {
  Msaa_Off = 0;
  Msaa_Sample2 = 1;
  Msaa_Sample4 = 2; // Only one supported in web
  Msaa_Sample8 = 3;
}

message AppSettings {
  string DEFAULT_LANGUAGE = 1;
  bool DISPLAY_PIANO = 2;
  bool DISPLAY_FPS = 3;
  bool DISCORD_SYNC = 4;
  bool DISPLAY_PING = 5;
  bool DISPLAY_3D_STATS = 6;
  bool DISPLAY_CURSORS = 7;
  bool DISPLAY_INST_DOCK = 8;
  bool DISPLAY_CHAT = 9;
  bool DISPLAY_SCENE_WIDGET_BUTTONS = 10;
  bool DISPLAY_WHOISTYPING = 11;
  bool KEEP_CHAT_IN_FOCUS = 12;
  int32 INST_DOCK_TRANSPOSE = 13;
  int32 INST_DOCK_OCTAVE = 14;
  float INST_DOCK_OPACITY = 15;
  float VOLUME_SAVED = 16;
  int32 AUDIO_MAX_POLYPHONY = 17;
  float AUDIO_VEL_MUTING = 18;
  float AUDIO_SUSTAIN_CUTOFF = 19;
  float AUDIO_SUSTAINED_NOTE_FADEOUT_DURATION = 20;
  float AUDIO_VEL_BOOST = 21;
  bool AUDIO_ENABLE_VEL_BOOST = 22;
  bool AUDIO_ENABLE_VEL = 23;
  bool AUDIO_ENABLE_REVERB = 24;
  bool AUDIO_MIDI_OUTPUT_ONLY = 25;
  bool AUDIO_MOUSE_POS_SETS_VELOCITY = 26;
  bool AUDIO_ENABLE_DRUM_CHANNEL = 27;
  int32 AUDIO_BUFFER_SIZE = 28;
  bool AUDIO_OUTPUT_OWN_NOTES_TO_MIDI = 29;
  bool CHAT_AUTO_SCROLL = 30;
  bool SHOW_EMBEDDED_LINKS = 31;
  bool MIDI_LISTEN_TO_PROGRAM_CHANGES = 32;
  bool MIDI_AUTO_FILL_EMPTY_CHANNELS = 33;
  bool MIDI_USE_DEFAULT_BANK_WHEN_MISSING = 34;
  bool AUDIO_USE_DEFAULT_INSTRUMENT_WHEN_MISSING_FOR_OTHER_USERS = 35;
  bool MIDI_ENABLE_STEREO_PANNING = 36;
  //  repeated Keybind KEYBINDS = 37;
  bool GRAPHICS_ONLY_SHOW_PIANO_KEYS = 39;
  bool GRAPHICS_DISPLAY_RENDER_STATS = 40;
  bool GRAPHICS_ENABLE_AVATARS = 44;
  bool GRAPHICS_ENABLE_PHYSICS = 45;
  bool GRAPHICS_ENABLE_SPECIAL_EFFECTS = 46;
  bool GRAPHICS_ENABLE_AUTO_ANIMATE_TO_INSTRUMENTS = 49;
  bool GRAPHICS_ENABLE_ANTIALIAS = 50;
  bool GRAPHICS_ENABLE_ORCHESTRA_MODELS = 51;
  bool GRAPHICS_RENDER_EVEN_IN_BACKGROUND = 52;
  bool GRAPHICS_ENABLE_AMBIENT_OCCLUSION = 53;
  bool GRAPHICS_ENABLE_DEPTH_OF_FIELD = 54;
  bool GRAPHICS_USE_OFFSCREEN_CANVAS = 55;
  bool CHAT_DISABLE_MARKDOWN = 56;
  bool CHAT_ENABLE_IMAGE_URL_PREVIEW = 57;
  bool ENABLE_DESKTOP_NOTIFICATIONS = 58;
  bool ALLOW_USERS_TO_NOTIFY_ME = 59;
  int32 AUDIO_SAMPLE_RATE = 60;
  bool AUDIO_IGNORE_SOUNDFONT_VOLUME_ENV_DELAY = 61;
  bool AUDIO_IGNORE_SOUNDFONT_VOLUME_ENV_DELAY2 = 62;
  bool AUDIO_USE_SEPARATE_DRUM_KIT = 63;
  float AUDIO_REVERB_LEVEL = 64;
  float AUDIO_REVERB_DAMP = 65;
  float AUDIO_REVERB_WIDTH = 66;
  float AUDIO_REVERB_ROOMSIZE = 67;
  bool AUDIO_CACHE_SOUNDFONTS_WEB = 68;
  bool DESKTOP_SAVE_SOUNDFONTS = 69;
  //  AppThemes UI_THEME = 70;
  bool UI_ENABLE_USER_NOTE_ACTIVITIES = 71;
  string WEBGL_POWER_PREFERENCE = 73;
  bool ENABLE_DEBUG_MODE = 74;
  bool AUTO_CHECK_FOR_UPDATES = 75;
  EqualizerPreset AUDIO_EQUALIZER_PRESET = 77;
  bool GAME_MANIA_DISABLE_BACKGROUND = 79;
  bool AUDIO_SFX_ENABLE = 80;
  float AUDIO_SFX_GLOBAL_VOLUME = 81;
  float AUDIO_SFX_STAGE_EFFECTS_GLOBAL_VOLUME = 82;
  float AUDIO_SFX_HOVER_VOLUME = 83;
  bool AUDIO_SFX_CHAT_ENABLE = 84;
  bool AUDIO_ENABLE_STAGE_SFX = 85;
  AudioSynthesizerEngine AUDIO_SYNTH_ENGINE = 86;
  float AUDIO_GLOBAL_USERS_VELOCITY_PERCENTAGE = 87;
  int32 AUDIO_MULTIMODE_MAX_CHANNELS = 88;
  bool APP_AUTO_LOGIN_ENABLE = 89;
  bool AUDIO_BG_MUSIC_ENABLE = 90;
  float AUDIO_BG_MUSIC_GLOBAL_VOLUME = 91;
  bool ONLINE_WARN_ABOUT_EXTERNAL_LINKS = 92;
  bool ENABLE_PLUGINS = 93;
  bool KEYBOARD_SHIFT_KEY_AUTO_NOTE_OFF = 94;
  string AUDIO_REVERB_DEFAULT_MIGRATED_VERSION = 95;
  bool GRAPHICS_ENABLE_FOG = 96;
  bool GRAPHICS_ENABLE_SHADOWS = 97;
  bool GRAPHICS_ENABLE_STAGE = 98;
  bool GRAPHICS_ENABLE_PIANO = 99;
  bool GRAPHICS_ENABLE_MOTION_BLUR = 101;
  bool GRAPHICS_ENABLE_GLOW = 102;
  bool GRAPHICS_ENABLE_DRUMS = 103;
  bool GRAPHICS_ENABLE_ALL_PARTICLES = 104;
  bool AUDIO_USE_WORKLET = 105;
  bool UI_ENABLE_SYNTH_NOTE_ACTIVITIES = 106;
  bool GRAPHICS_ENABLE_SOFT_SHADOWS = 107;
  bool GRAPHICS_ENABLE_POST_PROCESSING = 108;
  string GRAPHICS_TARGET_FPS = 109;
  bool GRAPHICS_ENABLE_LIGHTS = 110;
  bool GRAPHICS_ENABLE_BLOOM = 111;
  bool GRAPHICS_ENABLE_TONE_MAPPING = 112;
  bool GRAPHICS_ENABLE_HDR = 113;
  bool GRAPHICS_ENABLE_WEBGPU = 114;
  bool GRAPHICS_ENABLE_ANIMATIONS = 115;
  GraphicsMsaaSamples GRAPHICS_MSAA_SAMPLES = 116;
  GraphicsPresets GRAPHICS_PRESET = 117;
  GraphicShadowFilteringMethod GRAPHICS_SHADOW_FILTER = 118;
  bool GRAPHICS_ENABLE_ENGINE = 119;
  bool GRAPHICS_USE_LOW_POLY_MODELS = 120;
  bool GRAPHICS_ENABLE_GUITARS = 121;
  bool CHAT_MESSAGES_MINIMIZED = 122;
  bool MUTED_NOTES_SELF = 123;
  bool SEND_WHOISTYPING = 124;
  bool CHAT_MESSAGES_MAXIMIZED = 125;
  bool AUDIO_ENABLE_EQUALIZER = 126;
  float AUDIO_MAX_NOTE_ON_TIME = 127;
  uint32 AUDIO_MAX_VELOCITY = 128;
  uint32 AUDIO_MIN_VELOCITY = 129;
  float AUDIO_MIN_VOLUME_RELEASE = 130;
  bool INPUT_CTRL_KEY_LOWERS_OCTAVE = 131;
  bool INPUT_MIDI_TO_QWERTY_MOD = 132;
  bool INPUT_MIDI_TO_QWERTY_MOD_USE_CAPSLOCK = 133;
  PianoRhythm.Serialization.Midi.Msgs.ActiveChannelsMode SLOT_MODE = 134;
  bool AUDIO_USE_VELOCITY_CURVE = 135;
}

message AppCommonEnvironment {
  bool OFFLINE_DEV_MODE = 1;
  string COLYSEUS_HOST = 2;
  string COLYSEUS_PORT = 3;
  string HOST = 4;
  string WS_HOST = 5;
  string DOCS_HOST = 6;
  string FULL_HOST = 7;
  string SEQ_URL = 8;
  string STATUS_PAGE_URL = 9;
  string EXPRESS_API_HOST = 10;
  string METRICS_URL = 11;
  string WEBSOCKET_PROTOCOL = 12;
  int32 PORT = 13;
  string ASSETS_URL = 14;
  string ASSETS_PROXY_URL = 15;
  string MODE = 16;
  string SENTRY_DSN = 17;
  bool IS_LOCAL_DEV = 18;
  bool IS_TEST_MODE = 19;
  bool IS_DEV_MODE = 20;
  bool IS_DESKTOP_APP = 21;
  bool IS_WEB_APP = 22;
  bool IS_PRODUCTION = 23;
  bool IS_STAGING = 24;
  bool IS_AUTOMATED_TEST_MODE = 25;
  string CLIENT_VERSION = 26;
  string CLIENT_BUILD_DATE = 27;
  string RENDERER_WASM_FILE_PATH = 28;
  string RENDERER_JS_FILE_PATH = 29;
  string SYNTH_JS_FILE_PATH = 30;
  string CORE_JS_FILE_PATH = 31;
  string MANIA_JS_FILE_PATH = 32;
  string DAW_JS_FILE_PATH = 33;
  string SOUNDFONT_CACHE_PREFIX = 34;
}

enum AppPianoKeyType {
  Black = 0;
  White = 1;
}

message AppPianoKey {
  AppPianoKeyType keyType = 1;
  uint32 midiID = 2;
  optional float position_x = 3;
  optional float position_y = 4;
  optional float position_z = 5;
  optional float rotation_x = 6;
  optional float rotation_y = 7;
  optional float rotation_z = 8;
  optional float color_r = 9;
  optional float color_g = 10;
  optional float color_b = 11;
  optional float color_a = 12;
}

enum AppPianoPedalType {
  Sustain = 0;
  Dampen = 1;
}

message AppPianoPedal {
  string id = 1;
  optional string name = 2;
  optional float position_x = 3;
  optional float position_y = 4;
  optional float position_z = 5;
  optional float rotation_x = 6;
  optional float rotation_y = 7;
  optional float rotation_z = 8;
  optional float color_r = 9;
  optional float color_g = 10;
  optional float color_b = 11;
  optional float color_a = 12;
  AppPianoPedalType type = 13;
}

message AppRenderableEntity {
  string id = 1;
  optional string name = 2;

  optional float position_x = 3;
  optional float position_y = 4;
  optional float position_z = 5;

  optional float rotation_x = 6;
  optional float rotation_y = 7;
  optional float rotation_z = 8;

  optional float color_r = 9;
  optional float color_g = 10;
  optional float color_b = 11;
  optional float color_a = 12;

  optional float alpha = 13;
  optional float beta = 14;
  optional float radius = 15;

  optional float target_x = 16;
  optional float target_y = 17;
  optional float target_z = 18;

  optional float scale_x = 19;
  optional float scale_y = 20;
  optional float scale_z = 21;

  optional bool visible = 22;
}

message AppPageloaderDetail {
  bool active = 1;
  optional string details = 2;
}

enum AppNotificationConfigStatus {
  NotificationSuccess = 0;
  NotificationInfo = 1;
  NotificationWarning = 2;
  NotificationDanger = 3;
}

message AppNotificationConfig {
  string id = 1;
  optional AppNotificationConfigStatus status = 2;
  optional string title = 3;
  optional string description = 4;
  optional int32 duration = 5;
  optional bool persistent = 6;
  optional bool closable = 7;
  optional bool loading = 8;
}

message AppMidiSequencerProgramChange {
  int32 channel = 1;
  int32 program = 2;
  double time = 3;
  int32 tick = 4;
  int32 bank = 5;
}

message AppMidiSequencerTempoChange {
  double time = 1;
  double tempo = 2;
}

enum AppMidiSequencerEventType {
  UNKNOWN = 0;
  DATA = 1;
  LYRIC = 2;
  TEXT = 3;
  MARKER_TEXT = 4;
  COPYRIGHT_NOTICE = 5;
  INSTRUMENT_NAME = 6;
  TRACK_NAME = 7;
  TEMPO_CHANGE = 8;
  LOOP_START = 9;
  LOOP_END = 10;
  END_OF_TRACK = 11;
  CURRENT_TIME = 12;
  FILE_NAME = 13;
  FILE_OUTPUT = 14;
  TOTAL_TIME_CHANGE = 15;
  FINISHED = 16;
  STOPPED = 17;
  PROGRAM_CHANGE = 18;
  SEEK_POSITION_CHANGED = 19;
  FILE_TEMPO_CHANGE = 20;
}

message AppMidiSequencerEvent {
  AppMidiSequencerEventType eventType = 1;
  optional string dataStr = 2;
  optional double dataFloat = 3;
  optional int32 channel = 4;
  optional int32 command = 5;
  optional int32 data1 = 6;
  optional int32 data2 = 7;
  repeated string lyrics = 8;
  optional string fileName = 9;
  optional double totalTime = 10;
  repeated string trackNames = 11;
  optional int32 currentBPM = 12;
  repeated string copyrightNotice = 13;
  repeated string texts = 14;
  repeated string markerTexts = 15;
  optional int32 tick = 16;
  repeated AppMidiSequencerProgramChange programChanges = 17;
  repeated AppMidiSequencerTempoChange tempoChanges = 18;
  optional int32 ppq = 19;
  optional bool isVPSheet = 20;
  optional int32 index = 21;
  optional double time = 22;
}

message AppVPSequencerTrack {
  int32 index = 1;
  optional int32 tempo = 2;
  optional string name = 3;
}

message AppVPSequencerFileLoad {
  string data = 1;
  optional string fileName = 2;
  repeated AppVPSequencerTrack tracks = 3;
}

message AppMidiSequencerFileLoad {
  repeated uint32 data = 1;
  optional string fileName = 2;
}

message AppMidiTrack {
  uint32 index = 1;
  optional string name = 2;
  optional uint32 volume = 3;
  optional uint32 pan = 4;
  bool active = 5;
  bool hasData = 6;
  bool playing = 7;
  bool recording = 8;
}

enum AppSceneMode {
  ThreeD = 0;
  TwoD = 1;
  GameMode = 2;
  WorldMode = 3;
  Unknown = 4;
}

message AppRoomStageLoaded {
  PianoRhythm.Serialization.RoomRenditions.RoomStages roomStage = 1;
  PianoRhythm.Serialization.RoomRenditions.RoomType roomType = 2;
}

message AppKeyboardMappingVisualize {
  uint32 note = 1;
  string key = 2;
}

message AppKeyboardMappingVisualizeVec {
  repeated AppKeyboardMappingVisualize mappings = 1;
}

message AppThemeColors {
  string primary = 1;
  string accent = 2;
  string tertiary = 3;
}