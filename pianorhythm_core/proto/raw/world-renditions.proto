syntax = "proto3";
package PianoRhythm.Serialization.WorldRenditions;

enum WorldSpecies {
  HUMAN = 0;
  GOBLIN = 1;
  COW = 2;
  ZOMBIE = 3;
  PUG = 4;
}

enum WorldGender {
  MALE = 0;
  FEMALE = 1;
  OTHER = 2;
}

enum WorldItemType {
  UNKNOWN = 0;
  HAIR = 1;
  FACIAL_HAIR = 2;
  CLOTHING = 3;
  PANTS = 4;
  HELMET = 5;
  ARMOR = 6;
  WEAPON = 7;
  BACK = 8;
  ACCESSORY = 9;
  PRESET_AVATAR = 10;
}

// Helmet types including crowns and fish heads
enum WorldAssetHelmetType {
  HELMET_TYPE_UNSPECIFIED = 0;
  HELMET_TYPE_CROWN = 1;
  HELMET_TYPE_FANCY_CROWN = 2;
  HELMET_TYPE_CHEF_HAT = 3;
  HELMET_TYPE_FISH_ARMORED_CAT_FISH = 4;
  HELMET_TYPE_FISH_BETTA = 5;
  HELMET_TYPE_FISH_BLACK_LION_FISH = 6;
  HELMET_TYPE_FISH_BLUE_GOLD_FISH = 7;
  HELMET_TYPE_FISH_CORAL_GROUPER = 8;
  HELMET_TYPE_FISH_CLOWN_FISH = 9;
  HELMET_TYPE_FISH_BLOB_FISH = 10;
  HELMET_TYPE_FISH_ZEBRA_CLOWN_FISH = 11;
  HELMET_TYPE_FISH_BLUE_TANG = 12;
  HELMET_TYPE_FISH_BUTTERFLY_FISH = 13;
  HELMET_TYPE_FISH_CARDINAL_FISH = 14;
  HELMET_TYPE_FISH_COWFISH = 15;
  HELMET_TYPE_FISH_FLATFISH = 16;
  HELMET_TYPE_FISH_FLOWER_HORN = 17;
  HELMET_TYPE_FISH_GOBLIN_SHARK = 18;
  HELMET_TYPE_FISH_HUMPHEAD = 19;
  HELMET_TYPE_FISH_KOI = 20;
  HELMET_TYPE_FISH_LIONFISH = 21;
  HELMET_TYPE_FISH_MANDARIN_FISH = 22;
  HELMET_TYPE_FISH_MOORISH_IDOL = 23;
  HELMET_TYPE_FISH_PARROT_FISH = 24;
  HELMET_TYPE_FISH_PIRANHA = 25;
  HELMET_TYPE_FISH_PUFFER = 26;
  HELMET_TYPE_FISH_RED_SNAPPER = 27;
  HELMET_TYPE_FISH_ROYAL_GRAMMA = 28;
  HELMET_TYPE_FISH_SHARK = 29;
  HELMET_TYPE_FISH_SUNFISH = 30;
  HELMET_TYPE_FISH_SWORDFISH = 31;
  HELMET_TYPE_FISH_TANG = 32;
  HELMET_TYPE_FISH_TETRA = 33;
  HELMET_TYPE_FISH_TUNA = 34;
  HELMET_TYPE_FISH_TURBOT = 35;
  HELMET_TYPE_FISH_YELLOW_TANG = 36;
}

// Hair types
enum WorldAssetHairType {
  HAIR_TYPE_UNSPECIFIED = 0;
  HAIR_TYPE_HAIR_1 = 1;
  HAIR_TYPE_HAIR_2 = 2;
}

// Clothing types
enum WorldAssetClothingType {
  CLOTHING_TYPE_UNSPECIFIED = 0;
  CLOTHING_TYPE_CHEF = 1;
}

// Character preset types
enum WorldAssetPresetCharacterType {
  PRESET_CHARACTER_TYPE_UNSPECIFIED = 0;
  PRESET_CHARACTER_TYPE_WITCH = 1;
  PRESET_CHARACTER_TYPE_WIZARD = 2;
  PRESET_CHARACTER_TYPE_SUIT_MALE = 3;
  PRESET_CHARACTER_TYPE_DOCTOR_MALE_OLD = 4;
  PRESET_CHARACTER_TYPE_DOCTOR_MALE_YOUNG = 5;
  PRESET_CHARACTER_TYPE_SUIT_FEMALE = 6;
  PRESET_CHARACTER_TYPE_KIMONO_MALE = 7;
  PRESET_CHARACTER_TYPE_KIMONO_FEMALE = 8;
  PRESET_CHARACTER_TYPE_NINJA_MALE = 9;
  PRESET_CHARACTER_TYPE_NINJA_FEMALE = 10;
  PRESET_CHARACTER_TYPE_NINJA_MALE_HAIR = 11;
  PRESET_CHARACTER_TYPE_OLD_CLASSY_MALE = 12;
  PRESET_CHARACTER_TYPE_OLD_CLASSY_FEMALE = 13;
  PRESET_CHARACTER_TYPE_PIRATE_MALE = 14;
  PRESET_CHARACTER_TYPE_PIRATE_FEMALE = 15;
  PRESET_CHARACTER_TYPE_KNIGHT_MALE = 16;
  PRESET_CHARACTER_TYPE_CASUAL_MALE = 17;
  PRESET_CHARACTER_TYPE_CASUAL_FEMALE = 18;
  PRESET_CHARACTER_TYPE_VIKING_MALE = 19;
  PRESET_CHARACTER_TYPE_VIKING_FEMALE = 20;
}

message WorldVector3 {
  float x = 1;
  float y = 2;
  float z = 3;
}

message WorldAvatarItem {
  string item_key = 1;
  string title = 2;
  optional string description = 3;
  WorldItemType item_type = 4;
  string item_color1 = 5;
  optional string item_color2 = 6;
  optional WorldVector3 position_offset = 7;
  optional WorldVector3 rotation_offset = 8;
  optional WorldVector3 scale_offset = 9;
}

message WorldAvatarSpecies {
  string title = 1;
  optional string description = 2;
  WorldSpecies species = 3;
}

message WorldAvatarClothing {
  string item_key = 1;
  string title = 2;
  optional string description = 3;
  WorldItemType item_type = 4;
  string item_color1 = 5;
  optional string item_color2 = 6;
  optional WorldVector3 position_offset = 7;
  optional WorldVector3 rotation_offset = 8;
  optional WorldVector3 scale_offset = 9;
}

message WorldAvatarArmor {
  string item_key = 1;
  string title = 2;
  optional string description = 3;
  WorldItemType item_type = 4;
  string item_color1 = 5;
  string item_color2 = 6;
  optional WorldVector3 position_offset = 7;
  optional WorldVector3 rotation_offset = 8;
  optional WorldVector3 scale_offset = 9;
}

message WorldAvatarPants {
  string item_key = 1;
  string title = 2;
  optional string description = 3;
  WorldItemType item_type = 4;
  string item_color1 = 5;
  optional string item_color2 = 6;
  optional WorldVector3 position_offset = 7;
  optional WorldVector3 rotation_offset = 8;
  optional WorldVector3 scale_offset = 9;
}

message WorldAvatarUserData {
  optional string eye_color_left = 1;
  optional string eye_color_right = 2;
  optional string eyebrow_color_left = 3;
  optional string eyebrow_color_right = 4;
  WorldGender gender = 5;
  WorldSpecies species = 6;
  optional WorldAssetPresetCharacterType preset_avatar = 7;
  optional string species_color = 8;
  optional string hair_color = 9;
  optional string facial_hair_color = 10;
  WorldAvatarClothing clothing = 11;
  WorldAvatarItem hair = 12;
  WorldAvatarItem facial_hair = 13;
  WorldAvatarItem accessory = 14;
  WorldAvatarArmor helmet = 15;
  WorldAvatarArmor body_armor = 16;
  WorldAvatarPants pants = 17;
}

message AvatarCustomizationData {
  optional string eye_color_left = 1;
  optional string eye_color_right = 2;
  optional string eyebrow_color_left = 3;
  optional string eyebrow_color_right = 4;
  optional WorldGender gender = 5;
  optional WorldSpecies species = 6;
  optional WorldAssetPresetCharacterType preset_avatar = 7;
  optional string species_color = 8;
  optional string hair_color = 9;
  optional string facial_hair_color = 10;
  optional WorldAvatarClothing clothing = 11;
  optional WorldAvatarItem hair = 12;
  optional WorldAvatarItem facial_hair = 13;
  optional WorldAvatarItem accessory = 14;
  optional WorldAvatarArmor helmet = 15;
  optional WorldAvatarArmor body_armor = 16;
  optional WorldAvatarPants pants = 17;
}