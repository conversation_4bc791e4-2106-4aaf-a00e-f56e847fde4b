syntax = "proto3";
package PianoRhythm.AppStateEffects;

import "room-renditions.proto";
import "user-renditions.proto";
import "midi-renditions.proto";
import "client-message.proto";
import "pianorhythm-app-renditions.proto";

message AppStateEffects {
  enum Action {
    Unknown = 0;
    UpdateUser = 1;
    AddUser = 2;
    RemoveUser = 3;
    UsersSet = 4;
    JoinedRoomSuccess = 5;
    UsersTypingSet = 6;
    JoinRoomFailResponse = 7;
    Toast = 8;
    SetRooms = 9;
    AddRoom = 11;
    UpdateRoom = 12;
    DeleteRoom = 13;
    SetRoomChatHistory = 14;
    AddChatMessage = 15;
    EditChatMessage = 16;
    DeleteChatMessage = 17;
    OnWelcome = 18;
    SetChatMessages = 19;
    CreateRoomValidationErrors = 20;
    UpdateRoomValidationErrors = 21;
    SetRoomSettings = 22;
    SetRoomFullDetails = 23;
    SetRoomOwner = 24;
    MidiMessageOutput = 25;
    MuteEveryoneElse = 26;
    SynthSoundfontFailedToLoad = 27;
    SynthChannelUpdated = 28;
    SetSlotMode = 29;
    SetPrimaryChannel = 30;
    ListenToProgramChanges = 31;
    ClientIsMuted = 32;
    DrumChannelIsMuted = 33;
    MousePositionSetsVelocity = 34;
    MaxMultiModeChannels = 35;
    EqualizerEnabled = 36;
    ReverbEnabled = 37;
    ClientUpdated = 38;
    SoundfontPresetsLoaded = 39;
    SetPageLoaderDetails = 40;
    LoadRoomStage = 41;
    AppSettingsUpdated = 42;
    WebsocketDisconnected = 43;
    RoomStageLoaded = 44;
    EmittingNotesToServer = 45;
    ConnectionState = 46;
    UserChatMuted = 47;
    UserNotesMuted = 48;

    // Renderering
    RendererPianoKeyUpdate = 1002;
    RendererPianoPedalUpdate = 1003;
    RendererEntityUpdate = 1004;
    RendererMeshLoaded = 1005;
    RendererCameraLock = 1006;
    RendererCameraReset = 1007;
    RendererLoaded = 1008;

    // Midi Sequencer
    MidiSequencerEvent = 2000;
    MidiSequencerDownloadMidi = 2001;
    MidiSequencerFailedToLoadMidi = 2002;

    // Audio
    AudioApplyVelocityCurve = 3000;

    // Midi Looper
    AppMidiLooperTrackUpdated = 4000;

    // Avatar
    AvatarCustomizationUpdated = 5000;
  }

  message ProcessedMidiMessageOutput {
    string device_name = 1;
    float ms = 2;
    double delay = 3;
    string socketId = 4;
    PianoRhythm.Serialization.ServerToClient.Msgs.MidiMessageOutputDto.MidiMessageBuffer data = 5;
  }

  message LoadRoomStageDetails {
    PianoRhythm.Serialization.RoomRenditions.RoomStages roomStage = 1;
    PianoRhythm.Serialization.RoomRenditions.RoomType roomType = 2;
    optional PianoRhythm.Serialization.RoomRenditions.RoomStageDetails roomStageDetails = 3;
  }

  message BytesPayload {
    repeated uint32 payload = 1;
    optional string id = 2;
  }

  Action action = 1;
  optional string sourceSocketID = 999;

  oneof data {
    ProcessedMidiMessageOutput processedMidiMessageOutput = 2;
    PianoRhythm.Serialization.ServerToClient.UserRenditions.ClientSideUserDto clientSideUserDto = 3;
    string socketId = 4;
    PianoRhythm.Serialization.ServerToClient.Msgs.UserDtoList userDtoList = 5;
    PianoRhythm.Serialization.ServerToClient.Msgs.ClientSideUserDtoList clientSideUserDtoList = 6;
    PianoRhythm.Serialization.ServerToClient.Msgs.JoinedRoomData joinedRoomData = 7;
    PianoRhythm.Serialization.ServerToClient.Msgs.RoomChatHistory roomChatHistory = 8;
    PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDto chatMessageDto = 9;
    PianoRhythm.Serialization.ServerToClient.Msgs.MidiMessageOutputDto midiMessageOutputDto = 10;
    PianoRhythm.Serialization.ServerToClient.Msgs.SocketIdList socketIdList = 11;
    string message = 12;
    PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.JoinRoomFailResponse joinRoomFailResponse = 13;
    PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.RoomsList roomsList = 14;
    PianoRhythm.Serialization.RoomRenditions.BasicRoomDto basicRoomDto = 15;
    string roomId = 16;
    PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDtoList chatMessageDtoList = 17;
    string messageId = 18;
    PianoRhythm.Serialization.ServerToClient.Msgs.WelcomeDto welcomeDto = 19;
    PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.ClientValidationErrorList clientValidationErrorList = 20;
    PianoRhythm.Serialization.RoomRenditions.RoomSettings roomSettings = 21;
    PianoRhythm.Serialization.RoomRenditions.RoomFullDetails roomFullDetails = 22;
    PianoRhythm.Serialization.ServerToClient.UserRenditions.UserDto userDto = 23;
    PianoRhythm.Serialization.Midi.Msgs.AudioChannel audioChannel = 24;
    PianoRhythm.Serialization.Midi.Msgs.ActiveChannelsMode slotMode = 25;
    int32 int32Value = 26;
    uint32 uint32Value = 27;
    bool boolValue = 28;
    PianoRhythm.Serialization.ServerToClient.UserRenditions.UserClientDto userClientDto = 29;
    PianoRhythm.Serialization.Midi.Msgs.InstrumentsList instrumentsList = 30;
    LoadRoomStageDetails loadRoomStageDetails = 31;
    BytesPayload bytesPayload = 32;
    string stringValue = 33;

    // App Related
    PianoRhythm.AppRenditions.AppPianoKey appPianoKey = 5000;
    PianoRhythm.AppRenditions.AppPianoPedal appPianoPedal = 5001;
    PianoRhythm.AppRenditions.AppRenderableEntity appRenderableEntity = 5002;
    PianoRhythm.AppRenditions.AppPageloaderDetail appPageLoaderDetail = 5003;
    PianoRhythm.AppRenditions.AppNotificationConfig appNotificationConfig = 5004;
    PianoRhythm.AppRenditions.AppSettings appSettings = 5005;
    PianoRhythm.AppRenditions.AppMidiSequencerEvent midiSequencerEvent = 5006;
    PianoRhythm.AppRenditions.AppMidiTrack appMidiTrack = 5007;
    PianoRhythm.AppRenditions.AppRoomStageLoaded appRoomStageLoaded = 5008;
  }
}