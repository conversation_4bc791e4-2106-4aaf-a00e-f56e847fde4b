syntax = "proto3";
package PianoRhythm.Serialization.ClientToServer.Msgs;

import "room-renditions.proto";
import "user-renditions.proto";
import "midi-renditions.proto";
import "world-renditions.proto";

message ChatMessageInputData {
  message ChatMessageDataOptions {
    bool syncToDiscord = 1;
    bool isFromPlugin = 2;
    string messageReplyID = 3;
    string messageEditID = 4;
  }

  string text = 1;
  ChatMessageDataOptions options = 2;
}

message MidiMessageInputDto {
  message MidiMessageInputBuffer {
    PianoRhythm.Serialization.Midi.Msgs.MidiDto data = 1;
    double delay = 2;
  }

  string time = 1;
  repeated MidiMessageInputBuffer data = 2;
}

enum ServerMessageType {
  Invalid = 0;
  Hello = 1;
  Disconnect = 2;
  RoomChatMessage = 3;
  RoomChatServerCommand = 4;
  MousePosMessage = 5;
  MidiMessage = 6;
  ServerCommand = 7;
  ServerModCommand = 8;
  CreateRoomCommand = 9;
  UpdateRoomCommand = 10;
  JoinRoomByName = 11;
  RoomOwnerCommand = 12;
  JoinNextAvailableLobby = 13;
  AvatarCommand = 14;
}

message RoomIDWithPassword {
  string roomID = 1;
  string password = 2;
}

message LoginDataDto {
  string username = 1;
  string password = 2;
}

message SimpleEmailDto {
  string email = 1;
}

message ResetPasswordDataDto {
  string password = 1;
  string confirmpassword = 2;
  string token = 3;
}

message RegistrationData {
  string username = 1;
  string email = 2;
  string password = 3;
  string confirmpassword = 4;
  bool acceptedtos = 5;
}

message ServerCommandDU {
  enum CommandType {
    Invalid = 0;
    UserUpdateCommand = 2;
    PermissionUpdatedCommand = 3;
    Join = 7;
    JoinRoomWithPassword = 8;
    CreateOrJoinRoom = 10;
    GetRoomSettings = 11;
    EnterLobby = 12;
    CreateRoom = 13;
    UpdateRoom = 14;
    LeaveRoom = 15;
    Ping = 16;
    GetAllRooms = 17;
    GetAllUsersInRoom = 18;
    Login = 19;
    Register = 20;
    ResendEmailVerification = 21;
    ResetPassword = 22;
    ForgotPassword = 23;
    IsTyping = 24;
    RoomLoaded = 25;
    UploadClientSettings = 26;
    SendFriendRequest = 27;
    SendUnfriendRequest = 28;
    AcceptFriendRequest = 29;
    DenyFriendRequest = 30;
    DeleteSentFriendRequest = 31;
    UploadOrchestraModelCustomizationData = 33;
    UploadCharacterData = 34;
    DeleteChatMessageById = 35;
    DeleteChatMessageByUsertag = 36;
    GetRoomFullDetails = 37;
  }

  CommandType commandType = 1;

  oneof commandData {
    string roomID = 2;
    string roomName = 3;
    string roomIDorName = 4;
    string usertag = 5;
    string jsonValue = 7;
    bool boolean = 8;
    RoomIDWithPassword roomIDAndPassword = 9;
    LoginDataDto loginData = 10;
    SimpleEmailDto emailData = 11;
    ResetPasswordDataDto resetPasswordData = 12;
    RegistrationData registrationData = 13;
    string stringValue = 14;
  }
}

message RoomOwnerCommandDU {
  enum CommandType {
    Invalid = 0;
    KickUser = 1;
    RemovedKickedUser = 2;
    GetKickedUsersList = 3;
    GiveCrown = 4;
    ClearChat = 5;
    GetRoomSettings = 6;
  }

  CommandType commandType = 1;

  message KickedUserData {
    string usertag = 1;
    string socketID = 2;
    optional string time = 3;
  }

  oneof commandData {
    string usertag = 2;
    string socketID = 3;
    KickedUserData kickedUserData = 4;
  }
}

message ServerModCommandDto {
  enum ModCommandType {
    Invalid = 0;
    BanUser = 1;
    UnbanAccount = 2;
    UnbanIp = 3;
    AddBadge = 4;
    RemoveBadge = 5;
    PermissionUpdatedCommand = 6;
    KickUser = 7;
    ClearChat = 8;
  }

  ModCommandType commandType = 1;
  oneof commandData {
    string stringValue = 2;
    string jsonValue = 3;
    string usertag = 4;
    string socketID = 5;
    PianoRhythm.Serialization.ServerToClient.UserRenditions.UserBadges userBadge = 6;
  }
}

message ServerModCommandDU {
  string usertag = 1;
  ServerModCommandDto serverCommand = 2;
}

message RoomChatServerCommandMessage {
  string command = 1;
}

message CreateRoomParam {
  optional string RoomID = 1;
  string RoomName = 2;
  PianoRhythm.Serialization.RoomRenditions.RoomType RoomType = 3;
  string RoomOwner = 4;
  PianoRhythm.Serialization.RoomRenditions.RoomStatus RoomStatus = 5;
  optional string Password = 6;
  int32 MaxPlayers = 7;
  optional string WelcomeMessage = 8;
  bool AutoRemove = 9;
  bool OnlyOwnerCanChat = 10;
  bool OnlyOwnerCanPlay = 11;
  bool AllowBlackMidi = 12;
  bool AllowGuests = 13;
  bool AllowBots = 14;
  bool OnlyMods = 15;
  bool FilterProfanity = 16;
  optional string StageDetailsJSON = 17;
  optional PianoRhythm.Serialization.RoomRenditions.RoomHostDetails HostDetails = 18;
  bool NoChatAllowed = 19;
  bool NoPlayingAllowed = 20;
  bytes StageDetailsPROTO = 21;
}

message AvatarCommandDto {
  enum AvatarCommandType {
    Invalid = 0;
    SetPosition = 1;
    SetPianoBenchTarget = 2;
    SetAvatarCustomizationData = 3;
  }

  AvatarCommandType commandType = 1;
  optional string socketID = 2;

  oneof commandData {
    PianoRhythm.Serialization.ServerToClient.UserRenditions.AvatarWorldDataDto.AvatarMessageWorldPosition worldPosition = 3;
    int32 intValue = 4;
    PianoRhythm.Serialization.WorldRenditions.AvatarCustomizationData avatarCustomizationData = 5;
  }
}

message ServerMessage {
  ServerMessageType messageType = 1;

  message JoinRoomByNameRequest {
    string roomName = 1;
    bool createRoomIfNotExist = 2;
    bool joinNextAvailableLobby = 3;
  }

  oneof messageData {
    ChatMessageInputData chatMessageInputData = 2;
    MidiMessageInputDto midiMessageInputDto = 3;
    ServerCommandDU serverCommand = 4;
    ServerModCommandDU serverModCommand = 5;
    RoomChatServerCommandMessage roomChatServerCommand = 6;
    CreateRoomParam createRoomParam = 7;
    JoinRoomByNameRequest joinRoomRequest = 8;
    RoomOwnerCommandDU roomOwnerCommand = 9;
    AvatarCommandDto avatarCommand = 10;
  }
}