syntax = "proto3";
package PianoRhythm.AppStateActions;

import "room-renditions.proto";
import "user-renditions.proto";
import "client-message.proto";
import "server-message.proto";
import "midi-renditions.proto";
import "pianorhythm-app-renditions.proto";
import "world-renditions.proto";

message SocketIdWithInt32 {
  string socketId = 1;
  uint32 int32Value = 2;
}

message ChannelWithBool {
  uint32 channel = 1;
  bool boolValue = 2;
}

message ChannelWithUint32{
  uint32 channel = 1;
  uint32 uint32Value = 2;
}

message AudioSynthActionData {
  uint32 channel = 1;
  uint32 note = 2;
  uint32 velocity = 3;
  PianoRhythm.Serialization.Midi.Msgs.MidiNoteSource noteSource = 4;
}

message AudioSynthActions {
  enum Action {
    NoteOn = 0;
    NoteOff = 1;
    AddUser = 2;
    RemoveUser = 3;
    AddClient = 4;
    SetChannelVolume = 5;
    SetChannelPan = 6;
    SetChannelExpression = 7;
  }

  Action action = 1;
  optional string sourceSocketID = 999;
  optional uint64 sourceHashedSocketID = 998;

  oneof data {
    string socketId = 2;
    uint32 channel = 3;
    ChannelWithBool channelWithBool = 4;
    PianoRhythm.Serialization.Midi.Msgs.Instrument instrument = 5;
    uint32 uint32Value = 6;
    PianoRhythm.Serialization.Midi.Msgs.InstrumentsList instrumentsList = 7;
    bool boolValue = 8;
    ChannelWithUint32 channelWithUint32 = 9;
    AudioSynthActionData synthData = 10;
  }
}

message AppStateActions {
  enum Action {
    Unknown = 0;
    DisableUI = 87;
    ResetState = 998;
    EnableUI = 1;
    SetCanvasLoaded = 2;
    SetCurrentRoomName = 3;
    SetCurrentRoomOwner = 4;
    SetLoggedIn = 5;
    SetMaintenanceModeActive = 6;
    SetClientLoaded = 7;
    UpdateClient = 8;
    UpdateClientByCommand = 9;
    UpdateUser = 10;
    AddUser = 11;
    RemoveUser = 12;
    SetUsers = 13;
    JoinedRoom = 14;
    FailToJoinRoom = 15;
    AddRoom = 16;
    UpdateRoom = 17;
    DeleteRoom = 18;
    SetRooms = 19;
    SetUsersTyping = 20;
    JoinRoomByName = 21;
    JoinRoomById = 22;
    JoinNextAvailableLobby = 23;
    EmitToast = 24;
    EnterRoomPassword = 25;
    WebsocketDisconnected = 26;
    ServerToClientMessage = 27;
    AddRoomChatMessage = 28;
    EditRoomChatMessage = 29;
    DeleteRoomChatMessage = 30;
    SetMaintenanceMode = 31;
    SetRoomChatMessages = 32;
    SetRoomWelcomeMessage = 33;
    SetRoomSettings = 34;
    ClearChat = 35;
    ClearChatByMessageID = 36;
    ClearChatByUsername = 37;
    ClearChatByAmount = 38;
    ClearChatBySocketID = 39;
    ServerCommandResponse = 40;
    GetRoomFullDetails = 41;
    HandleMidiMessage = 42;
    MuteEveryoneElse = 43;
    SetServerTimeOffset = 44;
    SynthAction = 45;
    AddHashedSynthUser = 46;
    RemoveHashedSynthUser = 47;
    InitializeAudioState = 48;
    SetLoadedInstruments = 49;
    SetRoomIsSelfHosted = 50;
    SetChannelActive = 51;
    SetInstrumentOnChannel = 52;
    SetPrimaryChannel = 53;
    SetMaxMultiModeChannels = 54;
    SetIsDrumChannelMuted = 55;
    SetSlotMode = 56;
    SetUseSeparateDrumKit = 57;
    SetOutputOwnNotesToOutput = 58;
    SetUseDefaultBankWhenMissing = 59;
    RemoveInstrumentFromChannel = 60;
    ClearAllAudioChannels = 61;
    IncrementSlotMode = 62;
    ResetAudioChannelsToDefault = 63;
    ToggleChannelActive = 64;
    SetClientIsMuted = 65;
    SetListenToProgramChanges = 66;
    UpdateChannelParameter = 67;
    SetIsPlayingDrumsMode = 68;
    SetMousePositionSetsVelocity = 69;
    SetIsMobile = 70;
    SetCanPlayKeys = 71;
    UpdateChannelFromSynthProgramChangeEvent = 72;
    SetUserVolume = 73;
    SetUserVelocityPercentage = 74;
    SetEqualizerEnabled = 76;
    SetReverbEnabled = 77;
    SetAudioChannel = 78;
    SetRoomStageLoading = 79;
    SetRoomStageLoaded = 80;
    PersistSettings = 82;
    InitializeAppState = 83;
    SetAppSettings = 84;
    Logout = 85;
    TriggerOfflineMode = 86;
    SetConnectionState = 88;
    SetUserMuted = 89;
    SetUserChatMuted = 90;
    SetCommonEnvironment = 91;
    AudioSetApplyVelocityCurve = 92;
    SynthEngineCreated = 93;

    // Render related
    RendererPianoKeyModelLoaded = 1000;
    RendererPianoPedalModelLoaded = 1001;
    RendererMainCameraLoaded = 1002;
    RendererLoadMesh = 1003;
    RendererDrumSetMeshLoaded = 1004;
    RendererResetCamera = 1005;
    RendererToggleLockCamera = 1006;
    RendererEnableRenderLoop = 1007;
    RendererDisableRenderLoop = 1008;
    RendererSetKeyboardMappings = 1009;
    RendererToggleDisplayKeyboardMappings = 1010;
    RendererSetCameraTopPosition = 1011;

    // Midi File Sequencer related
    MidiSequencerResume = 2000;
    MidiSequencerPause = 2001;
    MidiSequencerStop = 2002;
    MidiSequencerEnableLoop = 2003;
    MidiSequencerDisableLoop = 2004;
    MidiSequencerSeekPosition = 2005;
    MidiSequencerMuteTrack = 2006;
    MidiSequencerSoloTrack = 2007;
    MidiSequencerSetSpeed = 2008;
    MidiSequencerSetBPM = 2009;
    MidiSequencerRewind = 2010;
    MidiSequencerForward = 2011;
    MidiSequencerEnablePreviewOnly = 2012;
    MidiSequencerDisablePreviewOnly = 2013;

    // VP Sheet Music Sequencer
    VPSequencerLoadData = 3000;
    VPSequencerResume = 3001;
    VPSequencerPause = 3002;
    VPSequencerStop = 3003;
    VPSequencerEnableLoop = 3004;
    VPSequencerDisableLoop = 3005;
    VPSequencerDownloadAsMidi = 3006;
    VPSequencerEnableSustain = 3007;
    VPSequencerDisableSustain = 3008;
    VPSequencerSetPlaybackSpeed = 3009;
    VPSequencerSetBPM = 3010;

    // App Midi Track (Looper)
    AppMidiLooperRecord = 4000;
    AppMidiLooperPlay = 4001;
    AppMidiLooperStop = 4002;
    AppMidiLooperSetBPM = 4003;
    AppMidiLooperDispose = 4004;
    AppMidiLooperStopRecord = 4005;
    AppMidiLooperEnableTrim = 4006;
    AppMidiLooperDisableTrim = 4007;
    AppMidiLooperSetTrack = 4008;
    AppMidiLooperGetTracks = 4009;
    AppMidiLooperClearTrack = 4010;
    AppMidiLooperToggleTrackPlaying = 4011;

    // Avatar
    SetAvatarPosition = 5000;
    SetAvatarPianoBench = 5001;
    UpdateAvatarCustomization = 5002;

    // Displays
    ShowAvatarCustomizationScreen = 6000;
    HideAvatarCustomizationScreen = 6001;
  }

  Action action = 1;
  optional string sourceSocketID = 999;

  message RendererLoadMeshDetails {
    string filePath = 1;
    string meshName = 2;
  }

  oneof data {
    string stringValue = 2;
    bool boolValue = 3;
    PianoRhythm.Serialization.ServerToClient.UserRenditions.UserDto userDto = 4;
    PianoRhythm.Serialization.ServerToClient.UserRenditions.UserClientDto userClientDto = 5;
    PianoRhythm.Serialization.RoomRenditions.BasicRoomDto basicRoomDto = 6;
    PianoRhythm.Serialization.ServerToClient.UserRenditions.UserUpdateCommand userUpdateCommand = 7;
    PianoRhythm.Serialization.ServerToClient.Msgs.UserDtoList userDtoList = 8;
    PianoRhythm.Serialization.ServerToClient.Msgs.SocketIdList socketIdList = 9;
    PianoRhythm.Serialization.ServerToClient.Msgs.FriendDtoList friendDtoList = 10;
    string socketId = 11;
    string usertag = 12;
    string userid = 13;
    PianoRhythm.Serialization.ServerToClient.Msgs.JoinedRoomData joinedRoomData = 14;
    PianoRhythm.Serialization.ServerToClient.Msgs.RoomChatHistory roomChatHistory = 15;
    PianoRhythm.Serialization.ServerToClient.Msgs.ChatMessageDto chatMessageDto = 16;
    PianoRhythm.Serialization.RoomRenditions.RoomSettings roomSettings = 17;
    PianoRhythm.Serialization.RoomRenditions.RoomFullDetails roomFullDetails = 18;
    PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.JoinRoomFailResponse joinRoomFailResponse = 19;
    PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse.RoomsList roomsList = 20;
    PianoRhythm.Serialization.ClientToServer.Msgs.ServerMessage.JoinRoomByNameRequest joinRoomByNameRequest = 21;
    PianoRhythm.Serialization.ClientToServer.Msgs.ServerCommandDU serverCommand = 22;
    PianoRhythm.Serialization.ClientToServer.Msgs.RoomOwnerCommandDU roomOwnerCommand = 23;
    PianoRhythm.Serialization.ClientToServer.Msgs.RoomIDWithPassword roomIdWithPassword = 24;
    string roomId = 25;
    PianoRhythm.Serialization.ServerToClient.Msgs.MidiMessageOutputDto midiMessageOutputDto = 26;
    PianoRhythm.Serialization.ServerToClient.Msgs.WelcomeDto welcomeDto = 27;
    PianoRhythm.Serialization.ServerToClient.Msgs.PendingFriendRequestList pendingFriendRequestList = 28;
    PianoRhythm.Serialization.ServerToClient.Msgs.KickedUsersList kickedUsersList = 29;
    PianoRhythm.Serialization.ServerToClient.Msgs.ClientSideUserDtoList clientSideUserDtoList = 30;
    PianoRhythm.Serialization.ServerToClient.UserRenditions.PendingFriendRequest pendingFriendRequest = 31;
    PianoRhythm.Serialization.ServerToClient.Msgs.ClientMessage clientMsg = 32;
    PianoRhythm.Serialization.ServerToClient.Msgs.CommandResponse commandResponse = 33;
    AudioSynthActions audioSynthAction = 34;
    int32 int32Value = 35;
    uint32 uint32Value = 36;
    SocketIdWithInt32 socketIdWithIn32 = 37;
    PianoRhythm.Serialization.Midi.Msgs.Instrument instrument = 38;
    PianoRhythm.Serialization.Midi.Msgs.InstrumentsList instrumentsList = 39;
    PianoRhythm.Serialization.Midi.Msgs.SetChannelInstrumentPayload setChannelInstrumentPayload = 40;
    PianoRhythm.Serialization.Midi.Msgs.SetChannelDetailsPayload setChannelDetailsPayload = 41;
    PianoRhythm.Serialization.Midi.Msgs.ActiveChannelsMode slotMode = 42;
    PianoRhythm.Serialization.Midi.Msgs.UpdateChannelPayload updateChannelPayload = 43;
    PianoRhythm.Serialization.Midi.Msgs.SynthEventProgramChangePayload synthEventProgramChangePayload = 44;
    PianoRhythm.Serialization.Midi.Msgs.AudioChannel audioChannel = 45;
    PianoRhythm.AppRenditions.AppPianoKey appPianoKey = 46;
    PianoRhythm.AppRenditions.AppPianoPedal appPianoPedal = 47;
    PianoRhythm.AppRenditions.AppRenderableEntity appRenderableEntity = 48;
    RendererLoadMeshDetails loadMeshDetails = 49;
    PianoRhythm.AppRenditions.AppNotificationConfig appNotificationConfig = 50;
    PianoRhythm.AppRenditions.AppSettings appSettings = 51;
    ChannelWithBool channelWithBool = 52;
    double doubleValue = 53;
    PianoRhythm.AppRenditions.AppVPSequencerFileLoad vpFileLoad = 54;
    PianoRhythm.AppRenditions.AppMidiTrack appMidiTrack = 55;
    PianoRhythm.AppRenditions.AppCommonEnvironment appCommonEnvironment = 56;
    PianoRhythm.AppRenditions.AppKeyboardMappingVisualizeVec keyboardVisualizeMappings = 57;
    PianoRhythm.Serialization.ServerToClient.UserRenditions.AvatarWorldDataDto.AvatarMessageWorldPosition avatarWorldPosition = 58;
    int32 avatarPianoBench = 59;
    PianoRhythm.Serialization.WorldRenditions.AvatarCustomizationData avatarCustomizationData = 60;
  }
}