export RUSTFLAGS="-C target-feature=+atomics,+bulk-memory,+mutable-globals -C link-arg=--max-memory=4294967296"

cargo rustc --package pianorhythm_bevy_renderer --features="webgl2" --lib --crate-type cdylib --target wasm32-unknown-unknown --release -Z build-std=std,panic_abort

wasm-bindgen ./target/wasm32-unknown-unknown/release/pianorhythm_bevy_renderer.wasm \
  --out-dir ./pkg/webgl2 \
  --typescript \
  --target web