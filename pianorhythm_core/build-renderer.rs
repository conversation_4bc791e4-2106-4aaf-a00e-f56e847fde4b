use std::fs::File;
use std::io::Write;
use std::path::Path;

fn main() {
    let target_arch = std::env::var("CARGO_CFG_TARGET_ARCH").unwrap_or_default();
    let out_dir = std::env::var("OUT_DIR").unwrap();
    let mut path = Path::new(&out_dir).join("../../../").join("assets/models.assets.ron");

    if target_arch == "wasm32"
    {
        path = Path::new(&out_dir).join("../../../../../../../public/models/").join("models.assets.ron");
    }

    let models_prefix = "models";
    let mut root_prefix = "";

    // Debug build desktop
    if cfg!(all(debug_assertions, not(target_arch = "wasm32"))) && target_arch != "wasm32" {
        root_prefix = "../../../../../public";
    }

    let models = r#"
        ({
            "model.babygrand-piano-high-poly": File(
                path: "{MODELS_PATH_PREFIX}/White_BabyGrand_Aug_2024.glb",
            ),
            "model.babygrand-piano-low-poly": File(
                path: "{MODELS_PATH_PREFIX}/White_BabyGrand_Aug_2024_Low_Poly.glb",
            ),
            "model.babygrand-piano-bench-high-poly": File(
                path: "{MODELS_PATH_PREFIX}/White_BabyGrand_Bench_Aug_2024.glb",
            ),
            "model.babygrand-piano-bench-low-poly": File(
                path: "{MODELS_PATH_PREFIX}/White_BabyGrand_Bench_Aug_2024_Low_Poly.glb",
            ),
            "model.guitars-high-poly": File (
                path: "{MODELS_PATH_PREFIX}/Guitars.glb",
            ),
            "model.drum-set-high-poly": File(
                path: "{MODELS_PATH_PREFIX}/Drum_Set.glb",
            ),
            "model.drum-set-low-poly": File(
                path: "{MODELS_PATH_PREFIX}/Drum_Set_Low_Poly.glb",
            ),
            "model.characters.base-chibi-boy-male": File(path: "{MODELS_PATH_PREFIX}/characters/chibi_boy.glb"),
            "model.characters.base-chibi-girl-female": File(path: "{MODELS_PATH_PREFIX}/characters/chibi_girl.glb"),
            "model.stage.forest": File(
                path: "{MODELS_PATH_PREFIX}/Forest_v2.glb"
            ),
            "texture.active-cursor-arrow": File(
                path: "{MODELS_PATH_PREFIX}/textures/active_cursor_arrow.png",
            )
        })
    "#;

    let mut models = models
        .replace("{MODELS_PATH_PREFIX}", &format!("{}/{}", root_prefix, models_prefix));

    models = models.trim().to_string();

    let prefix = path.parent().unwrap();
    std::fs::create_dir_all(prefix).unwrap();

    let mut file = File::create(path).unwrap();
    file.write_all(models.as_bytes()).unwrap();
}